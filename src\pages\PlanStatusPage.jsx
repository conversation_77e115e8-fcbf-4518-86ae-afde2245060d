import React, { useEffect, useState } from "react";
import { Button, Input, Modal, Select } from "@mantine/core";
import Header from "../components/Header";
import { DatePicker } from "@mantine/dates";
import { IconFilter, IconPhone, IconUserCheck } from "@tabler/icons";
import { getAlluserSubscription } from "../API/api-endpoint";
import Flow from "../components/subscription/Flow";
import { LoadingButton } from "@mui/lab";

const PlanStatusPage = () => {
  const [opened, setOpened] = useState(false);
  const [allPPrograms, setGetAllPrograms] = useState();
  const [allSubscriptionPackage, setGetAllSubscriptionPackage] = useState();
  const [getUsers, setgetUsers] = useState();
  const [isOpen, setIsOpen] = useState(false);


  console.log("allPPrograms", allSubscriptionPackage);

  const getSubscription = async () => {
    const result = await getAlluserSubscription();
    console.log("result", result);
    setGetAllPrograms(result?.programs);
    setGetAllSubscriptionPackage(result.ongoingsubs);
    setgetUsers(result.user)
  };
  useEffect(() => {
    getSubscription();
  }, []);

  const athletes = [
    {
      id: 1,
      name: "Rajan Mittal",
      img: "https://www.yoska.in/kona-coach/images/app/warning.png",
      run: "5 km",
      planEndDate: "25-Sep-2022",
      templateEndDate: "11-Sep-2022",
      phone: "+60122145767",
    },
    {
      id: 2,
      name: "Santhosh K C",
      img: "https://www.yoska.in/kona-coach/images/app/warning.png",
      run: "Half Marathon (21.1 Kms)",
      planEndDate: "21-Oct-2022",
      templateEndDate: "18-Sep-2022",
      phone: "+9980577557",
    },
    {
      id: 3,
      name: "Balaji Annadurai",
      img: "https://www.yoska.in/kona-coach/images/app/warning.png",
      run: "5 km",
      planEndDate: "27-Nov-2022",
      templateEndDate: "18-Sep-2022",
      phone: "+919865889996",
    },
    {
      id: 4,
      name: "Lemmie Dsilva",
      img: "https://www.yoska.in/kona-coach/images/app/caution.png",
      run: "5 km",
      planEndDate: "09-Dec-2022",
      templateEndDate: "30-Oct-2022",
      phone: "+9545096600",
    },
  ];

  return (
    <>
      <Header />
      
      {/* Topbar */}
      <div className="p-6">
        <div className="flex flex-col items-start gap-x-2">
        {isOpen &&
          <Modal opened={isOpen} onClose={() => setIsOpen(false)} title="Subscription">
          <Flow setIsOpen={setIsOpen} getSubscription={getSubscription} getUsers={getUsers}/>
          </Modal>
          }
          <h1 className="font-medium text-xl">Athlete Plan Status</h1>
          <small>Last Updated:05-Dec-2022 03:17 PM</small>
        </div>
        <br />
        <LoadingButton
      size="large"
      variant="contained"
      sx={{
        marginBottom: 2,
        // color: "white",
        // backgroundColor: "rgb(145, 85, 253)",
        color: "white",
        backgroundColor: "#2563EB",
      }}
      // onClick={() => onSubmit()}
      // loading={isLoading}
      loadingPosition="start"
      onClick={()=>setIsOpen(!isOpen)}
    >
    Create Subscription
    </LoadingButton>
        <div className="flex flex-wrap gap-4 md:gap-8 items-center mb-6">
          <div className="cursor-pointer">
            <Input.Wrapper id="input-demo">
              <Input id="input-demo" placeholder="Search" />
            </Input.Wrapper>
          </div>
          <div className="cursor-pointer" onClick={() => setOpened(true)}>
            <IconFilter size={28} color="#334155" stroke={1} />
          </div>
          <div className="cursor-pointer">
            <IconUserCheck color="#1e293b" stroke={1} />
          </div>
          <div>
            <p>Total: {allSubscriptionPackage?.length}</p>
          </div>
        </div>
        <br />
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 items-start gap-6">
{/**
          // {athletes.map((item) => (

/}
        
         {/*
         ))}
        */}
        {allSubscriptionPackage?.map((item)=>{
          return(
            <div
            className="w-full flex flex-col gap-1 p-4 border rounded-md"
            key={getUsers?.id}
          >
            <div className="flex gap-4 items-center mb-1">
              <p>{getUsers?.firstname} {getUsers?.lastname}</p>
            </div>
            <p className="text-sm">Program Name: {item?.program?.program_name}</p>

            
            <p className="text-sm">Plan End Date: {item?.end_date}</p>
            <p className="text-sm">
              Template End Date(Min): {item?.end_date}
            </p>
            <div className="flex gap-2 items-center text-sm">
              <IconPhone size={20} color="#334155" stroke={1} />
              <p>{getUsers?.phone}</p>
            </div>
          </div>
          )
        })}
        
        </div>
      </div>
      <Modal opened={opened} onClose={() => setOpened(false)} title="Filters">
        {/* Modal content */}
        <div className="w-full flex flex-col gap-4 items-start">
          <Select
            label="Coached by"
            className="w-full"
            data={[
              { value: "Aarathi Swaminathan", label: "Aarathi Swaminathan" },
              { value: "Abhishek Avhad", label: "Abhishek Avhad" },
              { value: "Agenlo Desa", label: "Agenlo Desa" },
              { value: "Amit Goyal", label: "Amit Goyal" },
            ]}
          />
          <Select
            label="Group"
            className="w-full"
            data={[
              { value: "Run", label: "Run" },
              { value: "All", label: "All" },
              { value: "Blank", label: "Blank" },
            ]}
          />
          <DatePicker label="Plan End By" className="w-full" />
          <div className="mt-2 flex gap-2 item-center flex-wrap">
            <button className="bg-blue-500 text-sm font-regular px-4 py-2 rounded-sm text-white">
              Apply
            </button>
            <button className="bg-blue-500 text-sm font-regular px-4 py-2 rounded-sm text-white">
              Clear
            </button>
          </div>
        </div>
      </Modal>
      
    </>
  );
};

export default PlanStatusPage;
