import { useState, useEffect } from "react";
import { But<PERSON> } from "../ui/button";
import { Input } from "../ui/input";
import { Label } from "../ui/label";
import { Textarea } from "../ui/textarea";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "../ui/select";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "../ui/dialog";
import {
	createSubSugmentdata,
	updateSubSugmentdata,
	getAllSugmentData,
} from "../../API/api-endpoint";
import Swal from "sweetalert2";

export const SubSegmentDialog = ({ open, onClose, onSuccess, editingItem }) => {
	const [formData, setFormData] = useState({
		name: "",
		segment_id: "",
		subsegment_percentage: "",
		comments: "",
	});
	const [isLoading, setIsLoading] = useState(false);
	const [segmentList, setSegmentList] = useState([]);
	const [isLoadingSegments, setIsLoadingSegments] = useState(false);

	useEffect(() => {
		if (open) {
			fetchSegments();
		}
	}, [open]);

	useEffect(() => {
		if (open) {
			if (editingItem?.id) {
				const editData = {
					name: editingItem.name || "",
					segment_id: String(editingItem.segment_id || ""),
					subsegment_percentage: String(
						editingItem.subsegment_percentage || ""
					),
					comments: editingItem.comments || "",
				};
				setFormData(editData);
			} else {
				const newData = {
					name: "",
					segment_id: "",
					subsegment_percentage: "",
					comments: "",
				};
				setFormData(newData);
			}
		}
	}, [open, editingItem]);

	const fetchSegments = async () => {
		try {
			setIsLoadingSegments(true);
			const response = await getAllSugmentData();
			if (response) {
				setSegmentList(response || []);
			}
		} catch (error) {
			console.error("Error fetching segments:", error);
			Swal.fire({
				title: "Error",
				text: "Failed to fetch segments",
				icon: "error",
				timer: 3000,
				showConfirmButton: false,
			});
		} finally {
			setIsLoadingSegments(false);
		}
	};

	const handleInputChange = (field, value) => {
		setFormData((prev) => ({
			...prev,
			[field]: value,
		}));
	};

	const handleSubmit = async (e) => {
		e.preventDefault();

		if (!formData.segment_id) {
			Swal.fire({
				title: "Error",
				text: "Segment name is required",
				icon: "error",
				timer: 1800,
				showConfirmButton: false,
			});
			return;
		}

		if (!formData.name.trim()) {
			Swal.fire({
				title: "Error",
				text: "Sub segment name is required",
				icon: "error",
				timer: 1800,
				showConfirmButton: false,
			});
			return;
		}

		if (!formData.subsegment_percentage.trim()) {
			Swal.fire({
				title: "Error",
				text: "Sub segment percentage is required",
				icon: "error",
				timer: 1800,
				showConfirmButton: false,
			});
			return;
		}

		const percentage = parseFloat(formData.subsegment_percentage);
		if (isNaN(percentage) || percentage < 0 || percentage > 100) {
			Swal.fire({
				title: "Error",
				text: "Sub segment percentage must be a number between 0 and 100",
				icon: "error",
				timer: 1800,
				showConfirmButton: false,
			});
			return;
		}

		try {
			setIsLoading(true);

			const apiData = {
				name: formData.name.trim(),
				segment_id: parseInt(formData.segment_id),
				subsegment_percentage: parseFloat(
					formData.subsegment_percentage
				),
				comments: formData.comments.trim(),
			};

			let response;
			if (editingItem?.id) {
				apiData.id = editingItem.id;
				response = await updateSubSugmentdata(apiData);
			} else {
				response = await createSubSugmentdata(apiData);
			}

			if (response?.status) {
				Swal.fire({
					title: "Success",
					text:
						response.message ||
						`Sub segment ${
							editingItem?.id ? "updated" : "created"
						} successfully`,
					icon: "success",
					timer: 2000,
					showConfirmButton: false,
				});
				onSuccess();
			} else {
				Swal.fire({
					title: "Error",
					text: response?.message || "Failed to save sub segment",
					icon: "error",
					timer: 3000,
					showConfirmButton: false,
				});
			}
		} catch (error) {
			console.error("Error saving sub segment:", error);
			Swal.fire({
				title: "Error",
				text: "An error occurred while saving the sub segment",
				icon: "error",
				timer: 3000,
				showConfirmButton: false,
			});
		} finally {
			setIsLoading(false);
		}
	};

	return (
		<Dialog open={open} onOpenChange={onClose}>
			<DialogContent className='sm:max-w-md bg-white'>
				<DialogHeader>
					<DialogTitle className='text-lg font-semibold text-gray-900'>
						{editingItem?.id
							? "Edit Sub Segment"
							: "Create Sub Segment"}
					</DialogTitle>
				</DialogHeader>

				<form onSubmit={handleSubmit} className='space-y-4'>
					<div className='grid gap-4'>
						<div className='space-y-2'>
							<Label
								htmlFor='segment_id'
								className='text-sm font-semibold'
							>
								Segment Name{" "}
							</Label>
							<Select
								value={formData.segment_id}
								onValueChange={(value) =>
									handleInputChange("segment_id", value)
								}
								disabled={isLoading || isLoadingSegments}
							>
								<SelectTrigger>
									<SelectValue placeholder='Select a segment' />
								</SelectTrigger>
								<SelectContent className='bg-white'>
									{segmentList.map((segment) => (
										<SelectItem
											key={segment.id}
											value={segment.id.toString()}
										>
											{segment.name}
										</SelectItem>
									))}
								</SelectContent>
							</Select>
						</div>

						<div className='space-y-2'>
							<Label
								htmlFor='name'
								className='text-sm font-semibold'
							>
								Sub Segment Name{" "}
							</Label>
							<Input
								id='name'
								type='text'
								className='w-full text-sm'
								value={formData.name}
								onChange={(e) =>
									handleInputChange("name", e.target.value)
								}
								placeholder='Enter sub segment name'
								disabled={isLoading}
								required
							/>
						</div>

						<div className='space-y-2'>
							<Label
								htmlFor='subsegment_percentage'
								className='text-sm font-semibold'
							>
								Sub Segment Percentage{" "}
							</Label>
							<Input
								id='subsegment_percentage'
								type='number'
								min='0'
								max='100'
								step='0.01'
								className='w-full text-sm'
								value={formData.subsegment_percentage}
								onChange={(e) =>
									handleInputChange(
										"subsegment_percentage",
										e.target.value
									)
								}
								placeholder='Enter percentage (0-100)'
								disabled={isLoading}
								required
							/>
						</div>

						<div className='space-y-2'>
							<Label
								htmlFor='comments'
								className='text-sm font-semibold'
							>
								Comments
							</Label>
							<Textarea
								id='comments'
								className='w-full text-sm'
								value={formData.comments}
								onChange={(e) =>
									handleInputChange(
										"comments",
										e.target.value
									)
								}
								placeholder='Enter comments (optional)'
								disabled={isLoading}
								rows={3}
							/>
						</div>
					</div>

					<div className='flex justify-end gap-3 pt-4'>
						<Button
							type='button'
							variant='outline'
							onClick={onClose}
							disabled={isLoading}
						>
							Cancel
						</Button>
						<Button
							type='submit'
							className='bg-orange-600 hover:bg-orange-700 text-white'
							disabled={isLoading}
						>
							{isLoading
								? "Saving..."
								: editingItem?.id
								? "Update Sub Segment"
								: "Create Sub Segment"}
						</Button>
					</div>
				</form>
			</DialogContent>
		</Dialog>
	);
};
