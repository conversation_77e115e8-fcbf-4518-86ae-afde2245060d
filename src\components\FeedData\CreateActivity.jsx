import { FormLabel, Grid, MenuItem, OutlinedInput, TextField } from '@mui/material';
import { Button, Modal, TimePicker } from 'antd'
import React, { useEffect, useState } from 'react'
import { createActivity,  updateActivity, uploadsaveFile } from '../../API/api-endpoint';
import { useFormik } from 'formik';
import Swal from 'sweetalert2';
import { IoCloseSharp } from "react-icons/io5";
import SlickCarousel from '../../pages/SlickCarousel';

const CreateActivity = ({ fetchReport, setShowAssesmentModal, showAssesmentModal, editData, setEditData }) => {
    const formik = useFormik({
        initialValues: {
            activity_name: "", 
            badge:"",
            synonyms:"",
            challengebannerimage:"",

            is_under_coach_profile:false,
        }, validate: (values) => {
            const errors = {};
            if (!values.activity_name) {
                errors.activity_name = "Activity Name is required";
            }
            if (!values.synonyms) {
                errors.synonyms = "Synonyms is required";
            }
            if (!values.challengebannerimage) {
                errors.challengebannerimage = "Banner Image is required";
            }
        
            return errors;
        },
        // validationSchema: {},
        onSubmit: (values, { resetForm }) => {
            handleSubmitAssesmentForm(values, resetForm)

        },
    });
    const handleSubmitAssesmentForm = async (data, resetForm) => {
        let response = ""
        if (editData?.id) {

            response = await updateActivity(data)

        } else {
            response = await createActivity(data)

        }
        if (response?.status) {
            Swal.fire({
                title: "Success",
                text: response.message,
                icon: "success",
            });
            setEditData({})
            setShowAssesmentModal(false)
            fetchReport()
            formik.resetForm()
            formik?.setValues({
                activity_name: "", 
                badge:"",
                synonyms:"",
            challengebannerimage:"",

            })
        } else {
            Swal.fire({
                title: "Error",
                text: response.message,
                icon: "error",
            });
        }
        console.log("response", response);
    }
    useEffect(() => {
        if (editData?.id) {
            const { srID, ...data } = editData
            formik?.setValues(data)
        }
    }, [editData?.id])

    const handleFileUpload = async (event,filename) => {
        const file = event.target.files[0];
        if (file) {

            const formData = new FormData();
            formData.append('file', file);

            const responst = await uploadsaveFile(formData)
            if (responst?.status) {

                formik.setFieldValue(filename, responst?.file)
            }
        }
    }
    return (
        <Modal
            width={1200}
            open={showAssesmentModal}
            onCancel={() => {
                setShowAssesmentModal(false)
                formik.resetForm()
                setEditData({})
                formik?.setValues({
                    activity_name: "", 
                    badge:"",
                    synonyms:""
                })
            }}
            footer={
                <div >
                    
                </div>
                //   loading={isLoading}
            }
        >
            <div className="headingCont">
        <span className="heading">{editData?.id ? "Edit " : "Create"}</span>{" "}
        <span className="orange heading">Activity</span>
       
      </div>

      <div className="parentCont">

            <form  className="form1" onSubmit={formik.handleSubmit}>
                <Grid container spacing={2}>
                    <Grid item xs={12} sm={11}>
                        <FormLabel >Activity Name<span className="text-[red]">*</span></FormLabel>
                    
                        <TextField
                            fullWidth
                            placeholder="Activity Name"
                            size="small"
                            type="text"
                            name="activity_name"
                            value={formik?.values?.activity_name}
                            onChange={formik.handleChange}
                            onBlur={formik.handleBlur}
                            error={formik.touched.activity_name && formik.errors.activity_name}
                            helperText={
                                formik.touched.activity_name && formik.errors.activity_name
                            }

                        />
                    </Grid>
                    <Grid item xs={12} sm={11} className='relative'>
                        <FormLabel >Upload Badge</FormLabel>
                    
                        <TextField
                            fullWidth
                            placeholder="Zone"
                            size="small"
                            type={formik?.values?.badge ? "text" : "file"}
                            name="badge"
                            disabled={formik?.values?.badge}
                            value={formik?.values?.badge}
                            onChange={(e)=>{handleFileUpload(e,'badge')}}
                            error={formik.touched.badge && formik.errors.badge}
                            helperText={
                                formik.touched.badge && formik.errors.badge
                            }
                            inputProps={{
                                accept: ".jpg, .png .jpeg", // Specify accepted file formats here
                            }}

                        />
                        {formik?.values?.badge &&
                            <IoCloseSharp onClick={()=>{
                                formik.setFieldValue("badge", "")
                            }}  color="darkgray" className="cursor-pointer absolute top-0 right-[-10px]" style={{fontSize:"24px"}} />
                        }
                    </Grid>
                      <Grid item xs={12} sm={11} className='relative'>
                        <FormLabel >Upload Banner Image</FormLabel>
                    
                        <TextField
                            fullWidth
                            placeholder="Zone"
                            size="small"
                            type={formik?.values?.challengebannerimage ? "text" : "file"}
                            name="challengebannerimage"
                            disabled={formik?.values?.challengebannerimage}
                            value={formik?.values?.challengebannerimage}
                            onChange={(e)=>{handleFileUpload(e,'challengebannerimage')}}
                            error={formik.touched.challengebannerimage && formik.errors.challengebannerimage}
                            helperText={
                                formik.touched.challengebannerimage && formik.errors.challengebannerimage
                            }
                            inputProps={{
                                accept: ".jpg, .png .jpeg", // Specify accepted file formats here
                            }}

                        />
                       
                        {formik?.values?.badge &&
                            <IoCloseSharp onClick={()=>{
                                formik.setFieldValue("badge", "")
                            }}  color="darkgray" className="cursor-pointer absolute top-0 right-[-10px]" style={{fontSize:"24px"}} />
                        }
                    </Grid>
                    <Grid item xs={12} sm={11}>
                    <FormLabel >Synonyms<span className="text-[red]">*</span></FormLabel>
               
                    <TextField
                        fullWidth
                        placeholder="Synonyms"
                        size="small"
                        type="text"
                        name="synonyms"
                        value={formik?.values?.synonyms}
                        onChange={formik.handleChange}
                        error={formik.touched.synonyms && formik.errors.synonyms}
                        helperText={
                            formik.touched.synonyms && formik.errors.synonyms
                        }

                    />
                </Grid>
                <Grid item xs={12} sm={11}>
                <FormLabel >Is Activity Under Coach Profile:</FormLabel>
                <TextField
                    fullWidth
                    size="small"
                    select
                    name="is_under_coach_profile"
                    value={formik?.values?.is_under_coach_profile}
                    onChange={formik.handleChange}
                    error={formik.touched.is_under_coach_profile && formik.errors.is_under_coach_profile}
                    helperText={
                        formik.touched.is_under_coach_profile && formik.errors.is_under_coach_profile
                    }

                    id="form-layouts-separator-select"
                    labelId="form-layouts-separator-select-label"
                    input={<OutlinedInput id="select-multiple-language" />}
                >
                    <MenuItem value={true}>
                        Yes
                    </MenuItem>
                    <MenuItem value={false}>
                        No
                    </MenuItem>
                </TextField>
            </Grid>
            <Grid item xs={12} sm={10}>
                  <Button 
                  className="btn"
                  key="submit"
                  type="primary"
                  
                  onClick={() => formik.handleSubmit()}
                >
                  Submit
                </Button>
                </Grid>
                </Grid>
            </form>

            <div className="slick-container">
          <SlickCarousel />
        </div>
            </div>
        </Modal>
    )
}
export default CreateActivity
