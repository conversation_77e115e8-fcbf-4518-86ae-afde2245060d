import { useEffect, useState, useMemo } from "react";
import { <PERSON><PERSON> } from "../../components/ui/button";
import { Input } from "../../components/ui/input";
import {
	Card,
	CardContent,
	CardHeader,
	CardTitle,
} from "../../components/ui/card";
import {
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableHeader,
	TableRow,
} from "../../components/ui/table";
import { Badge } from "../../components/ui/badge";
import { Edit, Trash2, Plus } from "lucide-react";
import { WorkoutDataDialog } from "../../components/admin/workout-data-dialog";
import { DeleteConfirmDialog } from "../../components/admin/delete-confirm-dialog";
import Header from "../../components/Header";
import { getAllworkoutData, deleteworkoutData } from "../../API/api-endpoint";
import Swal from "sweetalert2";

export default function WorkoutDataPage() {
	const [data, setData] = useState([]);
	const [isLoading, setIsLoading] = useState(true);
	const [searchTerm, setSearchTerm] = useState("");
	const [currentPage, setCurrentPage] = useState(1);
	const [showDialog, setShowDialog] = useState(false);
	const [editData, setEditData] = useState(null);
	const [deleteId, setDeleteId] = useState(null);

	const pageSize = 10;

	useEffect(() => {
		fetchWorkoutData();
	}, []);

	const fetchWorkoutData = async () => {
		try {
			setIsLoading(true);
			const res = await getAllworkoutData();
			setData(res);
		} catch (error) {
			console.error("Error fetching workout data:", error);
		} finally {
			setIsLoading(false);
		}
	};

	const filteredData = useMemo(() => {
		return data?.filter((row) =>
			row?.workout?.toLowerCase().includes(searchTerm.toLowerCase())
		);
	}, [data, searchTerm]);

	const paginatedData = useMemo(() => {
		const startIndex = (currentPage - 1) * pageSize;
		return filteredData.slice(startIndex, startIndex + pageSize);
	}, [filteredData, currentPage]);

	const totalPages = Math.ceil(filteredData.length / pageSize);

	const handleEdit = (row) => {
		setEditData(row);
		setShowDialog(true);
	};

	const handleDelete = async (id) => {
		try {
			await deleteworkoutData(id);
			setDeleteId(null);
			fetchWorkoutData();
		} catch (err) {
			console.error("Delete failed", err);
		}
	};

	const handleSave = () => {
		fetchWorkoutData();
		setShowDialog(false);
		setEditData(null);
		Swal.fire({
			title: "Success",
			text: "Sub-Workout saved successfully!",
			icon: "success",
			timer: 1800,
			showConfirmButton: false,
		});
	};

	const getWorkoutTypeBadge = (workoutName) => {
		if (workoutName === "Rest Day")
			return <Badge variant='secondary'>Rest</Badge>;
		if (workoutName?.includes("Long"))
			return <Badge className='bg-red-100 text-red-800'>Long</Badge>;
		if (workoutName?.includes("Medium"))
			return (
				<Badge className='bg-yellow-100 text-yellow-800'>Medium</Badge>
			);
		if (workoutName?.includes("Short"))
			return <Badge className='bg-green-100 text-green-800'>Short</Badge>;
		return <Badge variant='outline'>Other</Badge>;
	};

	return (
		<div>
			<Header />
			<div className='mx-auto p-6 max-w-[1300px] mt-16'>
				<Card>
					<CardHeader className='bg-orange-50 border-b'>
						<div className='flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4'>
							<CardTitle className='text-2xl font-bold text-orange-900'>
								Workout Data
							</CardTitle>
							<Button
								onClick={() => setShowDialog(true)}
								className='bg-orange-600 hover:bg-orange-700 text-white'
							>
								<Plus className='h-4 w-4 mr-2' />
								Create Workout Data
							</Button>
						</div>

						<div className='flex flex-col sm:flex-row gap-4 mt-4'>
							<div className='flex-1 text-sm'>
								<Input
									placeholder='Search by workout name...'
									value={searchTerm}
									onChange={(e) =>
										setSearchTerm(e.target.value)
									}
									className='max-w-sm'
								/>
							</div>
						</div>
					</CardHeader>

					<CardContent className='p-0'>
						<div className='overflow-x-auto'>
							<Table className='min-w-full w-full'>
								<TableHeader>
									<TableRow className='bg-blue-600 hover:bg-blue-600'>
										<TableHead className='text-white font-semibold'>
											Sr ID
										</TableHead>
										<TableHead className='text-white font-semibold'>
											Workout Name
										</TableHead>
										<TableHead className='text-white font-semibold'>
											Type
										</TableHead>
										<TableHead className='text-white font-semibold'>
											Action
										</TableHead>
									</TableRow>
								</TableHeader>
								<TableBody>
									{isLoading ? (
										<TableRow>
											<TableCell
												colSpan={4}
												className='text-center py-8'
											>
												Loading...
											</TableCell>
										</TableRow>
									) : paginatedData.length > 0 ? (
										paginatedData.map((row, index) => (
											<TableRow
												key={row.id}
												className='hover:bg-gray-50'
											>
												<TableCell className='font-medium'>
													{(currentPage - 1) *
														pageSize +
														index +
														1}
												</TableCell>
												<TableCell className='font-medium'>
													{row.workout}
												</TableCell>
												<TableCell>
													{getWorkoutTypeBadge(
														row.workout
													)}
												</TableCell>
												<TableCell>
													<div className='flex gap-2'>
														<Button
															variant='ghost'
															size='sm'
															onClick={() =>
																handleEdit(row)
															}
															className='text-blue-600 hover:text-blue-800'
														>
															<Edit className='h-4 w-4' />
														</Button>
														<Button
															variant='ghost'
															size='sm'
															onClick={() =>
																setDeleteId(
																	row.id
																)
															}
															className='text-red-700 hover:text-red-800'
														>
															<Trash2 className='h-4 w-4' />
														</Button>
													</div>
												</TableCell>
											</TableRow>
										))
									) : (
										<TableRow>
											<TableCell
												colSpan={4}
												className='text-center py-8 text-gray-500'
											>
												No data found
											</TableCell>
										</TableRow>
									)}
								</TableBody>
							</Table>
						</div>

						{totalPages > 1 && (
							<div className='flex justify-center gap-2 p-4 border-t'>
								<Button
									variant='outline'
									size='sm'
									onClick={() =>
										setCurrentPage(
											Math.max(1, currentPage - 1)
										)
									}
									disabled={currentPage === 1}
								>
									Previous
								</Button>

								{Array.from({ length: 5 }, (_, i) => {
									const startPage =
										Math.floor((currentPage - 1) / 5) * 5 +
										1;
									const page = startPage + i;
									if (page > totalPages) return null;

									return (
										<Button
											key={page}
											variant={
												currentPage === page
													? "default"
													: "outline"
											}
											size='sm'
											onClick={() => setCurrentPage(page)}
											className={
												currentPage === page
													? "bg-orange-500 hover:bg-orange-600 text-white"
													: ""
											}
										>
											{page}
										</Button>
									);
								})}

								<Button
									variant='outline'
									size='sm'
									onClick={() =>
										setCurrentPage(
											Math.min(
												totalPages,
												currentPage + 1
											)
										)
									}
									disabled={currentPage === totalPages}
								>
									Next
								</Button>
							</div>
						)}
					</CardContent>
				</Card>

				<WorkoutDataDialog
					open={showDialog}
					onOpenChange={setShowDialog}
					editData={editData}
					onSave={handleSave}
				/>

				<DeleteConfirmDialog
					open={!!deleteId}
					onOpenChange={() => setDeleteId(null)}
					onConfirm={() => handleDelete(deleteId)}
					title='Delete Workout Data'
					description='Are you sure you want to delete this workout data? This action cannot be undone.'
				/>
			</div>
		</div>
	);
}
