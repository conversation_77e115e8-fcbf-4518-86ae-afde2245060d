import { useEffect, useState, useMemo, useCallback } from "react";
import { <PERSON><PERSON> } from "../../components/ui/button";
import { Input } from "../../components/ui/input";
import { Card, CardContent, CardHeader } from "../../components/ui/card";
import {
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableHeader,
	TableRow,
} from "../../components/ui/table";
import { Edit, Trash2, Plus, ArrowLeft } from "lucide-react";
import { SegmentReadinessDialog } from "../../components/admin/segment-readiness-dialog";
import { DeleteConfirmDialog } from "../../components/admin/delete-confirm-dialog";
import Header from "../../components/Header";
import {
	getAllSegmentReadnessData,
	deleteSegmentReadnessData,
} from "../../API/api-endpoint";
import Swal from "sweetalert2";

const SegmentReadiness = () => {
	const [segmentData, setSegmentData] = useState([]);
	const [isLoading, setIsLoading] = useState(true);
	const [searchTerm, setSearchTerm] = useState("");
	const [currentPage, setCurrentPage] = useState(1);
	const [showDialog, setShowDialog] = useState(false);
	const [editingItem, setEditingItem] = useState(null);
	const [deleteId, setDeleteId] = useState(null);
	const pageSize = 10;

	// Fetch segment readiness data
	const fetchData = useCallback(async () => {
		try {
			setIsLoading(true);
			const response = await getAllSegmentReadnessData();
			console.log("Segment readiness data response:", response);

			if (response) {
				setSegmentData(response || []);
			} else {
				console.log("Response is empty or invalid");
				setSegmentData([]);
			}
		} catch (error) {
			console.error("Error fetching segment readiness data:", error);
			Swal.fire({
				title: "Error",
				text: "Failed to fetch segment readiness data. Please try again.",
				icon: "error",
				timer: 3000,
				showConfirmButton: false,
			});
			setSegmentData([]);
		} finally {
			setIsLoading(false);
		}
	}, []);

	// Initial data fetch
	useEffect(() => {
		fetchData();
	}, [fetchData]);

	// Filter data based on search term
	const filteredData = useMemo(() => {
		if (!searchTerm.trim()) return segmentData;

		return segmentData.filter((item) => {
			return item?.name?.toLowerCase().includes(searchTerm.toLowerCase());
		});
	}, [segmentData, searchTerm]);

	// Paginate filtered data
	const paginatedData = useMemo(() => {
		const startIndex = (currentPage - 1) * pageSize;
		const endIndex = startIndex + pageSize;
		return filteredData.slice(startIndex, endIndex);
	}, [filteredData, currentPage, pageSize]);

	// Calculate total pages
	const totalPages = Math.ceil(filteredData.length / pageSize);

	// Handle page change
	const handlePageChange = (page) => {
		setCurrentPage(page);
	};

	// Handle search
	const handleSearch = (value) => {
		setSearchTerm(value);
		setCurrentPage(1);
	};

	// Handle create new segment
	const handleCreate = () => {
		setEditingItem(null);
		setShowDialog(true);
	};

	// Handle edit segment
	const handleEdit = (item) => {
		setEditingItem(item);
		setShowDialog(true);
	};

	// Handle delete segment
	const handleDelete = async (id) => {
		try {
			const response = await deleteSegmentReadnessData(id);
			console.log("Delete response:", response);

			Swal.fire({
				title: "Success",
				text: "Segment readiness deleted successfully",
				icon: "success",
				timer: 2000,
				showConfirmButton: false,
			});

			setCurrentPage(1);
			fetchData();
		} catch (error) {
			console.error("Error deleting segment readiness:", error);
			Swal.fire({
				title: "Error",
				text: "Failed to delete segment readiness. Please try again.",
				icon: "error",
				timer: 3000,
				showConfirmButton: false,
			});
		}
	};

	// Handle dialog success (create/update)
	const handleDialogSuccess = () => {
		setShowDialog(false);
		setEditingItem(null);
		fetchData();
	};

	return (
		<div>
			<Header />
			<div className='mx-auto p-6 max-w-[1300px] mt-16'>
				<Card>
					<CardHeader className='bg-orange-50 border-b flex flex-col gap-4'>
						<div className='flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4'>
							<div>
								<h1 className='text-2xl font-bold text-orange-900'>
									Segment Readiness
								</h1>
								<p className='text-orange-700 mt-1 text-sm'>
									Manage readiness segments and their data
								</p>
							</div>
						</div>
						<div className='flex gap-3'>
							<Button
								onClick={handleCreate}
								className='bg-orange-600 hover:bg-orange-700 text-white'
							>
								<Plus className='h-4 w-4 mr-2' />
								Create Segment
							</Button>
							<Button
								onClick={() =>
									(window.location.href =
										"/subsegments-readness")
								}
								variant='outline'
								className='border-orange-300 text-orange-700 hover:bg-orange-100'
							>
								Get Sub Segments
							</Button>
							<Button
								onClick={() =>
									(window.location.href = "/readness")
								}
								variant='outline'
								className='border-gray-300 text-gray-700 hover:bg-gray-100'
							>
								<ArrowLeft className='h-4 w-4 mr-2' />
								Back
							</Button>
						</div>

						{/* Search Controls */}
						<div className='flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4'>
							<div className='flex gap-3'>
								<Input
									placeholder='Search by name...'
									value={searchTerm}
									onChange={(e) =>
										handleSearch(e.target.value)
									}
									className='max-w-sm text-sm'
								/>
							</div>
							<div className='text-sm text-orange-700 flex items-center'>
								Total: {filteredData.length} segments
							</div>
						</div>
					</CardHeader>
					<CardContent className='p-0'>
						<div className='overflow-x-auto'>
							<Table className='min-w-full w-full'>
								<TableHeader>
									<TableRow className='bg-blue-600 hover:bg-blue-600'>
										<TableHead className='text-white font-semibold'>
											Sr No
										</TableHead>
										<TableHead className='text-white font-semibold'>
											Name
										</TableHead>
										<TableHead className='text-white font-semibold'>
											Actions
										</TableHead>
									</TableRow>
								</TableHeader>
								<TableBody>
									{isLoading ? (
										<TableRow>
											<TableCell
												colSpan={3}
												className='text-center py-8'
											>
												Loading...
											</TableCell>
										</TableRow>
									) : paginatedData.length > 0 ? (
										paginatedData.map((item, index) => (
											<TableRow
												key={item.id || index}
												className='hover:bg-gray-50'
											>
												<TableCell className='font-medium'>
													{(currentPage - 1) *
														pageSize +
														index +
														1}
												</TableCell>
												<TableCell>
													{item?.name || "N/A"}
												</TableCell>
												<TableCell>
													<div className='flex gap-2'>
														<Button
															variant='ghost'
															size='sm'
															onClick={() =>
																handleEdit(item)
															}
															className='text-blue-600 hover:text-blue-800'
														>
															<Edit className='h-4 w-4' />
														</Button>
														<Button
															variant='ghost'
															size='sm'
															onClick={() =>
																setDeleteId(
																	item.id
																)
															}
															className='text-red-600 hover:text-red-800'
														>
															<Trash2 className='h-4 w-4' />
														</Button>
													</div>
												</TableCell>
											</TableRow>
										))
									) : (
										<TableRow>
											<TableCell
												colSpan={3}
												className='text-center py-8 text-gray-500'
											>
												No segments found
											</TableCell>
										</TableRow>
									)}
								</TableBody>
							</Table>
						</div>

						{/* Pagination */}
						{totalPages > 1 && (
							<div className='flex items-center justify-between px-6 py-4 border-t'>
								<div className='text-sm text-gray-700'>
									Showing {(currentPage - 1) * pageSize + 1}{" "}
									to{" "}
									{Math.min(
										currentPage * pageSize,
										filteredData.length
									)}{" "}
									of {filteredData.length} results
								</div>
								<div className='flex items-center gap-2'>
									<Button
										variant='outline'
										size='sm'
										onClick={() =>
											handlePageChange(
												Math.max(1, currentPage - 1)
											)
										}
										disabled={currentPage === 1}
									>
										Previous
									</Button>

									{/* Page numbers */}
									{Array.from(
										{ length: Math.min(5, totalPages) },
										(_, i) => {
											const pageNum =
												Math.max(
													1,
													Math.min(
														totalPages - 4,
														currentPage - 2
													)
												) + i;
											return (
												<Button
													key={pageNum}
													variant={
														currentPage === pageNum
															? "default"
															: "outline"
													}
													size='sm'
													onClick={() =>
														handlePageChange(
															pageNum
														)
													}
													className={
														currentPage === pageNum
															? "bg-orange-600 hover:bg-orange-700"
															: ""
													}
												>
													{pageNum}
												</Button>
											);
										}
									)}

									<Button
										variant='outline'
										size='sm'
										onClick={() =>
											handlePageChange(
												Math.min(
													totalPages,
													currentPage + 1
												)
											)
										}
										disabled={currentPage === totalPages}
									>
										Next
									</Button>
								</div>
							</div>
						)}
					</CardContent>
				</Card>
			</div>

			{/* Segment Readiness Dialog */}
			<SegmentReadinessDialog
				open={showDialog}
				onClose={() => setShowDialog(false)}
				onSuccess={handleDialogSuccess}
				editingItem={editingItem}
			/>

			{/* Delete Confirmation Dialog */}
			<DeleteConfirmDialog
				open={!!deleteId}
				onClose={() => setDeleteId(null)}
				onConfirm={() => {
					handleDelete(deleteId);
					setDeleteId(null);
				}}
				title='Delete Segment Readiness'
				description='Are you sure you want to delete this segment readiness? This action cannot be undone.'
			/>
		</div>
	);
};

export default SegmentReadiness;
