import {
  Chip,
  FormControl,
  FormLabel,
  Grid,
  MenuItem,
  OutlinedInput,
  Select,
  TextField,
} from "@mui/material";
import { <PERSON><PERSON>, Modal } from "antd";
import React, { useEffect, useState } from "react";
import {
  createChallengedata,
  getAllPrograms,
  updateChallengedata,
} from "../../API/api-endpoint";
import { useFormik } from "formik";
import Swal from "sweetalert2";
import SlickCarousel from "../../pages/SlickCarousel";
const scoreData = [1, 2, 3, 4, 5];
const CreateChallenge = ({
  fetchReport,
  setShowAssesmentModal,
  showAssesmentModal,
  editData,
  setEditData,
}) => {
  const [programList, setProgramList] = useState([]);
  console.log("editData", editData);
  const formik = useFormik({
    initialValues: {
      level: "",
    },
    validate: (values) => {
      const errors = {};
      if (!values.level) {
        errors.level = "Name is required";
      }
      return errors;
    },
    // validationSchema: {},
    onSubmit: (values, { resetForm }) => {
      handleSubmitAssesmentForm(values, resetForm);
    },
  });
  console.log("formik", formik?.values);
  const getAllProgramsData = async () => {
    const response = await getAllPrograms();
    console.log("response", response);
    setProgramList(response);
  };
  useEffect(() => {
    getAllProgramsData();
  }, []);

  const handleSubmitAssesmentForm = async (data, resetForm) => {
    let response = "";
    if (editData?.id) {
      response = await updateChallengedata(data);
    } else {
      response = await createChallengedata(data);
    }
    if (response?.status) {
      Swal.fire({
        title: "Success",
        text: response.message,
        icon: "success",
      });
      setEditData({});
      setShowAssesmentModal(false);
      fetchReport();
      resetForm();
      formik?.setValues({ level: "" });
    } else {
      Swal.fire({
        title: "Error",
        text: response.message,
        icon: "error",
      });
    }
    console.log("response", response);
  };
  useEffect(() => {
    if (editData?.id) {
      const { srID, ...data } = editData;
      console.log("data", data);
      formik?.setValues(data);
    } else {
      setEditData({});
    }
  }, [editData?.id]);
  return (
    <Modal
      width={1200}
      open={showAssesmentModal}
      onCancel={() => {
        setShowAssesmentModal(false);
        setEditData({});
        formik.resetForm();
        formik?.setValues({ level: "" });
      }}
      footer={<div></div>}
    >
      <div className="headingCont">
        <span className="heading">{editData?.id ? "Edit " : "Create"}</span>{" "}
        <span className="orange heading">Challenge</span>
      </div>

      <div className="parentCont">
        <form className="form1" onSubmit={formik.handleSubmit}>
          <Grid container spacing={2}>
            <Grid item xs={12} sm={11}>
              <FormLabel>Level Name<span className="text-[red]">*</span></FormLabel>

              <TextField
                fullWidth
                placeholder="Level name"
                size="small"
                type="text"
                name="level"
                value={formik?.values?.level}
                onChange={formik.handleChange}
                error={formik.touched.level && formik.errors.level}
                helperText={formik.touched.level && formik.errors.level}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <Button
                className="btn"
                key="submit"
                type="primary"
                onClick={() => formik.handleSubmit()}
              >
                Submit
              </Button>
            </Grid>
          </Grid>
        </form>

        <div className="slick-container">
          <SlickCarousel />
        </div>
      </div>
    </Modal>
  );
};
export default CreateChallenge;
