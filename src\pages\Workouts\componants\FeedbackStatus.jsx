import React, { useState, useEffect } from "react";
import { FaCheckCircle } from "react-icons/fa";
import { getFeedbackForWorkout } from "../../../API/api-endpoint";

const FeedbackStatus = ({ workoutId, workoutType }) => {

    const [loading, setLoading] = useState(true);
    const [userRoleId, setUserRoleId] = useState(0);
    const [recordedFeedback, setRecordedFeedback] = useState(null);

    // get workout status
    useEffect(() => {
        const getUserRoleId = () => {
            try {
                const roleId = localStorage.getItem("roleID");
                setUserRoleId(parseInt(roleId, 10));
            } catch (error) {
                throw error;
            }
        }
        const getRecordedFeedbackForWorkout = async () => {
            try {
                setLoading(true);
                // Check if feedback is recorded for this activity
                const recordedResponse = await getFeedbackForWorkout(workoutId, workoutType);
                const recordedData = recordedResponse?.data ? recordedResponse?.data : null;
                if (recordedData) {
                    const dataObj = getRecordedFeedbackForComponent(workoutType, recordedData);
                    setRecordedFeedback(dataObj);
                }
            } catch (error) {
                console.error("Error fetching feedback data for workout:", error);
            } finally {
                setLoading(false);
            }
        };
        getUserRoleId();
        getRecordedFeedbackForWorkout();
    }, [workoutId]);

    // handle recorded feedback get endpoint for component
    const getRecordedFeedbackForComponent = (workoutType, recordedData) => {
        if (workoutType === "strava") {
            const dataObj = Object.assign({}, {
                id: recordedData?.id,
                feedbackText: recordedData?.feedbackText,
                workoutFeedback: recordedData?.stravaworkoutfeedback
            });
            return dataObj;
        } else if (workoutType === "manual") {
            const dataObj = Object.assign({}, {
                id: recordedData?.id,
                feedbackText: recordedData?.feedbackText,
                workoutFeedback: recordedData?.manualworkoutfeedback
            });
            return dataObj;
        }
    }

    // render icon
    const renderFeedbackStatus = () => {
        return (
            <div>
                <FaCheckCircle className="text-green-500 text-base" />
            </div>
        )
    }

    if (loading) return <p>Loading...</p>;

    return (
        <>
            {((userRoleId === 1 || userRoleId === 2 || userRoleId === 3) && recordedFeedback?.workoutFeedback) && renderFeedbackStatus()}
        </>
    )
};

export default FeedbackStatus;