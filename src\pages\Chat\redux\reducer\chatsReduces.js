import { GET_CHATS_MESSAGES_FAIL, GET_CHATS_MESSAGES_INIT, GET_CHATS_MESSAGES_SUCCESS } from "../constant/chatsConstants";

export const chatsReducer = (state = { chatMessages: [] }, { type, payload }) => {
    switch (type) {

        case GET_CHATS_MESSAGES_INIT: return { ...state, loading: true }

        case GET_CHATS_MESSAGES_SUCCESS: return { ...state, loading: false, chatMessages: payload }

        case GET_CHATS_MESSAGES_FAIL: return { ...state, loading: false, error: payload }

        default: return state;
    }
}
