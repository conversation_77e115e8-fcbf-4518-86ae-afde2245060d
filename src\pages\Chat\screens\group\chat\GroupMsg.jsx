import { Avatar, Box, Typography } from "@mui/material";
import React, { useEffect, useRef, useState } from "react";
import PhotoOutlinedIcon from "@mui/icons-material/PhotoOutlined";
import EastOutlinedIcon from "@mui/icons-material/EastOutlined";
import FlightTakeoffOutlinedIcon from "@mui/icons-material/FlightTakeoffOutlined";
import { getDatabase, ref, runTransaction,get,update  } from "firebase/database";
import Popover from "../../../components/Popover";
import PlayCircleFilledIcon from "@mui/icons-material/PlayCircleFilled";
import VideoModal from "../../../components/preview/VideoModal";
import ReactLinkify from "react-linkify";
import ModalImage from "react-modal-image";
import Modal from "react-modal";
import { HiDotsVertical } from "react-icons/hi";
import styles from "@chatscope/chat-ui-kit-styles/dist/default/styles.min.css";
import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  MessageList,
  Message,
  MessageInput,
} from "@chatscope/chat-ui-kit-react";
import "./../../../styles/groupChat.css";
import { unset } from "lodash";


const blurStyle = {
  filter: "blur(4px)",
};

const GroupMsg = ({
  msg,
  currentUser,
  groupId,
  setIsReplyMsg,
  searchKeyword,
  isSearchActive,
  isLoading,
  onReply
}) => {
  const loggedInUserId = localStorage.getItem("userId");
  
  const messageRef = useRef(null);
  useEffect(() => {
    if (messageRef.current && isSearchActive && msg.type === 'text') {
      const regex = new RegExp(`(${searchKeyword})`, 'gi');
      const text = messageRef.current.textContent;
      const match = regex.exec(text);

      if (match) {
        messageRef.current.scrollIntoView({ behavior: 'smooth', block: 'center' });
      }
    }
  }, [isSearchActive, msg, searchKeyword]);

  const [popoverVisible, setPopoverVisible] = useState(false);
  const [typeOfFile, setTypeOfFile] = useState("Message");
  const [modalOpen, setModalOpen] = useState(false);

  const openModal = () => {
    setModalOpen(true);
  };

  const closeModal = () => {
    setModalOpen(false);
  };

  const combinedId = groupId;

  const parts = msg?.message?.split(new RegExp(`(${searchKeyword})`, "gi"));

  function updateIsReadStatus(timeStamp) {
    setTimeout(async () => {
    const db = getDatabase();
    const chatRef = ref(db, `chats/${combinedId}/messages`);

    const messagesRef = ref(db, `chats/${groupId}/messages`);
    const messagesSnapshot = await get(messagesRef);

    if (messagesSnapshot.exists()) {
      const messages = messagesSnapshot.val();

      const updates = {};
      for (const messageId in messages) {
        updates[`${messageId}/isSeen`] = true;
      }

      await update(ref(db, `chats/${groupId}/messages`), updates);
    }

    // Fetch the current value of messages
    runTransaction(chatRef, (messages) => {
      console.log(messages);
      if (messages) {
        // Convert messages object to an array
        const messageArray = Object.values(messages);

        for (let i = 0; i < messageArray.length; i++) {
          if (messageArray[i].timeStamp === timeStamp) {
            const seenBy = messageArray[i].seenBy;
            console.log(seenBy);
            seenBy[currentUser.uid] = true;
            messageArray[i].seenBy = seenBy;
            break;
          }
        }

        // Convert the modified array back to an object
        const updatedMessages = messageArray.reduce((acc, curr) => {
          acc[curr.timeStamp] = curr;
          return acc;
        }, {});

        return updatedMessages;
      }

      return messages;
    })
      .then(() => {
        console.log("Message seen successfully.");
      })
      .catch((error) => {
        console.log("Message seen failed: ", error);
      });
    }, 2000); // Delay of 1 second
  }

  const openDocumentInNewTab = (downloadUrl) => {
    const newWindow = window.open(downloadUrl, "_blank");
    if (newWindow) {
      newWindow.opener = null;
    }
  };

  const handleLongPress = () => {
    console.log("Long press detected!");
  };

  let timer;
  const handlePressHold = (event) => {
    event.persist(); // Preserve the event object for later use

    let timer;

    const startTimer = () => {
      timer = setTimeout(() => {
        setPopoverVisible(true);
        // Call your function here when long press is detected
        handleLongPress();
      }, 1000);
    };

    const cancelTimer = () => {
      clearTimeout(timer);
    };

    document.addEventListener("mouseup", cancelTimer);
    document.addEventListener("touchend", cancelTimer);

    startTimer();
  };

  const handleMouseUp = () => {
    clearTimeout(timer);
    // setPopoverVisible(false);
    document.removeEventListener("mouseup", handleMouseUp);
  };

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          msg.seenBy[currentUser.uid] === false &&
            msg.senderId !== currentUser.uid &&
            updateIsReadStatus(msg.timeStamp);
        }
      },
      {
        root: null,
        rootMargin: "0px",
        threshold: 1.0,
      }
    );

    if (messageRef.current) {
      observer.observe(messageRef.current);
    }

    return () => {
      if (messageRef.current) {
        observer.unobserve(messageRef.current);
      }
    };
  }, []);

  const decodeHtml = (html) => {
    const parser = new DOMParser();
    const doc = parser.parseFromString(html, "text/html");
    return doc.documentElement.textContent;
  };
  const decodeHtmlRecursively = (html) => {
    let decoded = decodeHtml(html);
    while (decoded !== html) {
      html = decoded;
      decoded = decodeHtml(html);
    }
    return decoded;
  };
  const isOutgoing = msg.userId === parseInt(loggedInUserId);
  return (
//   <Message
//   model={{
//     message: msg.fileUrl ? msg.fileUrl.split('/').pop() : decodeHtmlRecursively(msg.text),
//     sentTime: new Date(msg.timestamp).toLocaleTimeString(), // Format as needed
//     sender: msg.userName,
//     direction: msg.userId === parseInt(loggedInUserId) ? 'outgoing' : 'incoming',
//     position: 'single',
//     type: msg.fileUrl ? 'image' : 'text',
//     ...(msg.fileUrl && { payload: { src: msg.fileUrl } }) // For photo messages
//   }}
//   avatarPosition="clipped" // Adjust as per UI preferences
//   avatar={msg.userAvatar || undefined} // Add avatar if available
//   onMessageClick={() => onReply(msg)} // Trigger reply on click
// >
//   {/* Render reply if it exists */}
//   {msg.replyTo && (
//     <div style={{ fontStyle: 'italic', padding: '5px 10px', color: '#888' }}>
//       <strong>Reply:</strong> {decodeHtml(msg.replyTo.text)}
//     </div>
//   )}
// </Message>

<div
      className={`cs-message ${isOutgoing ? "cs-message--outgoing" : "cs-message--incoming"}`}
      style={{
        display: "flex",
        flexDirection: "column",
        alignItems: isOutgoing ? "flex-end" : "flex-start",
        marginTop: "10px",
      }}
    >
      {/* Message bubble */}
      <div
        style={{
          backgroundColor: isOutgoing ? "#ffdead" : "#ebebeb", // Updated background colors
          color: isOutgoing ? "#000" : "#000", // Ensure text color is visible
          padding: "5px 10px",
          borderRadius: "10px",
          maxWidth: "75%",
          boxShadow: "0 2px 6px rgba(0, 0, 0, 0.1)",
          wordBreak: "break-word",
          position: "relative",
          fontSize: "0.95rem",
        }}
      >
        {/* Sender name for all messages */}
        <div
          style={{
            fontSize: "0.85rem",
            fontWeight: "600",
            marginBottom: "4px",
            color: "#555",
          }}
        >
          {msg.userName}
        </div>

        {/* Message text or file */}
        {msg.fileUrl ? (
          <img
            src={msg.fileUrl}
            alt="message content"
            style={{
              maxWidth: "100%",
              borderRadius: "15px",
              marginBottom: msg.text ? "5px" : "0",
            }}
          />
        ) : (
          <div style={{color: "#3b3b3b"}}>{decodeHtmlRecursively(msg.text)}</div>
        )}

        {/* Timestamp inside the bubble */}
        <div
          style={{
            fontSize: "0.75rem",
            color: "#666", // Subtle timestamp color
            textAlign: "right",
            marginTop: "4px",
          }}
        >
          {new Intl.DateTimeFormat("en-US", { hour: "numeric", minute: "2-digit" }).format(new Date(msg.timestamp))}
        </div>
      </div>

      {/* Reply section */}
      {msg.replyTo && (
        <div
          style={{
            fontStyle: "italic",
            fontSize: "0.85rem",
            color: "#888",
            marginTop: "4px",
            padding: "5px 10px",
            backgroundColor: "#f1f1f1",
            borderRadius: "10px",
            maxWidth: "85%",
          }}
        >
          <strong>Reply:</strong> {decodeHtml(msg.replyTo.text)}
        </div>
      )}
    </div>
  );
};

export default GroupMsg;