import { Avatar, Box, Button, Checkbox, Paper, Tabs, Typography } from '@mui/material'
import React, { useEffect, useState } from 'react'
import ArrowOutwardSharpIcon from '@mui/icons-material/ArrowOutwardSharp';
import "../../styles/chatlanding.css"
import "./../../styles/newchat/newChat.css"
import NewGroupNav from '../../components/navbar/NewGroupNav';

import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import CheckCircleOutlineIcon from '@mui/icons-material/CheckCircleOutline';
import EastOutlinedIcon from '@mui/icons-material/EastOutlined';
import { useDispatch, useSelector } from 'react-redux';
import { getAllUsersAction, storeUsersToCreateGroup } from '../../redux/action/usersAction';
import { useNavigate } from 'react-router-dom';
import NavGroupInfo from '../../components/navbar/NavGroupInfo';
import { db, get, push, ref, serverTimestamp, set, update } from '../../../../API/firebase.config';
import Cookies from 'js-cookie';


export function ShareWith() {
    const paper = {
        padding: "0rem",
        minHeight: "100vh",
        maxWidth: '27rem'
    };

    const dispatch = useDispatch()
    const navigate = useNavigate()

    const { currentUser } = useSelector(state => state.auth)
    const { allUsers, storedUserToCreateGroup } = useSelector(state => state.users)
    const [filteredUsers, seTfilteredUsers] = useState([])

    const handleChange = (event) => {
        setChecked(event.target.checked);
    };

    const [checked, setChecked] = useState(false);
    const [selectedProfiles, setSelectedProfile] = useState(storedUserToCreateGroup)

    // const addToSelected = user => {
    //     const copiedSelected = [...selectedProfiles]

    //     if (copiedSelected.some(obj => obj.uid === user.uid)) return;
    //     copiedSelected.push(user)
    //     setSelectedProfile(copiedSelected)
    //     return;
    // }
    const addToSelected = user => {
        const copiedSelected = [...selectedProfiles];

        const userIndex = copiedSelected.findIndex(obj => obj.uid === user.uid);
        if (userIndex !== -1) {
            copiedSelected.splice(userIndex, 1);
        } else {
            copiedSelected.push(user);
        }

        setSelectedProfile(copiedSelected);
    };


    const removeSelected = uid => {
        const arr = [...selectedProfiles]

        const index = arr.findIndex(obj => obj.uid === uid);
        if (index !== -1) {
            arr.splice(index, 1);
        }
        setSelectedProfile(arr);
        return;
    }

    const filterAndSortData = searchTerm => {
        const filteredData = allUsers.filter(obj =>
            obj.displayName.toLowerCase().includes(searchTerm.toLowerCase())
        );

        const sortedData = [...filteredData].sort((a, b) =>
            a.displayName.localeCompare(b.displayName)
        );

        return sortedData;
    };

    const handleSearchChange = event => {
        const searchTerm = event.target.value;
        const filteredAndSortedData = filterAndSortData(searchTerm);
        console.log(filteredAndSortedData)
        seTfilteredUsers(filteredAndSortedData)
    };
    console.log(selectedProfiles)


    const handleSharePost = async () => {
        const shareWithIDs = selectedProfiles.map(obj => obj.uid);

        try {
            for (const uid of shareWithIDs) {
                const combinedId =
                    currentUser.uid > uid
                        ? currentUser.uid + uid
                        : uid + currentUser.uid;
                const chatRef = ref(db, `chats/${combinedId}/messages`);
                const checkChatRef = ref(db, 'chats/' + combinedId);
                const snapshot = await get(checkChatRef);

                //

                if (!snapshot.exists()) {
                    // Create a chat in the "chats" collection
                    set(chatRef, {
                        isMsgReqAccepted: false
                    })
                        .then(() => {
                            return;
                        })
                        .catch((error) => {
                            console.error('Failed to write chat data:', error);
                        });

                    // Create user chats
                    const currentUserChatsRef = ref(db, `userChats/${currentUser.uid}`);
                    const userChatsRef = ref(db, `userChats/${uid}`);

                    await update(currentUserChatsRef, {
                        [`${combinedId}/userInfo`]: {
                            uid: uid
                        },
                        [`${combinedId}/date`]: serverTimestamp(),
                        [`${combinedId}/isMsgReqAccepted`]: true,
                        [`${combinedId}/lastMessage`]: '',
                        [`${combinedId}/chatType`]: 'personal',
                    });

                    await update(userChatsRef, {
                        [`${combinedId}/userInfo`]: {
                            uid: currentUser.uid,
                        },
                        [`${combinedId}/date`]: serverTimestamp(),
                        [`${combinedId}/isMsgReqAccepted`]: false,
                        [`${combinedId}/lastMessage`]: '',
                        [`${combinedId}/chatType`]: 'personal',
                    });

                    navigate('/individual-chat')
                }
                //

                const updatedMessages = {
                    isDeleted: false,
                    isStarred: false,
                    senderId: currentUser.uid,
                    type: 'post',
                    isSeen: false,
                    timeStamp: serverTimestamp(),
                    message: Cookies.get('postId'),
                };

                await push(chatRef, updatedMessages);
            }
            console.log("post shared!")
            navigate('/')
            return;
        } catch (error) {
            console.error('Failed to send message:', error);
        }
    };

    useEffect(() => {
        dispatch(getAllUsersAction())
    }, [])

    return <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
        <Paper sx={{ flexGrow: 1 }} elevation={10} style={paper}>
            <NavGroupInfo
                groupName={'Share With'}
                backButtonPath={'/show-post'}
            />

            <Box>
                <Typography className='select-buddies-text'>Select Buddies to add to your Group</Typography>

                {/* <Box style={{ position: 'relative', textAlign: "center" }}>
                    <input onChange={handleSearchChange} placeholder='Search Buddies' type="text" className='search-buddy-input' />
                    <Box className="search-icon-parent" style={{ textAlign: "center" }}>
                        <img src="/images/search-icon.png" alt="search" />
                    </Box>
                </Box> */}

            </Box>
            {
                filteredUsers.legnth > 0
                    ? filteredUsers
                    : allUsers
                        ?.map((item, index) => <Box className="profile-row" onClick={() => {
                            addToSelected(item)
                            document.getElementById(`check-profile-${index}`).click()
                        }} key={index}
                            sx={{ padding: "8px", paddingTop: "12px", display: "flex", justifyContent: 'space-between', alignItems: 'center' }}>
                            <Box sx={{ display: "flex", alignItems: 'center', padding: '6px' }}>
                                <Avatar alt="Remy Sharp" src={item.photoURL} />
                                <Box>
                                    <Typography className='userName' fontWeight="fontWeightBold" sx={{ paddingLeft: '1.2rem', fontSize: '14px' }}>{item.displayName}</Typography>
                                    <Typography className='followers-text'>
                                        {'7724 Followers | 4.65'}
                                        <img src="/images/star-icon.png" alt="star" />
                                    </Typography>
                                </Box>
                            </Box>
                            <Checkbox
                                id={`check-profile-${index}`}
                                sx={{
                                    color: "#F4F4F4",
                                    '&.Mui-checked': {
                                        color: '#056B6B',
                                    }
                                }}
                                checked={selectedProfiles.some(obj => obj.uid === item.uid)}
                                icon={<CheckCircleOutlineIcon />}
                                checkedIcon={<CheckCircleIcon />}
                            />
                        </Box>)
            }

            <Box className="sticky-next-btn">
                <Button
                    disabled={!selectedProfiles.length > 0}
                    onClick={handleSharePost}
                    style={{ boxShadow: "-2px 4px 10px rgba(0, 0, 0, 0.25)", fontSize: 10, letterSpacing: "0.16px", display: "flex" }} className={selectedProfiles.length > 0 ? 'next-mui-btn-selected' : 'next-mui-btn'}>
                    Share
                    <EastOutlinedIcon sx={
                        selectedProfiles.length > 0
                            ? { fontSize: '14px', paddingLeft: '0.24rem', color: "black" }
                            : { fontSize: '14px', paddingLeft: '0.24rem', color: "#FFFFFF", }
                    } />
                </Button>
            </Box>
        </Paper >
    </Box >
}
