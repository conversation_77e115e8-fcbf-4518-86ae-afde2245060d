import {
    Chip,
    FormControl,
    FormLabel,
    Grid,
    MenuItem,
    OutlinedInput,
    Select,
    TextField,
  } from "@mui/material";
  import { But<PERSON>, Modal } from "antd";
  import React, { useEffect, useState } from "react";
  import {
    CreateLevels,
    CreatePrograms,
    CreateZonesClasification,
    createAssesmentdata,
    createSugmentdata,
    getAllAssesmentData,
    getAllPrograms,
    getAlllevels,
    updateAssesmentdata,
    updateLevel,
    updatePrograms,
    updateSugmentdata,
    updateZonesClasification,
    weeklyFeedDataPattern,
    weeklyFeedDataProgram,
  } from "../../API/api-endpoint";
  import { useFormik } from "formik";
  import Swal from "sweetalert2";
  import SlickCarousel from "../../pages/SlickCarousel";
  const scoreData = [1, 2, 3, 4, 5];
  const CreateSugments = ({
    fetchReport,
    setShowAssesmentModal,
    showAssesmentModal,
    editData,
    setEditData,
  }) => {
    const [assesmentList, setAssesmentList] = useState([]);
    console.log("editData", assesmentList);
    const formik = useFormik({
      initialValues: {
        name: "",
        assessment_id: "",
        segment_percentage: "",
      },
      validate: (values) => {
        const errors = {};
        if (!values.assessment_id) {
          errors.assessment_id = "Assessment name is required";
        }
        if (!values.name) {
          errors.name = "Name is required";
        }
        if (!values.segment_percentage) {
          errors.segment_percentage = "Segment percentage is required";
        }
        return errors;
      },
      // validationSchema: {},
      onSubmit: (values, { resetForm }) => {
        handleSubmitAssesmentForm(values, resetForm);
      },
    });
    console.log("formik", formik?.values);
    const assementdata = async () => {
      const response = await getAllAssesmentData();
      console.log("response", response);
      setAssesmentList(response);
    };
    useEffect(() => {
      assementdata();
    }, []);
  
    const handleSubmitAssesmentForm = async (data, resetForm) => {
      let response = "";
      if (editData?.id) {
        response = await updateSugmentdata(data);
      } else {
        response = await createSugmentdata(data);
      }
      if (response?.status) {
        Swal.fire({
          title: "Success",
          text: response.message,
          icon: "success",
        });
        setEditData({});
        setShowAssesmentModal(false);
        fetchReport();
        resetForm();
        formik?.setValues({ name: "", segment_percentage: "" });
      } else {
        Swal.fire({
          title: "Error",
          text: response.message,
          icon: "error",
        });
      }
      console.log("response", response);
    };
    useEffect(() => {
      if (editData?.id) {
        const { srID, ...data } = editData;
        console.log("data", data);
        formik?.setValues(data);
      } else {
        setEditData({});
      }
    }, [editData?.id]);
  
    return (
      <Modal
        width={1200}
        open={showAssesmentModal}
        onCancel={() => {
          setShowAssesmentModal(false);
          setEditData({});
          formik.resetForm();
          formik?.setValues({ name: "", segment_percentage: "" });
        }}
        footer={
          <div></div>
          //   loading={isLoading}
        }
      >
        <div className="headingCont">
          <span className="heading">{editData?.id ? "Edit " : "Create"}</span>{" "}
          <span className="orange heading">Segments</span>
        </div>
        {/* <h1>{editData ? editData.challengeId : values.challengeId}</h1> */}
        <div className="parentCont">
          <form className="form1" onSubmit={formik.handleSubmit}>
            <Grid container spacing={2}>
              <Grid item xs={12} sm={11}>
                <FormLabel>Assessment Name<span className="text-[red]">*</span></FormLabel>
  
                <TextField
                  fullWidth
                  size="small"
                  select
                  name="assessment_id"
                  value={
                    formik?.values?.assessment_id
                      ? formik?.values?.assessment_id
                      : ""
                  }
                  onChange={formik.handleChange}
                  error={
                    formik.touched.assessment_id && formik.errors.assessment_id
                  }
                  helperText={
                    formik.touched.assessment_id && formik.errors.assessment_id
                  }
                  id="form-layouts-separator-select"
                  labelId="form-layouts-separator-select-label"
                  input={<OutlinedInput id="select-multiple-language" />}
                >
                  <MenuItem value={""} disabled>
                    Select Name
                  </MenuItem>
                  {assesmentList?.map((value, index) => {
                    return <MenuItem value={value?.id}>{value?.name}</MenuItem>;
                  })}
                </TextField>
              </Grid>
              <Grid item xs={12} sm={11}>
                <FormLabel>Name<span className="text-[red]">*</span></FormLabel>
  
                <TextField
                  fullWidth
                  placeholder="Name"
                  size="small"
                  type="text"
                  name="name"
                  value={formik?.values?.name}
                  onChange={formik.handleChange}
                  error={formik.touched.name && formik.errors.name}
                  helperText={formik.touched.name && formik.errors.name}
                />
              </Grid>
              <Grid item xs={12} sm={11}>
                <FormLabel>Percentage<span className="text-[red]">*</span></FormLabel>
  
                <TextField
                  fullWidth
                  placeholder="Percentage"
                  size="small"
                  type="number"
                  name="segment_percentage"
                  value={formik?.values?.segment_percentage}
                  onChange={formik.handleChange}
                  error={
                    formik.touched.segment_percentage &&
                    formik.errors.segment_percentage
                  }
                  helperText={
                    formik.touched.segment_percentage &&
                    formik.errors.segment_percentage
                  }
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <Button
                  className="btn"
                  key="submit"
                  type="primary"
                  onClick={() => formik.handleSubmit()}
                >
                  Submit
                </Button>
              </Grid>
            </Grid>
          </form>
          <div className="slick-container">
            <SlickCarousel />
          </div>
        </div>
      </Modal>
    );
  };
  export default CreateSugments;
  