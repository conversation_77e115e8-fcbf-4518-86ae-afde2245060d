import React, { useContext, useEffect, useState } from "react";
import Header from "../components/Header";
import { IconLiveView, IconMessage, IconTrash } from "@tabler/icons";
import Sidebar from "../components/Sidebar";
import { useParams } from "react-router-dom";
import Month from "../components/Month";
import { getMonth } from "../util";
import GlobalContext from "../context/GlobalContext";
import EventModal from "../components/EventModal";
import CalendarHeader from "../components/CalendarHeader";
import dayjs from "dayjs";
import WorkoutSidebar from "../components/WorkoutSidebar";
import LibraryModal from "../components/LibraryModal";

export const PlansPage = ({ userId }) => {
  const [, setCreateModal] = useState(false);
  const [athletes, setAthletes] = useState([]);

  const [currentMonth, setCurrentMonth] = useState(getMonth());
  const {
    monthIndex,
    showEventModal,
    dispatchCalEvent,
    workoutMode,
    showLibraryModal,
  } = useContext(GlobalContext);

  useEffect(() => {
    setCurrentMonth(getMonth(monthIndex));
  }, [monthIndex]);

  const { id } = useParams();

  useEffect(() => {
    const token = "qmmvofrilpsj59rctkn0ivseuil1jji5";
    fetch(
      `https://app.yoska.in/kona-coach/api/clubs/3/planItems?endDate=2023-12-31&richTextEnabled=true&selectedAthletes=${id}&showAllWorkouts=true&startDate=2021-01-01&userType=coach`,
      {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
      }
    )
      .then((response) => response.json())
      .then((data) => {
        // console.log('Data', data[0].daysList)

        const plans = data[0].daysList.map((item) => {
          return {
            id: item.id,
            day: dayjs(item.date).format("DD-MM-YY"),
            title: item.planWorkouts,
          };
        });

        console.log("Plans", plans);
        dispatchCalEvent({ type: "push", payload: plans });
      })
      .catch((error) => console.error("Error ", error));
  }, [dispatchCalEvent, id]);

  const handleModalReveal = () => {
    setCreateModal(true);
  };

  const actions = [
    {
      id: 1,
      title: "Quick view",
      icon: <IconLiveView size={18} />,
    },
    {
      id: 2,
      title: "Chat",
      icon: <IconMessage size={18} />,
    },
    {
      id: 3,
      title: "Details",
      icon: <IconTrash size={18} />,
    },
  ];

  useEffect(() => {
    const userId = localStorage.getItem("userId");

    let apiUrl = `https://app.yoska.in/kona-coach/api/organizations/${userId}/athletes`;
    const bearerToken = "qmmvofrilpsj59rctkn0ivseuil1jji5";

    fetch(apiUrl, {
      method: "GET",
      headers: {
        Authorization: `Bearer ${bearerToken}`,
      },
    })
      .then((response) => response.json())
      .then((data) => {
        console.log("athletes", data);
        setAthletes(data);
      })
      .catch((error) => {
        console.error(error);
      });
  }, [userId]);

  return (
    <>
      <Header />
      <div className={`grid grid-cols-1 xl:grid-cols-5 items-start gap-x-4`}>
        <div
          className={`hidden xl:block p-4 bg-slate-100 fixed top-24 xl:w-2/12 left-0 overflow-hidden h-full`}
        >
          {workoutMode ? (
            <WorkoutSidebar actions={actions} />
          ) : (
            <Sidebar
              handleModalReveal={handleModalReveal}
              actions={actions}
              athletes={ athletes}
            />
          )}
        </div>
        <div className="lg:col-span-4">
          <div
            className={`flex flex-col md:flex-row items-center justify-center p-4 w-full xl:w-10/12 right-0 top-16 absolute overflow-y-scroll`}
          >
            <div className="fixed z-50">
              {showEventModal && <EventModal userId={userId} />}
            </div>
            {showLibraryModal ? (
              <div className="fixed z-50">
                <LibraryModal userId={userId} />
              </div>
            ) : (
              ""
            )}
            <div className="w-full h-screen flex flex-col relative top-4 calendar">
              <CalendarHeader />
              <div className="flex flex-1">
                <Month month={currentMonth} />
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};
