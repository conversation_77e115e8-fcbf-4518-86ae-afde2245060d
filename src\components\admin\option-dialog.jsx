import { useState, useEffect } from "react";
import { But<PERSON> } from "../ui/button";
import { Input } from "../ui/input";
import { Label } from "../ui/label";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "../ui/select";
import { <PERSON>alog, DialogContent, DialogHeader, DialogTitle } from "../ui/dialog";
import {
	createOptiondata,
	updateOptiondata,
	getAllQuestionData,
} from "../../API/api-endpoint";
import Swal from "sweetalert2";

export const OptionDialog = ({ open, onClose, onSuccess, editingItem }) => {
	const [formData, setFormData] = useState({
		question_id: "",
		weightage: "",
		option_text: "",
		lower_limit: "",
		upper_limit: "",
		is_quantative: "",
	});
	const [isLoading, setIsLoading] = useState(false);
	const [questionList, setQuestionList] = useState([]);
	const [isLoadingQuestions, setIsLoadingQuestions] = useState(false);

	const quantitativeOptions = [
		{ value: "true", label: "True" },
		{ value: "false", label: "False" },
	];

	useEffect(() => {
		if (open) {
			fetchQuestions();
		}
	}, [open]);

	useEffect(() => {
		if (open) {
			if (editingItem?.id) {
				const editData = {
					question_id: String(editingItem.question_id || ""),
					weightage: String(editingItem.weightage || ""),
					option_text: editingItem.option_text || "",
					lower_limit: String(editingItem.lower_limit || ""),
					upper_limit: String(editingItem.upper_limit || ""),
					is_quantative: String(editingItem.is_quantative || ""),
				};
				setFormData(editData);
			} else {
				const newData = {
					question_id: "",
					weightage: "",
					option_text: "",
					lower_limit: "",
					upper_limit: "",
					is_quantative: "",
				};
				setFormData(newData);
			}
		}
	}, [open, editingItem]);

	const fetchQuestions = async () => {
		try {
			setIsLoadingQuestions(true);
			const response = await getAllQuestionData();
			if (response) {
				setQuestionList(response || []);
			}
		} catch (error) {
			console.error("Error fetching questions:", error);
			Swal.fire({
				title: "Error",
				text: "Failed to fetch questions",
				icon: "error",
				timer: 3000,
				showConfirmButton: false,
			});
		} finally {
			setIsLoadingQuestions(false);
		}
	};

	const handleInputChange = (field, value) => {
		setFormData((prev) => ({
			...prev,
			[field]: value,
		}));
	};

	const handleSubmit = async (e) => {
		e.preventDefault();

		if (!formData.question_id) {
			Swal.fire({
				title: "Error",
				text: "Question is required",
				icon: "error",
				timer: 1800,
				showConfirmButton: false,
			});
			return;
		}

		if (!formData.weightage.trim()) {
			Swal.fire({
				title: "Error",
				text: "Weightage is required",
				icon: "error",
				timer: 1800,
				showConfirmButton: false,
			});
			return;
		}

		if (!formData.option_text.trim()) {
			Swal.fire({
				title: "Error",
				text: "Option text is required",
				icon: "error",
				timer: 1800,
				showConfirmButton: false,
			});
			return;
		}

		const weightage = parseFloat(formData.weightage);
		if (isNaN(weightage)) {
			Swal.fire({
				title: "Error",
				text: "Weightage must be a valid number",
				icon: "error",
				timer: 1800,
				showConfirmButton: false,
			});
			return;
		}

		try {
			setIsLoading(true);

			const apiData = {
				question_id: parseInt(formData.question_id),
				weightage: parseFloat(formData.weightage),
				option_text: formData.option_text.trim(),
				lower_limit: formData.lower_limit
					? parseFloat(formData.lower_limit)
					: null,
				upper_limit: formData.upper_limit
					? parseFloat(formData.upper_limit)
					: null,
				is_quantative: formData.is_quantative === "true",
			};

			let response;
			if (editingItem?.id) {
				apiData.id = editingItem.id;
				response = await updateOptiondata(apiData);
			} else {
				response = await createOptiondata(apiData);
			}

			if (response?.status) {
				Swal.fire({
					title: "Success",
					text:
						response.message ||
						`Option ${
							editingItem?.id ? "updated" : "created"
						} successfully`,
					icon: "success",
					timer: 2000,
					showConfirmButton: false,
				});
				onSuccess();
			} else {
				Swal.fire({
					title: "Error",
					text: response?.message || "Failed to save option",
					icon: "error",
					timer: 3000,
					showConfirmButton: false,
				});
			}
		} catch (error) {
			console.error("Error saving option:", error);
			Swal.fire({
				title: "Error",
				text: "An error occurred while saving the option",
				icon: "error",
				timer: 3000,
				showConfirmButton: false,
			});
		} finally {
			setIsLoading(false);
		}
	};

	return (
		<Dialog open={open} onOpenChange={onClose}>
			<DialogContent className='sm:max-w-lg bg-white'>
				<DialogHeader>
					<DialogTitle className='text-lg font-semibold text-gray-900'>
						{editingItem?.id ? "Edit Option" : "Create Option"}
					</DialogTitle>
				</DialogHeader>

				<form onSubmit={handleSubmit} className='space-y-4'>
					<div className='grid gap-4'>
						<div className='space-y-2'>
							<Label
								htmlFor='question_id'
								className='text-sm font-semibold'
							>
								Question
							</Label>
							<Select
								value={formData.question_id}
								onValueChange={(value) =>
									handleInputChange("question_id", value)
								}
								disabled={isLoading || isLoadingQuestions}
							>
								<SelectTrigger>
									<SelectValue placeholder='Select a question' />
								</SelectTrigger>
								<SelectContent className='bg-white max-h-60'>
									{questionList.map((question) => (
										<SelectItem
											key={question.id}
											value={question.id.toString()}
										>
											<div className='max-w-xs truncate'>
												{question.question}
											</div>
										</SelectItem>
									))}
								</SelectContent>
							</Select>
						</div>

						<div className='grid grid-cols-2 gap-4'>
							<div className='space-y-2'>
								<Label
									htmlFor='weightage'
									className='text-sm font-semibold'
								>
									Weightage{" "}
								</Label>
								<Input
									id='weightage'
									type='number'
									step='0.01'
									className='w-full text-sm'
									value={formData.weightage}
									onChange={(e) =>
										handleInputChange(
											"weightage",
											e.target.value
										)
									}
									placeholder='Enter weightage'
									disabled={isLoading}
									required
								/>
							</div>

							<div className='space-y-2'>
								<Label
									htmlFor='is_quantative'
									className='text-sm font-semibold'
								>
									Is Quantitative
								</Label>
								<Select
									value={formData.is_quantative}
									onValueChange={(value) =>
										handleInputChange(
											"is_quantative",
											value
										)
									}
									disabled={isLoading}
								>
									<SelectTrigger>
										<SelectValue placeholder='Select' />
									</SelectTrigger>
									<SelectContent className='bg-white'>
										{quantitativeOptions.map((option) => (
											<SelectItem
												key={option.value}
												value={option.value}
											>
												{option.label}
											</SelectItem>
										))}
									</SelectContent>
								</Select>
							</div>
						</div>

						<div className='space-y-2'>
							<Label
								htmlFor='option_text'
								className='text-sm font-semibold'
							>
								Option Text{" "}
							</Label>
							<Input
								id='option_text'
								type='text'
								className='w-full text-sm'
								value={formData.option_text}
								onChange={(e) =>
									handleInputChange(
										"option_text",
										e.target.value
									)
								}
								placeholder='Enter option text'
								disabled={isLoading}
								required
							/>
						</div>

						<div className='grid grid-cols-2 gap-4'>
							<div className='space-y-2'>
								<Label
									htmlFor='lower_limit'
									className='text-sm font-semibold'
								>
									Lower Limit
								</Label>
								<Input
									id='lower_limit'
									type='number'
									step='0.01'
									className='w-full text-sm'
									value={formData.lower_limit}
									onChange={(e) =>
										handleInputChange(
											"lower_limit",
											e.target.value
										)
									}
									placeholder='Enter lower limit'
									disabled={isLoading}
								/>
							</div>

							<div className='space-y-2'>
								<Label
									htmlFor='upper_limit'
									className='text-sm font-semibold'
								>
									Upper Limit
								</Label>
								<Input
									id='upper_limit'
									type='number'
									step='0.01'
									className='w-full text-sm'
									value={formData.upper_limit}
									onChange={(e) =>
										handleInputChange(
											"upper_limit",
											e.target.value
										)
									}
									placeholder='Enter upper limit'
									disabled={isLoading}
								/>
							</div>
						</div>
					</div>

					<div className='flex justify-end gap-3 pt-4'>
						<Button
							type='button'
							variant='outline'
							onClick={onClose}
							disabled={isLoading}
						>
							Cancel
						</Button>
						<Button
							type='submit'
							className='bg-orange-600 hover:bg-orange-700 text-white'
							disabled={isLoading}
						>
							{isLoading
								? "Saving..."
								: editingItem?.id
								? "Update Option"
								: "Create Option"}
						</Button>
					</div>
				</form>
			</DialogContent>
		</Dialog>
	);
};
