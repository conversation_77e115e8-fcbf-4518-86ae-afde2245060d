import React, { useEffect, useState } from 'react'
import { <PERSON>u, Switch } from '@mantine/core'
import {
  IconFilter,
  IconPlus,
  IconSearch,
  // IconX,
  IconUser,
  IconX
} from '@tabler/icons'
import { json, useNavigate } from 'react-router-dom'
import { getAllPrograms, getDayoptionValues, getFechAlGoals, getPersonlisation, getPrograms, getUsersProfile, getzonesupdation } from '../API/api-endpoint'
import { ToggleButton, ToggleButtonGroup } from '@mui/material'
import { removeNullStrings, removeNullValues } from '../utils/Resubale'
import { showInfo, showSuccess } from './Messages'
import Swal from 'sweetalert2'
// import { Link } from 'react-router-dom'

const Sidebar = ({ setIsZoneOpen, setfetchDaysoptionList, setFormGoalValue, formGoalValue, setTogglesidebar, togglesidebar, actions, formik, handleModalReveal, control, assignedCocahId, handleChangeSidebar, setUserDetails, setIsEnrolledChallengesOpen, athletes, AssignedCoach, setCreateModal, setAssignedCoachId, setIsProfileOpen, setIsEditProfile }) => {
  const [username, setUsername] = useState('')
  const [filterData, setFilterData] = useState([])
  const [onboardingState, setOnboardingState] = useState('')
  const [coachProgramID, setCoachProgramID] = useState()
  const navigate = useNavigate()

  useEffect(() => {
    setOnboardingState(JSON.parse(localStorage.getItem("user"))?.onboardingState);
    setCoachProgramID(localStorage.getItem("programID"));
  },[])

  const handleChange = async (item) => {
    const today = new Date();
    const inputEndDate = new Date(item?.sub?.end_date);
    if (inputEndDate < today) {
      Swal.fire({
        title: "Info!!",
        text: `The subscription for ${item?.athlete?.firstname} ${item?.athlete?.lastname} have expired for ${item?.program?.program_name} program`,
        icon: "info",
      });
    } 
      
      if (onboardingState == "community") {
        setAssignedCoachId(item?.id);
      } else {
        setAssignedCoachId(item?.athlete_id);
        setTogglesidebar("workout");
      }
      
      setCreateModal(true)
      setIsProfileOpen(false)
      setIsZoneOpen({ isOpen: false })

      setIsEnrolledChallengesOpen({ isOpen: false, assignCoachId: "" })
      setIsEditProfile(false)
      const actProgram = await getPrograms(item?.athlete_id);
      let filter = actProgram?.filter((data) => data.active == 1)
      let coachID = filter[0]?.program_id ? filter[0]?.program_id : coachProgramID;
      if (coachID) {
        const response = await getUsersProfile(coachID, item?.athlete_id)
        let data = { ...response?.existingUserContacts, ...response?.personalization_profile, ...response?.user }

        let destructer = removeNullStrings(data)
        let valueChange = removeNullValues(destructer)
        let athleteName = null;
        
        if (onboardingState === "community") {
          athleteName = `${item?.firstname} ${item?.lastname}`
        }else {
          athleteName = `${item?.athlete?.firstname} ${item?.athlete?.lastname}`
        }
        
        formik.setValues({
          ...valueChange,
          selectedAthleteName: athleteName
        });
        let responseOfactivity = await getPersonlisation(item?.athlete_id);
        console.log("responseOfactivity", responseOfactivity);
        formik.setFieldValue("daysoption", responseOfactivity[0]?.daysoption)
        formik.setFieldValue("daysinweek", responseOfactivity[0]?.daysinweek)
        formik.setFieldValue("activity", responseOfactivity[0]?.program?.program_name)


        const activityName = await getAllPrograms()
        let filteractivityName = activityName?.rows?.filter((ele) => ele?.program_id == responseOfactivity[0]?.program_id)
        setFormGoalValue({ ...formGoalValue, daysinweek: responseOfactivity[0]?.daysinweek, activities: responseOfactivity[0]?.program_id, id: responseOfactivity[0]?.id });
        //  if (!destructer?.goal) {
        //   fetchDGoals(responseOfactivity[0]?.program_id)
        //  }
        const getZonesUpdate = await getzonesupdation(item?.athlete_id, responseOfactivity[0]?.program_id)
        if (getZonesUpdate?.shouldshowzonesscreen) {
          showInfo("Time trial have been completed but zones have not been updated yet, please update zones to continue with personalization");
        }
        if (responseOfactivity[0]?.daysinweek) {
          fetchDaysOptuon(responseOfactivity[0]?.daysinweek, responseOfactivity[0]?.program_id);
        }
      }

    const fetchDaysOptuon = async (week, activity) => {
      const response = await getDayoptionValues(
        week,
        activity ? activity : formGoalValue?.activities
      );
      if (response) {
        setfetchDaysoptionList(response);
      }
    }
  };

  const handleProfile = async (id, athleteDetails) => {
    setAssignedCoachId(id)
    setCreateModal(false)
    setIsProfileOpen(true)
    setIsEditProfile(false)
    setIsZoneOpen({ isOpen: false })

    setIsEnrolledChallengesOpen({ isOpen: false, assignCoachId: "" })

    const actProgram = await getPrograms(id);
    let filter = actProgram?.filter((data) => data.active == 1)
    let coachID = filter[0]?.program_id ? filter[0]?.program_id : coachProgramID;
    console.log("actProgram", coachID, coachProgramID);
    if (coachID) {
      const response = await getUsersProfile(coachID, id)
      let data = { ...response?.existingUserContacts, ...response?.personalization_profile, ...response?.user, ...response?.user.usercontact }
      formik.setValues(data)
      formik.setFieldValue("height", response?.personalization_profile?.height)
      formik.setFieldValue("weight", response?.personalization_profile?.weight)

      setUserDetails(response)
      setIsEditProfile(false)
    }
    // formik
    // setProfile(!profile)
  };
  
  
  return (
    <div>
      {/*
    
     <div className='flex items-center justify-between '>
        <div className='flex items-center space-x-8 '>
          <div>
            <IconPlus
              size={24}
              color='dodgerblue'
              onClick={handleModalReveal}
              className='cursor-pointer'
            />
          </div>
          <div>
            <IconSearch size={24} color='dodgerblue' />
          </div>
          <div>
            <IconFilter size={24} color='dodgerblue' />
          </div>
        </div>
         <div>
          <IconX size={24} color='dodgerblue' />
        </div> 
      </div>
    */}
      <div className='my-4'>
        <ToggleButtonGroup size="small" {...control} aria-label="Small sizes">
          <ToggleButton
            style={{
              backgroundColor: togglesidebar === 'workout' ? '#E67E22' : 'inherit',
              color: togglesidebar === 'workout' ? 'white' : 'inherit',
            }}
            value="workout" key="workout"
            disabled={onboardingState === "community"}>
            Workouts
          </ToggleButton>,
          <ToggleButton
            style={{
              backgroundColor: togglesidebar === 'athlete' ? '#E67E22' : 'inherit',
              color: togglesidebar === 'athlete' ? 'white' : 'inherit',
            }}
            value="athlete" key="athlete">
            Athletes
          </ToggleButton>
        </ToggleButtonGroup>
      </div>
      <div className='flex flex-col gap-y-2 text-sm mb-4'>
        {/*
      <div className='text-slate-700 capitalize'>
          Showing Athletes for {username}
        </div>
      */}
        <div className='text-slate-700'>
          <span className='font-medium'>Total Athletes</span> :{' '}
          {AssignedCoach?.length > 0 ? AssignedCoach?.length : 'No data found'}
        </div>
      </div>
      <hr />
      <div className='flex flex-col gap-y-4 items-start h-[64vh] mt-4' style={{ overflowY: "auto", scrollbarWidth: "thin", scrollbarColor: "#E67E22 white" }}>
        {AssignedCoach?.length > 0 ? (
          AssignedCoach?.map((item) => (
            <div
              className='flex justify-between items-center gap-x-4 w-full'
              key={item.id}

            >
              <div className='flex items-center gap-x-2 cursor-pointer' onClick={() => handleChange(item)}>
                <span>
                  <IconUser size={20} color='#334155' />
                </span>
                <span className="text-slate-700">
                  {onboardingState === "community" ? (
                    <>
                      {item?.firstname} {item.lastname}
                    </>
                  ) : (
                    <>
                      {item?.athlete?.firstname} {item.lastName}
                    </>
                  )}
                </span>

              </div>
              <div>
                <Menu shadow='md' width={150} position='bottom-end'>
                  <Menu.Target className='cursor-pointer'>
                    <svg
                      xmlns='http://www.w3.org/2000/svg'
                      className='icon icon-tabler icon-tabler-dots-vertical'
                      width='20'
                      height='20'
                      viewBox='0 0 24 24'
                      strokeWidth='1.5'
                      stroke='#E67E22'
                      fill='none'
                      strokeLinecap='round'
                      strokeLinejoin='round'
                    >
                      <path stroke='none' d='M0 0h24v24H0z' fill='none' />
                      <circle cx='12' cy='12' r='1' />
                      <circle cx='12' cy='19' r='1' />
                      <circle cx='12' cy='5' r='1' />
                    </svg>
                  </Menu.Target>

                  <Menu.Dropdown>
                    {actions.map((action) => (
                      <Menu.Item color={'#E67E22'} icon={action.icon} key={action.id}
                        onClick={() => {
                          handleProfile(item?.athlete_id, item)
                        }}
                      >
                        {action.title}
                      </Menu.Item>
                    ))}
                  </Menu.Dropdown>
                </Menu>
              </div>
            </div>
          ))
        ) : (
          <div>No data found</div>
        )}
      </div>
    </div>
  )
}

export default Sidebar
