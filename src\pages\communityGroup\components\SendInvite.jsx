import React, { useState } from "react";
import {
    <PERSON><PERSON>,
    <PERSON>,
    TextField,
    Chip,
    Button,
    Typography,
} from "@mui/material";
import { sentInviteEmailForGroup } from "../../../API/api-endpoint";
import Swal from "sweetalert2";


const SendInvite = ({ open, handleClose, userGroup, handleReload }) => {
    const [emails, setEmails] = useState([]);
    const [inputValue, setInputValue] = useState("");

    // Handle adding emails as tags
    const handleKeyDown = (e) => {
        if (e.key === "Enter" || e.key === ",") {
            e.preventDefault();
            const trimmedValue = inputValue.trim();
            if (trimmedValue && validateEmail(trimmedValue)) {
                setEmails([...emails, trimmedValue]);
                setInputValue("");
            } else {
                Swal.fire({
                    title: "Invalid email format!",
                    icon: "error",
                    willOpen: () => {
                    document.querySelector('.swal2-container')?.style.setProperty('z-index', '2000', 'important');
                }
                });
            }
        }
    };

    // Validate email format
    const validateEmail = (email) => {
        const regex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return regex.test(email);
    };

    // Remove an email tag
    const handleDelete = (emailToDelete) => {
        setEmails(emails.filter((email) => email !== emailToDelete));
    };

    // Handle form submission
    const handleSubmit = async () => {
        if (emails.length === 0) {
            Swal.fire({
                title: "Error",
                text: "Please add at least one valid email.",
                icon: "error",
                willOpen: () => {
                    document.querySelector('.swal2-container')?.style.setProperty('z-index', '2000', 'important');
                }
            });
            return;
        }

        try {
            const { id } = userGroup;
            const response = await sentInviteEmailForGroup(emails, id);
            Swal.fire({
                title: "Success",
                text: response.message,
                icon: "success",
                willOpen: () => {
                    document.querySelector('.swal2-container')?.style.setProperty('z-index', '2000', 'important');
                }
            });
            handleReload(true);
            setEmails([]);
            handleClose(); // Close modal after successful submission
        } catch (error) {
            console.error("Error sending emails:", error);
            Swal.fire({
                title: "Failed to send emails",
                text: error.response.data.message,
                icon: "error",
                willOpen: () => {
                    document.querySelector('.swal2-container')?.style.setProperty('z-index', '2000', 'important');
                }
            });
            // throw error;
        }
    };
    return (
        <Modal
            width={1200}
            open={open}
            onCancel={handleClose}
            footer={null}>
            <Box
                className="bg-white p-6 rounded-lg shadow-lg w-96 mx-auto mt-20"
                sx={{
                    outline: "none",
                }}
            >
                <Typography variant="h6" className="mb-4 font-semibold">
                    {`Send Email Invites to ${userGroup?.communityName}.`}
                </Typography>

                {/* Email Input Field */}
                <TextField
                    fullWidth
                    variant="outlined"
                    label="Enter emails (Press Enter or Comma)"
                    value={inputValue}
                    onChange={(e) => setInputValue(e.target.value)}
                    onKeyDown={handleKeyDown}
                    margin="normal"
                />

                {/* Email Chips */}
                <div className="flex flex-wrap gap-2 mb-5">
                    {emails.map((email, index) => (
                        <Chip
                            key={index}
                            label={email}
                            onDelete={() => handleDelete(email)}
                            color="primary"
                            className="mr-2 mb-2"
                        />
                    ))}
                </div>

                {/* Action Buttons */}
                <div className="flex justify-end gap-3 mt-4">
                    <Button
                        onClick={handleClose}
                        variant="contained"
                    >
                        Cancel
                    </Button>
                    <Button
                        onClick={handleSubmit}
                        variant="contained"
                        disabled={emails.length === 0}
                    >
                        Send
                    </Button>
                </div>
            </Box>
        </Modal>
    )
};

export default SendInvite;