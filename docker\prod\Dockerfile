ARG NGINX_VARIANT=1.27.1-alpine

FROM node:20 AS base
WORKDIR /usr/src/app

FROM base AS deps
COPY package.json package-lock.json ./
RUN npm ci

FROM base AS builder
ARG DOPPLER_TOKEN
ENV DOPPLER_TOKEN=$DOPPLER_TOKEN
COPY scripts/secret_manager_setup.sh ./secret_manager_setup.sh
RUN ./secret_manager_setup.sh
COPY --from=deps /usr/src/app/node_modules ./node_modules
COPY . .
RUN export HISTIGNORE='doppler*'
RUN echo $DOPPLER_TOKEN | ./doppler configure set token --scope /usr/src/app
RUN ./doppler run -- npx react-scripts --max_old_space_size=4096 build

FROM nginx:$NGINX_VARIANT
WORKDIR /usr/share/nginx/html/
LABEL org.opencontainers.image.source="https://github.com/yoska-technology-solutions/yofit-frontend"
LABEL org.opencontainers.image.description="Frontend Image"
COPY --from=builder /usr/src/app/build ./
COPY ./nginx/*.conf /etc/nginx/

EXPOSE 80

CMD ["nginx", "-g", "daemon off;"]
