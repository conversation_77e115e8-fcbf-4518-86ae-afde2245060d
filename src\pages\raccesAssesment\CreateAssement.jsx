import React, { useEffect } from 'react';
import Header from '../../components/Header';
import { <PERSON>ton, FormControlLabel, Radio, RadioGroup } from '@mui/material';
import { useState } from 'react';
import { createRaccesReadinessData, createWellnessData, getRacesRedinessQuetions, getWellnessQuetions } from '../../API/api-endpoint';
import Swal from 'sweetalert2';
const CreateAssement = () => {
    const [discretion, setDiscretion] = useState([]);
  const [questions, setQuestions] = useState(null);
console.log("questions",questions,discretion);
  useEffect(() => {
    fetchAllWellness();
  }, []);

  const fetchAllWellness = async () => {
    const response = await getRacesRedinessQuetions();
    console.log("response",response);
    setQuestions(response);
  };

  const handleRadioChange = (segmentId, subSegmentId, optionId) => {
    const updatedDiscretion = [...discretion];
  
    const existingSegmentIndex = updatedDiscretion.findIndex(
      (segment) => segment.segment_id === segmentId
    );
  
    if (existingSegmentIndex !== -1) {
      const existingSegment = updatedDiscretion[existingSegmentIndex];
      const existingSubSegmentIndex = existingSegment.subsegmentsreadinesses.findIndex(
        (subSegment) => subSegment.subsegment_id === subSegmentId
      );
  
      if (existingSubSegmentIndex !== -1) {
        const existingSubSegment =
          existingSegment.subsegmentsreadinesses[existingSubSegmentIndex];
        existingSubSegment.option_id = optionId;
        updatedDiscretion[existingSegmentIndex].subsegmentsreadinesses[
          existingSubSegmentIndex
        ] = existingSubSegment;
      } else {
        existingSegment.subsegmentsreadinesses.push({
          subsegment_id: subSegmentId,
          option_id: optionId,
        });
        updatedDiscretion[existingSegmentIndex] = existingSegment;
      }
    } else {
      updatedDiscretion.push({
        segment_id: segmentId,
        subsegmentsreadinesses: [
          {
            subsegment_id: subSegmentId,
            option_id: optionId,
          },
        ],
      });
    }
  
    setDiscretion(updatedDiscretion);
  };
  
  
  
  

  const submitData = async () => {
   
    const response = await createRaccesReadinessData(discretion);
    console.log("response", response);
    if (response.status) {
      
    Swal.fire({
      title: "Success",
      text: response.message,
      icon: "success",
    });
    setDiscretion([])
  }

  };
  return (
    <div>
    <Header />
    <div style={{ marginTop: '90px', padding: '20px', display:"flex", justifyContent:"center" }}>
    <div className='bg-[#FFEADC] w-[70%] p-8 '>
      
      <h2 style={{borderBottom:"1px solid gray"}}>
          <strong  style={{color:"#E67E22"}} className='text-sm'><a href='/racessScore'>See Previous Assessment Result</a></strong>&nbsp;
        </h2>
        &nbsp;
        <ul style={{ listStyle: 'disc' }}>
        {questions?.map((item) => (
          <div key={item.id}>
            <div  className='mb-4'>
            <strong className='text-sm'>{item.name}</strong>
            </div>
            {item?.subsegmentsreadinesses?.map((subsegment) => (
              <div key={subsegment.id}>
                <strong className='text-sm'>{subsegment.measuring_factor}-Metric: &nbsp;{subsegment?.metric}</strong>
                <RadioGroup
                  aria-labelledby={`question-${subsegment.id}`}
                  name={`radio-buttons-group-${subsegment.id}`}
                  
                >
                  {subsegment?.OptionsReadinesses?.map((option) => (
                    <FormControlLabel
                      key={option.id}
                      value={JSON.stringify(option)}
                      control={<Radio sx={{ fontSize: "8px" }} />}
                      label={option.option_text}
                      checked={
                        discretion[`segment_${item.id}`]?.[`subsegment_${subsegment.id}`]?.[`option_${option.id}`]
                      }
                      onChange={() => handleRadioChange(item.id, subsegment.id, option.id)}
                    />
                  ))}
                </RadioGroup>
              </div>
            ))}
          </div>
        ))}
      </ul>
       <Button variant='contained' onClick={submitData}>create</Button>
    
    </div>
    </div>
  </div>
  )
}

export default CreateAssement
