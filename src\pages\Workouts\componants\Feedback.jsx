import React, { useState, useEffect } from "react";
import { FaSadTear, FaFrown, FaMeh, FaSmile, FaGrin } from "react-icons/fa";
import { getAllFeedbackOption, getFeedbackForWorkout, recordFeedbackForWorkout } from "../../../API/api-endpoint";


const iconMap = {
    1: <FaSadTear className="text-red-600 text-2xl" />,
    2: <FaFrown className="text-orange-500 text-2xl" />,
    3: <FaMeh className="text-gray-700 text-2xl" />,
    4: <FaSmile className="text-green-700 text-2xl" />,
    5: <FaGrin className="text-green-900 text-2xl" />,
};

const Feedback = ({ workoutId, workoutType }) => {
    const [feedbackOptions, setFeedbackOptions] = useState([]);
    const [recordedFeedback, setRecordedFeedback] = useState(null);
    const [selectedFeedback, setSelectedFeedback] = useState(null);
    const [loading, setLoading] = useState(true);
    const [feedbackComment, setFeedbackComment] = useState("");
    const [recordFeedbackLoading, setRecordFeedbackLoading] = useState(false);
    const [userRoleId, setUserRoleId] = useState(0);

    useEffect(() => {
        const fetchFeedbackData = async () => {
            try {
                setLoading(true);
                // fetch feedback options on mount of component
                const response = await getAllFeedbackOption();
                setFeedbackOptions(response?.data);
            } catch (error) {
                console.error("Error fetching feedback option data:", error);
            } finally {
                setLoading(false);
            }
        };
        const getUserRoleId = () => {
            try {
                const roleId = localStorage.getItem("roleID");

                setUserRoleId(parseInt(roleId, 10));
            } catch (error) {
                throw error;
            }
        }
        fetchFeedbackData();
        getUserRoleId();
    }, []);

    // check if feedback recorded for workout
    useEffect(() => {
        const getRecordedFeedbackForWorkout = async () => {
            try {
                setLoading(true);
                // Check if feedback is recorded for this activity
                const recordedResponse = await getFeedbackForWorkout(workoutId, workoutType);
                const recordedData = recordedResponse?.data ? recordedResponse?.data : null;
                if (recordedData) {
                    const dataObj = getRecordedFeedbackForComponent(workoutType, recordedData);
                    setRecordedFeedback(dataObj);
                }
            } catch (error) {
                console.error("Error fetching feedback data for workout:", error);
            } finally {
                setLoading(false);
            }
        };
        getRecordedFeedbackForWorkout();
    }, [workoutId]);

    // handle recorded feedback get endpoint for component
    const getRecordedFeedbackForComponent = (workoutType, recordedData) => {
        if (workoutType === "strava") {
            const dataObj = Object.assign({}, {
                id: recordedData?.id,
                feedbackText: recordedData?.feedbackText,
                workoutFeedback: recordedData?.stravaworkoutfeedback
            });
            return dataObj;
        } else if (workoutType === "manual") {
            const dataObj = Object.assign({}, {
                id: recordedData?.id,
                feedbackText: recordedData?.feedbackText,
                workoutFeedback: recordedData?.manualworkoutfeedback
            });
            return dataObj;
        }
    }

    // Handle feedback selection
    const handleFeedbackSelect = (feedbackValue) => {
        setSelectedFeedback(feedbackValue);
    };

    // updating the feedback comment
    const handleCommentChange = (e) => {
        setFeedbackComment(e.target.value);
    };

    // Submit feedback
    const submitFeedback = async () => {
        if (!selectedFeedback) return;
        try {
            setRecordFeedbackLoading(true);
            const response = await recordFeedbackForWorkout(workoutId, selectedFeedback, workoutType, feedbackComment);
            if (response) {
                setRecordFeedbackLoading(false);
                alert("Workout feedback recorded successfully.")
                setSelectedFeedback(null);
                const dataObj = getRecordedFeedbackForComponent(workoutType, response);
                setRecordedFeedback(dataObj);
            } else {
                setRecordFeedbackLoading(false);
                alert("Workout feedback recording failed !");
                console.error("Failed to submit feedback");
            }
        } catch (error) {
            setRecordFeedbackLoading(false);
            alert("Workout feedback recording failed !");
            console.error("Error submitting feedback:", error);
            throw error;
        }
    };

    // reset feedback
    const resetFeedback = () => {
        setSelectedFeedback(null);
        setFeedbackComment("");
    }

    const renderRecordedFeedback = () => {
        return (
            <div className="p-4 bg-gray-100 rounded shadow-md max-w-lg mx-auto">
                <h6 className="text-xl font-bold text-center"> Workout Experience</h6>
                <div className="p-2">
                    <div className="flex justify-center space-x-2 mb-2">
                        {feedbackOptions.map((option) => (
                            <div
                                key={option.id}
                                className={`flex flex-col items-center ${recordedFeedback.workoutFeedback.feedbackValue === option.feedbackValue
                                    ? "opacity-100 scale-110"
                                    : "opacity-50"
                                    } transition-transform duration-300`}
                            >
                                {iconMap[option.feedbackValue]}
                                <p className="text-sm font-extrabold">{option.displayName}</p>
                            </div>
                        ))}

                    </div>
                    <div>
                        <h3 className="text-center font-bold">How did the workout feel?</h3>
                        <p className="text-center">{recordedFeedback.feedbackText === "" ? "N/A" : recordedFeedback.feedbackText}</p>
                    </div>
                </div>
            </div>

        );
    }

    const renderAthleteWorkoutFeedback = () => {
        return (

            <div className="p-4 bg-gray-100 rounded shadow-md max-w-lg mx-auto">
                <h6 className="text-xl font-bold text-center">Rate Your Workout Experience</h6>
                <div className="p-2">
                    <div className="flex justify-center space-x-2 mb-2">
                        {feedbackOptions.map((option) => (
                            <div
                                key={option.id}
                                className={`flex flex-col items-center cursor-pointer ${selectedFeedback === option.id
                                    ? "opacity-100 scale-110"
                                    : "opacity-50"
                                    } transition-transform duration-300`}
                                onClick={() => handleFeedbackSelect(option.id)}
                            >
                                {iconMap[option.feedbackValue]}
                                <p className="text-sm font-semibold">{option.displayName}</p>
                            </div>
                        ))}
                    </div>

                    {/* Text Area Section */}
                    {selectedFeedback && (
                        <div className="mb-4">
                            <textarea
                                value={feedbackComment}
                                onChange={handleCommentChange}
                                placeholder="Add your workout exprience..."
                                className="w-full border border-gray-300 rounded-md p-2 resize-none focus:outline-none focus:ring-2 focus:ring-blue-400"
                                rows="4"
                            />
                        </div>
                    )}

                    {/* button section */}
                    {selectedFeedback && (
                        <div className="flex justify-evenly items-center ">
                            <button
                                onClick={submitFeedback}
                                className="sm:w-1/2 py-1 bg-orange-500 rounded-lg flex items-center justify-center transition duration-200 text-white text-xl font-medium md:text-base sm:text-base hover:bg-orange-400"
                                disabled={recordFeedbackLoading}
                            >
                                {recordFeedbackLoading ? (
                                    <div className="animate-spin bg-center rounded-full h-5 w-5 border-t-2 border-white"></div>
                                ) : (
                                    <span className="flex flex-row justify-center">
                                        <span className="font-bold">
                                            Submit Feedback
                                        </span>
                                    </span>
                                )}
                            </button>
                            <button
                                onClick={resetFeedback}
                                className="ml-2 sm:w-1/2 py-1 bg-red-500 rounded-lg flex items-center justify-center transition duration-200 text-white text-xl font-medium md:text-base sm:text-base hover:bg-red-400"
                            >
                                <span className="font-semibold">
                                    Reset
                                </span>
                            </button>
                        </div>

                    )}
                </div>
            </div>
        );
    }

    const renderCoachVisibleIfWorkoutFeedbackNotProvided = () => {
        return (
            <div className="p-4 bg-gray-100 rounded shadow-md max-w-lg mx-auto">
                <h6 className="text-xl font-bold text-center">Workout Feedback Not Yet Submitted</h6>
            </div>
        )
    }

    const renderWorkoutFeedback = () => {
        return (
            <>
                {(userRoleId === 1 || userRoleId === 2 || userRoleId === 3) ? renderCoachVisibleIfWorkoutFeedbackNotProvided() : (userRoleId === 5) ? renderAthleteWorkoutFeedback() : (
                    <div className="p-4 bg-gray-100 rounded shadow-md max-w-lg mx-auto">
                        <h6 className="text-xl font-bold text-center">Workout feedback not available</h6>
                    </div>
                )}
            </>

        )
    }

    if (loading) return <p>Loading...</p>;

    return (
        <>
            {recordedFeedback ? renderRecordedFeedback() : renderWorkoutFeedback()}
        </>
    );
};

export default Feedback;
