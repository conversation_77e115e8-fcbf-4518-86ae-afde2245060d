import {
    Chip,
    FormControl,
    FormLabel,
    Grid,
    MenuItem,
    OutlinedInput,
    Select,
    TextField,
  } from "@mui/material";
  import { <PERSON><PERSON>, Modal } from "antd";
  import React, { useEffect, useState } from "react";
  import {
    CreatePrograms,
    getAllActivityData,
    updatePrograms,
  } from "../../API/api-endpoint";
  import { useFormik } from "formik";
  import Swal from "sweetalert2";
  import SlickCarousel from "../../pages/SlickCarousel";
  const scoreData = [1, 2, 3, 4, 5];
  const CreateProgram = ({
    fetchReport,
    setShowAssesmentModal,
    showAssesmentModal,
    editData,
    setEditData,
  }) => {
    const [programList, setProgramList] = useState([]);
    console.log("editData", editData);
    const formik = useFormik({
      initialValues: {
        activity_id: "",
        program_name: "",
      },
      validate: (values) => {
        const errors = {};
        if (!values.activity_id) {
          errors.activity_id = "Activity name is required";
        }
        if (!values.program_name) {
          errors.program_name = "Program name is required";
        }
        return errors;
      },
      // validationSchema: {},
      onSubmit: (values, { resetForm }) => {
        handleSubmitAssesmentForm(values, resetForm);
      },
    });
    console.log("formik", formik?.values);
    const getAllProgramsData = async () => {
      const response = await getAllActivityData();
      console.log("response", response);
      setProgramList(response?.rows);
    };
    useEffect(() => {
      getAllProgramsData();
    }, []);
  
    const handleSubmitAssesmentForm = async (data, resetForm) => {
      let response = "";
      if (editData?.program_id) {
        let allDatadata = { ...data, activity_ids: data?.activity_id, status: 0 };
  
        response = await updatePrograms(allDatadata);
      } else {
        let allDatadata = { ...data, activity_ids: data?.activity_id };
        response = await CreatePrograms(allDatadata);
      }
  
      if (response?.status) {
        Swal.fire({
          title: "Success",
          text: response.message,
          icon: "success",
        });
        setShowAssesmentModal(false);
        setEditData({});
        fetchReport();
        resetForm();
        formik?.setValues({
          activity_id: "",
          program_name: "",
          activity_ids: "",
        });
      } else {
        Swal.fire({
          title: "Error",
          text: response.message,
          icon: "error",
        });
      }
      console.log("response", response);
    };
    useEffect(() => {
      if (editData?.program_id) {
        const { srID, ...data } = editData;
        console.log("data", data);
        formik?.setValues(data);
      } else {
        setEditData({});
      }
    }, [editData?.program_id]);
    return (
      <Modal
        width={1200}
        open={showAssesmentModal}
        onCancel={() => {
          setShowAssesmentModal(false);
          setEditData({});
          formik.resetForm();
          formik?.setValues({
            activity_id: "",
            program_name: "",
            activity_ids: "",
          });
        }}
        footer={
          <div></div>
          //   loading={isLoading}
        }
      >
        <div className="headingCont">
        <span className="heading">{editData?.program_id ? "Edit " : "Create"}</span>{" "}
          <span className="orange heading">Program</span>
        </div>
        {/* <h1>{editData ? editData.challengeId : values.challengeId}</h1> */}
        <div className="parentCont">
          <form className="form1" onSubmit={formik.handleSubmit}>
            <Grid container spacing={2}>
              <Grid item xs={12} sm={11}>
                <FormLabel>Activity Name<span className="text-[red]">*</span></FormLabel>
  
                <TextField
                  fullWidth
                  size="small"
                  select
                  name="activity_id"
                  SelectProps={{
                    MenuProps: {
                      PaperProps: {
                        style: {
                           scrollbarColor:"#E67E22 white",
                           scrollbarWidth:"thin"
                        },
                      },
                    },
                  }}
                  value={formik?.values?.activity_id}
                  onChange={formik.handleChange}
                  error={formik.touched.activity_id && formik.errors.activity_id}
                  helperText={
                    formik.touched.activity_id && formik.errors.activity_id
                  }
                  id="form-layouts-separator-select"
                  labelId="form-layouts-separator-select-label"
                  input={<OutlinedInput id="select-multiple-language" />}
                >
                  <MenuItem value={""} disabled>
                    Select Activity
                  </MenuItem>
                  {programList?.map((value, index) => {
                    return (
                      <MenuItem value={value?.id}>
                        {value?.activity_name}
                      </MenuItem>
                    );
                  })}
                </TextField>
              </Grid>
              <Grid item xs={12} sm={11}>
                <FormLabel>Program Name<span className="text-[red]">*</span></FormLabel>
  
                <TextField
                  fullWidth
                  placeholder="Program Name"
                  size="small"
                  type="text"
                  name="program_name"
                  value={formik?.values?.program_name}
                  onChange={formik.handleChange}
                  error={
                    formik.touched.program_name && formik.errors.program_name
                  }
                  helperText={
                    formik.touched.program_name && formik.errors.program_name
                  }
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <Button
                  className="btn"
                  key="submit"
                  type="primary"
                  onClick={() => formik.handleSubmit()}
                >
                  Submit
                </Button>
              </Grid>
            </Grid>
          </form>
          <div className="slick-container">
            <SlickCarousel />
          </div>
        </div>
      </Modal>
    );
  };
  
  export default CreateProgram;
  
