import React, { useEffect, useMemo, useState } from "react";
import Paper from "@mui/material/Paper";
import Table from "@mui/material/Table";
import TableBody from "@mui/material/TableBody";
import TableContainer from "@mui/material/TableContainer";
import TableHead from "@mui/material/TableHead";
import TableRow from "@mui/material/TableRow";
import TableCell, { tableCellClasses } from "@mui/material/TableCell";
import { styled } from "@mui/material/styles";
import { Button, FormLabel, Grid, Pagination, TextField } from "@mui/material";
import "../../components/AssignCoach/Assign.css";
import { ExclamationCircleFilled } from '@ant-design/icons';
import Header from "../../components/Header";
import { deleteGoalNameYTA, deleteGoalsData, deletePhaseBloackData, deletePhaseNameData, deletePrograms, deleteZonesClasification, getAlPhaseBlockData, getAllGoalNameForYTA, getAllGoalsData, getAllPhaseNameData, getAllPrograms, getZonesClasification, weeklyFeedData } from "../../API/api-endpoint";
import { IconEdit, IconTrash } from "@tabler/icons";
import { Modal } from "antd";
import CreateProgram from "../../components/FeedData/CreateProgram";
import CreateYrsca from "../../components/FeedData/CreateYrsca";
import CreateGoals from "../../components/FeedData/CreateGoals";
import CreatePhaseName from "../../components/FeedData/CreatePhaseName";
import CreatePhaseBloack from "../../components/FeedData/CreatePhaseBloack";
import MenuItem from "@mui/material/MenuItem";

// let PageSize = 15;
const StyledTableCell = styled(TableCell)(({ theme }) => ({
    [`&.${tableCellClasses.head}`]: {
        backgroundColor: "#1e40af",
        color: theme.palette.common.white,
    },
    [`&.${tableCellClasses.body}`]: {
        fontSize: 14,
    },
}));
const StyledTableRow = styled(TableRow)(({ theme }) => ({
    "&:nth-of-type(odd)": {
        backgroundColor: theme.palette.action.hover,
    },
    // hide last border
    "&:last-child td, &:last-child th": {
        border: 0,
    },
}));
const PhaseBlock = () => {
    const { confirm } = Modal;
    const [reportData, setReportData] = useState()
    const [isOpen, setIsOpen] = useState(true)

    const [editData, setEditData] = useState()

    const [showAssesmentModal, setShowAssesmentModal] = useState(false);

    console.log("reportData", reportData);

    const fetchReport = async () => {
        const response = await getAlPhaseBlockData()
        console.log("response", response);
        setReportData(response)
    }
    const getAllGoalsDataa = async () => {
        const response = await getAllPhaseNameData()
        console.log("response", response);
        setReportData(response)
    }
    useEffect(() => {
        if (isOpen) {
            fetchReport()
            setCurrentPage(1)
        }
        if (!isOpen) {
            getAllGoalsDataa()
            setCurrentPage(1)
        }
    }, [isOpen])
    const [currentPage, setCurrentPage] = useState(1);
    const [searchTerm, setSearchTerm] = useState('');
    const [selectedActivityFilter, setSelectedActivityFilter] = useState('All');

    const handleActivityFilterChange = (event) => {
        setSelectedActivityFilter(event.target.value);
        setCurrentPage(1); // Reset the current page when the filter changes
    };


    const filteredList = useMemo(() => {
        return reportData?.filter((row) => {
            let filetrName = row?.activity?.activity_name?row?.activity?.activity_name:row?.phase
            if (selectedActivityFilter === 'All') {
                return filetrName?.toLowerCase().includes(searchTerm.toLowerCase());
            } else {
                return (
                    filetrName?.toLowerCase().includes(searchTerm.toLowerCase()) &&
                    filetrName?.toLowerCase().trim() === selectedActivityFilter.toLowerCase()
                );
            }
        });
    }, [reportData, selectedActivityFilter, searchTerm]);
    let PageSize = searchTerm || selectedActivityFilter !== 'All' ? filteredList?.length : 15;
    const checkLastPage = useMemo(() => {
        let frstPgae = (currentPage - 1) * PageSize;
        let lastPage = frstPgae + PageSize;
        return filteredList?.slice(frstPgae, lastPage)?.map((row, index) => ({
            ...row,
            // Adjusting index on the first page and adding count from the second page onward
            srID: index + 1 + (currentPage > 1 ? frstPgae : 0),
        }));
    }, [currentPage, reportData,filteredList]);
    const handleSearchChange = (event) => {
        setSearchTerm(event.target.value);
        setCurrentPage(1); // Reset the current page when the search term changes
    };
    const handlePageChange = (event, page) => {
        setCurrentPage(page);
    };

    const DeleteZones = async (id) => {
        if (isOpen) {
            
            const response = await deletePhaseBloackData(id)
            setCurrentPage(1)
            fetchReport()
        }else{
            const response = await deletePhaseNameData(id)
            setCurrentPage(1)
            getAllGoalsDataa()
        }
    }
    const showDeleteConfirm = (id) => {
        confirm({
            title: 'Are you sure delete this phase block?',
            icon: <ExclamationCircleFilled />,
            okText: 'Yes',
            okType: 'danger',
            cancelText: 'No',
            onOk() {
                DeleteZones(id)
            },
            onCancel() {
                console.log('Cancel');
            },
        });
    };
    return (
        <div>
            <Header />
            <div className="grid grid-cols-1 xl:grid-cols-5 items-start gap-x-4"></div>
            <div style={{ marginTop: "100px", padding: "20px" }}>
                <TableContainer component={Paper}>
                    <div style={{ fontSize: "18px", background: "#FFEADC", width: "100%", padding: "10px" }}>
                        <Grid container spacing={2}>
                            <Grid item xs={12} sm={2} sx={{ marginTop: "30px" }}>
                                <Button variant="contained" onClick={() => setShowAssesmentModal(true)}>{isOpen ?"Create Phase Block":"Create Phase Name"}</Button>
                            </Grid>
                            <Grid item xs={12} sm={2} sx={{ marginTop: "30px" }}>
                                <Button variant="contained" onClick={() => setIsOpen(!isOpen)}>{isOpen ? "Get Phase Name" : "Get Phase Block"}</Button>
                            </Grid>
                            <Grid item xs={12} sm={6} sx={{ textAlign: "end", marginTop: "30px" }}>
                                <TextField
                                    select
                                    size="small"
                                    label="Filter by Activity"
                                    style={{ width: '150px' }} 
                                    value={selectedActivityFilter}
                                    onChange={handleActivityFilterChange}
                                    
                                >
                                    <MenuItem value="All">All</MenuItem>
                                    <MenuItem value="Running">Running</MenuItem>
                                    <MenuItem value="Cycling">Cycling</MenuItem>
                                    <MenuItem value="Fitness">Fitness</MenuItem>
                                    <MenuItem value="Triathlon">Triathlon</MenuItem>
                                    <MenuItem value="Swimming">Swimming</MenuItem>
                                </TextField>
                            </Grid>
                            <Grid item xs={12} sm={2}sx={{ marginTop: "30px" , textAlign: "end"}}>
                                <TextField type="text" size="small" value={searchTerm} onChange={handleSearchChange} placeholder="Search By Activity Name.." />
                            </Grid>
                        </Grid>
                        
                    </div>
                 {isOpen?(
                    <TableContainer style={{ maxHeight: 550,scrollbarWidth:"none" }}>
                    <Table stickyHeader sx={{ minWidth: 700,}} aria-label="customized table">
                    <TableHead>
                        <TableRow>
                            <StyledTableCell align="left">Sr ID</StyledTableCell>
                            <StyledTableCell align="left">Week</StyledTableCell>
                            <StyledTableCell align="left">Activity Name</StyledTableCell>
                            <StyledTableCell align="left">Phase Name</StyledTableCell>
                            <StyledTableCell align="left">Action</StyledTableCell>


                        </TableRow>
                    </TableHead>
                    <TableBody>

                        {checkLastPage?.length > 0 ? (
                            <>
                                {checkLastPage?.map((row, index) => (
                                    <StyledTableRow key={index}>
                                        <StyledTableCell align="left">
                                            {row?.srID}
                                        </StyledTableCell>
                                        <StyledTableCell align="left">
                                            {row?.week}

                                        </StyledTableCell>
                                        <StyledTableCell align="left">
                                            {row?.activity?.activity_name}

                                        </StyledTableCell>
                                        <StyledTableCell align="left">
                                            {row?.phasename?.phase}

                                        </StyledTableCell>
                                        <StyledTableCell align="left">
                                            <div className="flex ">
                                                <span className="px-2 cursor-pointer">
                                                    <IconEdit
                                                        color="blue"
                                                        onClick={() => {
                                                            setShowAssesmentModal(true);
                                                            setEditData(row);
                                                        }}
                                                    />
                                                </span>
                                                <span className="px-2 cursor-pointer">
                                                    <IconTrash
                                                        color="red"
                                                        onClick={() => showDeleteConfirm(row?.id)}
                                                    />
                                                </span>
                                            </div>

                                        </StyledTableCell>
                                    </StyledTableRow>
                                ))}
                            </>
                        ) : (
                            <div className="p-4">No data found</div>
                        )}
                    </TableBody>
                </Table>
                </TableContainer>
                 ):(
                    <TableContainer style={{ maxHeight: 550,scrollbarWidth:"none" }}>
                    <Table stickyHeader sx={{ minWidth: 700,}} aria-label="customized table">
                    <TableHead>
                        <TableRow>
                            <StyledTableCell align="left">Sr ID</StyledTableCell>
                            <StyledTableCell align="left">Phase Name</StyledTableCell>

                            <StyledTableCell align="left">Action</StyledTableCell>


                        </TableRow>
                    </TableHead>
                    <TableBody>

                        {checkLastPage?.length > 0 ? (
                            <>
                                {checkLastPage?.map((row, index) => (
                                    <StyledTableRow key={index}>
                                        <StyledTableCell align="left">
                                            {row?.srID}
                                        </StyledTableCell>
                                       
                                        <StyledTableCell align="left">
                                            {row?.phase}

                                        </StyledTableCell>
                                        <StyledTableCell align="left">
                                            <div className="flex ">
                                                <span className="px-2 cursor-pointer">
                                                    <IconEdit
                                                        color="blue"
                                                        onClick={() => {
                                                            setShowAssesmentModal(true);
                                                            setEditData(row);
                                                        }}
                                                    />
                                                </span>
                                                <span className="px-2 cursor-pointer">
                                                    <IconTrash
                                                        color="red"
                                                        onClick={() => showDeleteConfirm(row?.id)}
                                                    />
                                                </span>
                                            </div>

                                        </StyledTableCell>
                                    </StyledTableRow>
                                ))}
                            </>
                        ) : (
                            <div className="p-4">No data found</div>
                        )}
                    </TableBody>
                </Table>
                </TableContainer>
                 )}
                </TableContainer>
                &nbsp;
                <div className="flex justify-end">
                    <Pagination
                        count={Math.ceil(filteredList?.length / PageSize)} // Calculate total number of pages
                        color="primary"
                        page={currentPage}
                        onChange={handlePageChange}
                    />
                </div>
            </div>
            {
                isOpen?
                <CreatePhaseBloack editData={editData} setEditData={setEditData} fetchReport={fetchReport} showAssesmentModal={showAssesmentModal} setShowAssesmentModal={setShowAssesmentModal} />
                :
                <CreatePhaseName setEditData={setEditData} editData={editData} fetchReport={getAllGoalsDataa} showAssesmentModal={showAssesmentModal} setShowAssesmentModal={setShowAssesmentModal} />

            }
        </div>
    )
}
export default PhaseBlock
