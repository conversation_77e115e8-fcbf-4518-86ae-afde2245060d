import { useState, use<PERSON>emo, useEffect } from "react";
import { Plus, Search, Edit, Trash2 } from "lucide-react";

import { <PERSON><PERSON> } from "../../components/ui/button";
import { Input } from "../../components/ui/input";
import { Card, CardContent, CardHeader } from "../../components/ui/card";
import {
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableHeader,
	TableRow,
} from "../../components/ui/table";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "../../components/ui/select";
import { Badge } from "../../components/ui/badge";
import { PhaseBlockDialog } from "../../components/admin/phase-block-dialog";
import { DeleteConfirmDialog } from "../../components/admin/delete-confirm-dialog";
import Header from "../../components/Header";
import {
	getAlPhaseBlockData,
	getAllPhaseNameData,
	deletePhaseBloackData,
	deletePhaseNameData,
	getAllActivityData,
} from "../../API/api-endpoint";
import Swal from "sweetalert2";

const getPhaseColor = (phase) => {
	switch (phase) {
		case "Base":
			return "bg-blue-100 text-blue-800";
		case "Build":
			return "bg-yellow-100 text-yellow-800";
		case "Peak":
			return "bg-red-100 text-red-800";
		case "General Conditioning":
			return "bg-green-100 text-green-800";
		case "Running Specific":
			return "bg-purple-100 text-purple-800";
		case "Cycling Specific":
			return "bg-indigo-100 text-indigo-800";
		case "Triathlon Specific":
			return "bg-pink-100 text-pink-800";
		default:
			return "bg-gray-100 text-gray-800";
	}
};

export default function PhaseBlocksPage() {
	const [searchTerm, setSearchTerm] = useState("");
	const [selectedActivity, setSelectedActivity] = useState("All");
	const [showPhaseBlocks, setShowPhaseBlocks] = useState(true);
	const [dialogOpen, setDialogOpen] = useState(false);
	const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
	const [editingItem, setEditingItem] = useState(null);
	const [deletingId, setDeletingId] = useState(null);
	const [currentPage, setCurrentPage] = useState(1);
	const [phaseBlocks, setPhaseBlocks] = useState([]);
	const [phaseNames, setPhaseNames] = useState([]);
	const [activities, setActivities] = useState([]);
	const [isLoading, setIsLoading] = useState(true);
	const itemsPerPage = 10;

	const currentData = showPhaseBlocks ? phaseBlocks : phaseNames;

	useEffect(() => {
		fetchData();
		fetchActivities();
	}, []);

	useEffect(() => {
		fetchData();
	}, [showPhaseBlocks]);

	const fetchData = async () => {
		try {
			setIsLoading(true);
			if (showPhaseBlocks) {
				const response = await getAlPhaseBlockData();
				setPhaseBlocks(response || []);
			} else {
				const response = await getAllPhaseNameData();
				setPhaseNames(response || []);
			}
		} catch (error) {
			console.error("Error fetching data:", error);
			if (showPhaseBlocks) {
				setPhaseBlocks([]);
			} else {
				setPhaseNames([]);
			}
		} finally {
			setIsLoading(false);
		}
	};

	const fetchActivities = async () => {
		try {
			const response = await getAllActivityData();
			const activityNames =
				response?.rows?.map(
					(activity) => activity.activity || activity.activity_name
				) || [];
			setActivities(["All", ...activityNames]);
		} catch (error) {
			console.error("Error fetching activities:", error);
			setActivities(["All"]);
		}
	};

	const filteredData = useMemo(() => {
		return currentData.filter((item) => {
			const searchField = showPhaseBlocks
				? item.activity?.activity?.toLowerCase() ||
				  item.activity?.activity_name?.toLowerCase()
				: item.phase?.toLowerCase();

			const matchesSearch =
				searchField?.includes(searchTerm.toLowerCase()) || false;
			const matchesActivity =
				selectedActivity === "All" ||
				(showPhaseBlocks &&
					(item.activity?.activity === selectedActivity ||
						item.activity?.activity_name === selectedActivity));

			return matchesSearch && matchesActivity;
		});
	}, [currentData, searchTerm, selectedActivity, showPhaseBlocks]);

	const paginatedData = useMemo(() => {
		const startIndex = (currentPage - 1) * itemsPerPage;
		const endIndex = startIndex + itemsPerPage;
		return filteredData.slice(startIndex, endIndex);
	}, [filteredData, currentPage]);

	const totalPages = Math.ceil(filteredData.length / itemsPerPage);

	const handleEdit = (item) => {
		setEditingItem(item);
		setDialogOpen(true);
	};

	const handleDelete = (id) => {
		setDeletingId(id);
		setDeleteDialogOpen(true);
	};

	const handleCreate = () => {
		setEditingItem(null);
		setDialogOpen(true);
	};

	const handleDialogSuccess = () => {
		fetchData();
		Swal.fire({
			title: "Success",
			text: "Phase Block saved successfully!",
			icon: "success",
			timer: 1800,
			showConfirmButton: false,
		});
	};

	const confirmDelete = async () => {
		try {
			if (showPhaseBlocks) {
				await deletePhaseBloackData(deletingId);
			} else {
				await deletePhaseNameData(deletingId);
			}
			fetchData();
		} catch (error) {
			console.error("Error deleting item:", error);
		} finally {
			setDeleteDialogOpen(false);
			setDeletingId(null);
		}
	};

	return (
		<div>
			<Header />
			<div className='max-w-[1300px] mx-auto py-6 space-y-6 mt-16'>
				<div className='flex items-center justify-between'>
					<h1 className='text-3xl font-bold text-orange-950'>
						Phase Blocks Management
					</h1>
				</div>

				<Card>
					<CardHeader className='bg-orange-50 border-b'>
						<div className='flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between'>
							<div className='flex gap-2'>
								<Button
									onClick={handleCreate}
									className='bg-orange-500 hover:bg-orange-600 text-white'
								>
									<Plus className='h-4 w-4 mr-2' />
									{showPhaseBlocks
										? "Create Phase Block"
										: "Create Phase Name"}
								</Button>
								<Button
									variant='outline'
									onClick={() =>
										setShowPhaseBlocks(!showPhaseBlocks)
									}
									className='border-orange-200 text-orange-700 hover:bg-orange-50 bg-white'
								>
									{showPhaseBlocks
										? "Get Phase Names"
										: "Get Phase Blocks"}
								</Button>
							</div>

							<div className='flex gap-2 w-full sm:w-auto'>
								{showPhaseBlocks && (
									<Select
										value={selectedActivity}
										onValueChange={setSelectedActivity}
									>
										<SelectTrigger className='w-[150px]'>
											<SelectValue placeholder='Filter by Activity' />
										</SelectTrigger>
										<SelectContent className='bg-white'>
											{activities.map((activity) => (
												<SelectItem
													key={activity}
													value={activity}
												>
													{activity}
												</SelectItem>
											))}
										</SelectContent>
									</Select>
								)}

								<div className='relative'>
									<Search className='absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4' />
									<Input
										placeholder={`Search by ${
											showPhaseBlocks
												? "activity"
												: "phase name"
										}...`}
										value={searchTerm}
										onChange={(e) =>
											setSearchTerm(e.target.value)
										}
										className='pl-10 w-[250px] text-sm'
									/>
								</div>
							</div>
						</div>
					</CardHeader>

					<CardContent className='p-0'>
						<div className='overflow-x-auto'>
							<Table>
								<TableHeader>
									<TableRow className='bg-orange-500 hover:bg-orange-500'>
										<TableHead className='text-white font-semibold'>
											Sr ID
										</TableHead>
										{showPhaseBlocks && (
											<>
												<TableHead className='text-white font-semibold'>
													Week
												</TableHead>
												<TableHead className='text-white font-semibold'>
													Activity Name
												</TableHead>
											</>
										)}
										<TableHead className='text-white font-semibold'>
											Phase Name
										</TableHead>
										<TableHead className='text-white font-semibold'>
											Action
										</TableHead>
									</TableRow>
								</TableHeader>
								<TableBody>
									{isLoading ? (
										<TableRow>
											<TableCell
												colSpan={
													showPhaseBlocks ? 5 : 3
												}
												className='text-center py-8 text-gray-500'
											>
												Loading...
											</TableCell>
										</TableRow>
									) : paginatedData.length > 0 ? (
										paginatedData.map((item, index) => (
											<TableRow
												key={item.id}
												className='hover:bg-orange-50/50'
											>
												<TableCell className='font-medium'>
													{(currentPage - 1) *
														itemsPerPage +
														index +
														1}
												</TableCell>
												{showPhaseBlocks && (
													<>
														<TableCell>
															{item.week}
														</TableCell>
														<TableCell>
															<Badge
																variant='outline'
																className='border-orange-200 text-orange-700'
															>
																{item.activity
																	?.activity ||
																	item
																		.activity
																		?.activity_name ||
																	"N/A"}
															</Badge>
														</TableCell>
													</>
												)}
												<TableCell>
													<Badge
														className={getPhaseColor(
															showPhaseBlocks
																? item.phasename
																		?.phase ||
																		item.phase
																: item.phase
														)}
													>
														{showPhaseBlocks
															? item.phasename
																	?.phase ||
															  item.phase
															: item.phase}
													</Badge>
												</TableCell>
												<TableCell>
													<div className='flex gap-2'>
														<Button
															variant='ghost'
															size='sm'
															onClick={() =>
																handleEdit(item)
															}
															className='text-blue-600 hover:text-blue-800 hover:bg-blue-50'
														>
															<Edit className='h-4 w-4' />
														</Button>
														<Button
															variant='ghost'
															size='sm'
															onClick={() =>
																handleDelete(
																	item.id
																)
															}
															className='text-red-600 hover:text-red-800 hover:bg-red-50'
														>
															<Trash2 className='h-4 w-4' />
														</Button>
													</div>
												</TableCell>
											</TableRow>
										))
									) : (
										<TableRow>
											<TableCell
												colSpan={
													showPhaseBlocks ? 5 : 3
												}
												className='text-center py-8 text-gray-500'
											>
												No data found
											</TableCell>
										</TableRow>
									)}
								</TableBody>
							</Table>

							{/* Pagination Controls */}
							{totalPages > 1 && (
								<div className='flex justify-center gap-2 p-4 border-t'>
									<Button
										variant='outline'
										size='sm'
										onClick={() =>
											setCurrentPage(
												Math.max(1, currentPage - 1)
											)
										}
										disabled={currentPage === 1}
									>
										Previous
									</Button>

									{Array.from({ length: 5 }, (_, i) => {
										const startPage =
											Math.floor((currentPage - 1) / 5) *
												5 +
											1;
										const page = startPage + i;
										if (page > totalPages) return null;

										return (
											<Button
												key={page}
												variant={
													currentPage === page
														? "default"
														: "outline"
												}
												size='sm'
												onClick={() =>
													setCurrentPage(page)
												}
												className={
													currentPage === page
														? "bg-orange-500 hover:bg-orange-600 text-white"
														: ""
												}
											>
												{page}
											</Button>
										);
									})}

									<Button
										variant='outline'
										size='sm'
										onClick={() =>
											setCurrentPage(
												Math.min(
													totalPages,
													currentPage + 1
												)
											)
										}
										disabled={currentPage === totalPages}
									>
										Next
									</Button>
								</div>
							)}
						</div>
					</CardContent>
				</Card>

				<PhaseBlockDialog
					open={dialogOpen}
					onOpenChange={setDialogOpen}
					editingItem={editingItem}
					isPhaseBlock={showPhaseBlocks}
					onSuccess={handleDialogSuccess}
				/>

				<DeleteConfirmDialog
					open={deleteDialogOpen}
					onOpenChange={setDeleteDialogOpen}
					onConfirm={confirmDelete}
					title={`Are you sure you want to delete this ${
						showPhaseBlocks ? "phase block" : "phase name"
					}?`}
					description='This action cannot be undone.'
				/>
			</div>
		</div>
	);
}
