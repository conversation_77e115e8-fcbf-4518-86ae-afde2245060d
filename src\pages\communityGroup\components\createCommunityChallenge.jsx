import { <PERSON><PERSON>, <PERSON><PERSON> } from "antd";
import {
  FormLabel,
  Grid,
  MenuItem,
  OutlinedInput,
  TextField
} from "@mui/material";
import DatePicker from "react-datepicker";
import moment from "moment";
import RichTextEditor from "../../../../src/components/EditorFile";
import SlickCarousel from "../.././SlickCarousel";
import { Editor } from 'react-draft-wysiwyg';
import draftToHtml from 'draftjs-to-html';
import { convertToRaw } from 'draft-js';

const communityChallenge = ({handleCancel, editorState, setEditorState, isLoading, initialContentSet, setInitialContentSet, challengeForOptions, communityGroups, roleID, allChallengesData, isSavedStartDatePast, isModalOpen, setIsModalOpen, setChallengeData, initialChallengeData, challengeData, handleSubmit, handleInputChange, fetchAllActivities}) => {
    return(
        <Modal
            width={1200}
            open={isModalOpen}
            onCancel={() => {
              handleCancel();
              setChallengeData(initialChallengeData);
            }}
            footer={null}
          >
            <div className="headingCont paddingBot">
              <span className="heading">{challengeData.id ? "Edit " : "Create"}</span>
              <span className="orange heading"> Challenge</span>
              <p className="grey">Set up a new fitness challenge for Community Users</p>
            </div>
            <div className="parentCont">
              <form className="form1" onSubmit={handleSubmit}>
                <Grid container spacing={1}>
                  <Grid container spacing={3} className="marbot">
                    <Grid item xs={12} sm={5.5}>
                      <FormLabel>
                        Name<span className="text-[red]">*</span>
                      </FormLabel>
                      <TextField
                        fullWidth
                        placeholder="Challenge Name"
                        size="small"
                        type="text"
                        name="challengeName"
                        value={challengeData.challengeName}
                        onChange={handleInputChange}
                      />
                    </Grid>
                    <Grid item xs={12} sm={5.5}>
                      <FormLabel>
                        Activity<span className="text-[red]">*</span>
                      </FormLabel>
                      <TextField
                        fullWidth
                        size="small"
                        select
                        name="challengeActivity"
                        value={challengeData.challengeActivity}
                        onChange={(e) => {
                          const value = parseInt(e.target.value);
                          setChallengeData((prev) => ({
                            ...prev,
                            challengeActivity: value,
                          }));
                        }}
                        input={<OutlinedInput />}
                      >
                        <MenuItem value="" disabled>
                          Select Activity
                        </MenuItem>
                        {fetchAllActivities?.map((act) => (
                          <MenuItem key={act.id} value={act.id}>
                            {act.activity_name}
                          </MenuItem>
                        ))}
                      </TextField>
                    </Grid>
                  </Grid>

                  <Grid container spacing={3} className="marbot">
                    <Grid item xs={12} sm={5.5}>
                      <FormLabel>
                        Duration<span className="text-[red]">*</span>
                      </FormLabel>
                      <TextField
                        fullWidth
                        placeholder="Challenge Duration"
                        size="small"
                        type="number"
                        name="challengeDuration"
                        value={challengeData.challengeDuration}
                        onChange={handleInputChange}
                      />
                    </Grid>
                    <Grid item xs={12} sm={5.5}>
                      <FormLabel>Duration Unit:</FormLabel>
                      <TextField
                        fullWidth
                        size="small"
                        select
                        name="durationunit"
                        value={challengeData.durationunit}
                        onChange={handleInputChange}
                        input={<OutlinedInput />}
                      >
                        <MenuItem value="" disabled>
                          Select
                        </MenuItem>
                        <MenuItem value="days">Day</MenuItem>
                        <MenuItem value="weeks">Week</MenuItem>
                        <MenuItem value="months">Month</MenuItem>
                        <MenuItem value="years">Year</MenuItem>
                      </TextField>
                    </Grid>
                  </Grid>

                  <Grid container spacing={3} className="marbot dateWrap">
                    <Grid item xs={12} sm={5.5}>
                      <FormLabel>
                        Start Date<span className="text-[red]">*</span>
                      </FormLabel>
                      <DatePicker
                        selected={
                          challengeData.challengeStartDate
                            ? new Date(challengeData.challengeStartDate)
                            : null
                        }
                        onChange={(date) =>
                          setChallengeData((prev) => ({
                            ...prev,
                            challengeStartDate: moment(date).format("YYYY-MM-DD"),
                          }))
                        }
                        dateFormat="dd-MM-yyyy"
                        placeholderText="DD-MM-YYYY"
                        disabled={isSavedStartDatePast}
                        customInput={
                          <TextField
                            fullWidth
                            size="small"
                            placeholder="dd-mm-yyyy"
                            name="challengeStartDate"
                          />
                        }
                      />
                    </Grid>
                    <Grid item xs={12} sm={5.5}>
                      <FormLabel>
                        End Date<span className="text-[red]">*</span>
                      </FormLabel>
                      <DatePicker
                        selected={
                          challengeData.challengeEndDate
                            ? new Date(challengeData.challengeEndDate)
                            : null
                        }
                        onChange={(date) =>
                          setChallengeData((prev) => ({
                            ...prev,
                            challengeEndDate: moment(date).format("YYYY-MM-DD"),
                          }))
                        }
                        dateFormat="dd-MM-yyyy"
                        placeholderText="DD-MM-YYYY"
                        disabled={isSavedStartDatePast}
                        customInput={
                          <TextField
                            fullWidth
                            size="small"
                            placeholder="dd-mm-yyyy"
                            name="challengeEndDate"
                          />
                        }
                      />
                    </Grid>
                  </Grid>

                  <Grid container spacing={3} className="marbot">
                    <Grid item xs={12} sm={5.5}>
                      <FormLabel>
                        Level<span className="text-[red]">*</span>
                      </FormLabel>
                      <TextField
                        fullWidth
                        size="small"
                        select
                        name="challengeLevel"
                        value={challengeData.challengeLevel}
                        onChange={handleInputChange}
                        input={<OutlinedInput />}
                      >
                        <MenuItem value="" disabled>
                          Select Level
                        </MenuItem>
                        {allChallengesData?.levels?.map((level) => (
                          <MenuItem key={level.id} value={level.id}>
                            {level.level}
                          </MenuItem>
                        ))}
                      </TextField>
                    </Grid>
                    <Grid item xs={12} sm={5.5}>
                      <FormLabel>
                        Points<span className="text-[red]">*</span>
                      </FormLabel>
                      <TextField
                        fullWidth
                        placeholder="Challenge Points"
                        size="small"
                        type="number"
                        name="challengePoints"
                        value={challengeData.challengePoints}
                        onChange={handleInputChange}
                      />
                    </Grid>
                  </Grid>

                  <Grid container spacing={3} className="marbot">
                    {roleID === "3" && (
                      <Grid item xs={12} sm={5.5}>
                        <FormLabel>
                          Community Group<span className="text-[red]">*</span>
                        </FormLabel>
                        <TextField
                          fullWidth
                          size="small"
                          select
                          name="athleteCommunityGroupId"
                          value={challengeData.athleteCommunityGroupId}
                          onChange={handleInputChange}
                          input={<OutlinedInput />}
                        >
                          <MenuItem value="" disabled>
                            Select Community Group
                          </MenuItem>
                          {communityGroups?.map((group) => (
                            <MenuItem key={group.id} value={group.id}>
                              {group.communityName}
                            </MenuItem>
                          ))}
                        </TextField>
                      </Grid>
                    )}
                    <Grid item xs={12} sm={5.5}>
                      <FormLabel>
                        Challenge For<span className="text-[red]">*</span>
                      </FormLabel>
                      <TextField
                        fullWidth
                        size="small"
                        select
                        name="challengeFor"
                        value={challengeData.challengeFor}
                        onChange={handleInputChange}
                        input={<OutlinedInput />}
                      >
                        <MenuItem value="" disabled>
                          Select Challenge For
                        </MenuItem>
                        {challengeForOptions?.map((target) => (
                          <MenuItem key={target.id} value={target.id}>
                            {target.name}
                          </MenuItem>
                        ))}
                      </TextField>
                    </Grid>
                  </Grid>

                  <Grid container spacing={3} className="marbot">
                    <Grid item xs={12} sm={11}>
                      <FormLabel>
                        Description<span className="text-[red]">*</span>
                      </FormLabel>
                      {/* <RichTextEditor
                        setInitial={setInitialContentSet}
                        initial={initialContentSet}
                        initialContent={challengeData.challengeDescription}
                        onContentChange={(newContent) =>
                          setChallengeData((prev) => ({
                            ...prev,
                            challengeDescription: newContent,
                          }))
                        }
                        placeholder="Type your content here..."
                      /> */}
                      <Editor
                        editorState={editorState}
                        onEditorStateChange={(st) => {
                          setEditorState(st);
                          setChallengeData((prevData) => ({
                            ...prevData,
                            challengeDescription: draftToHtml(convertToRaw(st.getCurrentContent())),
                          }));
                        }}
                      />


                    {/* NEW: bind description into a real form control */}
                    <input
                      type="hidden"
                      name="description"
                      value={challengeData.challengeDescription}
                      required
                    />
                    </Grid>
                  </Grid>

                  <Grid container spacing={3} className="marbot">
                    {/* <Grid item xs={12} sm={5.5}>
                      <FormLabel>Target:</FormLabel>
                      {fetchChallengeTarget?.map((activity) => (
                        <React.Fragment key={activity.id}>
                          <FormControlLabel
                            control={
                              <Checkbox
                                checked={challengeData.challangeTarget.some(
                                  (item) => item.activity_id === activity.id
                                )}
                                onChange={(e) => {
                                  const { checked } = e.target;
                                  let updatedChallengeTarget;
                                  if (checked) {
                                    updatedChallengeTarget = [
                                      ...challengeData.challangeTarget,
                                      { activity_id: activity.id, quota: "" },
                                    ];
                                    if (activity.id === 6) {
                                      const triathlonActivities = [1, 2, 3];
                                      updatedChallengeTarget = [
                                        ...updatedChallengeTarget,
                                        ...triathlonActivities
                                          .filter(
                                            (id) =>
                                              !updatedChallengeTarget.some(
                                                (item) => item.activity_id === id
                                              )
                                          )
                                          .map((id) => ({ activity_id: id, quota: "" })),
                                      ];
                                    } else if (activity.id === 5) {
                                      const duoActivities = [1, 2];
                                      updatedChallengeTarget = [
                                        ...updatedChallengeTarget,
                                        ...duoActivities
                                          .filter(
                                            (id) =>
                                              !updatedChallengeTarget.some(
                                                (item) => item.activity_id === id
                                              )
                                          )
                                          .map((id) => ({ activity_id: id, quota: "" })),
                                      ];
                                    }
                                  } else {
                                    updatedChallengeTarget = challengeData.challangeTarget.filter(
                                      (item) => item.activity_id !== activity.id
                                    );
                                  }
                                  setChallengeData((prev) => ({
                                    ...prev,
                                    challangeTarget: updatedChallengeTarget,
                                  }));
                                }}
                              />
                            }
                            label={activity.activity_name}
                          />
                          {challengeData.challangeTarget.some(
                            (item) => item.activity_id === activity.id
                          ) && (
                            <div className="flex">
                              <TextField
                                sx={{ maxWidth: "150px" }}
                                className="target-distance"
                                placeholder="Distance"
                                size="small"
                                type="number"
                                value={
                                  challengeData.challangeTarget.find(
                                    (item) => item.activity_id === activity.id
                                  )?.quota || ""
                                }
                                onChange={(e) => {
                                  const updatedChallengeTarget = challengeData.challangeTarget.map(
                                    (item) =>
                                      item.activity_id === activity.id
                                        ? { ...item, quota: e.target.value }
                                        : item
                                  );
                                  setChallengeData((prev) => ({
                                    ...prev,
                                    challangeTarget: updatedChallengeTarget,
                                  }));
                                }}
                              />
                              <TextField
                                sx={{ minWidth: "150px", marginLeft: "5px" }}
                                size="small"
                                select
                                name="unit"
                                className="target-duration"
                                value={
                                  challengeData.challangeTarget.find(
                                    (item) => item.activity_id === activity.id
                                  )?.unit || ""
                                }
                                onChange={(e) => {
                                  const updatedChallengeTarget = challengeData.challangeTarget.map(
                                    (item) =>
                                      item.activity_id === activity.id
                                        ? { ...item, unit: e.target.value }
                                        : item
                                  );
                                  setChallengeData((prev) => ({
                                    ...prev,
                                    challangeTarget: updatedChallengeTarget,
                                  }));
                                }}
                                input={<OutlinedInput />}
                              >
                                <MenuItem value="" disabled>
                                  Select
                                </MenuItem>
                                <MenuItem value="km">Km</MenuItem>
                                <MenuItem value="mtr">Mtr</MenuItem>
                              </TextField>
                            </div>
                          )}
                        </React.Fragment>
                      ))}
                    </Grid> */}
                    <Grid item xs={12} sm={5.5} className="spcl">
                      <Button className="btn" htmlType="submit" loading={isLoading}>
                      <span style={{ color: '#fff' }}>Submit</span>
                      </Button>
                    </Grid>
                  </Grid>
                </Grid>
              </form>
              <div className="slick-container">
                <SlickCarousel />
              </div>
            </div>
          </Modal>
    )
}

export default communityChallenge;