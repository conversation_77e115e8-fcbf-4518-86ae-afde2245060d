import {
    Chip,
    FormControl,
    FormLabel,
    Grid,
    MenuItem,
    OutlinedInput,
    Select,
    TextField,
  } from "@mui/material";
  import { Button, Modal } from "antd";
  import React, { useEffect, useState } from "react";
  import {
    CreateReviewSectionOption,
    getAllReviewSection,
    updateRevieSectionOption,
  } from "../../API/api-endpoint";
  import { useFormik } from "formik";
  import Swal from "sweetalert2";
  import SlickCarousel from "../../pages/SlickCarousel";
  const scoreData = [1, 2, 3, 4, 5];
  const CreateFormReviewOption = ({
    fetchReport,
    setShowAssesmentModal,
    showAssesmentModal,
    editData,
    setEditData,
  }) => {
    const [formReviewSection, setFormReviewSection] = useState([]);
    console.log("editData", editData);
    const formik = useFormik({
      initialValues: {
        option: "",
        section_id:"",
        weightage:undefined
      },
      validate: (values) => {
        const errors = {};
      
        if (!values.section_id) {
          errors.section_id = "Section  name is required";
        }
        if (!values.option) {
            errors.option = "Option is required";
          }  if (values.weightage === '' || values.weightage === null || values.weightage === undefined) {
            errors.weightage = "Weightage is required";
          }
        return errors;
      },
      // validationSchema: {},
      onSubmit: (values, { resetForm }) => {
        handleSubmitAssesmentForm(values, resetForm);
      },
    });
    const getAllProgramsData = async () => {
      const response = await getAllReviewSection();
      console.log("response", response);
      setFormReviewSection(response?.data);
    };
    useEffect(() => {
      getAllProgramsData();
    }, []);
  
    const handleSubmitAssesmentForm = async (data, resetForm) => {
      let response = "";
      if (editData?.id) {
        response = await updateRevieSectionOption(data);
      } else {
        response = await CreateReviewSectionOption(data);
      }
  
      if (response?.status) {
        Swal.fire({
          title: "Success",
          text: response.message,
          icon: "success",
        });
        setShowAssesmentModal(false);
        setEditData({});
        fetchReport();
        resetForm();
        formik?.setValues({
          section_id: "",
          option: "",
          weightage:undefined
          
        });
      } else {
        Swal.fire({
          title: "Error",
          text: response.message,
          icon: "error",
        });
      }
      console.log("response", response);
    };
    useEffect(() => {
      if (editData?.id) {
        const { srID, ...data } = editData;
        console.log("data", data);
        formik?.setValues(data);
      } else {
        setEditData({});
      }
    }, [editData?.id]);
    return (
      <Modal
        width={1200}
        open={showAssesmentModal}
        onCancel={() => {
          setShowAssesmentModal(false);
          setEditData({});
          formik.resetForm();
          formik?.setValues({
            section_id: "",
            option: "",
            weightage:undefined
            
          });
        }}
        footer={
          <div></div>
          //   loading={isLoading}
        }
      >
        <div className="headingCont">
        <span className="heading">{editData?.id ? "Edit " : "Create"}</span>{" "}
          <span className="orange heading">Form Review Section Option</span>
        </div>
        {/* <h1>{editData ? editData.challengeId : values.challengeId}</h1> */}
        <div className="parentCont">
          <form className="form1" onSubmit={formik.handleSubmit}>
            <Grid container spacing={2}>
              <Grid item xs={12} sm={11}>
                <FormLabel>Section Name<span className="text-[red]">*</span></FormLabel>
  
                <TextField
                  fullWidth
                  size="small"
                  select
                  name="section_id"
                  SelectProps={{
                    MenuProps: {
                      PaperProps: {
                        style: {
                           scrollbarColor:"#E67E22 white",
                           scrollbarWidth:"thin"
                        },
                      },
                    },
                  }}
                  value={formik?.values?.section_id}
                  onChange={formik.handleChange}
                  error={formik.touched.section_id && formik.errors.section_id}
                  helperText={
                    formik.touched.section_id && formik.errors.section_id
                  }
                  id="form-layouts-separator-select"
                  labelId="form-layouts-separator-select-label"
                  input={<OutlinedInput id="select-multiple-language" />}
                >
                  <MenuItem value={""} disabled>
                    Select Activity
                  </MenuItem>
                  {formReviewSection?.map((value, index) => {
                    return (
                      <MenuItem value={value?.id}>
                        {value?.section}
                      </MenuItem>
                    );
                  })}
                </TextField>
              </Grid>
              <Grid item xs={12} sm={11}>
                <FormLabel>Option<span className="text-[red]">*</span></FormLabel>
  
                <TextField
                  fullWidth
                  placeholder="Option"
                  size="small"
                  type="text"
                  name="option"
                  value={formik?.values?.option}
                  onChange={formik.handleChange}
                  error={
                    formik.touched.option && formik.errors.option
                  }
                  helperText={
                    formik.touched.option && formik.errors.option
                  }
                />
              </Grid>
              <Grid item xs={12} sm={11}>
                <FormLabel>Weightage<span className="text-[red]">*</span></FormLabel>
  
                <TextField
                  fullWidth
                  placeholder="Weightage"
                  size="small"
                  type="number"
                  name="weightage"
                  value={formik?.values?.weightage}
                  onChange={(e)=>formik?.setFieldValue("weightage",e.target.value?parseFloat(e.target.value):"")}
                  error={
                    formik.touched.weightage && formik.errors.weightage
                  }
                  helperText={
                    formik.touched.weightage && formik.errors.weightage
                  }
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <Button
                  className="btn"
                  key="submit"
                  type="primary"
                  onClick={() => formik.handleSubmit()}
                >
                  Submit
                </Button>
              </Grid>
            </Grid>
          </form>
          <div className="slick-container">
            <SlickCarousel />
          </div>
        </div>
      </Modal>
    );
  };
export default CreateFormReviewOption
