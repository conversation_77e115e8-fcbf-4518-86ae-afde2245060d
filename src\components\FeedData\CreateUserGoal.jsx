import {
    Chip,
    FormControl,
    FormLabel,
    Grid,
    MenuItem,
    OutlinedInput,
    Select,
    TextField,
  } from "@mui/material";
  import { Button, Modal } from "antd";
  import React, { useEffect, useState } from "react";
  import {
    CreatePrograms,
    CreateZonesClasification,
    createGoalNameYTA,
    createGoalsdata,
    createUserGoaldata,
    getAllActivityData,
    getAllGoalNameForYTA,
    getAllPhaseNameData,
    getAllPrograms,
    getAllUserAdminGoalData,
    getAlllevels,
    updateGoalNameYTA,
    updateGoalsdata,
    updatePrograms,
    updateUserGoaldata,
    updateZonesClasification,
    weeklyFeedDataPattern,
    weeklyFeedDataProgram,
  } from "../../API/api-endpoint";
  import { useFormik } from "formik";
  import Swal from "sweetalert2";
  import SlickCarousel from "../../pages/SlickCarousel";
import { capitalizeFirstLetter } from "../../utils/Resubale";
  const scoreData = [1, 2, 3, 4, 5];
  const CreateUserGoal = ({
    fetchReport,
    setShowAssesmentModal,
    showAssesmentModal,
    editData,
    setEditData,
  }) => {
    const [programList, setProgramList] = useState([]);
    const [ytaGoalList, setYtaGoalList] = useState([]);
  
    console.log("editData", editData);
    const formik = useFormik({
      initialValues: {
        goal: "",
        comments: "",
        start_date: "",
        end_date: "",
        is_current: "",
      },
      validate: (values) => {
        const errors = {};
  
  
        // Validation for goal
        if (values.goal === "") {
          errors.goal = "Goal name is required";
        }
  
        // Validation for comments
        if (values.comments === "") {
          errors.comments = "Comment is required";
        }
  
        // Validation for start_date
        if (values.start_date === "") {
          errors.start_date = "Start date required";
        }
  
        // Validation for end_date
        if (values.end_date === "") {
          errors.end_date = "End date is required";
        }
  
        // Validation for is_current
        if (values.is_current === "") {
          errors.is_current = "Status is required";
        }
  
        return errors;
      },
      onSubmit: (values, { resetForm }) => {
        handleSubmitAssesmentForm(values, resetForm);
      },
    });
    const getPhaseName = async () => {
      const response = await getAllUserAdminGoalData();
      console.log("response", response);
      setYtaGoalList(response);
    };
    useEffect(() => {
      getPhaseName();
    }, []);
  
    const handleSubmitAssesmentForm = async (data, resetForm) => {
      let response = "";
      if (editData?.id) {
        response = await updateUserGoaldata(data);
      } else {
        response = await createUserGoaldata(data);
        console.log("response", response);
      }
      if (response?.status) {
        Swal.fire({
          title: "Success",
          text: capitalizeFirstLetter(response.message),
          icon: "success",
        });
        setShowAssesmentModal(false);
        setEditData({});
        fetchReport();
        resetForm();
  
        formik?.setValues({
          goal: "",
          comments: "",
          start_date: "",
          end_date: "",
          is_current: "",
        });
      } else {
        Swal.fire({
          title: "Error",
          text: response.message,
          icon: "error",
        });
      }
      console.log("response", response);
    };
    useEffect(() => {
      if (editData?.id) {
        const { srID, activity, phasename, ...data } = editData;
        console.log("data", data);
        formik?.setValues(data);
        formik.setFieldValue("phase", phasename?.phase);
        formik.setFieldValue("activities", data?.activities);
      }
    }, [editData?.id]);
    return (
      <Modal
        width={1200}
        open={showAssesmentModal}
        onCancel={() => {
          setShowAssesmentModal(false);
          formik.resetForm();
          setEditData({});
          formik?.setValues({
            goal: "",
            comments: "",
            start_date: "",
            end_date: "",
            is_current: "",
          });
          // formik?.setValues({activities:"",program_name:"",activity_ids:""})
        }}
        footer={
          <div></div>
          //   loading={isLoading}
        }
      >
        <div className="headingCont">
          <span className="heading">{editData?.id ? "Edit " : "Create"}</span>{" "}
          <span className="orange heading">User Goal</span>
        </div>
        {/* <h1>{editData ? editData.challengeId : values.challengeId}</h1> */}
        <div className="parentCont">
          <form className="form1" onSubmit={formik.handleSubmit}>
            <Grid container spacing={2}>
              <Grid item xs={12} sm={11}>
                <FormLabel>Goal Name<span className="text-[red]">*</span></FormLabel>
  
                <TextField
                  fullWidth
                  size="small"
                  select
                  SelectProps={{
                    MenuProps: {
                      PaperProps: {
                        style: {
                           scrollbarColor:"#E67E22 white",
                           scrollbarWidth:"thin"
                        },
                      },
                    },
                  }}
                  name="goal"
                  value={
                    formik?.values?.goal ? formik?.values?.goal : ""
                  }
                  onChange={formik.handleChange}
                  error={formik.touched.goal && formik.errors.goal}
                  helperText={
                    formik.touched.goal && formik.errors.goal
                  }
                  id="form-layouts-separator-select"
                  labelId="form-layouts-separator-select-label"
                  input={<OutlinedInput id="select-multiple-language" />}
                >
                  <MenuItem value={""} disabled>
                    Select Name
                  </MenuItem>
                  {ytaGoalList?.map((value, index) => {
                    return (
                      <MenuItem value={value?.goal}>{value?.goal}</MenuItem>
                    );
                  })}
                </TextField>
              </Grid>
              <Grid item xs={12} sm={11}>
              <FormLabel>Comments<span className="text-[red]">*</span></FormLabel>

              <TextField
                fullWidth
                placeholder="Comments"
                size="small"
                type="text"
                name="comments"
                error={formik.touched.comments && formik.errors.comments}
                helperText={formik.touched.comments && formik.errors.comments}
                value={formik?.values?.comments}
               onChange={formik.handleChange}
              />
            </Grid>
              <Grid item xs={12} sm={11}>
                      <FormLabel>Start Date<span className="text-[red]">*</span></FormLabel>

                      <TextField
                        fullWidth
                        placeholder="Start Date"
                        size="small"
                        type="date"
                        name="start_date"
                        value={formik?.values?.start_date}
                        error={formik.touched.start_date && formik.errors.start_date}
                        helperText={formik.touched.start_date && formik.errors.start_date}
                        inputProps={{
                          max: formik.values.end_date, // Assuming start_date is a valid date string
                        }}
                        onChange={formik.handleChange}
                      />
                    </Grid>
                    <Grid item xs={12} sm={11}>
                    <FormLabel>End Date<span className="text-[red]">*</span></FormLabel>

                    <TextField
                      fullWidth
                      placeholder="End Date"
                      size="small"
                      type="date"
                      name="end_date"
                      error={formik.touched.end_date && formik.errors.end_date}
                      helperText={formik.touched.end_date && formik.errors.end_date}
                      inputProps={{
                        min: formik.values.start_date, // Assuming start_date is a valid date string
                      }}
                      value={formik?.values?.end_date}
                     onChange={formik.handleChange}
                    />
                  </Grid>
                  <Grid item xs={12} sm={11}>
                  <FormLabel>Status<span className="text-[red]">*</span></FormLabel>
    
                  <TextField
                    fullWidth
                    size="small"
                    select
                    name="is_current"
                    value={formik?.values?.is_current}
                    onChange={formik.handleChange}
                    error={formik.touched.is_current && formik.errors.is_current}
                    helperText={formik.touched.is_current && formik.errors.is_current}
                    id="form-layouts-separator-select"
                    labelId="form-layouts-separator-select-label"
                    input={<OutlinedInput id="select-multiple-language" />}
                  >
                    <MenuItem value={true}>Yes</MenuItem>
                    <MenuItem value={"false"}>No</MenuItem>
                  </TextField>
                </Grid>
              <Grid item xs={12} sm={6}>
                <Button
                  className="btn"
                  key="submit"
                  type="primary"
                  onClick={() => formik.handleSubmit()}
                >
                  Submit
                </Button>
              </Grid>
            </Grid>
          </form>
          <div className="slick-container">
            <SlickCarousel />
          </div>
        </div>
      </Modal>
    );
  };
  
export default CreateUserGoal
