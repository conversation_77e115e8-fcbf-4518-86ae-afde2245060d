import React, { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "../../components/ui/button";
import {
	<PERSON><PERSON>,
	DialogContent,
	DialogHeader,
	DialogTitle,
} from "../../components/ui/dialog";
import { Input } from "../../components/ui/input";
import { Label } from "../../components/ui/label";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "../../components/ui/select";
import { Textarea } from "../../components/ui/textarea";
import {
	getAllActivityData,
	createsubactivitydata,
	updatesubactivitydata,
} from "../../API/api-endpoint";

export function SubWorkoutDialog({
	open,
	onOpenChange,
	editingItem,
	onSuccess,
}) {
	const [formData, setFormData] = useState({
		subworkout: "",
		activity_id: "",
		description: "",
	});
	const [activities, setActivities] = useState([]);
	const [isLoading, setIsLoading] = useState(false);
	const [isSubmitting, setIsSubmitting] = useState(false);

	useEffect(() => {
		if (open) {
			fetchActivities();
		}
	}, [open]);

	useEffect(() => {
		if (editingItem) {
			setFormData({
				subworkout: editingItem.subworkout || "",
				activity_id: editingItem.activity_id || "",
				description: editingItem.description || "",
			});
		} else {
			setFormData({
				subworkout: "",
				activity_id: "",
				description: "",
			});
		}
	}, [editingItem, open]);

	const fetchActivities = async () => {
		try {
			setIsLoading(true);
			const response = await getAllActivityData();
			setActivities(response?.rows || []);
		} catch (error) {
			console.error("Error fetching activities:", error);
			setActivities([]);
		} finally {
			setIsLoading(false);
		}
	};

	const handleSubmit = async (e) => {
		e.preventDefault();
		try {
			setIsSubmitting(true);
			let response;
			if (editingItem?.id) {
				const updateData = {
					...formData,
					id: editingItem.id,
				};
				response = await updatesubactivitydata(updateData);
			} else {
				response = await createsubactivitydata(formData);
			}

			if (response?.status) {
				onOpenChange(false);
				if (onSuccess) onSuccess();
			} else {
				console.log(response?.message || "Operation failed");
			}
		} catch (error) {
			console.error("Error submitting form:", error);
		} finally {
			setIsSubmitting(false);
		}
	};

	return (
		<Dialog open={open} onOpenChange={onOpenChange}>
			<DialogContent className='sm:max-w-[425px] bg-white'>
				<DialogHeader>
					<DialogTitle className='text-orange-950'>
						{editingItem ? "Edit" : "Create"} Sub-Workout
					</DialogTitle>
				</DialogHeader>

				<form onSubmit={handleSubmit} className='space-y-4'>
					<div className='space-y-2'>
						<Label htmlFor='subworkout'>Workout Name</Label>
						<Input
							id='subworkout'
							value={formData.subworkout}
							onChange={(e) =>
								setFormData({
									...formData,
									subworkout: e.target.value,
								})
							}
							className='text-sm w-full'
							placeholder='Enter workout name'
							required
						/>
					</div>

					<div className='space-y-2'>
						<Label htmlFor='activity'>Category</Label>
						<Select
							value={
								formData.activity_id
									? String(formData.activity_id)
									: ""
							}
							onValueChange={(value) =>
								setFormData({ ...formData, activity_id: value })
							}
							disabled={isLoading}
						>
							<SelectTrigger className='bg-white border border-gray-300 text-gray-900'>
								<SelectValue
									placeholder={
										isLoading
											? "Loading activities..."
											: activities.length > 0
											? "Select activity"
											: "No activities available"
									}
									className='text-gray-900'
								/>
							</SelectTrigger>
							<SelectContent className='bg-white border border-gray-200 shadow-lg '>
								{activities.length > 0 ? (
									activities.map((activity) => {
										return (
											<SelectItem
												key={activity.id}
												value={String(activity.id)}
												className='text-gray-900 hover:bg-blue-50 hover:text-blue-900 cursor-pointer pr-3 py-2 pl-8 relative'
											>
												{activity.activity ||
													activity.activity_name}
											</SelectItem>
										);
									})
								) : (
									<SelectItem
										value='no-activities'
										disabled
										className='text-gray-500 pr-3 py-2 pl-8'
									>
										No activities available
									</SelectItem>
								)}
							</SelectContent>
						</Select>
					</div>

					<div className='space-y-2'>
						<Label htmlFor='description'>Description</Label>
						<Textarea
							id='description'
							value={formData.description}
							onChange={(e) =>
								setFormData({
									...formData,
									description: e.target.value,
								})
							}
							placeholder='Enter workout description'
							rows={3}
						/>
					</div>

					<div className='flex justify-end gap-2 pt-4'>
						<Button
							type='button'
							variant='outline'
							onClick={() => onOpenChange(false)}
							disabled={isSubmitting}
						>
							Cancel
						</Button>
						<Button
							type='submit'
							className='bg-orange-600 hover:bg-orange-700 text-white'
							disabled={isSubmitting}
						>
							{isSubmitting
								? "Saving..."
								: editingItem
								? "Update"
								: "Create"}
						</Button>
					</div>
				</form>
			</DialogContent>
		</Dialog>
	);
}
