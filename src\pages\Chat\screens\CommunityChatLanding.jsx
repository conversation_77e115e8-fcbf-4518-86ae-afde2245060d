import { Avatar, Box, Paper, CircularProgress, Typography } from "@mui/material";
import React, { useEffect, useState, useMemo, useCallback } from "react";
import Navbar from "../components/Navbar";
import "../styles/chatlanding.css";
import InsertLinkOutlinedIcon from "@mui/icons-material/InsertLinkOutlined";
import NotificationsActiveOutlinedIcon from "@mui/icons-material/NotificationsActiveOutlined";
import {
  onValue,
  query,
  limitToLast,
  orderByChild,
  equalTo,
} from "firebase/database";
import MobileNavbar from "../components/mobile/Navbar";
import {
  getDatabase,
  ref,
  get,
  set,
  update,
  serverTimestamp,
  onDisconnect
} from "firebase/database";
import { useDispatch, useSelector } from "react-redux";
import {
  getAllUsersAction,
  setSearchUserData,
  storeOpenedUserInfoAction,
} from "../redux/action/usersAction";
import { getCurrentUserChatsAction } from "../redux/action/userChats";
import UserChats from "../components/UserChats";
import { useMessageContext } from "../../../context/MessageContext";
import { URL, fetchCommunityGroupEnrolment, fetchGroupMentors, listChatUser } from "../../../API/api-endpoint";
import axios from 'axios';
import { getGroupInfoAction } from "../redux/action/groupAction";


export default function CommunityChatLanding({ setOpenPage, setOpenPageOne, openPage, isUnRead, setIsUnRead }) {
  const paper = {
    padding: "0rem",
    overflowY: "scroll",
    maxWidth: "32rem",
    borderRadius: "8px",
    boxShadow: "unset",
  };

  const dispatch = useDispatch();
  const roleID = localStorage.getItem("roleID");
  const [allGroups, setAllGroups] = useState([]);
  const { currentUser } = useSelector((state) => state.auth);
  const { currentUserChats } = useSelector((state) => state.userChats);
  const [isSearchActive, setIsSearchActive] = useState(false);
  const [searchKeyword, setSearchKeyword] = useState("");
  const { totalUnseenMessageCount } = useMessageContext();
  const [isLoading, setIsLoading] = useState(true);
  const [isLoadingUsers, setIsLoadingusers] = useState(true);
  const [programIdChange, setprogramIdChange] = useState(false);
  const { openedUser } = useSelector(state => state.users);
  const [isClicked, setIsClicked] = useState(false);
  const [totalMessageCount, setTotalMessageCount] = useState(0);
  const [isActive, setIsActive] = useState(false);
  const [isLoadingAthlete, setIsLoadingAthlete] = useState(true);
  const [allUsers, setAllUsers] = useState([]);
  const { serchedUserData } = useSelector((state) => state.users);
  const [personalChatuser, setPersonalChatuser] = useState([]);
  const [isAddChat, setIsAddChat] = useState(false);

  function wrapPersonalChats(input) {
    // Determine the raw list of user objects:
    // 1. If you passed in an array, use it directly.
    // 2. Else if you passed in an object with a .data array, use that.
    // 3. Otherwise assume it’s a single user object and wrap it in an array.
    // const usersArray = Array.isArray(input)
    //   ? input
    //   : Array.isArray(input?.data)
    //     ? input.data
    //     : [input.data];
    
    //   if (roleID == 5) {
    //     return usersArray.map(user => ({
    //       id: user?.user?.id,                     // preserve original user id
    //       chatType: "personal",
    //       date: Date.now(),                // current timestamp
    //       isMsgReqAccepted: true,          // default or derive from logic
    //       isMsgReqDeclined: false,         // default or derive from logic
    //       lastMessage: "",                 // default empty
    //       lastMessageTimestamp: 0,         // default zero
    //       userInfo: {
    //         uid: user.user.google_id || null           // use google_id as uid
    //       }
    //      }));
    //   }else {
    //     // Map each user to the “personal chat” shape
    //     return usersArray.map(user => ({
    //       id: user?.id,                     // preserve original user id
    //       chatType: "personal",
    //       date: Date.now(),                // current timestamp
    //       isMsgReqAccepted: true,          // default or derive from logic
    //       isMsgReqDeclined: false,         // default or derive from logic
    //       lastMessage: "",                 // default empty
    //       lastMessageTimestamp: 0,         // default zero
    //       userInfo: {
    //         uid: user.google_id            // use google_id as uid
    //       }
    //     }));
    //   }

    const usersArray = Array.isArray(input)
      ? input
      : Array.isArray(input?.data)
        ? input.data
        : [input.data];
    
      if (roleID == 5) {
        return usersArray.map(user => ({
          id: user?.id,                     // preserve original user id
          chatType: "personal",
          date: Date.now(),                // current timestamp
          isMsgReqAccepted: true,          // default or derive from logic
          isMsgReqDeclined: false,         // default or derive from logic
          lastMessage: "",                 // default empty
          lastMessageTimestamp: 0,         // default zero
          userInfo: {
            uid: user.google_id || null           // use google_id as uid
          }
         }));
      }else {
        // Map each user to the “personal chat” shape
        return usersArray.map(user => ({
          id: user?.id,                     // preserve original user id
          chatType: "personal",
          date: Date.now(),                // current timestamp
          isMsgReqAccepted: true,          // default or derive from logic
          isMsgReqDeclined: false,         // default or derive from logic
          lastMessage: "",                 // default empty
          lastMessageTimestamp: 0,         // default zero
          userInfo: {
            uid: user.google_id            // use google_id as uid
          }
        }));
      }
    
  }

  useEffect(() => {
    const fetchData = async () => {
      const res = await listChatUser();
      if (res?.data) {
        setPersonalChatuser(wrapPersonalChats(res));
      } else {
        setPersonalChatuser([]);
      }
    }
    fetchData();
  }, []);
  
  const handleFetchAthlete = () => {
    fetchCommunityGroupEnrolment()
      .then(({ data }) => {
        const athletes = data.data.map(user => ({
          id: user.id,
          email: user.email,
          name: `${user.firstname} ${user.lastname}`,
          uid: user.google_id
        }));
        setAllUsers(athletes);
      })
      .catch(error => {
        console.error("Error fetching community group enrolment:", error);
      });
  };


  const handleFetchMentor = () => {
    const groupDetail = JSON.parse(localStorage.getItem("groupDetail"));
    fetchGroupMentors(groupDetail.id)
      .then(({ data }) => {
        const mentors = [{
          "id": data.data.athletecommunitygroup.id,
          "name": data.data.athletecommunitygroup.communityName,
          "email": data.data.user.email,
          "uid": data.data.user.google_id
        }]
        setAllUsers(mentors);
      })
      .catch((error) => {
        console.error("Error fetching group mentors:", error);
      });
  }

   const handleGotoGroupChat = async (groupId) => {
      dispatch(getGroupInfoAction(groupId));
  
      const messagesRef = ref(db, `chats/${groupId}/messages`);
      const messagesSnapshot = await get(messagesRef);
  
      if (messagesSnapshot.exists()) {
        const messages = messagesSnapshot.val();
  
        const updates = {};
        for (const messageId in messages) {
          updates[`${messageId}/isSeen`] = true;
        }
        await update(ref(db, `chats/${groupId}/messages`), updates);
      }
      
      // navigate("/group-chat/creator");
      setOpenPage("group-chat")
      if (window.matchMedia("(max-width: 430px)").matches) { const mobileSection = document.querySelector('.section-two-mobile'); const homeSection = document.querySelector('.section-one'); if (mobileSection) { mobileSection.style.display = 'block';  } if (homeSection) { homeSection.style.display = 'none'; } }
      setTimeout(() => {
        setIsUnRead(false);
      }, 3000);
    };

  const handleSelect = async (user) => {  
    if (user.chatType === "group") {
      handleGotoGroupChat(user.id);
    } else {
    setIsClicked(true);
    dispatch(storeOpenedUserInfoAction(user));
    const db = getDatabase();
    const combinedId =
      currentUser?.uid > user?.uid
        ? currentUser?.uid + user?.uid
        : user?.uid + currentUser?.uid;
    try {
      console.log(combinedId);
      const chatRef = ref(db, "chats/" + combinedId);
      const snapshot = await get(chatRef);

      if (!snapshot.exists()) {
        // Create user chats
        const currentUserChatsRef = ref(db, `userChats/${currentUser?.uid}`);
        const userChatsRef = ref(db, `userChats/${user?.uid}`);

        await update(currentUserChatsRef, {
          [`${combinedId}/userInfo`]: {
            uid: user?.uid,
          },
          [`${combinedId}/date`]: serverTimestamp(),
          [`${combinedId}/isMsgReqAccepted`]: true,
          [`${combinedId}/isMsgReqDeclined`]: false,
          [`${combinedId}/chatType`]: "personal",
        });

        await update(userChatsRef, {
          [`${combinedId}/userInfo`]: {
            uid: currentUser?.uid,
          },
          [`${combinedId}/date`]: serverTimestamp(),
          [`${combinedId}/isMsgReqAccepted`]: false,
          [`${combinedId}/isMsgReqDeclined`]: false,
          [`${combinedId}/chatType`]: "personal",
        });

        setOpenPage("individual")
        if (!isActive) {
          setIsUnRead(false)
        }

      }
      setOpenPage("individual")
      if (!isActive) {
        setIsUnRead(false)
      }
    } catch (err) {
      console.log(err);
    }
    }
  };

  useEffect(() => {
    if (roleID == 6) {
      handleFetchAthlete();
    } else if (roleID == 5) {
      handleFetchMentor();
    }
  }, [roleID]);

  const filteredAllUsers = useMemo(() => {
    return allUsers;
  }, [allUsers, roleID]);

  useEffect(() => {
    handleSearchUser(filteredAllUsers, searchKeyword);
  }, [filteredAllUsers, searchKeyword]);

  const firstLogin = localStorage.getItem('FirstLogin');

  useEffect(() => {
    const firstLogin = localStorage.getItem('FirstLogin');
    if (firstLogin === 'true') {
      // Set FirstLogin to false and reload the page
      localStorage.setItem('FirstLogin', 'false');
      window.location.reload();
    }
  }, [firstLogin]);


  useEffect(() => {
    if (allUsers.length > 0 && roleID !== 5) {
      setIsLoadingusers(false);
    }
    else if (roleID == 5) {
      setTimeout(() => {
        setIsLoadingusers(false);
      }, 8000);
    }
  }, [allUsers]);

  useEffect(() => {
    if (roleID == 3) {
      setTimeout(() => {
        setIsLoadingusers(false);
      }, 8000);
    }

  }, [allUsers]);

  useEffect(() => {
    if (roleID == 5) {
      setTimeout(() => {
        setIsLoadingAthlete(false)
      }, 8000);
    }

  }, []);


  useEffect(() => {
    console.log("program changed");
    const fetchData = async () => {
      await dispatch(getAllUsersAction(roleID));
    };

    fetchData();
    if (programIdChange == true) {
      window.location.reload();
    }

    setprogramIdChange(false)
  }, [programIdChange == true]);


  useEffect(() => {
    const fetchData = async () => {
      await dispatch(getAllUsersAction(roleID));
      await dispatch(getCurrentUserChatsAction(currentUser?.uid));
    };

    fetchData();
  }, [currentUser?.uid, roleID, dispatch]);

  // const handleSearchUser = () => {
  //   const keyword = searchKeyword.trim().toLowerCase();
  //   if (keyword === "") {
  //     setSerchedUserData(filteredAllUsers);
  //   } else {
  //     const matches = filteredAllUsers.filter((user) =>
  //       `${user.name}`.toLowerCase().includes(keyword)
  //     );
  //     setSerchedUserData(matches);
  //   }
  // };


  // useEffect(() => {
  //   handleSearchUser();
  // }, [searchKeyword]);

  async function fetchUserById(userId) {
    const snap = await get(ref(db, `users/${userId}`));
    return snap.exists() ? snap.val() : null;
  }

  async function fetchGroupById(groupId) {
       try {
         const authToken = localStorage.getItem("token");
    
         // 1️⃣ Load from Firebase
         const fbSnap = await get(ref(db, `groups/${groupId}`));
         if (!fbSnap.exists()) {
           console.warn(`Firebase group ${groupId} not found.`);
           return null;
         }
         const firebaseGroup = fbSnap.val();
    
         // 2️⃣ Load external subscription info
         const resp = await axios.get(`${URL}/subscribe-group`, {
           headers: { Authorization: authToken }
         });
         if (resp.status !== 200) {
           throw new Error(`Subscribe API failed: ${resp.status}`);
         }
         const externalGroups = resp.data.data;
    
         // 3️⃣ Find matching external record
         const matched = externalGroups.find(g =>
           g.communitygroup.id === groupId ||
           g.communitygroup.channelName === firebaseGroup.groupName
         );
    
         // 4️⃣ Merge just like UserChats does
         return {
           ...firebaseGroup,
           externalGroup: matched || null,
           // ensure the same shape:
           communitygroup: matched
             ? matched.communitygroup
             : { id: groupId, channelName: firebaseGroup.groupName }
         };
       } catch (err) {
         console.error("Error fetching group data:", err);
         return null;
       }
  }

  // const handleSearchUser = useCallback(async () => {
  //   const keyword = searchKeyword.trim().toLowerCase();

  //   // 1) If empty keyword, just dispatch all users
  //   if (!keyword) {
  //     dispatch(setSerchedUserData(filteredAllUsers, searchKeyword));
  //     return;
  //   }

  //   // 2) Lookup “active” personal chats
  //   const personalChats = await Promise.all(
  //     sortedCurrentUserChats
  //       .filter((c) => c.chatType === "personal")
  //       .map(async ({ userInfo }) => {
  //         const full = await fetchUserById(userInfo.uid);
  //         return {
  //           ...(full || userInfo),
  //           chatType: "personal",
  //           uid: userInfo.uid,
  //         };
  //       })
  //   );

  //   // 3) Lookup active group chats
  //   const groupChats = await Promise.all(
  //     sortedCurrentUserChats
  //       .filter((c) => c.chatType === "group")
  //       .map(async ({ groupId }) => {
  //         const data = await fetchGroupById(groupId);
  //         const cg = data?.communitygroup || { id: groupId, channelName: "" };
  //         return {
  //           ...cg,
  //           displayName: cg.channelName,
  //           chatType: "group",
  //           id: groupId,
  //         };
  //       })
  //   );

  //   // 4) Merge with filteredAllUsers (to include everyone else)
  //   const merged = [...personalChats, ...groupChats, ...filteredAllUsers];
    
  //   // 5) De‑duplicate by uid (personal) or id (group)
  //   const uniqueMerged = merged.filter((item, idx, arr) => {
  //     if (item.chatType === "group") {
  //       return (
  //         idx ===
  //         arr.findIndex(
  //           (i) => i.chatType === "group" && i.id === item.id
  //         )
  //       );
  //     } else {
  //       return (
  //         idx ===
  //         arr.findIndex(
  //           (i) => i.chatType === "personal" && i.uid === item.uid
  //         )
  //       );
  //     }
  //   });

  //   // 6) Finally, filter by the keyword in the display name / name
  //   const matches = uniqueMerged.filter((item) => {
  //     const name = item.displayName || item.name || "";
  //     return name.toLowerCase().includes(keyword);
  //   });
  //   console.log("matches", matches);
    
  //   // 7) Dispatch into your search reducer
  //   dispatch(setSerchedUserData(matches, searchKeyword));
  // }, [searchKeyword, dispatch]);

  // useEffect(() => {
  //   handleSearchUser().catch(console.error);
  // }, [handleSearchUser]);

  const getMessageCount = (otherUid) => {
    const combinedId =
      currentUser.uid > otherUid
        ? currentUser.uid + otherUid
        : otherUid + currentUser.uid;
    const messagesRef = ref(db, `chats/${combinedId}/messages`);
  
    return new Promise((resolve) => {
      onValue(
        messagesRef,
        (snapshot) => {
          resolve(snapshot.exists() ? snapshot.size : 0);
        },
        { onlyOnce: true }
      );
    });
  };

  const fetchAllChats = async () => {
    // 1️⃣ Personal chats with zero messages
    const personalChats = (
      await Promise.all(
        sortedCurrentUserChats
          .filter(c => c.chatType === 'personal')
          .map(async ({ userInfo, lastMessageTimestamp }) => {
            const full = await fetchUserById(userInfo.uid);
            if (!full) return null;
  
            const count = await getMessageCount(userInfo.uid);
            if (count > 0) return null;  // skip active chats
  
            return {
              chatType: 'personal',
              id:       userInfo.uid,
              uid:      userInfo.uid,
              name:     full.displayName,
              avatar:   full.photoURL,
              lastSeen: new Date(lastMessageTimestamp),
              totalMessages: count,
              ...full,
            };
          })
      )
    ).filter(Boolean);
  
    // 2️⃣ Group chats with zero messages
    const getGroupMessageCount = (groupId) => {
      const messagesRef = ref(db, `chats/${groupId}/messages`);
      return new Promise((resolve) => {
        onValue(
          messagesRef,
          (snapshot) => resolve(snapshot.exists() ? snapshot.size : 0),
          { onlyOnce: true }
        );
      });
    };
  
    const groupChats = (
      await Promise.all(
        sortedCurrentUserChats
          .filter(c => c.chatType === 'group')
          .map(async ({ groupId, lastMessageTimestamp }) => {
            const data = await fetchGroupById(groupId);
            const cg = data?.communitygroup || {};
  
            const count = await getGroupMessageCount(groupId);
            if (count > 0) return null;  // skip active groups
  
            return {
              chatType:    'group',
              id:          groupId,
              uid:         groupId,
              name:        cg.channelName,
              displayName: cg.channelName,
              avatar:      cg.profileURL || '',
              photoURL:    cg.profileURL || '',
              email:       '',
              onlineStatus:{ isOnline: false },
              lastSeen:    new Date(lastMessageTimestamp),
              totalMessages: count,
              ...cg,
            };
          })
      )
    ).filter(Boolean);
  
    // 3️⃣ Merge with autocomplete list, filter and dedupe as before
    const merged = [...personalChats, ...groupChats, ...filteredAllUsers];
    const filtered = merged.filter(item => {
      if (item.chatType === 'personal' && item.uid === currentUser.uid) return false;
      if (item.chatType === 'group' && !memoizedGroupExistence[item.id]) return false;
      return true;
    });
    const uniqueMerged = filtered.filter((item, idx, arr) =>
      item.chatType === 'group'
        ? idx === arr.findIndex(i => i.chatType === 'group' && i.id === item.id)
        : idx === arr.findIndex(i => i.chatType === 'personal' && i.id === item.id)
    );
  
    console.log('uniqueMerged', uniqueMerged);
    dispatch(setSearchUserData(uniqueMerged, searchKeyword));
  };
  
  useEffect(() => {
    if (isAddChat) {
      fetchAllChats();
    }
  },[isAddChat]);

  const handleSearchUser = async () => { 
  
    // 1️⃣ Build personal‐chat details exactly as before
    const personalChats = (
         await Promise.all(
           sortedCurrentUserChats
             .filter(c => c.chatType === "personal")
             .map(async ({ userInfo, lastMessageTimestamp }) => {
             const full = await fetchUserById(userInfo.uid);
             if (!full) return null;                // ← skip if missing
              console.log("full", full);
              
               return {
                 chatType: "personal",
                 id:       userInfo.uid,
                 name:     full.displayName,
                 avatar:   full.photoURL,
                 lastSeen: new Date(lastMessageTimestamp),
              // carry along any other props you need *only* when full exists:
              ...full,
               };
             })
         )
    ).filter(Boolean); 
    
    // 2️⃣ Build group‐chat details, *wrapping* them into the same shape
    const groupChats = await Promise.all(
      sortedCurrentUserChats
        .filter(c => c.chatType === "group")
        .map(async ({ groupId, lastMessageTimestamp }) => {
          const data = await fetchGroupById(groupId);
          const cg   = data?.communitygroup || {};
  
          // wrap into “personal‐style” fields:
          return {
            chatType:    "group",
            id:          groupId,
            uid:         groupId,                     // so search by `uid` still works
            name:        cg.channelName,
            displayName: cg.channelName,              // same key your search looks for
            avatar:      cg.profileURL || "",        
            photoURL:    cg.profileURL || "",         // match personal’s photoURL
            email:       "",                          // no email for groups
            onlineStatus: { isOnline: false },        // keep the same keyshape
            lastSeen:    new Date(lastMessageTimestamp),
            ...cg,                                    // any extra group fields
          };
        })
    );
  
    // 3️⃣ Merge in your “all users” autocomplete list
    const merged = [...personalChats, ...groupChats, ...filteredAllUsers];
  
    // 4️⃣ Apply the same sidebar filters
    const filtered = merged.filter(item => {
      // drop “chat with yourself”
      if (item.chatType === "personal" && item.uid === currentUser.uid) {
        return false;
      }
      // drop groups you aren’t actually in
      if (item.chatType === "group" && !memoizedGroupExistence[item.id]) {
        return false;
      }
      return true;
    });
  
    // 5️⃣ Dedupe exactly as before
    const uniqueMerged = filtered.filter((item, idx, arr) =>
      item.chatType === "group"
        ? idx === arr.findIndex(i => i.chatType === "group"    && i.id === item.id)
        : idx === arr.findIndex(i => i.chatType === "personal" && i.id === item.id)
    );
  
      if (searchKeyword == "" && isAddChat) {
        fetchAllChats();
      } else {
        dispatch(setSearchUserData(uniqueMerged, searchKeyword));
      }
  };
  
  useEffect(() => {
    handleSearchUser();
  }, [searchKeyword]);



  const getTotalUnseenMessageCount = (
    currentUser,
    allUsers,
    setTotalUnseenMessageCount
  ) => {
    let totalUnseenMsgesCount = 0;

    // Iterate over all users
    allUsers.forEach((userUid) => {
      const combinedId =
        currentUser?.uid > userUid
          ? currentUser?.uid + userUid
          : userUid + currentUser?.uid;

      const messagesRef = ref(db, `chats/${combinedId}/messages`);

      const unseenMessagesQuery = query(
        messagesRef,
        orderByChild("isSeen"),
        equalTo(false)
      );

      onValue(unseenMessagesQuery, (snapshot) => {
        let userUnseenMsgesCount = 0;

        snapshot.forEach((childSnapshot) => {
          const message = childSnapshot.val();
          if (message.senderId !== currentUser?.uid && !message.isSeen) {
            userUnseenMsgesCount++;
          }
        });

        totalUnseenMsgesCount += userUnseenMsgesCount;

        setTotalUnseenMessageCount(totalUnseenMsgesCount); // Set count after processing all users
      });
    });
  };


  const db = getDatabase();
  const isOnlineRef = ref(db, ".info/connected");
  const userStatusRef = ref(db, `/users/${currentUser?.uid}/onlineStatus/isOnline`);

  onValue(isOnlineRef, (snapshot) => {
    if (snapshot.val() === false) {
      return;
    }

    set(userStatusRef, true);
    onDisconnect(userStatusRef).set(false);
  });

  async function getGroupDataById(groupId) {
    try {
      const userRef = ref(db, `groups/${groupId}`);
      const snapshot = await get(userRef);

      if (snapshot.exists()) {
        const createdTime = snapshot.val()
        return createdTime;
      } else {
        console.warn("User data not found.");
        return null;
      }
    } catch (error) {
      console.error("Error getting user data:", error);
      return null;
    }
  }

  function convertToUnixEpoch(dateString) {
    const date = new Date(dateString);
    if (isNaN(date.getTime())) {
      throw new Error("Invalid date format.");
    }
    return date.getTime();
  }

  const getSubscribedGroup = async () => {
    const authToken = localStorage.getItem("token");
    const response = await axios.get(`${URL}/subscribe-group`, {
      headers: {
        Authorization: authToken,
      },
    });

    if (response.status !== 200) {
      throw new Error(`Axios API failed with status: ${response.status}`);
    }
    console.log(response.data.data, "All groups");

    setAllGroups(response.data.data);
  }

  useEffect(() => {
    getSubscribedGroup();
  }, []);

  const getLastMessage = (combinedId) => {
    return new Promise((resolve, reject) => {
      const messagesRef = ref(db, `chats/${combinedId}/messages`);
      const lastMessageQuery = query(
        messagesRef,
        orderByChild("timeStamp"),
        limitToLast(1)
      );

      onValue(lastMessageQuery, (snapshot) => {
        if (snapshot.exists()) {
          const lastMessageKey = Object.keys(snapshot.val())[0];
          const lastMessage = snapshot.val()[lastMessageKey];
          resolve({
            message: lastMessage.message,
            timestamp: lastMessage.timeStamp,
          });
        } else {
          resolve(null); // No messages found
        }
      }, (error) => {
        reject(error);
      });
    });
  };

  // Function to sort currentUserChats based on the last message timestamp of each chat
  const sortCurrentUserChatsByLastMessage = async (uniquePersonalChats) => {
    const sortedChats = [];

    // Iterate over currentUserChats and retrieve last message for each chat
    for (const chat of uniquePersonalChats) {
      let lastMessageTimestamp = 0;

      if (chat.chatType === 'personal') {
        const combinedId =
          currentUser?.uid > chat.userInfo?.uid
            ? currentUser?.uid + chat.userInfo?.uid
            : chat.userInfo?.uid + currentUser?.uid;

        const lastMessage = await getLastMessage(combinedId);
        lastMessageTimestamp = lastMessage ? lastMessage.timestamp : 0;
      }

      sortedChats.push({
        ...chat,
        lastMessageTimestamp,
      });
    }

    // Sort chats based on last message timestamp in descending order
    sortedChats.sort((a, b) => b.lastMessageTimestamp - a.lastMessageTimestamp);

    return sortedChats;
  };

  console.log(personalChatuser,"personalChatuser");
  

  let uniquePersonalChats = personalChatuser.filter((chat, index, chats) => {
    if (chat.chatType === 'personal') {
      const combinedId =
        currentUser?.uid > chat.userInfo?.uid
          ? currentUser?.uid + chat.userInfo?.uid
          : chat.userInfo?.uid + currentUser?.uid;

      // Check if this combined ID already exists in previous chats
      return chats.findIndex((prevChat) =>
        prevChat.chatType === 'personal' && prevChat.userInfo?.uid === chat.userInfo?.uid
      ) === index;
    }
    return true; // Keep non-personal chats
  });

  // Create a map to track unique groupIds and personal chats
  const groupIdMap = new Map();

  // Add existing group chats to the map (Ensure groupId uniqueness)
  uniquePersonalChats.forEach((chat) => {
    if (chat.chatType === 'group' && chat.groupInfo?.id) {
      groupIdMap.set(chat.groupInfo.id, true);
    }
  });

  // Merge all unique groups into uniquePersonalChats
  const mergedChats = [...uniquePersonalChats];

  // Add unique groups from allGroups
  allGroups.forEach((group) => {
    const groupId = group?.communitygroup?.id;

    if (groupId && !groupIdMap.has(groupId)) {
      // If groupId is not already in the map, add it
      groupIdMap.set(groupId, true);

      // Transform group data to match currentUserChats format
      const formattedGroupChat = {
        chatType: 'group',
        groupId: groupId,
        date: new Date(group?.communitygroup.createdAt).getTime() || 0,
        isMsgReqAccepted: true,
        groupInfo: group?.communitygroup, // Adjust the structure if needed to match your data
      };
      mergedChats.push(formattedGroupChat);
    }
  });

  // Ensure final uniqueness of all chats (including personal and group chats)
  uniquePersonalChats = mergedChats.filter((chat, index, self) =>
    index === self.findIndex((c) =>
      c.chatType === 'group' ? c.groupId === chat.groupId : c.userInfo?.uid === chat.userInfo?.uid
    )
  );
  //  -- END --
  console.log("uniquePersonalChats", uniquePersonalChats);
  console.log("currentUserChats", currentUserChats);
  console.log("\nallGroups", allGroups);
  console.log("mergedChats", mergedChats);

  const [uniqueUid, setUniqueUid] = useState([]);
  const [sortedCurrentUserChats, setSortedCurrentUserChats] = useState([]);
  useEffect(() => {
    // Extract unique UIDs from uniquePersonalChats
    const extractedUids = uniquePersonalChats
      .filter((chat) => chat.chatType === 'personal')
      .map((chat) => chat.userInfo?.uid)
      .filter((uid, index, uids) => uids.indexOf(uid) === index);

    // Update the uniqueUid state
    console.log("extractedUid", extractedUids);
    setUniqueUid(extractedUids);
  }, [sortedCurrentUserChats, personalChatuser]);


  // Get sorted currentUserChats based on last message
  useEffect(() => {
    sortCurrentUserChatsByLastMessage(uniquePersonalChats).then((sortedChats) => {
      setSortedCurrentUserChats(sortedChats);
      setIsLoading(false)
    }).catch((error) => {
      console.error('Error sorting chats:', error);
    });
  }, [personalChatuser, currentUserChats, currentUser?.uid, totalUnseenMessageCount, isUnRead]);

  useEffect(() => {
    const timeoutId = setTimeout(() => {
      sortCurrentUserChatsByLastMessage(uniquePersonalChats)
        .then((sortedChats) => {
          setSortedCurrentUserChats(sortedChats);
          setIsLoading(false);
        })
        .catch((error) => {
          console.error('Error sorting chats:', error);
        });
    }, 1000);

    return () => clearTimeout(timeoutId);
  }, [currentUserChats, personalChatuser]);


  const getTotalMessageCount = (userUid) => {
    const combinedId =
      currentUser?.uid > userUid
        ? currentUser?.uid + userUid
        : userUid + currentUser?.uid;

    const messagesRef = ref(db, `chats/${combinedId}/messages`);

    let totalMsgesCount = 0;

    const callback = (snapshot) => {
      totalMsgesCount = 0; // Reset count before updating
      snapshot.forEach((childSnapshot) => {
        totalMsgesCount++; // Increment count
      });
      console.log("isactive", totalMsgesCount);
      setTotalMessageCount(totalMsgesCount); // Set count after processing
    };

    onValue(messagesRef, callback);
    return;
  };

  useEffect(() => {
    // Call getTotalMessageCount when openedUser changes
    if (openedUser) {
      getTotalMessageCount(openedUser?.uid);
    }
  }, [openedUser]);

  useEffect(() => {
    // Call getTotalMessageCount when openedUser changes
    console.log("isactive", isActive);
  }, [isActive]);

  // Set setActive to true if totalMsgesCount is zero
  useEffect(() => {
    if (totalMessageCount === 0) {
      setIsActive(true);
    }
    else {
      setIsActive(false)
    }
  }, [totalMessageCount]);

  const [groupExistence, setGroupExistence] = useState({});

  useEffect(() => {
    const fetchGroupsData = async () => {
      // Collect all group IDs that need to be checked
      const groupIds = sortedCurrentUserChats
        .filter(chat => chat.chatType === 'group')
        .map(chat => chat.groupId);

      // Fetch group data in parallel for all group IDs
      const groupDataPromises = groupIds.map(groupId => getGroupDataById(groupId));

      // Wait for all promises to resolve and map them into a lookup object
      const groupDataResults = await Promise.all(groupDataPromises);
      const existenceMap = groupDataResults.reduce((acc, groupData, index) => {
        acc[groupIds[index]] = groupData !== null; // Set existence status
        return acc;
      }, {});

      setGroupExistence(existenceMap); // Update state with group existence data
    };

    fetchGroupsData();
  }, [sortedCurrentUserChats]);

  const memoizedGroupExistence = useMemo(() => {
    const existenceMap = {};
    for (const chat of sortedCurrentUserChats) {
      if (chat.chatType === 'group' && !existenceMap[chat.groupId]) {
        existenceMap[chat.groupId] = groupExistence[chat.groupId];
      }
    }
    return existenceMap;
  }, [sortedCurrentUserChats, groupExistence]);
    
  console.log("personalChatuser",personalChatuser);
  console.log(sortedCurrentUserChats,"sortedCurrentUserChats");
  
  return (
    <div>
      {/* <Header setprogramIdChange={setprogramIdChange}/> */}
      <Box
        sx={{
          display: "flex",
          // marginTop: "90px",
          justifyContent: "center",
          alignItems: "center",
          width: "100%"
        }}
      >
        <Paper
          className="chatPaper groupStepOne"
          sx={{ flexGrow: 1 }}
          elevation={10}
          style={paper}
        >
          <Navbar
            isUnRead={isUnRead}
            setIsUnRead={setIsUnRead}
            isSearchActive={isSearchActive}
            setIsSearchActive={setIsSearchActive}
            searchKeyword={searchKeyword}
            setSearchKeyword={setSearchKeyword}
            isAddChat={isAddChat}
            setIsAddChat={setIsAddChat}
          />

          {(isLoading && !isLoadingUsers) ? (
            <CircularProgress className="m-6" />) : (
            <>

              {searchKeyword === "" && isAddChat == false &&
                sortedCurrentUserChats.map((item, index) => {
                  if (item?.userInfo?.uid === currentUser?.uid) return null; // Skip rendering current user
                  if (item.chatType === 'group' && !memoizedGroupExistence[item.groupId]) {
                    return null; // Skip rendering non-existent groups
                  }

                  let foundGroup = null;
                  if (item.chatType === 'group') {
                    foundGroup = allGroups.find(group => group.communitygroup.id === item.groupId);
                  }
                  
                  return (
                    <UserChats
                      key={`cUser${index}`}
                      item={item}
                      currentUser={currentUser}
                      isUnRead={isUnRead}
                      setIsUnRead={setIsUnRead}
                      setOpenPage={setOpenPage}
                      openPage={openPage}
                      group={foundGroup}
                    />
                  );
                })}

            </>
          )}
          {searchKeyword && serchedUserData && serchedUserData.length === 0 && isAddChat === false ? (
            <Typography
              variant="h6"
              sx={{
                textAlign: "center",
                paddingTop: "20px",
                fontWeight: "700",
              }}
            >
              No Data Found.
            </Typography>
          ) : (
            serchedUserData && searchKeyword !== "" &&
            serchedUserData.length > 0 && isAddChat === false &&
            serchedUserData.map((item, index) => {
              if (item?.uid === currentUser?.uid ) return;
              return (
                <Box
                  onClick={() => handleSelect(item)}
                  key={index}
                  sx={
                    (openedUser?.uid === item?.uid && openPage === "individual" && isActive)
                    ?
                  
                    {
                      marginLeft:"10px !important",
                    cursor: "pointer",
                    padding: "8px",
                    paddingTop: "15px",
                    display: "flex",
                    justifyContent: "space-between",
                    alignItems: "center",
                    opacity: "1",
                    backgroundColor: "#E67E22",
                    borderRadius: "10px"
                    }
                    :
                    {
                      marginLeft:"10px !important",
                    cursor: "pointer",
                    padding: "8px",
                    paddingTop: "15px",
                    display: "flex",
                    justifyContent: "space-between",
                    alignItems: "center",
                    opacity: "0.5",
                    
                  }}
                >
                  <Box
                    sx={{
                      display: "flex",
                      alignItems: "center",
                      padding: "6px",
                    }}
                  >
                    <Avatar alt={item.displayName}
                     src={`${URL}/static/public/userimages/${item?.photoURL}`} />
                    <Box>
                      <Typography
                        className="userName"
                        fontWeight="fontWeightBold"
                        sx={
                          (openedUser?.uid === item?.uid && openPage === "individual" && isActive)
                    ?
                    {color: "white !important",paddingLeft: "1.2rem"}
                    :
                          { paddingLeft: "1.2rem" }}
                      >{item.displayName}</Typography>
                      <Typography
                        sx={{ paddingLeft: "1.2rem", fontSize: "15px" }}
                      >
                        {item.desc}
                      </Typography>
                    </Box>
                  </Box>
                  <Box sx={{ textAlign: "right" }}>
                    <Box className="timeStamp">{item.time}</Box>
                    <Box
                      sx={{
                        textAlign: "right",
                        display: "flex",
                        alignItems: "center",
                        justifyContent: "flex-end",
                      }}
                    >
                      {item.showMediaIcon && (
                        <InsertLinkOutlinedIcon className="chat-icon" />
                      )}
                      {item.showBellIcon && (
                        <NotificationsActiveOutlinedIcon className="chat-icon" />
                      )}
                      {item.msgCount > 0 && (
                        <Box className="badgeIcon chat-icon">
                          <Typography fontSize={15}>{item.msgCount}</Typography>
                        </Box>
                      )}
                    </Box>
                  </Box>
                </Box>
              );
            })
          )}

          {!isLoadingAthlete && allUsers.length == 0 && (roleID == 5) && searchKeyword == "" && totalUnseenMessageCount == 0 && (
            <Typography
              variant="h6"
              sx={{
                textAlign: "center",
                paddingTop: "20px",
                fontWeight: "700",
              }}
            >
              Your Messaging will be activated once Coach is assigned to you
            </Typography>
          )}


          {!isLoadingUsers && allUsers.length == 0 && roleID == 3 && searchKeyword == "" && isUnRead && (
            <Typography
              variant="h6"
              sx={{
                textAlign: "center",
                paddingTop: "20px",
                fontWeight: "700",
              }}
            >
              No Athletes Assigned.
            </Typography>
          )}

          {/* {isLoadingUsers ? (
            <CircularProgress className="m-6" />) : (
            <>

              {serchedUserData.length == 0 && totalMessageCount !== 0 &&
                searchKeyword === "" &&
                filteredAllUsers.map((item, index) => {
                  if (item?.uid === currentUser?.uid) return;
                  return (
                    <Box
                      onClick={() => handleSelect(item)}
                      key={index}
                      sx={
                        (openedUser?.uid === item?.id && openPage === "individual" && isActive)
                          ?
                          {
                            marginLeft: "10px !important",
                            cursor: "pointer",
                            padding: "8px",
                            paddingTop: "15px",
                            display: "flex",
                            justifyContent: "space-between",
                            alignItems: "center",
                            opacity: "1",
                            backgroundColor: "#E67E22",
                            borderRadius: "10px"
                          }
                          :
                          {
                            marginLeft: "10px !important",
                            cursor: "pointer",
                            padding: "8px",
                            paddingTop: "15px",
                            display: "flex",
                            justifyContent: "space-between",
                            alignItems: "center",
                            opacity: "0.5",

                          }}
                    >
                      <Box
                        sx={{
                          display: "flex",
                          alignItems: "center",
                          padding: "6px",
                        }}
                      >
                        <Avatar alt={item.name} />
                        <Box>
                          <Typography
                            className="userName"
                            fontWeight="fontWeightBold"
                            sx={{ paddingLeft: "1.2rem" }}
                          >{item.name}</Typography>
                          <Typography
                            sx={{ paddingLeft: "1.2rem", fontSize: "15px" }}
                          >
                            {item.desc}
                          </Typography>
                        </Box>
                      </Box>
                      <Box sx={{ textAlign: "right" }}>
                        <Box className="timeStamp">{item.time}</Box>
                        <Box
                          sx={{
                            textAlign: "right",
                            display: "flex",
                            alignItems: "center",
                            justifyContent: "flex-end",
                          }}
                        >
                          {item.showMediaIcon && (
                            <InsertLinkOutlinedIcon className="chat-icon" />
                          )}
                          {item.showBellIcon && (
                            <NotificationsActiveOutlinedIcon className="chat-icon" />
                          )}
                          {item.msgCount > 0 && (
                            <Box className="badgeIcon chat-icon">
                              <Typography fontSize={15}>{item.msgCount}</Typography>
                            </Box>
                          )}
                        </Box>
                      </Box>
                    </Box>
                  );
                })}
            </>
          )}  */}
          {searchKeyword && serchedUserData && serchedUserData.length === 0 && isAddChat ? (
            <Typography
              variant="h6"
              sx={{
                textAlign: "center",
                paddingTop: "20px",
                fontWeight: "700",
              }}
            >
              No Data Found.
            </Typography>
          ) : (
            serchedUserData && 
            serchedUserData.length > 0 && isAddChat &&
            serchedUserData.map((item, index) => {
              if (item?.uid === currentUser?.uid ) return;

              return (
                <Box
                  onClick={() => handleSelect(item)}
                  key={index}
                  sx={
                    (openedUser?.uid === item?.uid && openPage === "individual" && isActive)
                    ?
                  
                    {
                      marginLeft:"10px !important",
                    cursor: "pointer",
                    padding: "8px",
                    paddingTop: "15px",
                    display: "flex",
                    justifyContent: "space-between",
                    alignItems: "center",
                    opacity: "1",
                    backgroundColor: "#E67E22",
                    borderRadius: "10px"
                    }
                    :
                    {
                      marginLeft:"10px !important",
                    cursor: "pointer",
                    padding: "8px",
                    paddingTop: "15px",
                    display: "flex",
                    justifyContent: "space-between",
                    alignItems: "center",
                    opacity: "0.5",
                    
                  }}
                >
                  <Box
                    sx={{
                      display: "flex",
                      alignItems: "center",
                      padding: "6px",
                    }}
                  >
                    <Avatar alt={item.displayName}
                     src={`${URL}/static/public/userimages/${item?.photoURL}`} />
                    <Box>
                      <Typography
                        className="userName"
                        fontWeight="fontWeightBold"
                        sx={
                          (openedUser?.uid === item?.uid && openPage === "individual" && isActive)
                    ?
                    {color: "white !important",paddingLeft: "1.2rem"}
                    :
                          { paddingLeft: "1.2rem" }}
                      >{item.displayName}</Typography>
                      <Typography
                        sx={{ paddingLeft: "1.2rem", fontSize: "15px" }}
                      >
                        {item.desc}
                      </Typography>
                    </Box>
                  </Box>
                  <Box sx={{ textAlign: "right" }}>
                    <Box className="timeStamp">{item.time}</Box>
                    <Box
                      sx={{
                        textAlign: "right",
                        display: "flex",
                        alignItems: "center",
                        justifyContent: "flex-end",
                      }}
                    >
                      {item.showMediaIcon && (
                        <InsertLinkOutlinedIcon className="chat-icon" />
                      )}
                      {item.showBellIcon && (
                        <NotificationsActiveOutlinedIcon className="chat-icon" />
                      )}
                      {item.msgCount > 0 && (
                        <Box className="badgeIcon chat-icon">
                          <Typography fontSize={15}>{item.msgCount}</Typography>
                        </Box>
                      )}
                    </Box>
                  </Box>
                </Box>
              );
            })
          )}
        </Paper>

        <Paper className="mobileChatPaper"
          sx={{ flexGrow: 1, backgroundColor: "rgb(255, 222, 173)", position: "relative" }}
          elevation={10}
        >
          <MobileNavbar
            isUnRead={isUnRead}
            setIsUnRead={setIsUnRead}
            isSearchActive={isSearchActive}
            setIsSearchActive={setIsSearchActive}
            searchKeyword={searchKeyword}
            setSearchKeyword={setSearchKeyword}
          />
          {(isLoading && !isLoadingUsers) ? (
            <CircularProgress className="m-6" />) : (
            <>

              {searchKeyword === "" &&
                sortedCurrentUserChats.map((item, index) => {
                  if (item?.userInfo?.uid === currentUser?.uid) return null; // Skip rendering current user
                  if (item.chatType === 'group' && !memoizedGroupExistence[item.groupId]) {
                    return null; // Skip rendering non-existent groups
                  }

                  let foundGroup = null;
                  if (item.chatType === 'group') {
                    foundGroup = allGroups.find(group => group.communitygroup.id === item.groupId);
                  }

                  return (
                    <UserChats
                      key={`cUser${index}`}
                      item={item}
                      currentUser={currentUser}
                      isUnRead={isUnRead}
                      setIsUnRead={setIsUnRead}
                      setOpenPage={setOpenPage}
                      openPage={openPage}
                      group={foundGroup}
                    />
                  );
                })}
            </>
          )}
          {!isLoadingAthlete && allUsers.length == 0 && (roleID == 5) && searchKeyword == "" && totalUnseenMessageCount == 0 && (
            <Typography
              variant="h6"
              sx={{
                textAlign: "center",
                paddingTop: "20px",
                fontWeight: "700",
              }}
            >
              Your Messaging will be activated once Coach is assigned to you
            </Typography>
          )}


          {!isLoadingUsers && allUsers.length == 0 && roleID == 3 && searchKeyword == "" && isUnRead && (
            <Typography
              variant="h6"
              sx={{
                textAlign: "center",
                paddingTop: "20px",
                fontWeight: "700",
              }}
            >
              No Athletes Assigned.
            </Typography>
          )}

          {isLoadingUsers ? (
            <CircularProgress className="m-6" />) : (
            <>

              {serchedUserData.length == 0 &&
                searchKeyword === "" &&
                filteredAllUsers.map((item, index) => {
                  if (item?.id === currentUser?.uid) return;
                  return (
                    <Box
                      onClick={() => handleSelect(item)}
                      key={index}
                      sx={
                        (openedUser?.uid === item?.uid && openPage === "individual" && isActive)
                          ?
                          {
                            marginLeft: "10px !important",
                            cursor: "pointer",
                            padding: "8px",
                            paddingTop: "15px",
                            display: "flex",
                            justifyContent: "space-between",
                            alignItems: "center",
                            opacity: "1",
                            backgroundColor: "#E67E22",
                            borderRadius: "10px"
                          }
                          :
                          {
                            marginLeft: "10px !important",
                            cursor: "pointer",
                            padding: "8px",
                            paddingTop: "15px",
                            display: "flex",
                            justifyContent: "space-between",
                            alignItems: "center",
                            opacity: "0.5",
                            "@media (max-width: 430px)": {
                              backgroundColor: "#F5F5F5",
                              marginBottom: "10px",
                              marginLeft: "10px",
                              marginRight: "10px",
                              borderRadius: "10px"
                            }

                          }}
                    >
                      <Box
                        sx={{
                          display: "flex",
                          alignItems: "center",
                          padding: "6px",
                        }}
                      >
                        <Avatar alt={item.name} />
                        <Box>
                          <Typography
                            className="userName"
                            fontWeight="fontWeightBold"
                            sx={{ paddingLeft: "1.2rem" }}
                          >{item.name}</Typography>
                          <Typography
                            sx={{ paddingLeft: "1.2rem", fontSize: "15px" }}
                          >
                            {item.desc}
                          </Typography>
                        </Box>
                      </Box>
                      <Box sx={{ textAlign: "right" }}>
                        <Box className="timeStamp">{item.time}</Box>
                        <Box
                          sx={{
                            textAlign: "right",
                            display: "flex",
                            alignItems: "center",
                            justifyContent: "flex-end",
                          }}
                        >
                          {item.showMediaIcon && (
                            <InsertLinkOutlinedIcon className="chat-icon" />
                          )}
                          {item.showBellIcon && (
                            <NotificationsActiveOutlinedIcon className="chat-icon" />
                          )}
                          {item.msgCount > 0 && (
                            <Box className="badgeIcon chat-icon">
                              <Typography fontSize={15}>{item.msgCount}</Typography>
                            </Box>
                          )}
                        </Box>
                      </Box>
                    </Box>
                  );
                })}
            </>
          )}

          {searchKeyword && serchedUserData && serchedUserData.length === 0 ? (
            <Typography
              variant="h6"
              sx={{
                textAlign: "center",
                paddingTop: "20px",
                fontWeight: "700",
              }}
            >
              No Data Found.
            </Typography>
          ) : (
            serchedUserData &&
            serchedUserData.length > 0 &&
            serchedUserData.map((item, index) => {
              if (item?.id === currentUser?.uid) return;
              return (
                <Box
                  onClick={() => handleSelect(item)}
                  key={index}
                  sx={
                    (openedUser?.uid === item?.uid && openPage === "individual" && isActive)
                      ?

                      {
                        marginLeft: "10px !important",
                        cursor: "pointer",
                        padding: "8px",
                        paddingTop: "15px",
                        display: "flex",
                        justifyContent: "space-between",
                        alignItems: "center",
                        opacity: "1",
                        backgroundColor: "#E67E22",
                        borderRadius: "10px"
                      }
                      :
                      {
                        marginLeft: "10px !important",
                        cursor: "pointer",
                        padding: "8px",
                        paddingTop: "15px",
                        display: "flex",
                        justifyContent: "space-between",
                        alignItems: "center",
                        opacity: "0.5",
                        "@media (max-width: 430px)": {
                          backgroundColor: "#F5F5F5",
                          marginBottom: "10px",
                          marginLeft: "10px",
                          marginRight: "10px",
                          borderRadius: "10px"
                        }

                      }}
                >
                  <Box
                    sx={{
                      display: "flex",
                      alignItems: "center",
                      padding: "6px",
                    }}
                  >
                    <Avatar alt={item.displayName}
                      src={`${URL}/static/public/userimages/${item?.photoURL}`} />
                    <Box>
                      <Typography
                        className="userName"
                        fontWeight="fontWeightBold"
                        sx={
                          (openedUser?.uid === item?.uid && openPage === "individual" && isActive)
                            ?
                            { color: "white !important", paddingLeft: "1.2rem" }
                            :
                            { paddingLeft: "1.2rem" }}
                      >{item.displayName}</Typography>
                      <Typography
                        sx={{ paddingLeft: "1.2rem", fontSize: "15px" }}
                      >
                        {item.desc}
                      </Typography>
                    </Box>
                  </Box>
                  <Box sx={{ textAlign: "right" }}>
                    <Box className="timeStamp">{item.time}</Box>
                    <Box
                      sx={{
                        textAlign: "right",
                        display: "flex",
                        alignItems: "center",
                        justifyContent: "flex-end",
                      }}
                    >
                      {item.showMediaIcon && (
                        <InsertLinkOutlinedIcon className="chat-icon" />
                      )}
                      {item.showBellIcon && (
                        <NotificationsActiveOutlinedIcon className="chat-icon" />
                      )}
                      {item.msgCount > 0 && (
                        <Box className="badgeIcon chat-icon">
                          <Typography fontSize={15}>{item.msgCount}</Typography>
                        </Box>
                      )}
                    </Box>
                  </Box>
                </Box>
              );
            })
          )}
          <Box
            sx={{
              position: "fixed",
              bottom: 10,
              left: "50%",
              transform: "translateX(-50%)",
              zIndex: 1000
            }}
          >
            {roleID === 3 && (
              <div title="Create Group">
                <button
                  className="create-group-button
                  bg-[#E67E22]
                  border-none
                  rounded-[10%]
                  w-[40px] h-[40px]
                  flex justify-center items-center
                  cursor-pointer
                  shadow-[0px_8px_20px_rgba(0,0,0,0.2)]
                  "
                  onClick={() => setOpenPageOne("stepOne")}
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 20 20"
                    width="28"
                    height="28"
                    fill="white"
                  >
                    <path d="M10 5a1 1 0 0 1 1 1v3h3a1 1 0 1 1 0 2h-3v3a1 1 0 1 1-2 0v-3h-3a1 1 0 1 1 0-2h3v-3a1 1 0 0 1 1-1z" />
                  </svg>
                </button>
              </div>
            )}
          </Box>
        </Paper>
      </Box>
    </div>
  );
}