import { useEffect, useState, use<PERSON>emo, useCallback } from "react";
import { <PERSON><PERSON> } from "../../components/ui/button";
import { Input } from "../../components/ui/input";
import { Card, CardContent, CardHeader } from "../../components/ui/card";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "../../components/ui/select";
import {
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableHeader,
	TableRow,
} from "../../components/ui/table";
import { Edit, Trash2, Plus } from "lucide-react";
import { GoalWithoutVolumeDialog } from "../../components/admin/goal-without-volume-dialog";
import { DeleteConfirmDialog } from "../../components/admin/delete-confirm-dialog";
import Header from "../../components/Header";
import {
	getGoalWithoutVolumeData,
	deleteGoalWithoutVolumeData,
} from "../../API/api-endpoint";
import Swal from "sweetalert2";

const GoalWithoutVolume = () => {
	const [goalData, setGoalData] = useState([]);
	const [isLoading, setIsLoading] = useState(true);
	const [searchTerm, setSearchTerm] = useState("");
	const [selectedActivityFilter, setSelectedActivityFilter] = useState("All");
	const [currentPage, setCurrentPage] = useState(1);
	const [showDialog, setShowDialog] = useState(false);
	const [editingItem, setEditingItem] = useState(null);
	const [deleteId, setDeleteId] = useState(null);
	const pageSize = 10;

	const fetchData = useCallback(async () => {
		try {
			setIsLoading(true);
			const response = await getGoalWithoutVolumeData();

			if (response) {
				setGoalData(response || []);
			} else {
				console.log("Response is empty or invalid");
				setGoalData([]);
			}
		} catch (error) {
			console.error("Error fetching goal without volume data:", error);
			Swal.fire({
				title: "Error",
				text: "Failed to fetch goal without volume data. Please try again.",
				icon: "error",
				timer: 3000,
				showConfirmButton: false,
			});
			setGoalData([]);
		} finally {
			setIsLoading(false);
		}
	}, []);

	useEffect(() => {
		fetchData();
	}, [fetchData]);

	const filteredData = useMemo(() => {
		return goalData.filter((item) => {
			const matchesSearch =
				!searchTerm.trim() ||
				item?.goal?.toLowerCase().includes(searchTerm.toLowerCase());

			const matchesActivity =
				selectedActivityFilter === "All" ||
				item?.goal?.toLowerCase().trim() ===
					selectedActivityFilter.toLowerCase();

			return matchesSearch && matchesActivity;
		});
	}, [goalData, searchTerm, selectedActivityFilter]);

	const paginatedData = useMemo(() => {
		const startIndex = (currentPage - 1) * pageSize;
		const endIndex = startIndex + pageSize;
		return filteredData.slice(startIndex, endIndex);
	}, [filteredData, currentPage, pageSize]);

	const totalPages = Math.ceil(filteredData.length / pageSize);

	const handleSearch = (value) => {
		setSearchTerm(value);
		setCurrentPage(1);
	};

	const handleActivityFilterChange = (value) => {
		setSelectedActivityFilter(value);
		setCurrentPage(1);
	};

	const handleCreate = () => {
		setEditingItem(null);
		setShowDialog(true);
	};

	const handleEdit = (item) => {
		setEditingItem(item);
		setShowDialog(true);
	};

	const handleDelete = async (id) => {
		try {
			const response = await deleteGoalWithoutVolumeData(id);
			Swal.fire({
				title: "Success",
				text: "Goal without volume deleted successfully",
				icon: "success",
				timer: 2000,
				showConfirmButton: false,
			});

			setCurrentPage(1);
			fetchData();
		} catch (error) {
			console.error("Error deleting goal without volume:", error);
			Swal.fire({
				title: "Error",
				text: "Failed to delete goal without volume. Please try again.",
				icon: "error",
				timer: 3000,
				showConfirmButton: false,
			});
		}
	};

	const handleDialogSuccess = () => {
		setShowDialog(false);
		setEditingItem(null);
		fetchData();
	};

	return (
		<div>
			<Header />
			<div className='mx-auto p-6 max-w-[1400px] mt-16'>
				<Card>
					<CardHeader className='bg-orange-50 border-b flex flex-col gap-4'>
						<div className='flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4'>
							<div>
								<h1 className='text-2xl font-bold text-orange-900'>
									Goal Without Volume
								</h1>
								<p className='text-orange-700 mt-1 text-sm'>
									Manage goals without volume data and workout
									sequences
								</p>
							</div>
						</div>
						<div className='flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4'>
							<div className='flex gap-3'>
								<Button
									onClick={handleCreate}
									className='bg-orange-600 hover:bg-orange-700 text-white'
								>
									<Plus className='h-4 w-4 mr-2' />
									Create Fitness Goal
								</Button>
							</div>

							<div className='flex gap-3'>
								<Select
									value={selectedActivityFilter}
									onValueChange={handleActivityFilterChange}
								>
									<SelectTrigger className='w-[180px] text-sm'>
										<SelectValue placeholder='Filter by Activity' />
									</SelectTrigger>
									<SelectContent className='bg-white'>
										<SelectItem value='All'>All</SelectItem>
										<SelectItem value='Swim'>
											Swim
										</SelectItem>
										<SelectItem value='Bike'>
											Bike
										</SelectItem>
										<SelectItem value='Run'>Run</SelectItem>
										<SelectItem value='Nutrition'>
											Nutrition
										</SelectItem>
										<SelectItem value='Equipment'>
											Equipment
										</SelectItem>
									</SelectContent>
								</Select>
								<Input
									placeholder='Search by goal name...'
									value={searchTerm}
									onChange={(e) =>
										handleSearch(e.target.value)
									}
									className='w-full text-sm'
								/>
							</div>
						</div>
					</CardHeader>
					<CardContent className='p-0'>
						<div className='overflow-x-auto'>
							<Table className='min-w-full w-full'>
								<TableHeader>
									<TableRow className='bg-blue-600 hover:bg-blue-600'>
										<TableHead className='text-white font-semibold'>
											Sr No
										</TableHead>
										<TableHead className='text-white font-semibold'>
											Activity Name
										</TableHead>
										<TableHead className='text-white font-semibold'>
											Goal Name
										</TableHead>
										<TableHead className='text-white font-semibold'>
											First
										</TableHead>
										<TableHead className='text-white font-semibold'>
											Second
										</TableHead>
										<TableHead className='text-white font-semibold'>
											Third
										</TableHead>
										<TableHead className='text-white font-semibold'>
											Fourth
										</TableHead>
										<TableHead className='text-white font-semibold'>
											Fifth
										</TableHead>
										<TableHead className='text-white font-semibold'>
											Sixth
										</TableHead>
										<TableHead className='text-white font-semibold'>
											Seventh
										</TableHead>
										<TableHead className='text-white font-semibold'>
											Actions
										</TableHead>
									</TableRow>
								</TableHeader>
								<TableBody>
									{isLoading ? (
										<TableRow>
											<TableCell
												colSpan={11}
												className='text-center py-8'
											>
												Loading...
											</TableCell>
										</TableRow>
									) : paginatedData.length > 0 ? (
										paginatedData.map((item, index) => (
											<TableRow
												key={item.id || index}
												className='hover:bg-gray-50'
											>
												<TableCell className='font-medium'>
													{(currentPage - 1) *
														pageSize +
														index +
														1}
												</TableCell>
												<TableCell>
													{item?.activity
														?.activity_name ||
														"N/A"}
												</TableCell>
												<TableCell>
													{item?.goal || "N/A"}
												</TableCell>
												<TableCell>
													{item["1"]?.subworkout ||
														"N/A"}
												</TableCell>
												<TableCell>
													{item["2"]?.subworkout ||
														"N/A"}
												</TableCell>
												<TableCell>
													{item["3"]?.subworkout ||
														"N/A"}
												</TableCell>
												<TableCell>
													{item["4"]?.subworkout ||
														"N/A"}
												</TableCell>
												<TableCell>
													{item["5"]?.subworkout ||
														"N/A"}
												</TableCell>
												<TableCell>
													{item["6"]?.subworkout ||
														"N/A"}
												</TableCell>
												<TableCell>
													{item["7"]?.subworkout ||
														"N/A"}
												</TableCell>
												<TableCell>
													<div className='flex gap-2'>
														<Button
															variant='ghost'
															size='sm'
															onClick={() =>
																handleEdit(item)
															}
															className='text-blue-600 hover:text-blue-800'
														>
															<Edit className='h-4 w-4' />
														</Button>
														<Button
															variant='ghost'
															size='sm'
															onClick={() =>
																setDeleteId(
																	item.id
																)
															}
															className='text-red-600 hover:text-red-800'
														>
															<Trash2 className='h-4 w-4' />
														</Button>
													</div>
												</TableCell>
											</TableRow>
										))
									) : (
										<TableRow>
											<TableCell
												colSpan={11}
												className='text-center py-8 text-gray-500'
											>
												No goals found
											</TableCell>
										</TableRow>
									)}
								</TableBody>
							</Table>
						</div>

						{totalPages > 1 && (
							<div className='flex justify-center gap-2 p-4 border-t'>
								<Button
									variant='outline'
									size='sm'
									onClick={() =>
										setCurrentPage(
											Math.max(1, currentPage - 1)
										)
									}
									disabled={currentPage === 1}
								>
									Previous
								</Button>

								{Array.from({ length: 5 }, (_, i) => {
									const startPage =
										Math.floor((currentPage - 1) / 5) * 5 +
										1;
									const page = startPage + i;
									if (page > totalPages) return null;

									return (
										<Button
											key={page}
											variant={
												currentPage === page
													? "default"
													: "outline"
											}
											size='sm'
											onClick={() => setCurrentPage(page)}
											className={
												currentPage === page
													? "bg-orange-500 hover:bg-orange-600 text-white"
													: ""
											}
										>
											{page}
										</Button>
									);
								})}

								<Button
									variant='outline'
									size='sm'
									onClick={() =>
										setCurrentPage(
											Math.min(
												totalPages,
												currentPage + 1
											)
										)
									}
									disabled={currentPage === totalPages}
								>
									Next
								</Button>
							</div>
						)}
					</CardContent>
				</Card>
			</div>

			<GoalWithoutVolumeDialog
				open={showDialog}
				onClose={() => setShowDialog(false)}
				onSuccess={handleDialogSuccess}
				editingItem={editingItem}
			/>

			<DeleteConfirmDialog
				open={!!deleteId}
				onOpenChange={(open) => !open && setDeleteId(null)}
				onConfirm={() => {
					handleDelete(deleteId);
					setDeleteId(null);
				}}
				title='Delete Goal Without Volume'
				description='Are you sure you want to delete this goal without volume? This action cannot be undone.'
			/>
		</div>
	);
};

export default GoalWithoutVolume;
