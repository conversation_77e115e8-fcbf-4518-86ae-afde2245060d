import { useState, useEffect } from "react";
import { But<PERSON> } from "../ui/button";
import { Input } from "../ui/input";
import { Label } from "../ui/label";
import {
	<PERSON><PERSON>,
	DialogContent,
	<PERSON><PERSON>Header,
	<PERSON><PERSON>Title,
	DialogFooter,
} from "../ui/dialog";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "../ui/select";
import { CreatePrograms, updatePrograms } from "../../API/api-endpoint";
import Swal from "sweetalert2";

export function ProgramDialog({
	open,
	onClose,
	onSave,
	editingProgram,
	activities = [],
}) {
	const [formData, setFormData] = useState({
		program_name: "",
		activity_id: "",
	});
	const [isLoading, setIsLoading] = useState(false);

	useEffect(() => {
		if (open) {
			if (editingProgram) {
				setFormData({
					program_name: editingProgram.program_name || "",
					activity_id: editingProgram.activity_id?.toString() || "",
				});
			} else {
				setFormData({
					program_name: "",
					activity_id: "",
				});
			}
		}
	}, [open, editingProgram]);

	const handleInputChange = (field, value) => {
		setFormData((prev) => ({
			...prev,
			[field]: value,
		}));
	};

	const handleSubmit = async (e) => {
		e.preventDefault();

		if (!formData.program_name.trim()) {
			Swal.fire({
				title: "Error",
				text: "Program name is required",
				icon: "error",
				timer: 1800,
				showConfirmButton: false,
			});
			return;
		}

		if (!formData.activity_id) {
			Swal.fire({
				title: "Error",
				text: "Activity is required",
				icon: "error",
				timer: 1800,
				showConfirmButton: false,
			});
			return;
		}

		setIsLoading(true);

		try {
			let response;

			if (editingProgram) {
				const updateData = {
					...formData,
					program_id: editingProgram.program_id,
					activity_ids: formData.activity_id,
					status: 0,
				};
				response = await updatePrograms(updateData);
			} else {
				const createData = {
					...formData,
					activity_ids: formData.activity_id,
				};
				response = await CreatePrograms(createData);
			}

			if (response?.status) {
				Swal.fire({
					title: "Success",
					text:
						response.message ||
						`Program ${
							editingProgram ? "updated" : "created"
						} successfully`,
					icon: "success",
					timer: 1800,
					showConfirmButton: false,
				});
				onSave();
				onClose();
			} else {
				Swal.fire({
					title: "Error",
					text:
						response?.message ||
						`Failed to ${
							editingProgram ? "update" : "create"
						} program`,
					icon: "error",
				});
			}
		} catch (error) {
			console.error("Error saving program:", error);
			Swal.fire({
				title: "Error",
				text: `Failed to ${
					editingProgram ? "update" : "create"
				} program`,
				icon: "error",
			});
		} finally {
			setIsLoading(false);
		}
	};

	return (
		<Dialog open={open} onOpenChange={onClose}>
			<DialogContent className='sm:max-w-[500px] bg-white'>
				<DialogHeader>
					<DialogTitle className='text-xl font-semibold text-gray-900'>
						{editingProgram ? "Edit Program" : "Create New Program"}
					</DialogTitle>
				</DialogHeader>

				<form onSubmit={handleSubmit} className='space-y-6'>
					<div className='space-y-4'>
						<div className='space-y-2'>
							<Label
								htmlFor='activity_id'
								className='text-sm font-semibold'
							>
								Activity
							</Label>
							<Select
								value={formData.activity_id}
								onValueChange={(value) =>
									handleInputChange("activity_id", value)
								}
							>
								<SelectTrigger className='w-full'>
									<SelectValue placeholder='Select an activity' />
								</SelectTrigger>
								<SelectContent className='bg-white'>
									{activities.map((activity) => (
										<SelectItem
											key={activity.id}
											value={activity.id.toString()}
										>
											{activity.activity_name}
										</SelectItem>
									))}
								</SelectContent>
							</Select>
						</div>

						<div className='space-y-2'>
							<Label
								htmlFor='program_name'
								className='text-sm font-semibold'
							>
								Program Name{" "}
							</Label>
							<Input
								id='program_name'
								type='text'
								placeholder='Enter program name'
								value={formData.program_name}
								onChange={(e) =>
									handleInputChange(
										"program_name",
										e.target.value
									)
								}
								className='w-full text-sm'
								required
							/>
						</div>
					</div>

					<DialogFooter className='flex gap-3'>
						<Button
							type='button'
							variant='outline'
							onClick={onClose}
							disabled={isLoading}
						>
							Cancel
						</Button>
						<Button
							type='submit'
							className='bg-orange-600 hover:bg-orange-700 text-white'
							disabled={isLoading}
						>
							{isLoading
								? "Saving..."
								: editingProgram
								? "Update Program"
								: "Create Program"}
						</Button>
					</DialogFooter>
				</form>
			</DialogContent>
		</Dialog>
	);
}
