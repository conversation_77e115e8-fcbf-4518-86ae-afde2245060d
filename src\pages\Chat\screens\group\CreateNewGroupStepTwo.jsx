import { Avatar, Box, Button, Paper, Typography } from "@mui/material";
import React, { useMemo, useRef, useState } from "react";
import "../../styles/chatlanding.css";
import "./../../styles/newchat/newChat.css";
import NewGroupStepTwoNav from "../../components/navbar/NewGroupStepTwoNav";
import SearchIcon from '@mui/icons-material/Search';
import CheckIcon from "@mui/icons-material/Check";
import { useNavigate } from "react-router-dom";
import { useSelector, useDispatch } from "react-redux";
import {
  get,
  getDatabase,
  push,
  ref,
  serverTimestamp,
  set,
  update,
} from "firebase/database";

import {
  getStorage,
  ref as storageRef,
  uploadBytes,
  getDownloadURL,
} from "firebase/storage";
import EmojiPicker from "emoji-picker-react";
import { useEffect } from "react";
import Swal from "sweetalert2";
import axios from 'axios';
import InsertEmoticonIcon from "@mui/icons-material/InsertEmoticon";
import Header from "../../../../components/Header";
import { SetGroupData } from "../../redux/action/groupAction";
import { URL } from "../../../../../src/API/api-endpoint";

export default function CreateNewGroupStepTwo({ setOpenPageOne }) {

  const paper = {
    padding: "0rem",
    overflowY: "scroll",
    borderRadius: "8px",
  };

  const [groupName, setGroupName] = useState("");
  const [groupDescription, setGroupDescription] = useState("");
  const [profilePicture, setProfilePicture] = useState(null);
  const [showEmojis, setShowEmojis] = useState(false);
  const [isMobile, setIsMobile] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const { storedUserToCreateGroup } = useSelector((state) => state.users);
  const { currentUser } = useSelector((state) => state.auth);
  const [selectedImage, setSelectedImage] = useState(null);

  const showEmojisRef = useRef(showEmojis);

  const handleImageChange = (event) => {
    const file = event.target.files[0];
    setProfilePicture(file);
    setSelectedImage(URL.createObjectURL(file));
  };

  const checkGroupExist = async (channelName, groupName) => {
    const authToken = localStorage.getItem("token");
    const response = await axios.get(`${URL}/group-chat`,{
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `${authToken}`
      }
    });

    const data = response.data.data;
    let exists = false;
    
    for (let i = 0; i < data.length; i++) {
      if (data[i].displayName === groupName && data[i].isActive === true) {
        exists = true;
        break;
      }
    }
    
    return exists;
  };

  const handleCreateGroup = async () => {
    const usersUIDs = storedUserToCreateGroup
      .filter((user) => user.uid)
      .map((user) => user.uid);
    
    const usersEMAIL = storedUserToCreateGroup
      .filter((user) => user.uid)
      .map((user) => user.email);
      
    const emailListObject = storedUserToCreateGroup.find((obj) => obj.emailList);
    const emailList = emailListObject?.emailList;

    const db = getDatabase();
    const newRef = push(ref(db, "groups"));
    const groupId = newRef.key;

    const downloadURL =
      profilePicture && (await uploadImageAndGetLink(profilePicture));

    function formatDate(date) {
      const options = { year: "numeric", month: "2-digit", day: "2-digit" };
      const dateString = date.toLocaleDateString("en-US", options);

      const timeOptions = { hour: "numeric", minute: "2-digit", hour12: true };
      const timeString = date.toLocaleTimeString("en-US", timeOptions);
      
      return `${dateString} ${timeString}`;
    }

    const currentDate = new Date();
    const formattedDate = formatDate(currentDate);
    const channelName = `${groupName}-${Math.floor(1000 + Math.random() * 9000)}`;
    
    const apiData = {
      channelName: channelName,
      displayName: groupName,
      channelDescription: `${groupDescription}`
    };
  
    try {
      const groupExists = await checkGroupExist(channelName, groupName);
      
      if (groupExists) {
        Swal.fire({
          title: "Caution!",
          text: "Group already exists",
          icon: "warning",
        });
      } else {
          // ---------- Commented the code for pubnub -----------
          // pubnub.subscribe({ channels: [channelName] });
          // pubnub.objects.setChannelMetadata({
          //   channel: channelName,
          //   data: {
          //     name: groupName,
          //     description: groupDescription,
          //     custom: { members: usersUIDs.join(",") },
          //   },
          // });
          // -----------------------------------------------------

        const authToken = localStorage.getItem("token");
        const response = await axios.post(`${URL}/group-chat`, apiData, {
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `${authToken}`
          }
        });
        
        const groupId = parseInt(response.data.data.id);
        
        if (response.status === 200) {
          let respList = [];
  
          for (const email of usersEMAIL) {
            const response = await axios.post(
              `${URL}/subscribe-group/add-member`,
              { email, groupId },
              {
                headers: {
                  Authorization: `${authToken}`,
                  "Content-Type": "application/json",
                },
              }
            );
            respList.push(response);
          }
  
          for (const resp of respList) {
            if (resp.status === 200) {
              console.log("Member added successfully:", resp.data);
            } else {
              console.error("Error adding member to group:", resp.data);
            }
          }
  
          const groupData = {
            groupId,
            createdById: currentUser.uid,
            createdByName: currentUser.displayName,
            createdAt: formattedDate,
            groupProfileURL: downloadURL,
            groupName: groupName,
            groupDescription: groupDescription,
            groupMembers: storedUserToCreateGroup.some(
              (obj) => obj.uid === currentUser.uid
            )
              ? usersUIDs
              : [currentUser.uid, ...usersUIDs],
          };
  
          await set(ref(db, `groups/${groupId}`), groupData);
  
          storedUserToCreateGroup.map(async (item) => {
            const grpRef = ref(db, "usersGroups/" + item.uid);
            const grpSnapshot = await get(grpRef);
            const grpPreData = grpSnapshot.val();
            const existingGrps = grpPreData || [];
  
            await set(ref(db, `usersGroups/${item.uid}`), [
              ...existingGrps,
              groupData,
            ]);
          });
  
          storedUserToCreateGroup.map(async (item) => {
            const userChatsRef = ref(db, `userChats/${item.uid}`);
  
            await update(userChatsRef, {
              [`${groupId}/chatType`]: "group",
              [`${groupId}/groupId`]: groupId,
              [`${groupId}/date`]: serverTimestamp(),
              [`${groupId}/isMsgReqAccepted`]: true,
            });
          });
  
          if (emailList.length > 0) {
            const inviteData = {
              emails: emailList,
              groupId: response.data.data.id,
            };
            const authToken = localStorage.getItem("token");
            await axios.post(`${URL}/group-invite`, inviteData, {
              headers: {
                'Content-Type': 'application/json',
                'Authorization': `${authToken}`
              }
            });
          }
  
          Swal.fire({
            title: "Success",
            text: "New group created!",
            icon: "success",
          });
  
          setOpenPageOne("home");
        } else {
          console.log("Failed to create group via API:", response.statusText);
        }
      }
    } catch (error) {
      console.log("Error in group creation:", error);
    }
  };

  const uploadImageAndGetLink = async (file) => {
    const storage = getStorage();
    const storageReference = storageRef(storage, "images/" + file.name);

    const database = getDatabase();
    const databaseReference = ref(database, "images");

    try {
      await uploadBytes(storageReference, file);

      const downloadURL = await getDownloadURL(storageReference);

      console.log("Image uploaded and URL saved successfully.");

      return downloadURL;
    } catch (error) {
      console.error("Error uploading image:", error);
      throw error;
    }
  };

  const handleGrpNameChange = (event) => {
    const { value } = event.target;

    if (value.length <= 100) {
      setGroupName(value);
    }
  };

  const handleGrpDescriptionChange = (event) => {
    const { value } = event.target;
    setGroupDescription(value);
  };
  const handleEmojiClick = (emojiObject) => {
    setGroupName(groupName + emojiObject.emoji);

  };
  useEffect(() => {
    showEmojisRef.current = showEmojis;

  }, [showEmojis]);

  const handleClickOutside = (event) => {
    if (showEmojisRef.current) {
      showEmojis && setShowEmojis(false);
    }
  };

  useEffect(() => {
    let timerId = null;

    if (showEmojis) {
      timerId = setTimeout(() => {
        document.addEventListener("click", handleClickOutside);
      }, 200);
    }

    return () => {
      clearTimeout(timerId);
      document.removeEventListener("click", handleClickOutside);
    };
  }, [showEmojis, handleClickOutside]);

  useEffect(() => {
    const checkScreenWidth = () => {
      const isMobileScreen = window.innerWidth <= 768;
      setIsMobile(isMobileScreen);
    };
    window.addEventListener("resize", checkScreenWidth);
    checkScreenWidth();
    return () => {
      window.removeEventListener("resize", checkScreenWidth);
    };
  }, []);

  const filteredAndSortedData = useMemo(() => {
    const filteredData = storedUserToCreateGroup?.filter(obj =>
      obj.displayName?.toLowerCase().includes(searchTerm.toLowerCase())
    );

    const sortedData = [...filteredData].sort((a, b) =>
      a.displayName?.localeCompare(b.displayName)
    );

    return sortedData;
  }, [storedUserToCreateGroup, searchTerm]);

  const emailList = useMemo(() => {
    return storedUserToCreateGroup.find(obj => obj.emailList)?.emailList || [];
  }, [storedUserToCreateGroup]);

  const filteredAndSortedEmails = useMemo(() => {
    const filteredEmails = emailList.filter(email =>
      email.toLowerCase().includes(searchTerm.toLowerCase())
    );

    const sortedEmails = filteredEmails.sort((a, b) => a.localeCompare(b));

    return sortedEmails;
  }, [emailList, searchTerm]);

  const handleSearchChange = event => {
    setSearchTerm(event.target.value);
  };

  return (
    <>
      <Header />
      <Box
        sx={{ display: "flex", justifyContent: "center", alignItems: "center" }}
      >
        <Paper className='stepTwoGroupCreation chatPaper' sx={{ flexGrow: 1 }} elevation={10} style={paper}>
          <NewGroupStepTwoNav setOpenPageOne={setOpenPageOne} />

          <Box
            sx={{
              backgroundColor: "#F5F5F5",
              padding: "8px",
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
              marginTop: "5px"
            }}
          >
            <Box
              sx={{
                width: "100%",
                display: "flex",
                alignItems: "center",
                padding: "6px",
                overflowX: "auto",
                position: "relative",
              }}
            >
              <Box
                style={{
                  position: "relative",
                  marginRight: "1.5rem",
                  overflowX: "hidden",
                }}
              >
                <Avatar
                  onClick={() =>
                    document.getElementById("create-grp-profile").click()
                  }
                  sx={{
                    height: 58,
                    width: 58,
                    backgroundColor: "#B7B6B1",
                    cursor: "pointer",
                  }}
                  alt="Remy Sharp"
                  src={
                    selectedImage
                      ? selectedImage
                      : "/images/camera-white-icon.png"
                  }
                />
                <input
                  hidden
                  onChange={handleImageChange}
                  type="file"
                  id="create-grp-profile"
                />
              </Box>
              <input
                value={groupName}
                onChange={handleGrpNameChange}
                placeholder="Type here..."
                type="text"
                className="add-grp-name-input"
              />
              <Typography className="group-name-length">
                {100 - groupName.length}
              </Typography>
            </Box>

            <InsertEmoticonIcon onClick={() => setShowEmojis((pre) => !pre)} />
          </Box>

          {/* <Box
          sx={{
            backgroundColor: "#F5F5F5",
            padding: "8px",
            marginTop: "8px",
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
          }}
        >
          <input
            value={groupDescription}
            onChange={handleGrpDescriptionChange}
            placeholder="Add group description..."
            type="text"
            className="add-grp-description-input"
          />
        </Box> */}

          <Box>

            {emailList.length > 0 && (
              <>
                <Typography className="select-buddies-text">
                  Added emails
                </Typography>

                {filteredAndSortedEmails?.reduce((acc, email, index, arr) => {
                  if (index % 2 === 0) {
                    acc.push(arr.slice(index, index + 2));
                  }
                  return acc;
                }, []).map((row, rowIndex) => (
                  <Box
                    key={rowIndex}
                    sx={{
                      display: "flex",
                      justifyContent: "space-between",
                      gap: "16px",
                      marginBottom: "12px",
                    }}
                  >
                    {row.map((email, index) => (
                      <Box
                        className="email-row"
                        key={index}
                        sx={{
                          flex: 1,
                          display: "flex",
                          alignItems: "center",
                          padding: "6px",
                          backgroundColor: "#f9f9f9",
                          borderRadius: "8px",
                        }}
                      >
                        <Avatar sx={{ backgroundColor: "#3f51b5", color: "#fff" }}>
                          {email.charAt(0).toUpperCase()}
                        </Avatar>
                        <Box sx={{ paddingLeft: "1.2rem" }}>
                          <Typography
                            className="emailText"
                            fontWeight="fontWeightBold"
                            sx={{ fontSize: "14px" }}
                          >
                            {email}
                          </Typography>
                        </Box>
                      </Box>
                    ))}
                  </Box>
                ))}
              </>
            )}
            <Box style={{ position: "relative", textAlign: "center" }}>
              <input
                placeholder="Search Buddies"
                type="text"
                onChange={handleSearchChange}
                className="search-buddy-input"
              />
              <Box className="search-icon-parent" style={{ textAlign: "center", backgroundColor: "#E67E22", color: 'white' }}>
                <SearchIcon />
              </Box>
            </Box>
          </Box>

          {filteredAndSortedData?.map((item, index) => {
            if (item.uid === currentUser.uid) return;
            return (
              <Box
                className="profile-row"
                key={index}
                sx={{
                  padding: "8px",
                  paddingTop: "12px",
                  display: "flex",
                  justifyContent: "space-between",
                  alignItems: "center",
                }}
              >
                <Box
                  sx={{ display: "flex", alignItems: "center", padding: "6px" }}
                >
                  <Avatar alt="Remy Sharp" src={item.photoURL} />
                  <Box>
                    <Typography
                      className="userName"
                      fontWeight="fontWeightBold"
                      sx={{ paddingLeft: "1.2rem", fontSize: "14px" }}
                    >
                      {item.displayName}
                    </Typography>
                  </Box>
                </Box>
              </Box>
            );
          })}

          <Box className="sticky-next-btn">
            <Button
              onClick={handleCreateGroup}
              disabled={!(groupName.length > 0)}
              style={{ boxShadow: "-2px 4px 10px rgba(0, 0, 0, 0.25)" }}
              className={
                groupName.length > 0 ? "next-mui-btn-selected" : "next-mui-btn"
              }
            >
              Create
              <CheckIcon
                sx={
                  groupName.length > 0
                    ? {
                      fontSize: "18px",
                      paddingLeft: "0.24rem",
                      color: "white",
                      marginTop: "-1px",
                    }
                    : {
                      fontSize: "18px",
                      paddingLeft: "0.24rem",
                      color: "#FFFFFF",
                      marginTop: "-1px",
                    }
                }
              />
            </Button>
          </Box>

          <Box
            className="sticky-footer-parent"
            style={{
              boxShadow: "0px -4px 10px rgba(0, 0, 0, 0.1)",
              marginBottom: 0,
            }}
          >
            {showEmojis && (
              <Box onClick={(e) => e.stopPropagation()}>
                <EmojiPicker
                  onEmojiClick={handleEmojiClick}
                  width={isMobile ? "100vw" : "33vw"}
                  height={"30rem"}
                />
              </Box>
            )}
          </Box>
        </Paper>
      </Box>
    </>
  );
}

