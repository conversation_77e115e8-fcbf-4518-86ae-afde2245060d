import { useState, useEffect } from "react";
import { But<PERSON> } from "../ui/button";
import { Input } from "../ui/input";
import { Label } from "../ui/label";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "../ui/dialog";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "../ui/select";
import {
	createRaceCalculation,
	updateRaceCalculation,
} from "../../API/api-endpoint";
import Swal from "sweetalert2";

export function RaceCalculatorDialog({
	open,
	onClose,
	onSave,
	editingRaceCalculation,
	activities = [],
}) {
	const [formData, setFormData] = useState({
		goal_name: "",
		quota: "",
		type: "",
	});
	const [isLoading, setIsLoading] = useState(false);

	// Reset form when dialog opens/closes or when editing different race calculation
	useEffect(() => {
		if (open) {
			if (editingRaceCalculation) {
				// Editing existing race calculation
				setFormData({
					goal_name: editingRaceCalculation.goal_name || "",
					quota: editingRaceCalculation.quota || "",
					type: editingRaceCalculation.type || "",
				});
			} else {
				// Creating new race calculation
				setFormData({
					goal_name: "",
					quota: "",
					type: "",
				});
			}
		}
	}, [open, editingRaceCalculation]);

	// Handle input changes
	const handleInputChange = (field, value) => {
		setFormData((prev) => ({
			...prev,
			[field]: value,
		}));
	};

	// Handle form submission (following Program dialog pattern)
	const handleSubmit = async (e) => {
		e.preventDefault();

		// Sweet Alert validation - exactly like Program dialog
		if (!formData.goal_name.trim()) {
			Swal.fire({
				title: "Error",
				text: "Goal name is required",
				icon: "error",
				timer: 1800,
				showConfirmButton: false,
			});
			return;
		}

		if (!formData.quota.trim()) {
			Swal.fire({
				title: "Error",
				text: "Distance/Quota is required",
				icon: "error",
				timer: 1800,
				showConfirmButton: false,
			});
			return;
		}

		if (!formData.type) {
			Swal.fire({
				title: "Error",
				text: "Activity type is required",
				icon: "error",
				timer: 1800,
				showConfirmButton: false,
			});
			return;
		}

		setIsLoading(true);

		try {
			let response;
			const submitData = { ...formData };

			if (editingRaceCalculation?.id) {
				// Update existing race calculation
				submitData.id = editingRaceCalculation.id;
				response = await updateRaceCalculation(submitData);
			} else {
				// Create new race calculation
				response = await createRaceCalculation(submitData);
			}

			if (response?.status) {
				Swal.fire({
					title: "Success",
					text:
						response.message ||
						`Race calculation ${
							editingRaceCalculation?.id ? "updated" : "created"
						} successfully`,
					icon: "success",
					timer: 1800,
					showConfirmButton: false,
				});
				onSave();
				onClose();
			} else {
				Swal.fire({
					title: "Error",
					text:
						response?.message || "Failed to save race calculation",
					icon: "error",
				});
			}
		} catch (error) {
			console.error("Error saving race calculation:", error);
			Swal.fire({
				title: "Error",
				text: "Failed to save race calculation",
				icon: "error",
			});
		} finally {
			setIsLoading(false);
		}
	};

	const handleClose = () => {
		if (!isLoading) {
			onClose();
		}
	};

	return (
		<Dialog open={open} onOpenChange={handleClose}>
			<DialogContent className='sm:max-w-[500px] bg-white'>
				<DialogHeader>
					<DialogTitle className='text-xl font-bold text-orange-900'>
						{editingRaceCalculation?.id ? "Edit" : "Create"} Race
						Calculation
					</DialogTitle>
				</DialogHeader>

				<form onSubmit={handleSubmit} className='space-y-6'>
					{/* Goal Name */}
					<div className='space-y-2'>
						<Label
							htmlFor='goal_name'
							className='text-sm font-medium'
						>
							Goal Name
						</Label>
						<Input
							id='goal_name'
							type='text'
							className='text-sm w-full'
							value={formData.goal_name}
							onChange={(e) =>
								handleInputChange("goal_name", e.target.value)
							}
							placeholder='Enter goal name'
							disabled={isLoading}
						/>
					</div>

					{/* Distance/Quota */}
					<div className='space-y-2'>
						<Label htmlFor='quota' className='text-sm font-medium'>
							Distance/Quota{" "}
						</Label>
						<Input
							id='quota'
							type='text'
							className='text-sm w-full'
							value={formData.quota}
							onChange={(e) =>
								handleInputChange("quota", e.target.value)
							}
							placeholder='Enter distance or quota'
							disabled={isLoading}
						/>
					</div>

					{/* Activity Type */}
					<div className='space-y-2'>
						<Label htmlFor='type' className='text-sm font-medium'>
							Activity Type{" "}
						</Label>
						<Select
							value={formData.type}
							onValueChange={(value) =>
								handleInputChange("type", value)
							}
							disabled={isLoading}
						>
							<SelectTrigger>
								<SelectValue placeholder='Select activity type' />
							</SelectTrigger>
							<SelectContent className='bg-white text-black'>
								{activities.length > 0 ? (
									activities.map((activity) => (
										<SelectItem
											key={activity}
											value={activity}
										>
											{activity}
										</SelectItem>
									))
								) : (
									<SelectItem value='no-activities' disabled>
										No activities available
									</SelectItem>
								)}
							</SelectContent>
						</Select>
					</div>

					{/* Action Buttons */}
					<div className='flex justify-end gap-3 pt-4'>
						<Button
							type='button'
							variant='outline'
							onClick={handleClose}
							disabled={isLoading}
						>
							Cancel
						</Button>
						<Button
							type='submit'
							className='bg-orange-600 hover:bg-orange-700 text-white'
							disabled={isLoading}
						>
							{isLoading
								? "Saving..."
								: editingRaceCalculation?.id
								? "Update"
								: "Create"}
						</Button>
					</div>
				</form>
			</DialogContent>
		</Dialog>
	);
}
