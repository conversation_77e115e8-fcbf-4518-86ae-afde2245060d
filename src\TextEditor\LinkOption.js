import React, { Component } from "react";
import PropTypes from "prop-types";
import { EditorState, RichUtils } from "draft-js";

import {
  isOneBlockSelected,
  isLinkInSelection,
  getClearSelectionEditor,
} from "./EditFile";

class LinkOption extends Component {
  constructor(props) {
    super(props);
    this.addLink = this.addLink.bind(this);
    this.checkisYoutubeUrl = this.checkisYoutubeUrl.bind(this);
  }

  checkisYoutubeUrl(link) {
    const youtubeRegex = /^(?:https?:\/\/)?(?:www\.)?(?:youtube\.com\/watch\?v=([a-zA-Z0-9_]+)|youtu\.be\/([a-zA-Z\d_]+))(?:&.*)?$/;
    return youtubeRegex.test(link);
  }


  // addLink(event) {
  //   event.preventDefault();
  //   if (this._isEnabled()) {
  //     const { editorState, onChange } = this.props;
  //     // Get the selected text
  //     const selection = editorState.getSelection();
  //     const contentState = editorState.getCurrentContent();
  //     const startKey = selection.getStartKey();
  //     const startOffset = selection.getStartOffset();
  //     const endOffset = selection.getEndOffset();
  //     const blockWithText = contentState.getBlockForKey(startKey);
  //     const selectedText = blockWithText.getText().slice(startOffset, endOffset);
  //     const isYoutubeUrl = this.checkisYoutubeUrl(selectedText);
  //     const contentStateWithEntity = editorState
  //       .getCurrentContent()
  //       .createEntity("LINK", "MUTABLE", {
  //         url: isYoutubeUrl ? `${selectedText}` : `http://play.min.io/coworker/isis2.jpg`,
  //       });
  //     const entityKey = contentStateWithEntity.getLastCreatedEntityKey();
  //     const tmpEditorState = EditorState.set(editorState, {
  //       currentContent: contentStateWithEntity,
  //     });
  //     const newEditorState = RichUtils.toggleLink(
  //       tmpEditorState,
  //       tmpEditorState.getSelection(),
  //       entityKey
  //     );
  //     onChange(getClearSelectionEditor(newEditorState));
  //   }
  // }

  addLink(event) {
  event.preventDefault();
  if (this._isEnabled()) {
    const { editorState, onChange } = this.props;
    const selection = editorState.getSelection();
    const contentState = editorState.getCurrentContent();
    const startKey = selection.getStartKey();
    const startOffset = selection.getStartOffset();
    const endOffset = selection.getEndOffset();
    const blockWithText = contentState.getBlockForKey(startKey);
    const selectedText = blockWithText.getText().slice(startOffset, endOffset);

    const contentStateWithEntity = editorState
      .getCurrentContent()
      .createEntity("LINK", "MUTABLE", {
        url: selectedText,
      });

    const entityKey = contentStateWithEntity.getLastCreatedEntityKey();
    const tmpEditorState = EditorState.set(editorState, {
      currentContent: contentStateWithEntity,
    });

    const newEditorState = RichUtils.toggleLink(
      tmpEditorState,
      tmpEditorState.getSelection(),
      entityKey
    );

    onChange(getClearSelectionEditor(newEditorState));
  }
}



  _isEnabled() {
    const { editorState } = this.props;
    const isCollapsed = editorState.getSelection().isCollapsed();
    const isOneBlock = isCollapsed ? false : isOneBlockSelected(editorState);
    const isLink = isOneBlock ? isLinkInSelection(editorState) : false;
    return !isCollapsed && isOneBlock && !isLink;
  }

  render() {
    const classes = this._isEnabled()
      ? "rdw-option-wrapper"
      : "rdw-option-wrapper rdw-option-disabled";
    return (
      <div onClick={this.addLink}>
        <div className={classes} title="Link">
          <img
            src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTUiIGhlaWdodD0iMTUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTEzLjk2Ny45NUEzLjIyNiAzLjIyNiAwIDAgMCAxMS42Ny4wMDJjLS44NyAwLTEuNjg2LjMzNy0yLjI5Ny45NDhMNy4xMDUgMy4yMThBMy4yNDcgMy4yNDcgMCAwIDAgNi4yNCA2LjI0YTMuMjI1IDMuMjI1IDAgMCAwLTMuMDIyLjg2NUwuOTUgOS4zNzNhMy4yNTMgMy4yNTMgMCAwIDAgMCA0LjU5NCAzLjIyNiAzLjIyNiAwIDAgMCAyLjI5Ny45NDhjLjg3IDAgMS42ODYtLjMzNiAyLjI5OC0uOTQ4TDcuODEyIDExLjdhMy4yNDcgMy4yNDcgMCAwIDAgLjg2NS0zLjAyMyAzLjIyNSAzLjIyNSAwIDAgMCAzLjAyMi0uODY1bDIuMjY4LTIuMjY3YTMuMjUyIDMuMjUyIDAgMCAwIDAtNC41OTV6TTcuMTA1IDEwLjk5M0w0LjgzNyAxMy4yNmEyLjIzMyAyLjIzMyAwIDAgMS0xLjU5LjY1NSAyLjIzMyAyLjIzMyAwIDAgMS0xLjU5LS42NTUgMi4yNTIgMi4yNTIgMCAwIDEgMC0zLjE4bDIuMjY4LTIuMjY4YTIuMjMyIDIuMjMyIDAgMCAxIDEuNTktLjY1NWMuNDMgMCAuODQxLjEyIDEuMTk1LjM0M0w0Ljc3MiA5LjQzOGEuNS41IDAgMSAwIC43MDcuNzA3bDEuOTM5LTEuOTM4Yy41NDUuODY4LjQ0MiAyLjAzLS4zMTMgMi43ODV6bTYuMTU1LTYuMTU1bC0yLjI2OCAyLjI2N2EyLjIzMyAyLjIzMyAwIDAgMS0xLjU5LjY1NWMtLjQzMSAwLS44NDEtLjEyLTEuMTk1LS4zNDNsMS45MzgtMS45MzhhLjUuNSAwIDEgMC0uNzA3LS43MDdMNy40OTkgNi43MWEyLjI1MiAyLjI1MiAwIDAgMSAuMzEzLTIuNzg1bDIuMjY3LTIuMjY4YTIuMjMzIDIuMjMzIDAgMCAxIDEuNTktLjY1NSAyLjIzMyAyLjIzMyAwIDAgMSAyLjI0NiAyLjI0NWMwIC42MDMtLjIzMiAxLjE2OC0uNjU1IDEuNTl6IiBmaWxsPSIjMDAwIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiLz48L3N2Zz4="
            alt=""
          />
        </div>
      </div>
    );
  }
}

LinkOption.propTypes = {
  onChange: PropTypes.func,
  editorState: PropTypes.object,
};

export default LinkOption;
