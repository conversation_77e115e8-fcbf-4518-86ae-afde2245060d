import React, { useRef, useState, useEffect } from 'react';
import Webcam from 'react-webcam';
import './../../styles/camera/cameraIndividual.css';
import EastIcon from '@mui/icons-material/East';
import { useNavigate } from 'react-router-dom';
import { useSelector } from 'react-redux';
import { getStorage, ref as storageRef, uploadBytes, getDownloadURL } from 'firebase/storage';
import { getDatabase, push, ref, ref as rtdbRef, set as rtdbSet, serverTimestamp } from 'firebase/database';

function CameraIndividualChat({setOpenPageTwo,setIsUnRead}) {
    const webcamRef = useRef(null);
    const [capturedPhoto, setCapturedPhoto] = useState(null);
    const [cameraOpen, setCameraOpen] = useState(false);

    const { openedUser } = useSelector(state => state.users)
    const { currentUser } = useSelector(state => state.auth)

    const navigate = useNavigate()

    useEffect(() => {
        setCameraOpen(true);
    }, []);

    const handleCapture = () => {
        const imageSrc = webcamRef.current.getScreenshot();
        const base64String = imageSrc.split(",")[1];
        const mimeType = imageSrc.match(/[^:]\w+\/[\w-+\d.]+(?=;|,)/)[0];
        const byteCharacters = atob(base64String);
        const byteArrays = [];

        for (let offset = 0; offset < byteCharacters.length; offset += 512) {
            const slice = byteCharacters.slice(offset, offset + 512);
            const byteNumbers = new Array(slice.length);

            for (let i = 0; i < slice.length; i++) {
                byteNumbers[i] = slice.charCodeAt(i);
            }

            const byteArray = new Uint8Array(byteNumbers);
            byteArrays.push(byteArray);
        }

        const file = new File(byteArrays, "capturedPhoto.png", { type: mimeType });
        setCapturedPhoto(file);
        setCameraOpen(false);
    };


    const handleRetakePhoto = () => {
        setCapturedPhoto(null);
        setCameraOpen(true);
    };

    const handleSendPhoto = async () => {
        if (capturedPhoto) {
            try {
                const downloadURL = await uploadImageAndGetLink(capturedPhoto);
                console.log('Download URL:', downloadURL);
            } catch (error) {
                console.error('Error uploading image:', error);
            }
        };

        // console.log('Uploading photo:', capturedPhoto);
    };


    const uploadImageAndGetLink = async (file) => {
        const storage = getStorage();
        const storageReference = storageRef(storage, 'images/' + file.name);

        const database = getDatabase();
        const databaseReference = rtdbRef(database, 'images');

        try {
            // Upload the image to Firebase Storage
            await uploadBytes(storageReference, file);

            // Get the download URL of the uploaded image
            const downloadURL = await getDownloadURL(storageReference);

            // Save the download URL in Firestore
            // (your Firestore code here)
            console.log(downloadURL)
            await handleSendMessage('photo', downloadURL)

            console.log('Image uploaded and URL saved successfully.');

            return downloadURL;
        } catch (error) {
            console.error('Error uploading image:', error);
            throw error;
        }
    };

    const handleSendMessage = async (msgType, msg, docType, fileName) => {
        const db = getDatabase();

        const combinedId =
            currentUser.uid > openedUser.uid
                ? currentUser.uid + openedUser.uid
                : openedUser.uid + currentUser.uid;
        try {
            const chatRef = ref(db, `chats/${combinedId}/messages`);

            const updatedMessages =
            {
                isDeleted: false,
                isStarred: false,
                senderId: currentUser.uid,
                type: msgType,
                isSeen: false,
                timeStamp: serverTimestamp(),
                message: msg,
            }

            await push(chatRef, updatedMessages);
            setOpenPageTwo("chat")
            setIsUnRead(false)
            console.log('PHOTO SENT!');
            return;
        } catch (error) {
            console.error('Failed to send photo:', error);
        }
    };

    const handleGoBack = () => {
        setOpenPageTwo("chat")
        setIsUnRead(false)
    };

    return (
        <div className="camera-container">
            <div className="back-arrow" onClick={handleGoBack}>
                <span>
                    <EastIcon />
                </span>
            </div>

            {cameraOpen && (
                <div className="video-container">
                    <Webcam
                        audio={false}
                        ref={webcamRef}
                        screenshotFormat="image/png"
                        className="video-preview"
                    />
                    <button className="capture-button" onClick={handleCapture}></button>
                </div>
            )}

            {capturedPhoto && (
                <div className="preview-container">
                    <img src={URL.createObjectURL(capturedPhoto)} alt="Captured" className="photo-preview" />
                    <button className="retake-button" onClick={handleRetakePhoto}>
                        Retake Photo
                    </button>
                    <button className="send-button" onClick={handleSendPhoto}>
                        Send Photo
                    </button>
                </div>
            )}
        </div>
    );
}

export default CameraIndividualChat;