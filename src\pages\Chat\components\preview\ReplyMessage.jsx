import React, { useEffect, useState } from 'react'
import { Card, CardActionArea, Typography } from '@mui/material'
import Cookies from 'js-cookie';
import ClearIcon from '@mui/icons-material/Clear';

const ReplyMessage = ({ openedUser, setIsReplyMsg }) => {
    const replyMsgInfo = JSON.parse(Cookies.get('replyMsgInfo'))

    return <>
        <Card sx={{ marginTop: '5rem', border: '0px solid black', width: '100%', minWidth: "26.rem", boxShadow: '0px -4px 10px rgba(0, 0, 0, 0.1);' }
        }>
            <CardActionArea sx={{ padding: '1rem', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <Typography textAlign={'left'} color={'gray'} fontSize={14}>
                    {` ${replyMsgInfo.senderId === openedUser.uid ? openedUser.displayName : 'You'} : ${replyMsgInfo.message}`}
                </Typography>
                <ClearIcon mt={-3} onClick={() => setIsReplyMsg(false)} fontSize='14px' />
            </CardActionArea>
        </Card >
    </>
}

export default ReplyMessage