import {
    Chip,
    FormControl,
    FormLabel,
    Grid,
    MenuItem,
    OutlinedInput,
    Select,
    TextField,
  } from "@mui/material";
  import { Button, Modal } from "antd";
  import React, { useEffect, useState } from "react";
  import {
    CreatePrograms,
    CreateZonesClasification,
    createGoalNameYTA,
    createGoalsdata,
    createPhaseNamedata,
    createUserAdminGoaldata,
    createYTAdata,
    getAllActivityData,
    getAllGoalNameForYTA,
    getAllPrograms,
    getAlllevels,
    updateGoalNameYTA,
    updateGoalsdata,
    updatePhaseNamedata,
    updatePrograms,
    updateUserAdminGoaldata,
    updateYTAdata,
    updateZonesClasification,
    weeklyFeedDataPattern,
    weeklyFeedDataProgram,
  } from "../../API/api-endpoint";
  import { useFormik } from "formik";
  import Swal from "sweetalert2";
  import SlickCarousel from "../../pages/SlickCarousel";
import { capitalizeFirstLetter } from "../../utils/Resubale";
  const scoreData = [1, 2, 3, 4, 5];
  const CreateUserAdminGoal = ({
    fetchReport,
    setShowAssesmentModal,
    showAssesmentModal,
    editData,
    setEditData,
  }) => {
    const formik = useFormik({
      initialValues: {
        goal: "",
      },
      validate: (values) => {
        const errors = {};
        if (!values.goal) {
          errors.goal = "Goal name is required";
        }
        return errors;
      },
      // validationSchema: {},
      onSubmit: (values, { resetForm }) => {
        handleSubmitAssesmentForm(values, resetForm);
      },
    });
  
    const handleSubmitAssesmentForm = async (data, resetForm) => {
      let response = "";
      if (editData?.id) {
        response = await updateUserAdminGoaldata(data);
      } else {
        response = await createUserAdminGoaldata(data);
        console.log("response", response);
      }
      if (response?.status) {
        Swal.fire({
          title: "Success",
          text:capitalizeFirstLetter(response.message),
          icon: "success",
        });
        setShowAssesmentModal(false);
        fetchReport();
        resetForm();
        setEditData({});
        formik?.setValues({
          goal: "",
        });
      } else {
        Swal.fire({
          title: "Error",
          text: response.message,
          icon: "error",
        });
      }
      console.log("response", response);
    };
    useEffect(() => {
      if (editData?.id) {
        const { srID, ...data } = editData;
        console.log("data", data);
        formik?.setValues(data);
      }
    }, [editData?.id]);
    return (
      <Modal
        width={1200}
        // title={editData?.id ? "Edit YTA Goal" : "Add YTA Goal"}
        // title={editData ? "Edit Challenge" : "Add Challenges"}
        open={showAssesmentModal}
        onCancel={() => {
          setShowAssesmentModal(false);
          setEditData({});
          formik.resetForm();
          formik?.setValues({
            goal: "",
          });
        }}
        footer={
          <div></div>
          //   loading={isLoading}
        }
      >
        <div className="headingCont">
          <span className="heading">{editData?.id ? "Edit " : "Create"}</span>{" "}
          <span className="orange heading">User Admin Goal</span>
        </div>
        {/* <h1>{editData ? editData.challengeId : values.challengeId}</h1> */}
        <div className="parentCont">
          <form className="form1" onSubmit={formik.handleSubmit}>
            <Grid container spacing={2}>
              <Grid item xs={12} sm={11}>
                <FormLabel>Goal Name<span className="text-[red]">*</span></FormLabel>
  
                <TextField
                  fullWidth
                  placeholder="goal name"
                  size="small"
                  type="text"
                  name="goal"
                  value={formik?.values?.goal}
                  onChange={formik.handleChange}
                  error={formik.touched.goal && formik.errors.goal}
                  helperText={formik.touched.goal && formik.errors.goal}
                />
              </Grid>
             
              <Grid item xs={12} sm={6}>
                <Button
                  className="btn"
                  key="submit"
                  type="primary"
                  onClick={() => formik.handleSubmit()}
                >
                  Submit
                </Button>
              </Grid>
            </Grid>
          </form>
          <div className="slick-container">
            <SlickCarousel />
          </div>
        </div>
      </Modal>
    );
  };
export default CreateUserAdminGoal
