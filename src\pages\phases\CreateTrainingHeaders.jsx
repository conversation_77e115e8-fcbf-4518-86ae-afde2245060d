import { Button, TextField } from "@mui/material";
import React, { useState } from "react";

const initialValues = {
    trainingHeader: "",
  };

const CreateTrainingHeaders = () => {
  const [values, setValues] = useState(initialValues);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    console.log(name, value);
    setValues({
      ...values,
      [name]: value,
    });
  };

  const handleSubmit = async () => {
    console.log(values);
    // try {
    //   const response = await axios.post(
    //     "https://api.example.com/endpoint",
    //     { data: values },
    //     {
    //       headers: {
    //         "Content-Type": "application/json",
    //          Authorization: "Bearer your_access_token_here",
    //       },
    //     }
    //   );
    //   console.log(response.data);
    // } catch (err) {
    //   console.log(err.message);
    // }
  };
  return (
    <div className="container">
      <div className="title">Create Training Headers</div>
      <div className="form-body">
        <div className="form-group">
          <label className="lable">Create Headers Name</label>
          <div className="activity-inputs">
            <TextField
              className="activity-input"
              id="outlined-basic"
              label="Running"
              variant="outlined"
              name="trainingHeader"
              value={values.trainingHeader}
              onChange={handleInputChange}
            />
          </div>
        </div>

        <div className="form-group">
          <Button
            sx={{ background: "black", marginLeft: "-4.5%" }}
            onClick={handleSubmit}
            variant="contained"
          >
            Submit
          </Button>
        </div>
      </div>
    </div>
  );
};

export default CreateTrainingHeaders;
