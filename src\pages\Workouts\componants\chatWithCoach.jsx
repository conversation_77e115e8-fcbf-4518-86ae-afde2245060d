import Modal from '@mui/material/Modal';
import { useDispatch, useSelector } from "react-redux";
import {
    onValue,
    query,
    limitToLast,
    orderByKey,
    orderByChild,
    equalTo,
    getDatabase,
    ref,
    get,
    set,
    update,
    serverTimestamp,
    off,
    onDisconnect,
    push,
} from "firebase/database";
import { getStorage, ref as storageRef, uploadBytes, getDownloadURL } from 'firebase/storage';
import React, { useEffect, useState, useRef } from "react";
import NavIndividualChat from '../../Chat/components/NavIndividualChat';
import ChatMsg from '../../Chat/screens/individual/ChatMsg';
import { Avatar, Box, CircularProgress, Paper, Typography } from '@mui/material'
import Swal from 'sweetalert2';
import { ref as rtdbRef, set as rtdbSet } from 'firebase/database';
import EmojiPicker from 'emoji-picker-react';
import InsertEmoticonIcon from "@mui/icons-material/InsertEmoticon";
import CameraAltIcon from "@mui/icons-material/CameraAlt";
import AddBoxIcon from "@mui/icons-material/AddBox";
import SendIcon from "@mui/icons-material/Send";
import AcceptMessage from '../../Chat/components/prompt/AcceptMessage';
import { getChatMessagesAction } from '../../Chat/redux/action/chatsActions';
import "./chatWithCoach.css";

const ChatWithCoach = ({ isOpen, setIsOpen }) => {
    const { openedUsers } = useSelector((state) => state.users);
    const { allUsers } = useSelector((state) => state.users);
    const openedUser = [...allUsers][0];
    const userId = JSON.parse(localStorage.getItem('userId'));
    // const openedUser = JSON.parse(localStorage.getItem('currentUser'));
    const currentUser = JSON.parse(localStorage.getItem('currentUser'));
    const [isSearchActive, setIsSearchActive] = useState(false);
    const [searchKeyword, setSearchKeyword] = useState('');
    const { chatMessages } = useSelector(state => state.chats)
    const [isLoading, setIsLoading] = useState(true);
    const [lastMessage, setLastMessage] = useState(null);
    const [lastMessageTime, setLastMessageTime] = useState(null);
    const chatContainerRef = React.useRef(null);
    const [isMessageReqAccepted, setIsMessageReqAccepted] = useState(true);
    const [showToolBox, setShowToolBox] = useState(false);
    const [isReplyMsg, setIsReplyMsg] = useState(false);
    const [typedMessage, setTypedMessage] = useState('');
    const [showEmojis, setShowEmojis] = useState(false);
    const [isOnline, setIsOnline] = useState(false);
    const [isMobile, setIsMobile] = useState(false);
    const [previewData, setPreviewData] = useState(null);
    const inputRef = useRef(null);
    const [url, setUrl] = useState(null);

    const dispatch = useDispatch()

    const paper = {
        padding: "0rem",
        minHeight: "80vh",
        maxHeight: "80vh",
        maxWidth: "47rem",
        paddingBottom: "5rem",
        // backgroundImage: 'url("https://cdn.wallpapersafari.com/80/83/K7l2qB.jpg")',
    };

    const handleEmojiClick = (emojiObject) => {
        // Assuming emojiObject is an object with a property 'emoji'
        setTypedMessage((prevTypedMessage) => {
            // Append the clicked emoji to the existing message
            return prevTypedMessage + emojiObject.emoji;
        });
    };

    const handleTypeChange = event => {
        setTypedMessage(event.target.value)

        const inputText = event.target.value;

        // Regular expression to match URLs
        const urlRegex = /(https?:\/\/[^\s]+)/g;
        const match = inputText.match(urlRegex);

        if (match) {
            // If URL found, set it to the state variable
            const foundUrl = match[0];
            console.log(foundUrl);
            if (foundUrl !== url) setUrl(foundUrl);
        } else {
            // If no URL found, set the state variable to null
            setUrl(null);
        }
    }

    const uploadDocumentAndGetURL = async (file) => {
        const storage = getStorage();
        const storageReference = storageRef(storage, 'documents/' + file.name);

        const database = getDatabase();
        const databaseReference = rtdbRef(database, 'documents');

        try {
            // Upload the image to Firebase Storage
            await uploadBytes(storageReference, file);

            // Get the download URL of the uploaded image
            const downloadURL = await getDownloadURL(storageReference);

            // Save the download URL in Firestore
            // (your Firestore code here)

            await handleSendMessage('document', downloadURL, file.name.split('.').pop(), file.name)

            console.log('Document uploaded and URL saved successfully.');

            return downloadURL;
        } catch (error) {
            console.error('Error uploading document:', error);
            throw error;
        }
    };

    const handleDocumentsUpload = async (event) => {
        setShowToolBox(false);
        if (event.target.files[0]) {
            const allowedFileTypes = [".csv", ".doc", ".docx", ".pdf", ".ppt", ".txt"];
            const selectedFileType = event.target.files[0].name.substring(
                event.target.files[0].name.lastIndexOf(".")
            );
            if (allowedFileTypes.includes(selectedFileType)) {
                try {
                    const downloadURL = await uploadDocumentAndGetURL(event.target.files[0]);
                    console.log('Download URL:', downloadURL);
                } catch (error) {
                    console.error('Error uploading image:', error);
                }
            } else {
                console.log('Selected file is not from the allowed list.');
            }
        }
    };

    function filterMessagesByDateRange(messages) {
        const today = new Date().toDateString();
        const yesterday = new Date();
        yesterday.setDate(yesterday.getDate() - 1);
        const yesterdayDateString = yesterday.toDateString();

        const groupedMessages = {};
        for (const message of messages) {
            const date = new Date(message.timeStamp);
            if (isNaN(date.getTime())) {
                continue; // Skip this message if the timeStamp is not a valid date
            }
            const dateString = date.toDateString();

            if (!groupedMessages[dateString]) {
                groupedMessages[dateString] = [];
            }

            groupedMessages[dateString].push(message);
        }

        const formattedArray = [];
        for (const date in groupedMessages) {
            const filteredDate =
                date === today ? "Today" : date === yesterdayDateString ? "Yesterday" : date;
            const messages = groupedMessages[date];

            formattedArray.push({ filteredDate, messages });
        }

        return formattedArray;
    }

    const handleImageUpload = async (event) => {
        setShowToolBox(false);
        if (event.target.files[0]) {
            const acceptedTypes = [
                'image/jpeg',
                'image/jpg',
                'image/png',
                'image/gif',
                'image/svg+xml',
                'video/mp4',
                'video/webm',
                'video/mkv',
                'video/ogg',
            ];

            const fileType = event.target.files[0].type;

            if (acceptedTypes.includes(fileType)) {
                const isVideo = fileType.startsWith('video/');

                try {
                    if (isVideo) {
                        setIsLoading(true);
                        console.log("Uploading video...");
                        const video = document.createElement('video');
                        video.preload = 'metadata';
                        video.src = URL.createObjectURL(event.target.files[0]);

                        console.log('Video duration:', video.duration, video.src);
                        await new Promise((resolve) => {
                            video.onloadedmetadata = resolve;
                        });

                        if (!isFinite(video.duration) || video.duration <= 0) {
                            throw new Error('Invalid video duration.');
                        }

                        // Seek to the middle of the video
                        const seekTime = video.duration / 2;
                        video.currentTime = seekTime;

                        await new Promise((resolve) => {
                            video.onseeked = resolve;
                        });

                        const canvas = document.createElement('canvas');
                        canvas.width = video.videoWidth;
                        canvas.height = video.videoHeight;
                        const ctx = canvas.getContext('2d');

                        ctx.drawImage(video, 0, 0, canvas.width, canvas.height);

                        const thumbnailBlob = await new Promise((resolve) =>
                            canvas.toBlob(resolve, 'image/jpeg', 0.8)
                        );

                        const thumbnailFile = new File([thumbnailBlob], 'thumbnail.jpg', {
                            type: 'image/jpeg',
                        });

                        // Pass the thumbnailFile to the next arrow function or perform any desired operations
                        console.log('Thumbnail File:', thumbnailFile);

                        const downloadURL = await uploadImageAndGetLink(
                            event.target.files[0],
                            isVideo,
                            thumbnailFile
                        );
                        console.log('Download URL:', downloadURL);

                        // Show success message
                        console.log("Video uploaded successfully.");
                        Swal.fire({
                            title: "Success",
                            text: "Video Uploaded Successfully.",
                            icon: "success",
                        });
                    } else {
                        // Handle image upload
                        const downloadURL = await uploadImageAndGetLink(event.target.files[0]);
                        console.log('Download URL:', downloadURL);

                        // Show success message
                        console.log("Image uploaded successfully.");
                        Swal.fire({
                            title: "Success",
                            text: "Image Uploaded Successfully.",
                            icon: "success",
                        });
                    }
                } catch (error) {
                    console.error('Error uploading image:', error);
                    setIsLoading(false);
                    // Show error message
                    console.log("Error uploading image:", error);
                    Swal.fire({
                        title: "Error",
                        text: error.message || error,
                        icon: "error",
                    });
                }
            } else {
                console.log('File is not from the given options.');
                Swal.fire({
                    title: "Error",
                    text: "File Type Doesn't match",
                    icon: "error",
                });
            }
        }
    };

    const uploadImageAndGetLink = async (file, isVideo, thumbnailData) => {
        const storage = getStorage();
        const storageReference = isVideo
            ? storageRef(storage, 'videos/' + file.name)
            : storageRef(storage, 'images/' + file.name);

        try {
            console.log(thumbnailData)
            const thumbnailURL = thumbnailData ? await handleUploadVdoThumbnailAndGetLink(thumbnailData) : null

            console.log(thumbnailURL)
            // Upload the image to Firebase Storage
            await uploadBytes(storageReference, file);
            // Get the download URL of the uploaded image
            const downloadURL = await getDownloadURL(storageReference);

            // Save the download URL in Firestore
            // (your Firestore code here)

            await handleSendMessage(isVideo ? 'video' : 'photo', downloadURL, null, null, thumbnailURL)

            console.log('Media ploaded and URL saved successfully.');
            setIsLoading(false)

            return downloadURL;
        } catch (error) {
            setIsLoading(false)
            Swal.fire({
                title: "Error",
                text: error,
                icon: "error",
            });
            console.error('Error uploading media:', error);
            throw error;
        }
    };

    const handleUploadVdoThumbnailAndGetLink = async (thumbnailData) => {
        const storage = getStorage();
        const storageReference = storageRef(storage, 'thumbnail/' + thumbnailData.name);
        try {
            await uploadBytes(storageReference, thumbnailData);
            const downloadURL = await getDownloadURL(storageReference);
            console.log("VDO thumbnailURL : ", downloadURL)
            return downloadURL;
        } catch (error) {
            console.error('Error uploading media:', error);
            throw error;
        }
    };

    const handleSendMessage = async (msgType, msg, docType, fileName, thumbnailURL) => {
        const db = getDatabase();

        setTypedMessage('')
        setShowEmojis(false)
        console.log(thumbnailURL)
        const combinedId =
            currentUser.uid > openedUser.uid
                ? currentUser.uid + openedUser.uid
                : openedUser.uid + currentUser.uid;

        try {
            const chatRef = ref(db, `chats/${combinedId}/messages`);

            const updatedMessages =
            {
                isDeleted: false,
                isStarred: false,
                senderId: currentUser.uid,
                type: msgType,
                isSeen: false,
                timeStamp: serverTimestamp(),
                message: msg,
                docType: docType ? docType : null,
                fileName: fileName ? fileName : null,
                thumbnailURL: thumbnailURL ? thumbnailURL : null
            }

            await push(chatRef, updatedMessages);
            window.scrollTo({
                top: document.documentElement.scrollHeight,
                behavior: 'smooth'
            });

            // dispatch(getChatMessagesAction(combinedId))
            console.log(msgType, 'sent!');
            return;
        } catch (error) {
            console.error('Failed to send message:', error);
        }
    };

    const checkMessageReqAccepted = async (combinedId) => {
        try {
            const db = getDatabase();
            const userInfoPath = `userChats/${currentUser.uid}/${combinedId}`;
            const userInfoRef = ref(db, userInfoPath);

            onValue(userInfoRef, (snapshot) => {
                const userInfo = snapshot.val();
                setIsMessageReqAccepted(userInfo?.isMsgReqAccepted);
            });
        } catch (error) {
            console.log(error)
        }
    }

    useEffect(() => {
        // inputRef.current.focus();
        const combinedId =
            currentUser.uid > openedUser.uid
                ? currentUser.uid + openedUser.uid
                : openedUser.uid + currentUser.uid;

        checkMessageReqAccepted(combinedId)

        dispatch(getChatMessagesAction(combinedId))
    }, [openedUser.uid])

    return (
        <>
            <Modal
                open={isOpen}
                onClose={() => setIsOpen(false)}
                aria-labelledby="modal-modal-title"
                aria-describedby="modal-modal-description"
                style={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }}
            >
                <Box
                    sx={{ display: "flex", justifyContent: "center", marginTop: "90px", alignItems: "center", position: 'relative', width: "40vw" }}
                >
                    <Paper sx={{ flexGrow: 1 }} elevation={10} style={paper}>
                        <NavIndividualChat
                            backButtonPath={'/chat'}
                            isOnline={isOnline}
                            photoURL={openedUser.photoURL}
                            displayName={openedUser.displayName}
                            isSearchActive={isSearchActive}
                            setIsSearchActive={setIsSearchActive}
                            searchKeyword={searchKeyword}
                            setSearchKeyword={setSearchKeyword}
                        />
                        <div ref={chatContainerRef} style={{ height: "69vh", overflowY: "scroll" }} className="chatPaper1">
                            {filterMessagesByDateRange(chatMessages)?.map((item, index) => (
                                <Box key={index}>
                                    <Box className="date-box-parent" mt={1}>
                                        <Box className="date-box">
                                            <Typography className="ind-chat-date">
                                                {item.filteredDate}
                                            </Typography>
                                        </Box>
                                    </Box>

                                    {item.messages?.map((msg, mindex) => (
                                        <ChatMsg
                                            isLoading={isLoading}
                                            onClick={() => setShowToolBox(false)}
                                            key={msg.timeStamp}
                                            currentUser={currentUser}
                                            openedUser={openedUser}
                                            isMessageReqAccepted={isMessageReqAccepted}
                                            msg={msg}
                                            setIsReplyMsg={setIsReplyMsg}
                                            searchKeyword={searchKeyword}
                                            isSearchActive={isSearchActive}
                                        />
                                    ))}
                                </Box>
                            ))}
                        </div>

                        {/* {isLoading && (
                            <CircularProgress className="m-6" />
                        )} */}

                        {/* Toolbox */}
                        {showToolBox && (
                            <Box className="plus-bar-content">
                                <Box className="plus-bar-icon-div">
                                    <img
                                        onClick={() => document.getElementById("selectDocumentsInput").click()}
                                        style={{ cursor: "pointer" }}
                                        height={32}
                                        src="/images/document-icon.png"
                                        alt="emoji"
                                    />
                                    <Typography
                                        onClick={() => document.getElementById("selectDocumentsInput").click()}
                                        style={{ cursor: "pointer" }}
                                        className="plus-bar-icon-label"
                                    >
                                        Document
                                    </Typography>
                                </Box>

                                <Box className="line-plus-icon-menu"></Box>

                                <Box className="plus-bar-icon-div">
                                    <img
                                        onClick={() => document.getElementById("selectImgInput").click()}
                                        height={32}
                                        src="/images/gallery-icon.png"
                                        style={{ cursor: "pointer" }}
                                        alt="emoji"
                                    />
                                    <Typography
                                        onClick={() => document.getElementById("selectImgInput").click()}
                                        className="plus-bar-icon-label"
                                        style={{ cursor: "pointer" }}
                                    >
                                        Gallery
                                    </Typography>
                                </Box>
                            </Box>
                        )}

                        <input
                            id="selectImgInput"
                            hidden
                            type="file"
                            accept=".jpeg, .jpg, .png, .gif, .svg, video/*"
                            onChange={handleImageUpload}
                        />
                        <input
                            id="selectDocumentsInput"
                            hidden
                            type="file"
                            accept=".csv, .doc, .docx, .pdf, .ppt, .txt"
                            onChange={handleDocumentsUpload}
                        />

                        {/* Footer */}
                        {isMessageReqAccepted ? (
                            <Box className="footer-container">
                                {showEmojis && (
                                    <Box onClick={(e) => e.stopPropagation()}>
                                        <EmojiPicker
                                            onEmojiClick={handleEmojiClick}
                                            width={isMobile ? "100vw" : "46.8vw"}
                                            height={"25rem"}
                                            style={{ cursor: "pointer" }}
                                        />
                                    </Box>
                                )}

                                <Box
                                    className="footer"
                                    style={
                                        previewData || showEmojis || isReplyMsg
                                            ? { boxShadow: "none" }
                                            : {}
                                    }
                                >
                                    <Box className="input-box">
                                        <InsertEmoticonIcon
                                            onClick={() => setShowEmojis((pre) => !pre)}
                                            style={{ cursor: "pointer" }}
                                        />
                                        <input
                                            ref={isMessageReqAccepted && inputRef}
                                            value={typedMessage}
                                            onChange={handleTypeChange}
                                            onKeyPress={(e) => {
                                                e.key === "Enter" &&
                                                    typedMessage.length > 0 &&
                                                    handleSendMessage("text", typedMessage);
                                            }}
                                            type="text"
                                            placeholder="Type here..."
                                            className="type-msg"
                                        />
                                        <AddBoxIcon
                                            onClick={() => setShowToolBox((pre) => !pre)}
                                            style={{ cursor: "pointer" }}
                                        />
                                    </Box>

                                    <Box
                                        onClick={() =>
                                            typedMessage.length > 0 &&
                                            handleSendMessage("text", typedMessage)
                                        }
                                        style={{ cursor: "pointer" }}
                                    >
                                        <SendIcon />
                                    </Box>
                                </Box>
                            </Box>
                        ) : (
                            <AcceptMessage
                                currentUserId={currentUser.uid}
                                combinedId={
                                    currentUser.uid > openedUser.uid
                                        ? currentUser.uid + openedUser.uid
                                        : openedUser.uid + currentUser.uid
                                }
                                openedUserId={openedUser.uid}
                            />
                        )}
                    </Paper>
                </Box>
            </Modal>
        </>
    );
};

export default ChatWithCoach;