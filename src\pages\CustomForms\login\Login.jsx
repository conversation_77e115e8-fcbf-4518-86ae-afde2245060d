import React, { useState } from "react";
import "./login.css";
import { auth, provider } from "../../firebase";
import { signInWithPopup } from "firebase/auth";
import { useNavigate } from "react-router-dom";
import Modal from "@mui/material/Modal";
import CloseIcon from "@mui/icons-material/Close";
import Typography from "@mui/material/Typography";
import Box from "@mui/material/Box";

const style = {
  position: "absolute",
  top: "50%",
  left: "50%",
  transform: "translate(-50%, -50%)",
  width: 350,
  bgcolor: "background.paper",
  border: "1px solid #000",
  boxShadow: 24,
  p: 2,
};

const Login = ({ setUserToken }) => {
  const [open, setOpen] = useState(false);

  const navigate = useNavigate();
  const signin = () => {
    signInWithPopup(auth, provider).then((data) => {
      setUserToken(data?.user?.accessToken);
      if (data?.user?.accessToken) {
        localStorage.setItem(
          "usertoken",
          JSON.stringify(data?.user?.accessToken)
        );
        handleOpen();
        navigate("/flow");
      }
      return;
    });
  };

  const handleOpen = () => setOpen(true);
  const handleClose = () => setOpen(false);

  return (
    <>
      <div className="login-main">
        <div className="login-section">
          <h1 className="login-header">User Login</h1>
          <div className="login-form-group">
            <lable className="login-lable">Username</lable>
            <input className="login-input" type="text" />
          </div>
          <div className="login-form-group">
            <lable className="login-lable">Password</lable>
            <input className="login-input" type="text" />
          </div>
          <div className="login-footer-section">
            <div className="login-checkbox">
              <input type="checkbox" />
              <label className="login-lable">Remember me</label>
            </div>
            <div className="forget-text login-lable">Forget Password</div>
          </div>
          <div className="login-btn-section">
            <button className="login-btn">Log in</button>
          </div>
          <div className="login-btn-section">
            <button
              type="button"
              className="login-with-google-btn"
              onClick={signin}
            >
              Sign in with Google
            </button>
          </div>
        </div>
      </div>
      <Modal
        open={open}
        onClose={handleClose}
        aria-labelledby="modal-modal-title"
        aria-describedby="modal-modal-description"
      >
        <Box sx={style}>
          <Box
            sx={{
              width: "100%",
              display: "flex",
              justifyContent: "right",
              paddingBottom: "10px",
            }}
          >
            <CloseIcon onClick={handleClose} />
          </Box>
          <Typography
            id="modal-modal-title"
            variant="h6"
            component="h2"
            sx={{ textAlign: "center" }}
          >
            {/* Do You want to continue with Strava ? */}
            {/* Please selct amount that you have to pay */}
          </Typography>
          <Box sx={{ mt: 2 }}>
            {/* <div className="strava-btn-main">
              Sign in with
              <button className="strava-btn" onClick={handleStravaSignin}>
                Strava
              </button>
            </div> */}

            {/* <div>
              <div className="form-group">
                <div className="activity-inputs">
                  <TextField
                    className="activity-input"
                    id="outlined-basic"
                    label="Rs."
                    variant="outlined"
                    name="amount"
                    value={amount}
                    onChange={(e) => setAmount(e.target.value)}
                  />
                </div>
              </div>
              <button
                style={{ marginTop: "20px", background: "blue" }}
                className="strava-btn"
                onClick={() => openPayModal(options)}
              >
                Pay with RazorPay
              </button>
            </div> */}
          </Box>
        </Box>
      </Modal>
    </>
  );
};

export default Login;
