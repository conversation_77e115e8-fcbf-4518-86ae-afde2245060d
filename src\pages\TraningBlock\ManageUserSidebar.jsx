import React, { useState, useEffect } from "react";
import SearchIcon from "@mui/icons-material/Search";
import { styled, alpha } from "@mui/material/styles";
import InputBase from "@mui/material/InputBase";
import Button from "@mui/material/Button";
import { Checkbox, Collapse, TextField, Grid, Stack } from "@mui/material";
import { assignTariningPlanToUser, getAllUsers, unLinkTariningPlanToUser } from "../../API/api-endpoint";
import DatePicker from "react-datepicker";
import moment from "moment/moment";
import { useSelector } from "react-redux";
import Swal from "sweetalert2";

const Search = styled("div")(({ theme }) => ({
  position: "relative",
  border: "1px solid black",
  backgroundColor: alpha(theme.palette.common.white, 0.15),
  "&:hover": {
    backgroundColor: alpha(theme.palette.common.white, 0.25),
  },
  marginRight: theme.spacing(2),
  marginLeft: 0,
  width: "100%",
  [theme.breakpoints.up("sm")]: {
    marginLeft: theme.spacing(1),
    width: "auto",
  },
}));

const SearchIconWrapper = styled("div")(({ theme }) => ({
  padding: theme.spacing(0, 2),
  height: "100%",
  position: "absolute",
  pointerEvents: "none",
  display: "flex",
  alignItems: "center",
  justifyContent: "center",
}));

const StyledInputBase = styled(InputBase)(({ theme }) => ({
  color: "inherit",
  "& .MuiInputBase-input": {
    padding: theme.spacing(1, 1, 1, 0),
    // vertical padding + font size from searchIcon
    paddingLeft: `calc(1em + ${theme.spacing(4)})`,
    transition: theme.transitions.create("width"),
    width: "100%",
    [theme.breakpoints.up("md")]: {
      width: "20ch",
    },
  },
}));

const ManageUserSidebar = (props) => {
  const {
    selectedLibraryItem,
    setIsLibraryChange,
    setTogglesidebar,
    setManageUserSidebar,
  } = props;

  let CurrentTrainingBlock = useSelector(
    (state) => state.MultiTrainingBlocksSlice.CurrentTrainingBlock
  );

  const [searchTerm, setSearchTerm] = useState("");
  const [selectAllChecked, setSelectAllChecked] = useState(false);
  const [searchResults, setSearchResults] = useState([]);
  const [userList, setUserList] = useState([]);
  const [userDetails, setUserDetails] = useState({});
  const [selectedUser, setSelectedUser] = useState(null);
  const [masterStartDate, setMasterStartDate] = useState(null);
  const [masterStartDay, setMasterStartDay] = useState(null);

  const [masterEndDay, setMasterEndDay] = useState(null);
  const handleChange = (e) => {
    setSearchTerm(e.target.value);
  };

  const getUsers = async () => {
    let result = await getAllUsers();
    setUserList(result);
  };

  useEffect(() => {
    getUsers();
  }, []);

  useEffect(() => {
    const filteredResults = userList
      .map((user) => ({
        ...user,
        isChecked: false,
        userStartDate: "",
        userStartDay: "",
        userEndDay: "",
      }))
      // .filter((user) => {
      //   const isNameMatch = user.firstname
      //     .toLowerCase()
      //     .includes(searchTerm.toLowerCase());

      //   return isNameMatch;
      // });
    setSearchResults(filteredResults);
  }, [searchTerm, userList]);

  
  const toggleSelectAll = () => {
    setSelectAllChecked(!selectAllChecked);
    const updatedSearchResults = searchResults.map((user) => {
      return {
        ...user,
        isChecked: !selectAllChecked,
      };
    });
    setSearchResults(updatedSearchResults);
  };

  const handleUserClick = (userId) => {
    const userIndex = searchResults.findIndex(
      (user) => user.id === userId
    );
    if (userIndex !== -1) {
      const updatedSearchResults = [...searchResults];
      updatedSearchResults[userIndex].isChecked =
        !updatedSearchResults[userIndex].isChecked;
      setSearchResults(updatedSearchResults);
    }
  };
  const filteredUsers = searchResults.filter((user) => {
    const fullName = `${user.firstname ? user.firstname : 'NA'} ${user.lastname}`;
    return fullName.toLowerCase().includes(searchTerm.toLowerCase());
  });
  const handleLinkButtonClick = async (userId) => {
    if (CurrentTrainingBlock.name === null) {
      Swal.fire({
        title: "Error",
        text: "Please First Select any Training Block",
        icon: "error",
      });
    } else {
      let selectALLCheckBox = document.getElementById("select_all");
      if (selectALLCheckBox.checked) {
        if (masterStartDate &&  masterStartDay && masterEndDay) {
          
       
        // Create an array to store user details objects
        const updatedUserDetailsArray = [];
  
        // Iterate through the searchResults and populate user details
        searchResults.forEach((user) => {
          if (user.isChecked) {
            // Create a simple object for each user and push it to the array
            updatedUserDetailsArray.push({
              user: user.id,
              start_date: moment(masterStartDate).format("YYYY-MM-DD"),
              startday: masterStartDay,
              endday:masterEndDay,
              trainingplan: CurrentTrainingBlock.id,
            });
          }
        });
  
        // Update the user details state
        const response = await assignTariningPlanToUser(updatedUserDetailsArray);
      }else{
        Swal.fire({
          title: "Info",
          text: "Please Select  Date and Day",
          icon: "info",
        });
      }
        // Optionally, you might want to update the state or do something with the response
      } else {
        // Create an array to store user details objects
        if (masterStartDate &&  masterStartDay) {
        const updatedUserDetailsArray = [];
  
        // Iterate through the searchResults and populate user details
        searchResults.forEach((user) => {
          if (user.isChecked) {
            // Create a simple object for each user and push it to the array
            updatedUserDetailsArray.push({
              user: user.id,
              start_date: moment(masterStartDate).format("YYYY-MM-DD"),
              startday: masterStartDay,
              endday:masterEndDay,
              trainingplan: CurrentTrainingBlock.id,
            });
          }
        });
  
        // Update the user details state
        if (updatedUserDetailsArray?.length>0) {
          
          const transformData = (dataArray) => {
            const transformedData = dataArray.reduce((acc, item) => {
              Object.keys(item).forEach((key) => {
                if (key !== 'user') {
                  // If the key exists, create an array or append to the existing array
                  acc[key] = acc[key] ? acc[key] : item[key];
                } else {
                  // For the 'user' key, collect all user values into an array
                  acc[key] = acc[key] ? [...acc[key], item[key]] : [item[key]];
                }
              });
              return acc;
            }, {});
          
            return transformedData;
          };
          
          // Usage:
          const transformedObject = transformData(updatedUserDetailsArray);
          const response = await assignTariningPlanToUser(transformedObject);
        
          if (response?.status) {
            setTogglesidebar(false);
          setManageUserSidebar(false);
          setMasterStartDate("")
setMasterStartDay("")
            Swal.fire({
              title: "Success",
              text: "Linked Successfully!",
              icon: "success",
            });
          }else{
            Swal.fire({
              title: "Error",
              text: response.message,
              icon: "error",
            });
          }
        }else{
          Swal.fire({
            title: "Info",
            text: "Please Select  Atleast one user  ",
            icon: "info",
          });
        }
       
      }else{
        Swal.fire({
          title: "Info",
          text: "Please Select  Date and Day",
          icon: "info",
        });
      }
  
        // Optionally, you might want to update the state or do something with the response
      }
    }
  };
  const handleUnLinkButtonClick = async (userId) => {
    if (CurrentTrainingBlock.name === null) {
      Swal.fire({
        title: "Error",
        text: "Please First Select any Training Block",
        icon: "error",
      });
    } else {
      let selectALLCheckBox = document.getElementById("select_all");
      if (selectALLCheckBox.checked) {
        // Create an array to store user details objects
        const updatedUserDetailsArray = [];
  
        // Iterate through the searchResults and populate user details
        searchResults.forEach((user) => {
          if (user.isChecked) {
            // Create a simple object for each user and push it to the array
            updatedUserDetailsArray.push({
              user: user.id,
              trainingplan: CurrentTrainingBlock.id,
            });
          }
        });
  
        // Update the user details state
        const response = await unLinkTariningPlanToUser(updatedUserDetailsArray);
      
        // Optionally, you might want to update the state or do something with the response
      } else {
        // Create an array to store user details objects
        const updatedUserDetailsArray = [];
  
        // Iterate through the searchResults and populate user details
        searchResults.forEach((user) => {
          if (user.isChecked) {
            // Create a simple object for each user and push it to the array
            updatedUserDetailsArray.push({
              user: user.id,
              trainingplan: CurrentTrainingBlock.id,
            });
          }
        });
  
        // Update the user details state
        if (updatedUserDetailsArray?.length>0) {
          
          const transformData = (dataArray) => {
            const transformedData = dataArray.reduce((acc, item) => {
              Object.keys(item).forEach((key) => {
                if (key !== 'user') {
                  // If the key exists, create an array or append to the existing array
                  acc[key] = acc[key] ? acc[key] : item[key];
                } else {
                  // For the 'user' key, collect all user values into an array
                  acc[key] = acc[key] ? [...acc[key], item[key]] : [item[key]];
                }
              });
              return acc;
            }, {});
          
            return transformedData;
          };
          
          // Usage:
          const transformedObject = transformData(updatedUserDetailsArray);
          const response = await unLinkTariningPlanToUser(transformedObject);
        
          if (response?.status) {
            setTogglesidebar(false);
          setManageUserSidebar(false);
          setMasterStartDate("")
setMasterStartDay("")
setMasterEndDay("")
            Swal.fire({
              title: "Success",
              text: "Unlinked Successfully!",
              icon: "success",
            });
          }else{
            Swal.fire({
              title: "Error",
              text: response.message,
              icon: "error",
            });
          }
        }else{
          Swal.fire({
            title: "Info",
            text: "Please Select  Atleast one user  ",
            icon: "info",
          });
        }
       
      
  
        // Optionally, you might want to update the state or do something with the response
      }
    }
  };
  

  return (
    <div>
       <div>
      <Button
        size="small"
        style={{ margin: "10px", backgroundColor: "rgb(230, 126, 34)" }}
        variant="contained"
        onClick={() => {
          setTogglesidebar(false);
          setManageUserSidebar(false);
        }}
      >
        Open Training Plan
      </Button>
      </div>
      <div>
        <Button
          size="small"
          style={{ margin: "10px", backgroundColor: "rgb(230, 126, 34)" }}
          variant="contained"
          onClick={handleLinkButtonClick}
        >
          Link
        </Button>
        <Button
          size="small"
          style={{ margin: "10px", backgroundColor: "rgb(230, 126, 34)" }}
          variant="contained"
          onClick={handleUnLinkButtonClick}
        >
          Unlink
        </Button>
      </div>
      <div>
        <div style={{ display: "flex"}}>
          <Checkbox
            name="select_all"
            id="select_all"
            style={{color:"#E67E22"}}
            checked={selectAllChecked}
            onChange={toggleSelectAll}
          />
          <p className="mt-2" style={{fontSize:"medium"}}>Select All</p>
        </div>
        <Grid container spacing={2} sx={{mt:1}}  className="inputWrapper" style={{width:"100%"}}>
          <Grid item sx={{ml:1}}>
            <DatePicker
              name="date"
              selected={masterStartDate}
              showYearDropdown
              showMonthDropdown
              dateFormat="dd-MM-yyyy"
              placeholderText="DD-MM-YYYY"
              style={{backgroundColor:"white",borderColor:"white"}}
              customInput={
                <TextField label="Start Date" autoComplete="off" name="start" />
              }
              onChange={(date) => {
                setMasterStartDate(date);
              }}
            />
          </Grid>
          <Grid item sx={{ml:1}} className="inputWrapper" style={{width:"100%"}}>
            <TextField
              label="Start Day"
              type="number"
              placeholder="0"
              value={masterStartDay}
              style={{backgroundColor:"white",borderColor:"white"}}
              onChange={(e) => {
                setMasterStartDay(e.target.value);
              }}
            />
          </Grid>
          <Grid item sx={{ml:1}} className="inputWrapper" style={{width:"100%"}}>
            <TextField
              label="End Day"
              type="number"
              placeholder="0"
              style={{backgroundColor:"white",borderColor:"white"}}
              value={masterEndDay}
              onChange={(e) => {
                setMasterEndDay(e.target.value);
              }}
            />
          </Grid>
         {/*
        
         <Grid item>
            <TextField
              label="End Day"
              placeholder="0"
              value={masterEndDay}
              onChange={(e) => {
                setMasterEndDay(e.target.value);
              }}
            />
          </Grid>
        */}
          {/*
        
        <Grid item>
            <Button
              size="small"
              style={{ margin: "10px", backgroundColor: "rgb(230, 126, 34)" }}
              variant="contained"
              onClick={() => {
                // Handle the change default button click here
                // You can access userDetails.startDate, userDetails.startDay, and userDetails.endDay here
              }}
            >
              Change Default
            </Button>
          </Grid>
        */}
        </Grid>
        &nbsp;&nbsp;&nbsp;
      </div>
      <Search style={{backgroundColor:"white",borderColor:"white"}}>
        <SearchIconWrapper>
          <SearchIcon style={{color:"#E67E22"}}/>
        </SearchIconWrapper>
        <StyledInputBase
          placeholder="Search…"
          inputProps={{ "aria-label": "search" }}
          value={searchTerm}
          onChange={handleChange}
        />
      </Search>
      <div style={{height:"40vh",overflow:"scroll"}}>
        <Stack direction="column" gap={1}>
          {filteredUsers !== undefined &&
            filteredUsers.map((user) => {
              return (
                <div className="border-b">
                  <div style={{ display: "flex" }}>
                    <Checkbox
                      name={`select_${user.id}`}
                      checked={user.isChecked}
                      style={{color:"#E67E22"}}
                      
                      onChange={() => {
                        handleUserClick(user.id);
                        setSelectedUser(user.id);
                      }}
                    />
                    <p className="mt-2" style={{fontSize:"medium"}}>{user["firstname"]?user["firstname"]:"NA"}  {user["lastname"]}</p>
                    </div>
                   

             {/*
            
            
                 <Collapse in={user.isChecked}>
                    <div>
                      <Grid container spacing={1}>
                        <Grid item>
                          <DatePicker
                            name="date"
                            selected={user.userStartDate}
                            showYearDropdown
                            showMonthDropdown
                            placeholderText="DD-MM-YYYY"
                            customInput={
                              <TextField
                                label="Start Date"
                                autoComplete="off"
                                name="start"
                              />
                            }
                            onChange={(date) => {
                              const updatedSearchResults = [...searchResults];
                              const userIndex = updatedSearchResults.findIndex(
                                (u) => u.id === user.id
                              );
                              if (userIndex !== -1) {
                                updatedSearchResults[userIndex].userStartDate =
                                  date;
                                setSearchResults(updatedSearchResults);
                              }
                            }}
                          />
                        </Grid>
                        <Grid item>
                          <TextField
                            label="From day"
                            placeholder="0"
                            value={user.userStartDay}
                            onChange={(e) => {
                              const updatedSearchResults = [...searchResults];
                              const userIndex = updatedSearchResults.findIndex(
                                (u) => u.id === user.id
                              );
                              if (userIndex !== -1) {
                                updatedSearchResults[userIndex].userStartDay =
                                  e.target.value;
                                setSearchResults(updatedSearchResults);
                              }
                            }}
                          />
                        </Grid>
                        <Grid item>
                          <TextField
                            label="End Day"
                            placeholder="0"
                            value={user.userEndDay}
                            onChange={(e) => {
                              const updatedSearchResults = [...searchResults];
                              const userIndex = updatedSearchResults.findIndex(
                                (u) => u.id === user.id
                              );
                              if (userIndex !== -1) {
                                updatedSearchResults[userIndex].userEndDay =
                                  e.target.value;
                                setSearchResults(updatedSearchResults);
                              }
                            }}
                          />
                        </Grid>
                      </Grid>
                    </div>
                  </Collapse>
            */}
                </div>
              );
            })}
        </Stack>
      </div>
    </div>
  );
};

export default ManageUserSidebar;
