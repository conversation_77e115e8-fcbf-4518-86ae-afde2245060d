import { IconCheckbox, IconFlag, IconPin } from '@tabler/icons'
import React from 'react'
import Header from '../components/Header'

const NotificationPage = () => {
  const notifications = [
    {
      id: 1,
      title: '<PERSON><PERSON> added Sh. NS Bundela to your athlete list',
      date: '27/09/2022',
      time: '7:41PM'
    },
    {
      id: 2,
      title: '<PERSON><PERSON> added <PERSON><PERSON><PERSON><PERSON> to your athlete list',
      date: '27/09/2022',
      time: '7:41PM'
    },
    {
      id: 3,
      title: '<PERSON><PERSON> added Sh. NS Bundela to your athlete list',
      date: '27/09/2022',
      time: '7:41PM'
    },
    {
      id: 4,
      title: '<PERSON><PERSON> added <PERSON><PERSON> to your athlete list',
      date: '27/09/2022',
      time: '7:41PM'
    },
    {
      id: 5,
      title: '<PERSON><PERSON> added Ni<PERSON>h kulkarni to your athlete list',
      date: '27/09/2022',
      time: '7:41PM'
    },
    {
      id: 6,
      title: '<PERSON><PERSON> added nitish kulkarni to your athlete list',
      date: '27/09/2022',
      time: '7:41PM'
    },
    {
      id: 7,
      title: '<PERSON><PERSON> added <PERSON><PERSON><PERSON><PERSON> to your athlete list',
      date: '23/09/2022',
      time: '1:38PM'
    },
    {
      id: 8,
      title: '<PERSON>ak <PERSON> added Shobhit <PERSON>xena to your athlete list',
      date: '12/08/2022',
      time: '12:09PM'
    },
    {
      id: 9,
      title: 'Deepak <PERSON> added <PERSON>t sethi to your athlete list',
      date: '12/08/2022',
      time: '12:09PM'
    }
  ]
  return (
    <>
      <Header />
      <div className='grid grid-cols-1 lg:grid-cols-5 items-start gap-x-4 w-full'>
        <div className='bg-slate-200/75 p-4 sticky top-20 w-full h-screen'>
          <div className='mb-5'>
            <p className='mb-1 text-slate-600 px-1 font-medium'>
              Notification type
            </p>
            <select
              name='status'
              id='status'
              placeholder='Group'
              className='w-full border-b-2 border-slate-200 py-2 focus:outline-none text-slate-600 bg-transparent text-sm '
            >
              <option value='all'>All</option>
              <option value='workout'>Workout</option>
              <option value='race'>Race</option>
              <option value='zone'>Zone</option>
              <option value='goal'>Goal</option>
              <option value='performance-summary'>Performance summary</option>
              <option value='note'>Note</option>
              <option value='activity-workout-level'>
                Activity workout level
              </option>
              <option value='move-athlete'>Move athlete</option>
            </select>
          </div>
          <div className='flex flex-row lg:flex-col items-start my-2 lg:my-6 gap-4'>
            <div className='flex space-x-3'>
              <span>
                <IconFlag size={20} color='#444' />
              </span>
              <span className='text-slate-600 text-sm'>Flagged</span>
            </div>
            <div className='flex space-x-3'>
              <span>
                <IconPin size={20} color='#444' />
              </span>
              <span className='text-slate-600 text-sm'>Todo</span>
            </div>
            <div className='flex space-x-3'>
              <span>
                <IconCheckbox size={20} color='#444' />
              </span>
              <span className='text-slate-600 text-sm'>Permission</span>
            </div>
          </div>
        </div>
        <div className='lg:col-span-4 p-4'>
          {notifications.map((item) => (
            <div className='w-full p-4 rounded flex items-start justify-between bg-slate-100 mb-3 shadow-md gap-2' key={item.id}>
              <div className='flex space-x-4'>
                <div>
                  <img
                    src='https://www.yoska.in/kona-coach/images/athletes/profiles/f45d86d7-3fac-4b9f-8e1d-5b2e62444718.png'
                    alt='...'
                    className='w-12 lg:w-10 rounded-full'
                  />
                </div>
                <div>
                  <h3 className='text-xs md:text-sm'>{item.title}</h3>
                  <p className='text-xs text-slate-500 mt-1'>
                    {item.date} at {item.time}
                  </p>
                </div>
              </div>
              <div className='flex space-x-2 lg:space-x-4'>
                <span>
                  <IconFlag size={18} color='#444' />
                </span>
                <span>
                  <IconPin size={18} color='#444' />
                </span>
                <span>
                  <IconCheckbox size={18} color='#444' />
                </span>
              </div>
            </div>
          ))}
        </div>
      </div>
    </>
  )
}

export default NotificationPage
