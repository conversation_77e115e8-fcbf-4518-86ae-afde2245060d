import React, { useEffect, useState} from "react";
import { useSearchParams, useNavigate } from "react-router-dom";

export default function InviteUser() {
    const [searchParams] = useSearchParams();
    const channelId = searchParams.get("channelId");
    const channelName = searchParams.get("channelName");
    const navigate = useNavigate();

    useEffect(() => {
        const token = localStorage.getItem("token");
        const currentUser = JSON.parse(localStorage.getItem("currentUser"));

        if (token && currentUser) {
            navigate(`/coach-yoska?showSubscribeBox=true&channelId=${channelId}&channelName=${channelName}`);
        } else if(!token && !currentUser) {
            
            // localStorage.setItem(
            //     "invitationParams",
            //     JSON.stringify({
            //         channelId,
            //         channelName,
            //     })
            // );
            // navigate("/");
            navigate(`/?channelId=${channelId}&channelName=${channelName}`);
        }
    }, [navigate, channelId, channelName]);

    return null;
}