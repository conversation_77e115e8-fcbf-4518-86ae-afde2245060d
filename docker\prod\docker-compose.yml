services:
  app:
    image: ghcr.io/yoska-technology-solutions/yofit-frontend:release-latest
    container_name: yofit-frontend-nginx-prod
    labels:
      - "traefik.enable=true"
      - "traefik.docker.network=traefik_network"
      - "traefik.http.routers.yofit-frontend.entrypoints=websecure"
      - "traefik.http.routers.yofit-frontend.rule=Host(`fit.yoska.in`)"
      - "traefik.http.routers.yofit-frontend.tls.certresolver=lets-encrypt"
      - "traefik.http.services.yofit-frontend.loadbalancer.server.port=80"
    restart: unless-stopped
    healthcheck:
      test: "curl -f http://localhost/healthcheck || exit 1"
    networks:
      - "traefik_network"
    security_opt:
      - "no-new-privileges:true"
    expose:
      - "80"
networks:
  traefik_network:
    external: true
