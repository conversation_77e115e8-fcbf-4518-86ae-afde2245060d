import { useState, useMemo, useEffect } from "react";
import { <PERSON><PERSON> } from "../../components/ui/button";
import { Input } from "../../components/ui/input";
import {
	Card,
	CardContent,
	CardHeader,
	CardTitle,
} from "../../components/ui/card";
import {
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableHeader,
	TableRow,
} from "../../components/ui/table";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "../../components/ui/select";
import { Badge } from "../../components/ui/badge";
import { Edit, Trash2, Plus } from "lucide-react";
import { WeeklyFeedDialog } from "../../components/admin/weekly-feed-dialog";
import { DeleteConfirmDialog } from "../../components/admin/delete-confirm-dialog";
import Header from "../../components/Header";
import {
	weeklyFeedData,
	deleteWeeklyPattern,
	getAllActivityData,
} from "../../API/api-endpoint";

export default function WeeklyFeedDataPage() {
	const [data, setData] = useState([]);
	const [isLoading, setIsLoading] = useState(true);
	const [searchTerm, setSearchTerm] = useState("");
	const [selectedActivity, setSelectedActivity] = useState("All");
	const [currentPage, setCurrentPage] = useState(1);
	const [activities, setActivities] = useState([]);
	const [showDialog, setShowDialog] = useState(false);
	const [editData, setEditData] = useState(null);
	const [deleteId, setDeleteId] = useState(null);

	const pageSize = 10;

	// Fetch activities for filter dropdown (following PhaseBlock pattern)
	const fetchActivities = async () => {
		try {
			const response = await getAllActivityData();
			const activityNames =
				response?.rows?.map(
					(activity) => activity.activity || activity.activity_name
				) || [];
			setActivities(["All", ...activityNames]);
		} catch (error) {
			console.error("Error fetching activities:", error);
			setActivities(["All"]);
		}
	};

	useEffect(() => {
		fetchReport();
		fetchActivities();
	}, []);

	const fetchReport = async () => {
		setIsLoading(true);
		const response = await weeklyFeedData();
		setIsLoading(false);
		setData(response?.data || []);
	};

	const filteredData = useMemo(() => {
		return data.filter((row) => {
			const matchesSearch = row.activity.activity_name
				.toLowerCase()
				.includes(searchTerm.toLowerCase());
			const matchesActivity =
				selectedActivity === "All" ||
				row.activity.activity_name === selectedActivity;
			return matchesSearch && matchesActivity;
		});
	}, [data, searchTerm, selectedActivity]);

	const paginatedData = useMemo(() => {
		const startIndex = (currentPage - 1) * pageSize;
		return filteredData.slice(startIndex, startIndex + pageSize);
	}, [filteredData, currentPage]);

	const totalPages = Math.ceil(filteredData.length / pageSize);

	const handleEdit = (row) => {
		const normalized = {
			...row,
			activity: row.activity && row.activity.id ? row.activity.id : "",
			monday: row.monday && row.monday.id ? String(row.monday.id) : "",
			tuesday:
				row.tuesday && row.tuesday.id ? String(row.tuesday.id) : "",
			wednesday:
				row.wednesday && row.wednesday.id
					? String(row.wednesday.id)
					: "",
			thursday:
				row.thursday && row.thursday.id ? String(row.thursday.id) : "",
			friday: row.friday && row.friday.id ? String(row.friday.id) : "",
			saturday:
				row.saturday && row.saturday.id ? String(row.saturday.id) : "",
			sunday: row.sunday && row.sunday.id ? String(row.sunday.id) : "",
		};
		setEditData(normalized);
		setShowDialog(true);
	};

	const handleDelete = async (id) => {
		await deleteWeeklyPattern(id);
		fetchReport();
		setDeleteId(null);
	};

	const getWorkoutBadge = (workout) => {
		if (!workout) return <Badge variant='outline'>—</Badge>;
		if (workout === "Rest Day")
			return (
				<Badge className='bg-gray-100 text-gray-800 whitespace-nowrap'>
					Rest Day
				</Badge>
			);
		if (workout?.includes("Long"))
			return <Badge className='bg-red-100 text-red-800'>Long</Badge>;
		if (workout?.includes("Medium"))
			return (
				<Badge className='bg-yellow-100 text-yellow-800'>Medium</Badge>
			);
		if (workout?.includes("Short"))
			return <Badge className='bg-green-100 text-green-800'>Short</Badge>;
		return <Badge variant='outline'>{workout}</Badge>;
	};

	return (
		<div>
			<Header />
			<div className=' mx-auto max-w-[1300px] mt-16'>
				<Card>
					<CardHeader className='bg-orange-50 border-b'>
						<div className='flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4'>
							<CardTitle className='text-2xl font-bold text-orange-900'>
								Weekly Feed Data
							</CardTitle>
							<Button
								onClick={() => {
									setEditData(null);
									setShowDialog(true);
								}}
								className='bg-orange-600 hover:bg-orange-700 text-white'
							>
								<Plus className='h-4 w-4 mr-2' />
								Create Days
							</Button>
						</div>

						<div className='flex flex-col sm:flex-row gap-4 mt-4 justify-between'>
							<Input
								placeholder='Search by activity...'
								value={searchTerm}
								onChange={(e) => setSearchTerm(e.target.value)}
								className='max-w-sm text-sm'
							/>
							<Select
								value={selectedActivity}
								onValueChange={setSelectedActivity}
							>
								<SelectTrigger className='w-[180px]'>
									<SelectValue placeholder='Filter by Activity' />
								</SelectTrigger>
								<SelectContent className='bg-white'>
									{activities.map((activity) => (
										<SelectItem
											key={activity}
											value={activity}
										>
											{activity}
										</SelectItem>
									))}
								</SelectContent>
							</Select>
						</div>
					</CardHeader>

					<CardContent className='p-0'>
						<div className='overflow-x-auto'>
							<Table>
								<TableHeader>
									<TableRow className='bg-blue-600 hover:bg-blue-600'>
										{[
											"Sr ID",
											"Activity",
											"Monday",
											"Tuesday",
											"Wednesday",
											"Thursday",
											"Friday",
											"Saturday",
											"Sunday",
											"Days in Week",
											"Option",
											"Action",
										].map((head) => (
											<TableHead
												key={head}
												className='text-white font-semibold'
											>
												{head}
											</TableHead>
										))}
									</TableRow>
								</TableHeader>
								<TableBody>
									{isLoading ? (
										<TableRow>
											<TableCell
												colSpan={12}
												className='text-center py-8'
											>
												Loading...
											</TableCell>
										</TableRow>
									) : paginatedData.length > 0 ? (
										paginatedData.map((row, index) => (
											<TableRow
												key={row.id}
												className='hover:bg-gray-50'
											>
												<TableCell>
													{(currentPage - 1) *
														pageSize +
														index +
														1}
												</TableCell>
												<TableCell>
													<Badge variant='outline'>
														{
															row.activity
																.activity_name
														}
													</Badge>
												</TableCell>
												<TableCell>
													{getWorkoutBadge(
														row.monday?.workout
													)}
												</TableCell>
												<TableCell>
													{getWorkoutBadge(
														row.tuesday?.workout
													)}
												</TableCell>
												<TableCell>
													{getWorkoutBadge(
														row.wednesday?.workout
													)}
												</TableCell>
												<TableCell>
													{getWorkoutBadge(
														row.thursday?.workout
													)}
												</TableCell>
												<TableCell>
													{getWorkoutBadge(
														row.friday?.workout
													)}
												</TableCell>
												<TableCell>
													{getWorkoutBadge(
														row.saturday?.workout
													)}
												</TableCell>
												<TableCell>
													{getWorkoutBadge(
														row.sunday?.workout
													)}
												</TableCell>
												<TableCell>
													<Badge className='bg-blue-100 text-blue-800'>
														{row.run_days_per_week}
													</Badge>
												</TableCell>
												<TableCell>
													<Badge className='bg-purple-100 text-purple-800'>
														{row.options}
													</Badge>
												</TableCell>
												<TableCell>
													<div className='flex gap-2'>
														<Button
															variant='ghost'
															size='sm'
															onClick={() =>
																handleEdit(row)
															}
															className='text-blue-600 hover:text-blue-800'
														>
															<Edit className='h-4 w-4' />
														</Button>
														<Button
															variant='ghost'
															size='sm'
															onClick={() =>
																setDeleteId(
																	row.id
																)
															}
															className='text-red-600 hover:text-red-800'
														>
															<Trash2 className='h-4 w-4' />
														</Button>
													</div>
												</TableCell>
											</TableRow>
										))
									) : (
										<TableRow>
											<TableCell
												colSpan={12}
												className='text-center py-8 text-gray-500'
											>
												No data found
											</TableCell>
										</TableRow>
									)}
								</TableBody>
							</Table>
						</div>

						{totalPages > 1 && (
							<div className='flex justify-center gap-2 p-4 border-t'>
								<Button
									variant='outline'
									size='sm'
									onClick={() =>
										setCurrentPage((prev) =>
											Math.max(1, prev - 1)
										)
									}
									disabled={currentPage === 1}
								>
									Previous
								</Button>
								{Array.from(
									{ length: totalPages },
									(_, i) => i + 1
								).map((page) => (
									<Button
										key={page}
										variant={
											currentPage === page
												? "default"
												: "outline"
										}
										size='sm'
										onClick={() => setCurrentPage(page)}
										className={
											currentPage === page
												? "bg-orange-600 hover:bg-orange-700 text-white"
												: ""
										}
									>
										{page}
									</Button>
								))}
								<Button
									variant='outline'
									size='sm'
									onClick={() =>
										setCurrentPage((prev) =>
											Math.min(totalPages, prev + 1)
										)
									}
									disabled={currentPage === totalPages}
								>
									Next
								</Button>
							</div>
						)}
					</CardContent>
				</Card>

				<WeeklyFeedDialog
					open={showDialog}
					onOpenChange={setShowDialog}
					editData={editData}
					onSave={fetchReport}
				/>

				<DeleteConfirmDialog
					open={!!deleteId}
					onOpenChange={() => setDeleteId(null)}
					onConfirm={() => handleDelete(deleteId)}
					title='Delete Weekly Feed Data'
					description='Are you sure you want to delete this weekly feed data? This action cannot be undone.'
				/>
			</div>
		</div>
	);
}
