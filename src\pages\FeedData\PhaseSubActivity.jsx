import { useState, useMemo, useEffect } from "react";
import { Plus, Search, Edit, Trash2 } from "lucide-react";

import { <PERSON><PERSON> } from "../../components/ui/button";
import { Input } from "../../components/ui/input";
import { Card, CardContent, CardHeader } from "../../components/ui/card";
import {
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableHeader,
	TableRow,
} from "../../components/ui/table";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "../../components/ui/select";
import { Badge } from "../../components/ui/badge";
import { PhaseSubActivityDialog } from "../../components/admin/phase-sub-activity-dialog";
import { DeleteConfirmDialog } from "../../components/admin/delete-confirm-dialog";
import Header from "../../components/Header";
import {
	getAllPhaseSubData,
	deletePhaseSubData,
	getAllActivityData,
} from "../../API/api-endpoint";
import Swal from "sweetalert2";

const getActivityColor = (activity) => {
	switch (activity) {
		case "Running":
			return "bg-green-100 text-green-800";
		case "Swimming":
			return "bg-blue-100 text-blue-800";
		case "Cycling":
			return "bg-yellow-100 text-yellow-800";
		case "Fitness":
			return "bg-purple-100 text-purple-800";
		case "Triathlon":
			return "bg-red-100 text-red-800";
		default:
			return "bg-gray-100 text-gray-800";
	}
};

const getSubworkoutColor = (subworkout) => {
	switch (subworkout) {
		case "Aerobic":
			return "bg-blue-100 text-blue-800";
		case "Fartlek":
			return "bg-orange-100 text-orange-800";
		case "Easy":
			return "bg-green-100 text-green-800";
		case "Tempo":
			return "bg-yellow-100 text-yellow-800";
		case "Upper":
			return "bg-purple-100 text-purple-800";
		case "Lower":
			return "bg-indigo-100 text-indigo-800";
		case "General":
			return "bg-gray-100 text-gray-800";
		default:
			return "bg-gray-100 text-gray-800";
	}
};

export default function PhaseSubActivitiesPage() {
	const [searchTerm, setSearchTerm] = useState("");
	const [selectedActivity, setSelectedActivity] = useState("All");
	const [dialogOpen, setDialogOpen] = useState(false);
	const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
	const [editingItem, setEditingItem] = useState(null);
	const [deletingId, setDeletingId] = useState(null);
	const [currentPage, setCurrentPage] = useState(1);
	const [isLoading, setIsLoading] = useState(true);
	const [phaseSubActivities, setPhaseSubActivities] = useState([]);
	const [activities, setActivities] = useState([]);

	const itemsPerPage = 10;

	useEffect(() => {
		fetchData();
		fetchActivities();
	}, []);

	const fetchData = async () => {
		try {
			setIsLoading(true);
			const response = await getAllPhaseSubData();
			if (response?.rows && Array.isArray(response.rows)) {
				setPhaseSubActivities(response.rows);
			} else if (Array.isArray(response)) {
				setPhaseSubActivities(response);
			} else {
				setPhaseSubActivities([]);
			}
		} catch (error) {
			console.error("Error fetching phase sub activities:", error);
			setPhaseSubActivities([]);
		} finally {
			setIsLoading(false);
		}
	};

	const fetchActivities = async () => {
		try {
			const response = await getAllActivityData();
			const activityNames =
				response?.rows?.map((activity) => activity.activity_name) || [];
			setActivities(["All", ...activityNames]);
		} catch (error) {
			console.error("Error fetching activities:", error);
			setActivities(["All"]);
		}
	};

	const filteredData = useMemo(() => {
		const result = phaseSubActivities.filter((item) => {
			if (!item || !item.activity || !item.activity.activity_name) {
				return false;
			}

			const searchField = item.activity.activity_name.toLowerCase();
			const matchesSearch = searchField.includes(
				searchTerm.toLowerCase()
			);
			const matchesActivity =
				selectedActivity === "All" ||
				item.activity.activity_name === selectedActivity;

			return matchesSearch && matchesActivity;
		});
		return result;
	}, [phaseSubActivities, searchTerm, selectedActivity]);

	const paginatedData = useMemo(() => {
		const startIndex = (currentPage - 1) * itemsPerPage;
		const endIndex = startIndex + itemsPerPage;
		const result = filteredData.slice(startIndex, endIndex);
		return result;
	}, [filteredData, currentPage]);

	const totalPages = Math.ceil(filteredData.length / itemsPerPage);

	const handleEdit = (item) => {
		setEditingItem({
			...item,
			activity_id: item.activity?.id,
		});
		setDialogOpen(true);
		setDialogOpen(true);
	};

	const handleDelete = (id) => {
		setDeletingId(id);
		setDeleteDialogOpen(true);
	};

	const handleCreate = () => {
		setEditingItem(null);
		setDialogOpen(true);
	};

	const handleDialogSuccess = () => {
		fetchData();
		Swal.fire({
			title: "Success",
			text: "Phase Sub-Activity saved successfully!",
			icon: "success",
			timer: 1800,
			showConfirmButton: false,
		});
	};

	const confirmDelete = async () => {
		try {
			await deletePhaseSubData(deletingId);
			fetchData();
		} catch (error) {
			console.error("Error deleting phase sub-activity:", error);
		} finally {
			setDeleteDialogOpen(false);
			setDeletingId(null);
		}
	};

	return (
		<div>
			<Header />
			<div className='max-w-[1300px] mx-auto py-6 space-y-6 mt-16'>
				<div className='flex items-center justify-between'>
					<h1 className='text-3xl font-bold text-orange-950 '>
						Phase Sub-Activities Management
					</h1>
				</div>

				<Card>
					<CardHeader className='bg-orange-50 border-b'>
						<div className='flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between'>
							<Button
								onClick={handleCreate}
								className='bg-orange-500 hover:bg-orange-600 text-white'
							>
								<Plus className='h-4 w-4 mr-2' />
								Create Phase Sub-Activity
							</Button>

							<div className='flex gap-2 w-full sm:w-auto'>
								<Select
									value={selectedActivity}
									onValueChange={setSelectedActivity}
								>
									<SelectTrigger className='w-[150px]'>
										<SelectValue placeholder='Filter by Activity' />
									</SelectTrigger>
									<SelectContent className='bg-white'>
										{activities.map((activity) => (
											<SelectItem
												key={activity}
												value={activity}
											>
												{activity}
											</SelectItem>
										))}
									</SelectContent>
								</Select>

								<div className='relative'>
									<Search className='absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4' />
									<Input
										placeholder='Search by activity name...'
										value={searchTerm}
										onChange={(e) =>
											setSearchTerm(e.target.value)
										}
										className='pl-10 w-[250px] text-sm'
									/>
								</div>
							</div>
						</div>
					</CardHeader>

					<CardContent className='p-0'>
						<div className='overflow-x-auto'>
							<Table>
								<TableHeader>
									<TableRow className='bg-orange-500 hover:bg-orange-500'>
										<TableHead className='text-white font-semibold'>
											Sr ID
										</TableHead>
										<TableHead className='text-white font-semibold'>
											Activity Name
										</TableHead>
										<TableHead className='text-white font-semibold'>
											Phase Name
										</TableHead>
										<TableHead className='text-white font-semibold'>
											Workout Name
										</TableHead>
										<TableHead className='text-white font-semibold'>
											Subworkout Name
										</TableHead>
										<TableHead className='text-white font-semibold'>
											Action
										</TableHead>
									</TableRow>
								</TableHeader>
								<TableBody>
									{isLoading ? (
										<TableRow>
											<TableCell
												colSpan={6}
												className='text-center py-8 text-gray-500'
											>
												Loading...
											</TableCell>
										</TableRow>
									) : paginatedData.length > 0 ? (
										paginatedData.map((item, index) => (
											<TableRow
												key={item.id ?? index}
												className='hover:bg-orange-50/50'
											>
												<TableCell className='font-medium'>
													{(currentPage - 1) *
														itemsPerPage +
														index +
														1}
												</TableCell>
												<TableCell>
													<Badge
														className={getActivityColor(
															item.activity
																?.activity_name
														)}
													>
														{item.activity
															?.activity_name ||
															"N/A"}
													</Badge>
												</TableCell>
												<TableCell>
													<Badge
														variant='outline'
														className='border-orange-200 text-orange-700'
													>
														{item.phasename
															?.phase || "N/A"}
													</Badge>
												</TableCell>
												<TableCell className='font-medium'>
													{item.workout?.workout ||
														"N/A"}
												</TableCell>
												<TableCell>
													<Badge
														className={getSubworkoutColor(
															item.subworkout
																?.subworkout
														)}
													>
														{item.subworkout
															?.subworkout ||
															"N/A"}
													</Badge>
												</TableCell>
												<TableCell>
													<div className='flex gap-2'>
														<Button
															variant='ghost'
															size='sm'
															onClick={() =>
																handleEdit(item)
															}
															className='text-blue-600 hover:text-blue-800 hover:bg-blue-50'
														>
															<Edit className='h-4 w-4' />
														</Button>
														<Button
															variant='ghost'
															size='sm'
															onClick={() =>
																handleDelete(
																	item.id ||
																		item.activity_id
																)
															}
															className='text-red-600 hover:text-red-800 hover:bg-red-50'
														>
															<Trash2 className='h-4 w-4' />
														</Button>
													</div>
												</TableCell>
											</TableRow>
										))
									) : (
										<TableRow>
											<TableCell
												colSpan={6}
												className='text-center py-8 text-gray-500'
											>
												No data found
											</TableCell>
										</TableRow>
									)}
								</TableBody>
							</Table>

							{/* Pagination Controls */}
							{totalPages > 1 && (
								<div className='flex justify-center gap-2 p-4 border-t'>
									<Button
										variant='outline'
										size='sm'
										onClick={() =>
											setCurrentPage(
												Math.max(1, currentPage - 1)
											)
										}
										disabled={currentPage === 1}
									>
										Previous
									</Button>

									{Array.from({ length: 5 }, (_, i) => {
										const startPage =
											Math.floor((currentPage - 1) / 5) *
												5 +
											1;
										const page = startPage + i;
										if (page > totalPages) return null;

										return (
											<Button
												key={page}
												variant={
													currentPage === page
														? "default"
														: "outline"
												}
												size='sm'
												onClick={() =>
													setCurrentPage(page)
												}
												className={
													currentPage === page
														? "bg-orange-500 hover:bg-orange-600 text-white"
														: ""
												}
											>
												{page}
											</Button>
										);
									})}

									<Button
										variant='outline'
										size='sm'
										onClick={() =>
											setCurrentPage(
												Math.min(
													totalPages,
													currentPage + 1
												)
											)
										}
										disabled={currentPage === totalPages}
									>
										Next
									</Button>
								</div>
							)}
						</div>
					</CardContent>
				</Card>

				<PhaseSubActivityDialog
					open={dialogOpen}
					onOpenChange={setDialogOpen}
					editingItem={editingItem}
					onSuccess={handleDialogSuccess}
				/>

				<DeleteConfirmDialog
					open={deleteDialogOpen}
					onOpenChange={setDeleteDialogOpen}
					onConfirm={confirmDelete}
					title='Are you sure you want to delete this phase sub-activity?'
					description='This action cannot be undone.'
				/>
			</div>
		</div>
	);
}
