import { Button, MenuItem, Select, TextField } from "@mui/material";
import React, { useState } from "react";

const initialValues = {
  phaseBlockName: "",
  weeks: 12,
  week1: "Base",
  week2: "Build",
  week: "Base",
  week12: "Build",
};

const CreatePhaseBlock = () => {
  const [values, setValues] = useState(initialValues);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    console.log(name, value);
    setValues({
      ...values,
      [name]: value,
    });
  };

  const handleSubmit = async () => {
    console.log(values);
    // try {
    //   const response = await axios.post(
    //     "https://api.example.com/endpoint",
    //     { data: values },
    //     {
    //       headers: {
    //         "Content-Type": "application/json",
    //          Authorization: "Bearer your_access_token_here",
    //       },
    //     }
    //   );
    //   console.log(response.data);
    // } catch (err) {
    //   console.log(err.message);
    // }
  };
  return (
    <div className="container">
      <div className="title">Create Phase Block</div>
      <div className="form-body">
        <div className="form-group">
          <label className="lable">Phase Block Name</label>
          <div className="activity-inputs">
            <TextField
              className="activity-input"
              id="outlined-basic"
              label="Running"
              variant="outlined"
              name="phaseBlockName"
              value={values.phaseBlockName}
              onChange={handleInputChange}
            />
          </div>
        </div>

        <div className="form-group">
          <label className="lable">How many Weeks ? </label>
          <div
            className="activity-inputs"
            style={{ display: "flex", gap: "20px" }}
          >
            <Select
              labelId="demo-simple-select-label"
              id="demo-simple-select"
              className="activity-autocomplete"
              name="weeks"
              value={values.weeks}
              onChange={handleInputChange}
            >
              <MenuItem value={12}>12</MenuItem>
              <MenuItem value={20}>20</MenuItem>
              <MenuItem value={26}>26</MenuItem>
            </Select>
            <button style={{ width: "100px" }}>Next</button>
          </div>
        </div>

        <div className="form-group phase-block">
          <div className="phase-block-title">
            <h3>Week Number</h3>
            <h3>Phase Option</h3>
          </div>
          <div className="phase-block-form-group">
            <p>Week 1</p>
            <Select
              labelId="demo-simple-select-label"
              id="demo-simple-select"
              className="phase-block-autocomplete"
              name="week1"
              value={values.week1}
              onChange={handleInputChange}
            >
              <MenuItem value={"Base"}>Base</MenuItem>
              <MenuItem value={"Build"}>Build</MenuItem>
            </Select>
          </div>
          <div className="phase-block-form-group">
            <p>Week 2</p>
            <Select
              labelId="demo-simple-select-label"
              id="demo-simple-select"
              className="phase-block-autocomplete"
              name="week2"
              value={values.week2}
              onChange={handleInputChange}
            >
              <MenuItem value={"Base"}>Base</MenuItem>
              <MenuItem value={"Build"}>Build</MenuItem>
            </Select>
          </div>
          <div className="phase-block-form-group">
            <p>Week... </p>
            <Select
              labelId="demo-simple-select-label"
              id="demo-simple-select"
              className="phase-block-autocomplete"
              name="week"
              value={values.week}
              onChange={handleInputChange}
            >
              <MenuItem value={"Base"}>Base</MenuItem>
              <MenuItem value={"Build"}>Build</MenuItem>
            </Select>
          </div>
          <div className="phase-block-form-group">
            <p>Week12</p>
            <Select
              labelId="demo-simple-select-label"
              id="demo-simple-select"
              className="phase-block-autocomplete"
              name="week12"
              value={values.week12}
              onChange={handleInputChange}
            >
              <MenuItem value={"Base"}>Base</MenuItem>
              <MenuItem value={"Build"}>Build</MenuItem>
            </Select>
          </div>
        </div>

        <div className="form-group">
          <Button
            sx={{ background: "black", marginLeft: "-4.5%" }}
            onClick={handleSubmit}
            variant="contained"
          >
            Add Phase Block
          </Button>
        </div>
      </div>
    </div>
  );
};

export default CreatePhaseBlock;
