import React from "react";
import { Bar } from "react-chartjs-2";
import {
    Chart as ChartJS,
    CategoryScale,
    LinearScale,
    BarElement,
    Title,
    Tooltip,
    Legend,
} from "chart.js";

// Register necessary Chart.js components
ChartJS.register(CategoryScale, LinearScale, BarElement, Title, Tooltip, Legend);

const DistanceMetric = ({ activity, data }) => {
    const { plannedDistance, actualDistance, distanceUnit } = data;
    console.log("activity --> ", activity);

    // Chart Data
    const chartData = {
        labels: ["Distance"],
        datasets: [
            {
                label: "Planned",
                data: [plannedDistance],
                backgroundColor: "rgba(54, 162, 235, 0.7)",
                barThickness: 20,
            },
            {
                label: "Actual",
                data: [actualDistance],
                backgroundColor: "rgba(255, 99, 132, 0.7)",
                barThickness: 20,
            },
        ],
    };

    // Chart Options
    const options = {
        responsive: true,
        plugins: {
            title: {
                display: true,
                text: `Distance [planned(${plannedDistance}${distanceUnit}) v/s actual(${actualDistance}${distanceUnit})]`,
            },
            tooltip: {
                callbacks: {
                    label: function (context) {
                        return `${context.raw} ${distanceUnit}`;
                    },
                },
            },
        },
        scales: {
            x: {
                barPercentage: 0.8, // Adjust bar width (0.1 to 1.0)
                categoryPercentage: 0.6, // Adjust category spacing (0.1 to 1.0)
            },
            y: {
                beginAtZero: true,
                title: {
                    display: true,
                    text: `Distance (${distanceUnit})`,
                },
            },
        },
    };

    return <Bar data={chartData} options={options} />;
};

export default DistanceMetric;
