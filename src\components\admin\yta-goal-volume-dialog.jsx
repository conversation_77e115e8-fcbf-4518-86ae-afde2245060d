import { useState, useEffect } from "react";
import { Button } from "../ui/button";
import { Input } from "../ui/input";
import { Label } from "../ui/label";
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogFooter,
	DialogHeader,
	DialogTitle,
} from "../ui/dialog";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "../ui/select";
import {
	createYTAVolumedata,
	updateYTAVolumedata,
	getAllActivityData,
	getAllYTAData,
} from "../../API/api-endpoint";
import Swal from "sweetalert2";

export function YtaGoalVolumeDialog({ open, onClose, onSuccess, editingItem }) {
	const [formData, setFormData] = useState({
		activities: "",
		ytagoal_id: "",
		week: "",
		running: "",
		cycling: "",
		swimming: "",
	});
	const [isLoading, setIsLoading] = useState(false);
	const [goalNames, setGoalNames] = useState([]);
	const [isLoadingGoals, setIsLoadingGoals] = useState(false);
	const [activities, setActivities] = useState([]);
	const [isLoadingActivities, setIsLoadingActivities] = useState(false);

	// Reset form when dialog opens/closes or when editing different item
	useEffect(() => {
		if (open) {
			if (editingItem) {
				const goalId =
					editingItem.ytagoal_id ||
					editingItem.goalname_id ||
					editingItem.goal_id ||
					editingItem.ytaGoal?.id ||
					"";

				const editData = {
					activities: String(editingItem.activities || ""),
					ytagoal_id: String(goalId),
					week: String(editingItem.week || ""),
					running: String(editingItem.running || ""),
					cycling: String(editingItem.cycling || ""),
					swimming: String(editingItem.swimming || ""),
				};
				setFormData(editData);
			} else {
				const newData = {
					activities: "",
					ytagoal_id: "",
					week: "",
					running: "",
					cycling: "",
					swimming: "",
				};
				setFormData(newData);
			}
		}
	}, [open, editingItem]);

	// Fetch goal names and activities when dialog opens
	useEffect(() => {
		if (open) {
			fetchGoalNames();
			fetchActivities();
		}
	}, [open]);

	const fetchGoalNames = async () => {
		try {
			setIsLoadingGoals(true);
			const response = await getAllYTAData();
			if (response) {
				setGoalNames(response || []);
			}
		} catch (error) {
			console.error("Error fetching goal names:", error);
			Swal.fire({
				title: "Error",
				text: "Failed to fetch goal names",
				icon: "error",
				timer: 3000,
				showConfirmButton: false,
			});
		} finally {
			setIsLoadingGoals(false);
		}
	};

	const fetchActivities = async () => {
		try {
			setIsLoadingActivities(true);
			const response = await getAllActivityData();
			if (response?.rows) {
				setActivities(response.rows || []);
			}
		} catch (error) {
			console.error("Error fetching activities:", error);
			Swal.fire({
				title: "Error",
				text: "Failed to fetch activities",
				icon: "error",
				timer: 3000,
				showConfirmButton: false,
			});
		} finally {
			setIsLoadingActivities(false);
		}
	};

	const handleInputChange = (field, value) => {
		setFormData((prev) => ({
			...prev,
			[field]: value,
		}));
	};

	const handleSubmit = async (e) => {
		e.preventDefault();
		if (!formData.activities) {
			Swal.fire({
				title: "Error",
				text: "Activity is required",
				icon: "error",
				timer: 1800,
				showConfirmButton: false,
			});
			return;
		}

		if (!formData.ytagoal_id) {
			Swal.fire({
				title: "Error",
				text: "Goal name is required",
				icon: "error",
				timer: 1800,
				showConfirmButton: false,
			});
			return;
		}

		if (!formData.week || formData.week === "") {
			Swal.fire({
				title: "Error",
				text: "Week is required",
				icon: "error",
				timer: 1800,
				showConfirmButton: false,
			});
			return;
		}

		if (!formData.running || formData.running === "") {
			Swal.fire({
				title: "Error",
				text: "Running value is required",
				icon: "error",
				timer: 1800,
				showConfirmButton: false,
			});
			return;
		}

		if (!formData.cycling || formData.cycling === "") {
			Swal.fire({
				title: "Error",
				text: "Cycling value is required",
				icon: "error",
				timer: 1800,
				showConfirmButton: false,
			});
			return;
		}

		if (!formData.swimming || formData.swimming === "") {
			Swal.fire({
				title: "Error",
				text: "Swimming value is required",
				icon: "error",
				timer: 1800,
				showConfirmButton: false,
			});
			return;
		}

		try {
			setIsLoading(true);

			const apiData = {
				...formData,
				activities: parseInt(formData.activities) || 0,
				goalname_id: parseInt(formData.ytagoal_id) || 0,
				week: parseInt(formData.week) || 0,
				running: parseFloat(formData.running) || 0,
				cycling: parseFloat(formData.cycling) || 0,
				swimming: parseFloat(formData.swimming) || 0,
			};

			delete apiData.ytagoal_id;

			if (editingItem) {
				apiData.id = editingItem.id;
			}

			const response = editingItem
				? await updateYTAVolumedata(apiData)
				: await createYTAVolumedata(apiData);

			if (response?.status) {
				Swal.fire({
					title: "Success",
					text:
						response.message ||
						`Volume ${
							editingItem ? "updated" : "created"
						} successfully`,
					icon: "success",
					timer: 2000,
					showConfirmButton: false,
				});
				onSuccess();
			} else {
				throw new Error(response?.message || "Operation failed");
			}
		} catch (error) {
			console.error("Error saving volume:", error);
			Swal.fire({
				title: "Error",
				text:
					error.message || "Failed to save volume. Please try again.",
				icon: "error",
				timer: 3000,
				showConfirmButton: false,
			});
		} finally {
			setIsLoading(false);
		}
	};

	const handleClose = () => {
		if (!isLoading) {
			onClose();
		}
	};

	return (
		<Dialog open={open} onOpenChange={handleClose}>
			<DialogContent className='sm:max-w-[500px] bg-white'>
				<DialogHeader>
					<DialogTitle>
						{editingItem ? "Edit" : "Create"} YTA Goal Volume
					</DialogTitle>
					<DialogDescription>
						{editingItem
							? "Update the YTA goal volume information below."
							: "Fill in the details to create a new YTA goal volume."}
					</DialogDescription>
				</DialogHeader>

				<form onSubmit={handleSubmit} className='space-y-4'>
					<div className='grid gap-4'>
						<div className='space-y-2'>
							<Label
								htmlFor='activities'
								className='text-sm font-semibold'
							>
								Activity
							</Label>
							<Select
								value={formData.activities}
								onValueChange={(value) =>
									handleInputChange("activities", value)
								}
								disabled={isLoading || isLoadingActivities}
							>
								<SelectTrigger>
									<SelectValue placeholder='Select an activity' />
								</SelectTrigger>
								<SelectContent className='bg-white'>
									{activities.map((activity) => (
										<SelectItem
											key={activity.id}
											value={activity.id.toString()}
										>
											{activity.activity_name}
										</SelectItem>
									))}
								</SelectContent>
							</Select>
						</div>

						<div className='space-y-2'>
							<Label
								htmlFor='ytagoal_id'
								className='text-sm font-semibold'
							>
								Goal Name{" "}
							</Label>
							<Select
								value={formData.ytagoal_id}
								onValueChange={(value) =>
									handleInputChange("ytagoal_id", value)
								}
								disabled={isLoading || isLoadingGoals}
							>
								<SelectTrigger>
									<SelectValue placeholder='Select a goal name' />
								</SelectTrigger>
								<SelectContent className='bg-white'>
									{goalNames.map((goal) => (
										<SelectItem
											key={goal.id}
											value={goal.id.toString()}
										>
											{goal.goalname}
										</SelectItem>
									))}
								</SelectContent>
							</Select>
						</div>

						<div className='space-y-2'>
							<Label
								htmlFor='week'
								className='text-sm font-semibold'
							>
								Week
							</Label>
							<Input
								id='week'
								type='number'
								className='w-full text-sm'
								value={formData.week || ""}
								onChange={(e) =>
									handleInputChange("week", e.target.value)
								}
								placeholder='Enter week number'
								disabled={isLoading}
								required
							/>
						</div>

						<div className='space-y-2'>
							<Label
								htmlFor='running'
								className='text-sm font-semibold'
							>
								Running
							</Label>
							<Input
								id='running'
								type='number'
								step='0.01'
								className='w-full text-sm'
								value={formData.running || ""}
								onChange={(e) =>
									handleInputChange("running", e.target.value)
								}
								placeholder='Enter running value'
								disabled={isLoading}
								required
							/>
						</div>

						<div className='space-y-2'>
							<Label
								htmlFor='cycling'
								className='text-sm font-semibold'
							>
								Cycling
							</Label>
							<Input
								id='cycling'
								type='number'
								step='0.01'
								className='w-full text-sm'
								value={formData.cycling || ""}
								onChange={(e) =>
									handleInputChange("cycling", e.target.value)
								}
								placeholder='Enter cycling value'
								disabled={isLoading}
								required
							/>
						</div>

						<div className='space-y-2'>
							<Label
								htmlFor='swimming'
								className='text-sm font-semibold'
							>
								Swimming
							</Label>
							<Input
								id='swimming'
								type='number'
								step='0.01'
								className='w-full text-sm'
								value={formData.swimming || ""}
								onChange={(e) =>
									handleInputChange(
										"swimming",
										e.target.value
									)
								}
								placeholder='Enter swimming value'
								disabled={isLoading}
								required
							/>
						</div>
					</div>

					<DialogFooter className='gap-2'>
						<Button
							type='button'
							variant='outline'
							onClick={handleClose}
							disabled={isLoading}
						>
							Cancel
						</Button>
						<Button
							type='submit'
							disabled={isLoading}
							className='bg-orange-600 hover:bg-orange-700 text-white'
						>
							{isLoading
								? "Saving..."
								: editingItem
								? "Update Volume"
								: "Create Volume"}
						</Button>
					</DialogFooter>
				</form>
			</DialogContent>
		</Dialog>
	);
}
