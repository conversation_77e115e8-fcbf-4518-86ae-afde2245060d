import { Input } from '@mantine/core'
import { IconX } from '@tabler/icons'
import React, { useState } from 'react'

const ScheduleWorkout = ({
  preview,
  setPreview,
  workoutName,
  setWorkoutName,
  workoutDes,
  setWorkoutDes,
  handleSubmit
}) => {
  const [plannedDuration, setPlannedDuration] = useState('')
  const [plannedDistance, setPlannedDistance] = useState('')
  const [actualDuration, setActualDuration] = useState('')
  const [actualDistance, setActualDistance] = useState('')

  return (
    <>
      <div
        className={`${
          preview ? 'md:fixed' : 'md:absolute'
        } md:top-32 md:right-2/2 px-4 py-6 bg-slate-50 drop-shadow-2xl rounded-md border-2 border-slate-200/75 w-full md:w-8/12 lg:w-4/12 z-50`}
        style={{ display: preview ? 'block' : 'none' }}
      >
        <div className='flex items-center justify-between'>
          <span className='text-lg m-0 pb-2'>Manage Workout</span>
          <span className='text-slate-600'>
            <IconX
              size={22}
              className='mb-2 cursor-pointer'
              onClick={() => setPreview(false)}
            />
          </span>
        </div>
        <form
          className='mt-4 flex w-full flex-col items-start gap-5'
          onSubmit={handleSubmit}
        >
          {/* <h2 className='text-sm bg-blue-500 text-white px-4 py-1 rounded'>
            {title.endsWith('Walk') ? 'Walking' : 'Running'}
          </h2> */}
          <div className='w-full flex flex-col items-start gap-2'>
            <label htmlFor='name' className='w-full'>
              Name
            </label>
            <input
              type='text'
              name='name'
              id='name'
              placeholder='Enter workout title'
              value={workoutName}
              onChange={(e) => setWorkoutName(e.target.value)}
              className='border p-2 w-full text-sm focus:outline-none rounded border-slate-300'
            />
          </div>
          <div className='w-full flex flex-col items-start gap-2'>
            <label htmlFor='name' className='w-full'>
              Description
            </label>
            <textarea
              name='description'
              className='border p-2 w-full text-sm focus:outline-none rounded border-slate-300'
              id='description'
              placeholder='Enter workout description'
              cols='30'
              rows='6'
              value={workoutDes}
              onChange={(e) => setWorkoutDes(e.target.value)}
            ></textarea>
          </div>
          <div className='w-full grid grid-cols-1 lg:grid-cols-2 items-start gap-4'>
            {/* durations */}
            <div className='w-full flex flex-col items-start gap-4'>
              <div>
                <h2 className='text-lg'>Planned</h2>
              </div>
              <div className='w-full grid grid-cols-1 md:grid-cols-2 items-start gap-4'>
                <div className='w-full flex flex-col items-start gap-2'>
                  <label htmlFor='name' className='w-full text-sm'>
                    Duration
                  </label>
                  <Input
                    type={'nunmber'}
                    placeholder={'00:00:00'}
                    value={plannedDuration}
                    onChange={(e) => setPlannedDuration(e.target.value)}
                    className='w-full text-sm'
                  />
                </div>
                <div className='w-full flex flex-col items-start gap-2'>
                  <label htmlFor='name' className='w-full text-sm'>
                    Distance (Km)
                  </label>
                  <Input
                    type={'nunmber'}
                    value={plannedDistance}
                    placeholder={'Ex. 2'}
                    onChange={(e) => setPlannedDistance(e.target.value)}
                    className='w-full text-sm'
                  />
                </div>
              </div>
              <div className='w-full flex flex-col items-start gap-4'>
                <div>
                  <h2 className='text-lg'>Actuals</h2>
                </div>
                <div className='w-full grid grid-cols-1 md:grid-cols-2 items-start gap-4'>
                  <div className='w-full flex flex-col items-start gap-2'>
                    <label htmlFor='name' className='w-full text-sm'>
                      Duration
                    </label>
                    <Input
                      type={'nunmber'}
                      placeholder={'00:00:00'}
                      value={actualDuration}
                      onChange={(e) => setActualDuration(e.target.value)}
                      className='w-full text-sm'
                    />
                  </div>
                  <div className='w-full flex flex-col items-start gap-2'>
                    <label htmlFor='name' className='w-full text-sm'>
                      Distance (Km)
                    </label>
                    <Input
                      type={'nunmber'}
                      value={actualDistance}
                      placeholder={'Ex. 5'}
                      onChange={(e) => setActualDistance(e.target.value)}
                      className='w-full'
                    />
                  </div>
                </div>
              </div>
            </div>
            {/* comments */}
            <div className='w-full flex flex-col items-start gap-2'>
              <label htmlFor='name' className='w-full text-lg'>
                Comments
              </label>
              <textarea
                name='description'
                className='border p-2 w-full text-sm focus:outline-none rounded border-slate-300'
                id=''
                cols='30'
                rows='9'
              ></textarea>
            </div>
          </div>
          <div>
            <button
              className='px-6 py-2 text-white bg-orange-500 rounded'
              type='submit'
            >
              Submit
            </button>
          </div>
        </form>
      </div>
    </>
  )
}

export default ScheduleWorkout
