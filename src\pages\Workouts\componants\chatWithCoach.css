/* .sticky-footer {
    position: unset !important;
    width: 30.7vw !important;
}

.type-msg {
    width: 20vw;
} */

.footer-container {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    width: 100%;
    /* padding: 10px; */
    box-sizing: border-box;
}

.footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px;
    background-color: white;
    border-top: 1px solid #ccc;
    box-shadow: 0 -1px 5px rgba(0, 0, 0, 0.1);
    width: 100%;
    box-sizing: border-box;
}

.input-box {
    display: flex;
    align-items: center;
    flex-grow: 1;
    max-width: calc(100% - 60px); /* Adjusts for the icons */
    margin-right: 10px; /* Optional: adjust margin between the input and icons */
}

.type-msg {
    flex-grow: 1;
    border: 1px solid #ccc;
    border-radius: 20px;
    padding: 5px 10px;
    box-sizing: border-box;
    width: 100%;
}

.plus-bar-content, .plus-bar-icon-div {
    margin: 0 10px;
}

.plus-bar-icon-label {
    font-size: 14px;
    color: #666;
    text-align: center;
}

.line-plus-icon-menu {
    border-top: 1px solid #ccc;
    margin: 5px 0;
}

.footer .input-box svg {
    flex-shrink: 0; /* Prevents icons from shrinking */
}

.chatPaper1 {
    padding-bottom: 5% !important;
}