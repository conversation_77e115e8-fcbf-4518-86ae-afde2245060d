import React from "react";
import Calendar from "react-calendar";
// import "react-calendar/dist/Calendar.css";
import "./minicalender.css";

const MiniCalender = (props) => {
  const { date, setDate } = props;

  return (
    <div style={{ padding: "5%" }}>
      <Calendar
        onChange={setDate}
        value={date}
        next2Label={null}
        prev2Label={null}
      />
    </div>
  );
};

export default MiniCalender;
