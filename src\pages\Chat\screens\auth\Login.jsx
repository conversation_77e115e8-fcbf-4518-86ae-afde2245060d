import React from "react";
import "./../../styles/auth/login.css";
import { Box, Button, Divider, Paper, Typography } from "@mui/material";
import { useNavigate } from "react-router-dom";
import { auth, provider } from "../../../../API/firebase.config";
import { signInWithPopup } from "firebase/auth";
import { getDatabase, ref, set, get } from "firebase/database";
import { storeUserCreds } from "../../redux/action/authAction";
import { useDispatch } from "react-redux";
import Swal from "sweetalert2";

const Login = () => {
  const navigate = useNavigate();
  const dispatch = useDispatch();

  const paper = {
    padding: "0rem",
    minHeight: "80vh",
    maxWidth: "27rem",
  };

  const handleSignUpWithGoogle = () => {
    signInWithPopup(auth, provider).then((data) => {
      console.log(data);
      const { uid, displayName, email, photoURL } = data.user;
      console.log(uid, displayName, email, photoURL);
      writeUserData(uid, displayName, email, photoURL);
      return;
    });
  };

  function writeUserData(uid, displayName, email, photoURL) {
    const db = getDatabase();
    const userRef = ref(db, "users/" + uid);

    get(userRef)
      .then((snapshot) => {
        if (snapshot.exists()) {
          dispatch(storeUserCreds(uid, displayName, email, photoURL));
          Swal();
          Swal.fire({
            title: "Success",
            text: "Sign in success!",
            icon: "success",
          });
          navigate("/");
          return;
        } else {
          set(userRef, {
            uid,
            displayName,
            email,
            photoURL,
            onlineStatus: {
              isOnline: false,
            },
          })
            .then(() => {
              Swal.fire({
                title: "Success",
                text: "Sign in success!",
                icon: "success",
              });

              dispatch(storeUserCreds(uid, displayName, email, photoURL));
              navigate("/");
              return;
            })
            .catch((error) => {
              console.error("Failed to write user data:", error);
            });
        }
      })
      .catch((error) => {
        console.error("Failed to retrieve user data:", error);
      });
  }

  return (
    <Box
      sx={{ display: "flex", justifyContent: "center", alignItems: "center" }}
    >
      <Paper sx={{ flexGrow: 1 }} elevation={10} style={paper}>
        <div className="login-container">
          <img
            className="logo-img-travel-buddy"
            height={150}
            src="https://play-lh.googleusercontent.com/TECjsAu9DWLbKBuDl2sDHEUhlTh-n0eTgrHbt-7-a33Fy2p-slkHg8FN6r_dCbKBdA"
            alt=""
          />

          <Typography variant="h2" className="title">
            Sign In
          </Typography>
          <Box className="inputs-parent">
            <input className="auth-inputs" type="text" placeholder="E-mail" />
            <input
              className="auth-inputs"
              type="password"
              placeholder="Password"
            />
            <Button
              className="auth-btns"
              variant="contained"
              color="primary"
              type="submit"
            >
              Sign In
            </Button>
          </Box>
          <Divider className="or-divider" />
          <span className="auth-span ">or</span>
          <Button onClick={handleSignUpWithGoogle} className="google-login-btn">
            <img
              className="google-icon"
              height={14}
              src="/images/google.png"
              alt="google-icon"
            />
            Continue with google
          </Button>
          <span className="not-have-acc auth-span ">
            Not have a account ?
            <span
              onClick={() => navigate("/register")}
              className="reg-span auth-span "
            >
              Register
            </span>
          </span>
        </div>
      </Paper>
    </Box>
  );
};

export default Login;
