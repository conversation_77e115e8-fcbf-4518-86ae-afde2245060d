import {
	Dialog,
	DialogContent,
	DialogHeader,
	DialogTitle,
	DialogDescription,
} from "../../components/ui/dialog";
import { Button } from "../../components/ui/button";
import { AlertTriangle } from "lucide-react";

export function DeleteConfirmDialog({
	open,
	onOpenChange,
	onConfirm,
	title,
	description,
}) {
	return (
		<Dialog open={open} onOpenChange={onOpenChange}>
			<DialogContent className='max-w-md bg-white'>
				<DialogHeader>
					<div className='flex items-center gap-2'>
						<AlertTriangle className='h-5 w-5 text-red-600' />
						<DialogTitle className='text-red-900'>
							{title}
						</DialogTitle>
					</div>
					<DialogDescription className='text-gray-600'>
						{description}
					</DialogDescription>
				</DialogHeader>

				<div className='flex justify-end gap-2 pt-4'>
					<Button
						variant='outline'
						onClick={() => onOpenChange(false)}
					>
						Cancel
					</Button>
					<Button
						className='bg-[#ef4444] hover:bg-[#dc2626] text-white'
						variant='destructive'
						onClick={() => {
							onConfirm();
							onOpenChange(false);
						}}
					>
						Delete
					</Button>
				</div>
			</DialogContent>
		</Dialog>
	);
}
