import React, { useState } from 'react'
import Header from '../components/Header'
import { RichTextEditor } from '@mantine/rte'

const initialValue = `
  <h3><u>Walking Pace Guidelines</u></h3>
  
  <p><u>Easy Pace</u> - This would be a easy pace where you would walk at a normal pace with a light swing of arms and keeping the steps continuous.</p> 

  <p><u>Moderate Pace</u> - Your effort levels would be slightly higher than an easy pace walk and this would be mean that you would be swinging your arms a bit more and also increasing your stride (distance you take in a walking step) length little more and also the cadence (feet turn over - number of times of steps you take in a minute for example) would increase meaning you start to take a few more steps in a minute(no need to count but just be aware to step up the efforts) as compared to an easy paced walk.</p>

  <p><u>Brisk Pace</u> - Your effort levels would be slightly higher than a moderate pace walk and this would be mean that you would be swinging your arms as fast as you can and also increasing your stride (distance you take in a walking step) length to as much as you can and also the cadence (feet turn over - number of times of steps you take in a minute for example) would increase meaning you start to take a few more steps in a minute (no need to count but just be aware to step up the efforts) as compared to a moderate paced walk.</p>

  <p>If you are using a smart phone app to measure the distance or pace while you are walking, you could use the below also as a guidance. Please note that they may vary a bit based on your current fitness.</p>

  <p><u>Easy Pace</u> - This would be a easy pace where you would maybe cover 1 km in about 15 to 18 minutes.</p> 

  <p><u>Moderate Pace</u> - This would be a easy pace where you would maybe cover 1 km in about 12 to 15 minutes.</p> 

  <p><u>Brisk Pace</u> - This would be a easy pace where you would maybe cover 1 km in about 10 to 12 minutes.</p> 
  `

const InstructionsPage = () => {
  const [value, onChange] = useState(initialValue)
  return (
    <>
      <Header />
      <div className='p-4'>
        {/* Title */}
        <div className='w-full mb-6'>
          <h1 className='font-medium text-xl'>Instructions</h1>
        </div>
        {/* /Title */}
        {/* Text editor */}
        <RichTextEditor value={value} onChange={onChange} id='rte' />
        {/* /Text editor */}
        <br />
      </div>
    </>
  )
}

export default InstructionsPage
