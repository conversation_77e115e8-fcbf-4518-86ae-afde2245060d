import { Box, Paper, Typography } from "@mui/material";
import React from "react";
import "./../styles/chatlanding.css";
import "./../styles/newchat/newChat.css";
import { useSelector } from "react-redux";
import NavGroupInfo from "../components/navbar/NavGroupInfo";
// import GroupMembers from "../../components/group/GroupMembers";
// import { db, get, ref, runTransaction } from "../../../../API/firebase.config";
import Swal from "sweetalert2";
import { URL } from "../../../API/api-endpoint";
import FiberManualRecordIcon from '@mui/icons-material/FiberManualRecord';

export default function ChatProfile() {
  const paper = {
    maxWidth: "27rem",
    overflowY : "scroll",
    backgroundColor: "rgb(255, 222, 173)",
    boxShadow:"unset",
  };

  const { currentUser } = useSelector((state) => state.auth);
  const { openedUser } = useSelector(state => state.users)
  const userDetail = JSON.parse(localStorage.getItem("user"));

 
  return (
    <Box
      sx={{ display: "flex", justifyContent: "center", alignItems: "center" }}
    >
      <Paper sx={{ flexGrow: 1 }} elevation={10} style={paper} className="chatPaper">
        {(userDetail?.onboardingState == "community") ? (
          <NavGroupInfo
          backButtonPath={"/group-chat/creator"}
          groupName={openedUser?.name}
        />
        ) : (
          <NavGroupInfo
          backButtonPath={"/group-chat/creator"}
          groupName={openedUser?.displayName}
        />
        )}

        <Box
          sx={{
            // backgroundColor: "#FDFBE7",
            padding: "8px",
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
          }}
        >
          <img
            width={"250px"}
            style={{ borderRadius: "1000px" }}
            
            src={openedUser?.photoURL ? `${URL}/static/public/userimages/${openedUser?.photoURL}` : "https://i.ibb.co/5xCF7vx/u-https-spng-pngfind-com-pngs-s-610-6104451-image-placeholder-png-user-profile-placeholder-image-png.jpg"}
            alt=""
          />
        </Box>
        <Box sx={{ display: 'flex', justifyContent: "center", alignItems: 'center', flexDirection: 'row',margin:"10px 0 20px 0" }} >
                                <FiberManualRecordIcon sx={
                                    openedUser?.onlineStatus?.isOnline
                                        ? { color: '#056B6B', display: 'inline-block', fontSize: '10px' }
                                        : { color: '#F49696', display: 'inline-block', fontSize: '10px' }
                                } />
                                <Typography className='active-inactive'>
                                    {openedUser?.onlineStatus?.isOnline ? "Active now" : "Not active"}
                                </Typography>
                            </Box>

        <h5
          style={{ textAlign: "center", fontSize: "16px", marginTop: "11px" , color:"gray"}}
        >
          Email
        </h5>
        
        <h5
          style={{ textAlign: "center", fontSize: "16px", marginTop: "11px" }}
        >
          {openedUser?.email}
        </h5>

       
      </Paper>
    </Box>
  );
}
