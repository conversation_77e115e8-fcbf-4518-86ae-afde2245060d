import {
    Chip,
    FormControl,
    FormLabel,
    Grid,
    MenuItem,
    OutlinedInput,
    Select,
    TextField,
  } from "@mui/material";
  import { <PERSON><PERSON>, Modal } from "antd";
  import React, { useEffect, useState } from "react";
  import {
    CreatePrograms,
    CreateZonesClasification,
    createPhaseSubdata,
    createworkoutdata,
    getAllActivityData,
    getAllPrograms,
    getAlllevels,
    getAllsubactivityData,
    getAllworkoutData,
    updatePhaseSubdata,
    updatePrograms,
    updateZonesClasification,
    updateworkoutdata,
    weeklyFeedDataPattern,
    weeklyFeedDataProgram,
  } from "../../API/api-endpoint";
  import { useFormik } from "formik";
  import Swal from "sweetalert2";
  import SlickCarousel from "../../pages/SlickCarousel";
  const scoreData = [1, 2, 3, 4, 5];
  const CreateWorkoutData = ({
    fetchReport,
    setShowAssesmentModal,
    showAssesmentModal,
    editData,
    setEditData,
  }) => {
    const [programList, setProgramList] = useState([]);
    const [workoutList, setworkoutList] = useState([]);
    const [subworkoutList, setsubworkoutList] = useState([]);
  
    console.log("editData", editData);
    const formik = useFormik({
      initialValues: {
        activity_id: "",
        workout: "",
      },
      validate: (values) => {
        const errors = {};
        if (!values.activity_id) {
          errors.activity_id = "Activity name is required";
        }
        if (!values.workout) {
          errors.workout = "Workout name is required";
        }
        return errors;
      },
      // validationSchema: {},
      onSubmit: (values, { resetForm }) => {
        handleSubmitAssesmentForm(values, resetForm);
      },
    });
    console.log("formik", formik?.values);
    const getAllProgramsData = async () => {
      const response = await getAllActivityData();
      console.log("response", response);
      setProgramList(response?.rows);
    };
  
    const getAllWorkoutData = async () => {
      const response = await getAllworkoutData();
      console.log("response", response);
      setworkoutList(response);
    };
    const getAllSubworkoutData = async () => {
      const response = await getAllsubactivityData();
      console.log("response", response);
      setsubworkoutList(response);
    };
    useEffect(() => {
      getAllProgramsData();
      getAllWorkoutData();
      getAllSubworkoutData();
    }, []);
  
    const handleSubmitAssesmentForm = async (data, resetForm) => {
      console.log("dfvdsbvf bdvbv");
      let response = "";
      if (editData?.id) {
        response = await updateworkoutdata(data);
      } else {
        response = await createworkoutdata(data);
      }
      if (response?.status) {
        Swal.fire({
          title: "Success",
          text: response.message,
          icon: "success",
        });
        setShowAssesmentModal(false);
        setEditData({});
        formik?.setValues({ workout: "" });
        fetchReport();
        resetForm();
      } else {
        Swal.fire({
          title: "Error",
          text: response.message,
          icon: "error",
        });
      }
      console.log("response", response);
    };
    useEffect(() => {
      if (editData?.id) {
        const { srID, ...data } = editData;
        console.log("data", data);
        formik?.setValues(data);
      }
    }, [editData?.id]);
    return (
      <Modal
        width={1200}
        open={showAssesmentModal}
        onCancel={() => {
          setShowAssesmentModal(false);
          setEditData({});
          formik?.setValues({ workout: "" });
          formik.resetForm();
        }}
        footer={
          <div></div>
          //   loading={isLoading}
        }
      >
        <div className="headingCont">
          <span className="heading">{editData?.id ? "Edit " : "Create"}</span>{" "}
          <span className="orange heading">Workout Data</span>
        </div>
        {/* <h1>{editData ? editData.challengeId : values.challengeId}</h1> */}
        <div className="parentCont">
          <form className="form1" onSubmit={formik.handleSubmit}>
            <Grid container spacing={2}>
              <Grid item xs={12} sm={11}>
                <FormLabel>Activity Name<span className="text-[red]">*</span></FormLabel>
  
                <TextField
                  fullWidth
                  size="small"
                  select
                  name="activity_id"
                  SelectProps={{
                    MenuProps: {
                      PaperProps: {
                        style: {
                           scrollbarColor:"#E67E22 white",
                           scrollbarWidth:"thin"
                        },
                      },
                    },
                  }}
                  value={formik?.values?.activity_id}
                  onChange={formik.handleChange}
                  error={formik.touched.activity_id && formik.errors.activity_id}
                  helperText={
                    formik.touched.activity_id && formik.errors.activity_id
                  }
                  id="form-layouts-separator-select"
                  labelId="form-layouts-separator-select-label"
                  input={<OutlinedInput id="select-multiple-language" />}
                >
                  <MenuItem value={""} disabled>
                    Select Activity
                  </MenuItem>
                  {programList?.map((value, index) => {
                    return (
                      <MenuItem value={value?.id}>
                        {value?.activity_name}
                      </MenuItem>
                    );
                  })}
                </TextField>
              </Grid>
              <Grid item xs={12} sm={11}>
                <FormLabel>Workout Name<span className="text-[red]">*</span></FormLabel>
  
                <TextField
                  fullWidth
                  placeholder="Workout name"
                  size="small"
                  type="text"
                  name="workout"
                  value={formik?.values?.workout}
                  onChange={formik.handleChange}
                  error={formik.touched.workout && formik.errors.workout}
                  helperText={formik.touched.workout && formik.errors.workout}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <Button
                  className="btn"
                  key="submit"
                  type="primary"
                  onClick={() => formik.handleSubmit()}
                >
                  Submit
                </Button>
              </Grid>
            </Grid>
          </form>
          <div className="slick-container">
            <SlickCarousel />
          </div>
        </div>
      </Modal>
    );
  };
  
  export default CreateWorkoutData;
  