import { useState, useEffect } from "react";
import { Button } from "../ui/button";
import { Input } from "../ui/input";
import { Label } from "../ui/label";
import { Textarea } from "../ui/textarea";
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from "../ui/dialog";
import {
	createScoreReadnessdata,
	updateScoreReadnessdata,
} from "../../API/api-endpoint";
import Swal from "sweetalert2";

export const ScorDataDialog = ({ open, onClose, onSuccess, editingItem }) => {
	const [formData, setFormData] = useState({
		ironman_description: "",
		olympic_description: "",
		sprint_description: "",
		seventy_point_three_description: "",
		points_range_from: "",
		points_range_to: "",
	});
	const [isLoading, setIsLoading] = useState(false);

	useEffect(() => {
		if (open) {
			if (editingItem?.id) {
				const editData = {
					ironman_description: editingItem.ironman_description || "",
					olympic_description: editingItem.olympic_description || "",
					sprint_description: editingItem.sprint_description || "",
					seventy_point_three_description:
						editingItem.seventy_point_three_description || "",
					points_range_from: editingItem.points_range_from || "",
					points_range_to: editingItem.points_range_to || "",
				};
				setFormData(editData);
			} else {
				const newData = {
					ironman_description: "",
					olympic_description: "",
					sprint_description: "",
					seventy_point_three_description: "",
					points_range_from: "",
					points_range_to: "",
				};
				setFormData(newData);
			}
		}
	}, [open, editingItem]);

	const handleInputChange = (field, value) => {
		setFormData((prev) => ({
			...prev,
			[field]: value,
		}));
	};

	const handleSubmit = async (e) => {
		e.preventDefault();

		if (!formData.ironman_description.trim()) {
			Swal.fire({
				title: "Error",
				text: "Ironman description is required",
				icon: "error",
				timer: 1800,
				showConfirmButton: false,
			});
			return;
		}

		if (!formData.olympic_description.trim()) {
			Swal.fire({
				title: "Error",
				text: "Olympic description is required",
				icon: "error",
				timer: 1800,
				showConfirmButton: false,
			});
			return;
		}

		if (!formData.sprint_description.trim()) {
			Swal.fire({
				title: "Error",
				text: "Sprint description is required",
				icon: "error",
				timer: 1800,
				showConfirmButton: false,
			});
			return;
		}

		if (!formData.seventy_point_three_description.trim()) {
			Swal.fire({
				title: "Error",
				text: "Seventy point three description is required",
				icon: "error",
				timer: 1800,
				showConfirmButton: false,
			});
			return;
		}

		if (!formData.points_range_from.trim()) {
			Swal.fire({
				title: "Error",
				text: "Points range from is required",
				icon: "error",
				timer: 1800,
				showConfirmButton: false,
			});
			return;
		}

		if (!formData.points_range_to.trim()) {
			Swal.fire({
				title: "Error",
				text: "Points range to is required",
				icon: "error",
				timer: 1800,
				showConfirmButton: false,
			});
			return;
		}

		try {
			setIsLoading(true);

			const apiData = {
				ironman_description: formData.ironman_description.trim(),
				olympic_description: formData.olympic_description.trim(),
				sprint_description: formData.sprint_description.trim(),
				seventy_point_three_description:
					formData.seventy_point_three_description.trim(),
				points_range_from: parseInt(formData.points_range_from),
				points_range_to: parseInt(formData.points_range_to),
			};

			let response;
			if (editingItem?.id) {
				apiData.id = editingItem.id;
				response = await updateScoreReadnessdata(apiData);
			} else {
				response = await createScoreReadnessdata(apiData);
			}

			if (response?.status) {
				Swal.fire({
					title: "Success",
					text:
						response.message ||
						`Score data ${
							editingItem?.id ? "updated" : "created"
						} successfully`,
					icon: "success",
					timer: 2000,
					showConfirmButton: false,
				});
				onSuccess();
			} else {
				Swal.fire({
					title: "Error",
					text: response?.message || "Failed to save score data",
					icon: "error",
					timer: 3000,
					showConfirmButton: false,
				});
			}
		} catch (error) {
			console.error("Error saving score data:", error);
			Swal.fire({
				title: "Error",
				text: "An error occurred while saving the score data",
				icon: "error",
				timer: 3000,
				showConfirmButton: false,
			});
		} finally {
			setIsLoading(false);
		}
	};

	return (
		<Dialog open={open} onOpenChange={onClose}>
			<DialogContent className='sm:max-w-2xl bg-white max-h-[90vh] overflow-y-auto'>
				<DialogHeader>
					<DialogTitle className='text-lg font-semibold text-gray-900'>
						{editingItem?.id
							? "Edit Score Data"
							: "Create Score Data"}
					</DialogTitle>
				</DialogHeader>

				<form onSubmit={handleSubmit} className='space-y-4'>
					<div className='grid gap-4'>
						<div className='space-y-2'>
							<Label
								htmlFor='ironman_description'
								className='text-sm font-semibold'
							>
								Ironman Description{" "}
							</Label>
							<Textarea
								id='ironman_description'
								className='w-full text-sm min-h-[40px]'
								value={formData.ironman_description}
								onChange={(e) =>
									handleInputChange(
										"ironman_description",
										e.target.value
									)
								}
								placeholder='Enter ironman description'
								disabled={isLoading}
								required
							/>
						</div>

						<div className='space-y-2'>
							<Label
								htmlFor='olympic_description'
								className='text-sm font-semibold'
							>
								Olympic Description{" "}
							</Label>
							<Textarea
								id='olympic_description'
								className='w-full text-sm min-h-[40px]'
								value={formData.olympic_description}
								onChange={(e) =>
									handleInputChange(
										"olympic_description",
										e.target.value
									)
								}
								placeholder='Enter olympic description'
								disabled={isLoading}
								required
							/>
						</div>

						<div className='space-y-2'>
							<Label
								htmlFor='sprint_description'
								className='text-sm font-semibold'
							>
								Sprint Description{" "}
							</Label>
							<Textarea
								id='sprint_description'
								className='w-full text-sm min-h-[40px]'
								value={formData.sprint_description}
								onChange={(e) =>
									handleInputChange(
										"sprint_description",
										e.target.value
									)
								}
								placeholder='Enter sprint description'
								disabled={isLoading}
								required
							/>
						</div>

						<div className='space-y-2'>
							<Label
								htmlFor='seventy_point_three_description'
								className='text-sm font-semibold'
							>
								Seventy Point Three Description{" "}
							</Label>
							<Textarea
								id='seventy_point_three_description'
								className='w-full text-sm min-h-[40px]'
								value={formData.seventy_point_three_description}
								onChange={(e) =>
									handleInputChange(
										"seventy_point_three_description",
										e.target.value
									)
								}
								placeholder='Enter seventy point three description'
								disabled={isLoading}
								required
							/>
						</div>

						<div className='grid grid-cols-1 sm:grid-cols-2 gap-4'>
							<div className='space-y-2'>
								<Label
									htmlFor='points_range_from'
									className='text-sm font-semibold'
								>
									Points Range From{" "}
								</Label>
								<Input
									id='points_range_from'
									type='number'
									className='w-full text-sm'
									value={formData.points_range_from}
									onChange={(e) =>
										handleInputChange(
											"points_range_from",
											e.target.value
										)
									}
									placeholder='Enter start range'
									disabled={isLoading}
									required
								/>
							</div>

							<div className='space-y-2'>
								<Label
									htmlFor='points_range_to'
									className='text-sm font-semibold'
								>
									Points Range To{" "}
								</Label>
								<Input
									id='points_range_to'
									type='number'
									className='w-full text-sm'
									value={formData.points_range_to}
									onChange={(e) =>
										handleInputChange(
											"points_range_to",
											e.target.value
										)
									}
									placeholder='Enter end range'
									disabled={isLoading}
									required
								/>
							</div>
						</div>
					</div>

					<div className='flex justify-end gap-3 pt-4'>
						<Button
							type='button'
							variant='outline'
							onClick={onClose}
							disabled={isLoading}
						>
							Cancel
						</Button>
						<Button
							type='submit'
							className='bg-orange-600 hover:bg-orange-700 text-white'
							disabled={isLoading}
						>
							{isLoading
								? "Saving..."
								: editingItem?.id
								? "Update Score Data"
								: "Create Score Data"}
						</Button>
					</div>
				</form>
			</DialogContent>
		</Dialog>
	);
};
