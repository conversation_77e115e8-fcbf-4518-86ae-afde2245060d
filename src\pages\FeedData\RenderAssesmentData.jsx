import { useEffect, useState, useMemo, useCallback } from "react";
import { But<PERSON> } from "../../components/ui/button";
import { Input } from "../../components/ui/input";
import { Card, CardContent, CardHeader } from "../../components/ui/card";
import {
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableHeader,
	TableRow,
} from "../../components/ui/table";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "../../components/ui/select";
import Header from "../../components/Header";
import { getAllrenderAssesment } from "../../API/api-endpoint";
import Swal from "sweetalert2";
import { Link } from "react-router-dom";

const RenderAssessmentData = () => {
	const [assessmentData, setAssessmentData] = useState([]);
	const [segmentsData, setSegmentsData] = useState([]);
	const [availableActivities, setAvailableActivities] = useState([]);
	const [isLoading, setIsLoading] = useState(true);
	const [searchTerm, setSearchTerm] = useState("");
	const [currentPage, setCurrentPage] = useState(1);
	const [activityFilter, setActivityFilter] = useState("All");
	const pageSize = 10;

	const fetchData = useCallback(async () => {
		try {
			setIsLoading(true);
			const response = await getAllrenderAssesment();
			let parsedData = response;
			if (typeof response === "string") {
				try {
					parsedData = JSON.parse(response);
				} catch (parseError) {
					console.error("JSON parse error:", parseError);
					setAssessmentData([]);
					return;
				}
			}

			let dataArray = [];
			if (Array.isArray(parsedData)) {
				dataArray = parsedData;
			} else if (parsedData && typeof parsedData === "object") {
				if (parsedData.data && Array.isArray(parsedData.data)) {
					dataArray = parsedData.data;
				} else if (
					parsedData.assessments &&
					Array.isArray(parsedData.assessments)
				) {
					dataArray = parsedData.assessments;
				} else if (
					parsedData.Segments &&
					Array.isArray(parsedData.Segments)
				) {
					dataArray = [parsedData];
				} else {
					const arrayValues = Object.values(parsedData).filter(
						(val) => Array.isArray(val)
					);
					if (arrayValues.length > 0) {
						dataArray = arrayValues[0];
					} else {
						dataArray = [parsedData];
					}
				}
			}

			setAssessmentData(dataArray);

			let segments = [];
			if (dataArray.length > 0 && dataArray[0]?.Segments) {
				segments = dataArray[0].Segments.map((segment, index) => ({
					...segment,
					srID: index + 1,
				}));
			}
			setSegmentsData(segments);

			const activities = [
				...new Set(segments.map((segment) => segment.name)),
			].filter(Boolean);
			setAvailableActivities(activities);
		} catch (error) {
			console.error("Error fetching assessment data:", error);
			Swal.fire({
				title: "Error",
				text: "Failed to fetch assessment data. Please try again.",
				icon: "error",
				timer: 3000,
				showConfirmButton: false,
			});
			setAssessmentData([]);
		} finally {
			setIsLoading(false);
		}
	}, []);

	useEffect(() => {
		fetchData();
	}, [fetchData]);

	const filteredData = useMemo(() => {
		const dataArray = Array.isArray(segmentsData) ? segmentsData : [];
		let filtered = dataArray;

		if (searchTerm.trim()) {
			filtered = filtered.filter((segment) => {
				return segment?.name
					?.toLowerCase()
					.includes(searchTerm.toLowerCase());
			});
		}

		if (activityFilter !== "All") {
			filtered = filtered.filter(
				(segment) =>
					segment?.name?.toLowerCase() ===
					activityFilter.toLowerCase()
			);
		}

		return filtered;
	}, [segmentsData, searchTerm, activityFilter]);

	const paginatedData = useMemo(() => {
		const dataArray = Array.isArray(filteredData) ? filteredData : [];
		const startIndex = (currentPage - 1) * pageSize;
		const endIndex = startIndex + pageSize;
		return dataArray.slice(startIndex, endIndex);
	}, [filteredData, currentPage, pageSize]);

	const totalPages = Math.ceil(
		(Array.isArray(filteredData) ? filteredData.length : 0) / pageSize
	);

	const handleSearch = (value) => {
		setSearchTerm(value);
		setCurrentPage(1);
	};

	const handleActivityFilterChange = (value) => {
		setActivityFilter(value);
		setCurrentPage(1);
	};

	const navigationButtons = [
		{ href: "/assesmentname", label: "Get Assessment" },
		{ href: "/segments", label: "Get Segments" },
		{ href: "/sub-segments", label: "Get Sub Segments" },
		{ href: "/questions", label: "Get Questions" },
		{ href: "/option", label: "Get Option" },
	];

	return (
		<div>
			<Header />
			<div className='mx-auto p-6 max-w-[1400px] mt-16'>
				<Card>
					<CardHeader className='bg-orange-50 border-b'>
						<div className='flex flex-col gap-4'>
							<div>
								<h1 className='text-2xl font-bold text-orange-900'>
									Render Assessment Data
								</h1>
								<p className='text-orange-700 mt-1 text-sm'>
									Manage wellness assessment data and related
									components
								</p>
							</div>

							<div className='flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 flex-wrap'>
								<div className='flex flex-wrap gap-3'>
									{navigationButtons.map((button, index) => (
										<Link to={button.href} key={index}>
											<Button
												variant='outline'
												className='border-orange-300 text-orange-700 hover:bg-orange-100'
											>
												{button.label}
											</Button>
										</Link>
									))}
								</div>

								<div className='flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4'>
									<div className='flex gap-3'>
										<Input
											placeholder='Search by name...'
											value={searchTerm}
											onChange={(e) =>
												handleSearch(e.target.value)
											}
											className='max-w-sm text-sm'
										/>
										<Select
											value={activityFilter}
											onValueChange={
												handleActivityFilterChange
											}
										>
											<SelectTrigger className='w-[180px]'>
												<SelectValue placeholder='Filter by Activity' />
											</SelectTrigger>
											<SelectContent className='bg-white'>
												<SelectItem value='All'>
													All Activities
												</SelectItem>
												{availableActivities.map(
													(activity) => (
														<SelectItem
															key={activity}
															value={activity}
														>
															{activity}
														</SelectItem>
													)
												)}
											</SelectContent>
										</Select>
									</div>
								</div>
							</div>
						</div>
					</CardHeader>
					<CardContent className='p-0'>
						<div className='overflow-x-auto'>
							<Table className='min-w-full w-full'>
								<TableHeader>
									<TableRow className='bg-blue-600 hover:bg-blue-600'>
										<TableHead className='text-white font-semibold'>
											Sr No
										</TableHead>
										<TableHead className='text-white font-semibold'>
											Name
										</TableHead>
										<TableHead className='text-white font-semibold'>
											Percentage
										</TableHead>
									</TableRow>
								</TableHeader>
								<TableBody>
									{isLoading ? (
										<TableRow>
											<TableCell
												colSpan={3}
												className='text-center py-8'
											>
												Loading...
											</TableCell>
										</TableRow>
									) : paginatedData.length > 0 ? (
										paginatedData.map((segment, index) => (
											<TableRow
												key={segment.id || index}
												className='hover:bg-gray-50'
											>
												<TableCell className='font-medium'>
													{(currentPage - 1) *
														pageSize +
														index +
														1}
												</TableCell>
												<TableCell>
													{segment?.name || "N/A"}
												</TableCell>
												<TableCell>
													<span className='bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs font-medium'>
														{segment?.segment_percentage ||
															"N/A"}
														%
													</span>
												</TableCell>
											</TableRow>
										))
									) : (
										<TableRow>
											<TableCell
												colSpan={3}
												className='text-center py-8 text-gray-500'
											>
												No segments found
											</TableCell>
										</TableRow>
									)}
								</TableBody>
							</Table>
						</div>

						{/* Pagination */}
						{totalPages > 1 && (
							<div className='flex justify-center gap-2 p-4 border-t'>
								<Button
									variant='outline'
									size='sm'
									onClick={() =>
										setCurrentPage(
											Math.max(1, currentPage - 1)
										)
									}
									disabled={currentPage === 1}
								>
									Previous
								</Button>

								{Array.from({ length: 5 }, (_, i) => {
									const startPage =
										Math.floor((currentPage - 1) / 5) * 5 +
										1;
									const page = startPage + i;
									if (page > totalPages) return null;

									return (
										<Button
											key={page}
											variant={
												currentPage === page
													? "default"
													: "outline"
											}
											size='sm'
											onClick={() => setCurrentPage(page)}
											className={
												currentPage === page
													? "bg-orange-500 hover:bg-orange-600 text-white"
													: ""
											}
										>
											{page}
										</Button>
									);
								})}

								<Button
									variant='outline'
									size='sm'
									onClick={() =>
										setCurrentPage(
											Math.min(
												totalPages,
												currentPage + 1
											)
										)
									}
									disabled={currentPage === totalPages}
								>
									Next
								</Button>
							</div>
						)}
					</CardContent>
				</Card>
			</div>
		</div>
	);
};

export default RenderAssessmentData;
