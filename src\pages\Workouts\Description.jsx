import { Modal } from 'antd'
import React from 'react'

const Description = ({description,isModalOpen,handleCancel}) => {
  return (
    <div>
        <Modal
            width={700}
            open={isModalOpen?.isOpen}
            onCancel={()=>handleCancel({isOpen:false})}
            footer={<div></div>}
          >
            <div className="p-4" style={{"border":"2px solid #d2caca","margin":"7px","borderRadius":"10px","boxShadow":"rgba(0, 0, 0, 0.15) 0px 15px 25px, rgba(0, 0, 0, 0.05) 0px 5px 10px", margin:"20px"}}>
        <label dangerouslySetInnerHTML={{
          __html: isModalOpen?.description,
        }}></label>
            </div>
          </Modal>
    </div>
  )
}

export default Description