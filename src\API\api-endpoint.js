import axios from "axios";
import moment from "moment";
import "moment-timezone";
import { WriteUserData } from "./firebase.config";
import { getAuth, signInAnonymously } from "firebase/auth";
import Swal from "sweetalert2";

console.log("REACT_APP_API_BASE_URL: ", process.env.REACT_APP_API_BASE_URL);

export const URL =
	process.env.REACT_APP_ENVIRONMENT === "local"
		? "http://localhost:8000"
		: process.env.REACT_APP_API_BASE_URL;
// export const URL = "https://api.dev.fit.yoska.in";
/********************************************************
 *                Authtication Apis Start               *
 **********************************************************/

export const providerSignup = async (data) => {
	try {
		let authToken = localStorage.getItem("token");
		let body = {
			firstName: data.fullName,
			lastName: data.fullName,
			email: data.email,
			google_id: data.uid,
		};
		const apiUrl = `${URL}/sign-up/signUpwithgoogle`;

		let response = await axios.post(apiUrl, body, {
			headers: {
				"Content-Type": `application/json`,
			},
		});
		let Token = `Bearer ${response.data.token}`;
		localStorage.setItem("token", Token);
		//localStorage.removeItem('authtoken')
		return response.data;
	} catch (error) {
		return error?.response?.data;
	}
};

export const providerSignin = async (data) => {
	try {
		let authToken = localStorage.getItem("token");
		let body = {
			uid: data.uid,
			email: data.email,
		};
		const apiUrl = `${URL}/user/userLoginWithGoogle1`;

		let response = await axios.post(apiUrl, body, {
			headers: {
				"Content-Type": `application/json`,
			},
		});
		let Token = `Bearer ${response.data.token}`;
		console.log("responsemmm", response);
		localStorage.setItem("token", Token);
		localStorage.setItem("profileImage", response?.data?.user?.profile);
		return response.data;
	} catch (error) {
		return error?.response?.data;
	}
};
export const EmailSignup = async (data, dispatch, name) => {
	let body = Object.assign({}, data, {
		google_id: "",
	});
	const apiUrl = `${URL}/sign-up`;
	try {
		let response = await axios.post(apiUrl, body, {
			headers: {
				"Content-Type": `application/json`,
			},
		});

		localStorage.setItem("userTokentoken", `Bearer ${response.data.token}`);
		localStorage.setItem("userId", response.data.userId);
		localStorage.setItem("email", response.data.firebaseUser.email);

		console.log("response", response?.data, name);
		WriteUserData(
			response.data.firebaseUser.uid,
			name,
			response.data.firebaseUser.email,
			"",
			dispatch
		);

		return response.data;
	} catch (error) {
		return error?.response?.data;
	}
};

export const Login = async (data, dispatch) => {
	let body = {
		email: data.email, //Replace with the Email,
		password: data.password, //Replace with the PassWord,
	};
	const apiUrl = `${URL}/user/login`;

	try {
		const response = await axios.post(apiUrl, body, {
			headers: {
				"Content-Type": `application/json`,
			},
		});
		// If the user is not authenticated with your application,
		// then sign in the user anonymously with Firebase
		if (!response.data.isAuthenticated) {
			const auth = getAuth();
			await signInAnonymously(auth);
		}

		let Token = `Bearer ${response.data.token}`;
		console.log("response", response?.data);
		localStorage.setItem("token", Token);
		localStorage.setItem(
			"userId",
			response.data.user_id
				? response.data.user_id
				: response?.data?.user?.id
		);
		localStorage.setItem("roleID", response?.data?.user?.role_id);
		localStorage.setItem("google_id", response?.data?.user?.google_id);

		WriteUserData(
			response.data.user.google_id,
			response?.data.user?.firstname + " " + response.data.user?.lastname,
			response.data.user?.email,
			response?.data.user?.profile,
			dispatch
		);
		return response.data;
	} catch (error) {
		return error.response;
	}
};

export const getAccesToken = async (id) => {
	console.log("Line145");
	let AuthToken = localStorage.getItem("token");
	let user_ID = localStorage.getItem("userId");

	const apiUrl = `${URL}/getstravaaccesstoken/${id ? id : user_ID}`;

	try {
		const response = await axios.get(apiUrl, {
			headers: {
				"Content-Type": "application/json",
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		console.log(error,"Line163");
		console.error("Error fetching data:", error);
		throw error; // Rethrow the error to be handled higher up in the call stack
	}
};

export const UnderMentenance = async (id) => {
	const apiUrl = `${URL}/getapplicationmode`;

	try {
		const response = await axios.get(apiUrl, {
			headers: {
				"Content-Type": "application/json",
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		console.error("Error fetching data:", error);
		throw error; // Rethrow the error to be handled higher up in the call stack
	}
};

// export const AdminLogin = async (data, dispatch) => {
//   let body = {
//     email: data.email, //Replace with the Email,
//   };
//   const apiUrl = `${URL}/user/userLoginforadmin`;
//   try {
//     let response = await axios.post(apiUrl, body, {
//       headers: {
//         "Content-Type": `application/json`,
//       },
//     });

//     // Check if Google ID is null in the response
//     if (!response.data.user.google_id) {
//       const createGoogleIdBody = {
//         firstName: response.data.user.firstname,
//         lastName: response.data.user.lastname,
//         email: response.data.user.email,
//         password: "12345678",
//       };

//       // Make a request to create Google ID
//       let googleIdResponse = await axios.post(
//         `${URL}/sign-up/creategoogleids`,
//         createGoogleIdBody,
//         {
//           headers: {
//             "Content-Type": `application/json`,
//           },
//         }
//       );
//       if (googleIdResponse.status) {
//         const response = await axios.post(`${URL}/user/userLoginforadmin`, body, {
//           headers: {
//             "Content-Type": `application/json`,
//           },
//         });
//       }

//     }

//     // Rest of your existing code
//     let Token = `Bearer ${response.data.token}`;
//     console.log("xyz", response?.data);
//     localStorage.setItem("token", Token);
//     localStorage.setItem(
//       "userId",
//       response.data.user_id ? response.data.user_id : response?.data?.user?.id
//     );
//     localStorage.setItem("roleID", response?.data?.user?.role_id);
//     localStorage.setItem("google_id", response.data.user.google_id);
//     localStorage.setItem("fullname", response?.data.user.firstname + " " + response.data.user?.lastname);
//     console.log("9999response", response?.data);
//     localStorage.setItem("email", response?.data.user.email);

//     WriteUserData(
//       response.data.user.google_id,
//       response?.data.user?.firstname + " " + response.data.user?.lastname,
//       response.data.user?.email,
//       response?.data.user?.profile,
//       dispatch
//     );

//     return response.data;
//   } catch (error) {
//     console.error("Error in AdminLogin:", error);
//     return error.response.data;
//   }
// };

export const AdminLogin = async (data, dispatch) => {
	let body = {
		email: data.email, //Replace with the Email,
	};
	console.log("6661", data);
	const apiUrl = `${URL}/user/userLoginforadmin`;
	try {
		let response = await axios.post(apiUrl, body, {
			headers: {
				"Content-Type": `application/json`,
			},
		});
		let Token = `Bearer ${response.data.token}`;
		console.log("response", response?.data);
		localStorage.setItem("token", Token);
		localStorage.setItem("profileImage", response?.data?.user?.profile);
		localStorage.setItem(
			"userId",
			response.data.user_id
				? response.data.user_id
				: response?.data?.user?.id
		);
		localStorage.setItem("roleID", response?.data?.user?.role_id);
		localStorage.setItem("google_id", response.data.user.google_id);

		WriteUserData(
			response.data.user.google_id,
			response?.data.user?.firstname + " " + response.data.user?.lastname,
			response.data.user?.email,
			response?.data.user?.profile,
			dispatch
		);
		return response.data;
	} catch (error) {
		return error.response.data;
	}
};
export const generateUid = async (firstname, lastname, email, dispatch) => {
	const createGoogleIdBody = {
		firstName: firstname,
		lastName: lastname,
		email: email,
		password: "12345678",
	};
	let data = { email: email };
	console.log("createGoogleIdBody", createGoogleIdBody);
	// Make a request to create Google ID
	let googleIdResponse = await axios.post(
		`${URL}/sign-up/creategoogleids`,
		createGoogleIdBody,
		{
			headers: {
				"Content-Type": `application/json`,
			},
		}
	);
	return AdminLogin(data, dispatch);
};
export const generateUidLogin = async (
	firstname,
	lastname,
	email,
	dispatch,
	password
) => {
	const createGoogleIdBody = {
		firstName: firstname,
		lastName: lastname,
		email: email,
		password: "12345678",
	};
	let data = { email: email, password: password };
	console.log("createGoogleIdBody", createGoogleIdBody);
	// Make a request to create Google ID
	let googleIdResponse = await axios.post(
		`${URL}/sign-up/creategoogleids`,
		createGoogleIdBody,
		{
			headers: {
				"Content-Type": `application/json`,
			},
		}
	);
	return Login(data, dispatch);
};

export const fetchCurrentProfile = async (id) => {
	let AuthToken = localStorage.getItem("token");
	let user_ID = localStorage.getItem("userId");

	const apiUrl = `${URL}/program/getcurrentprofile/${id ? id : user_ID}`;

	try {
		const response = await axios.get(apiUrl, {
			headers: {
				"Content-Type": "application/json",
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		console.error("Error fetching data:", error);
		throw error; // Rethrow the error to be handled higher up in the call stack
	}
};
export const VerifyOTP = async (data) => {
	let body = {
		email: data.email, //Replace with the Email,
		otp: data.otp, //Replace with the OTP,
	};
	const apiUrl = `${URL}/sign-up/verify`;

	let response = await axios.post(apiUrl, body, {
		headers: {
			"Content-Type": `application/json`,
		},
	});

	return response.data;
};

export const Forgotpassword = async (email) => {
	try {
		let body = {
			email: email, //Replace with the Email,
		};
		const apiUrl = `${URL}/user/forget-password`;

		let response = await axios.post(apiUrl, body, {
			headers: {
				"Content-Type": `application/json`,
			},
		});

		return response.data;
	} catch (error) {
		return error?.response?.data?.error;
	}
};

export const Resetpassword = async (password, token) => {
	try {
		let body = {
			newPassword: password,
		};
		const apiUrl = `${URL}/user/reset-password`;
		let Bearer_token = `Bearer ${token}`;
		let response = await axios.post(apiUrl, body, {
			headers: {
				"Content-Type": `application/json`,
				Authorization: Bearer_token,
			},
		});

		return response.data;
	} catch (e) {
		return e.response?.data;
	}
};

export const ResendOtp = async (email) => {
	try {
		let body = {
			email: email,
		};
		const apiUrl = `${URL}/sign-up/resend-otp`;
		let response = await axios.post(apiUrl, body, {
			headers: {
				"Content-Type": `application/json`,
			},
		});

		return response.data;
	} catch (error) {
		return error?.response?.data;
	}
};

export const updatePhoneVerificationStatus = async () => {
	let body = {
		isPhoneVerified: true,
	};
	let AuthToken = localStorage.getItem("token");
	const apiUrl = `${URL}/user/updatePhoneVerification`;
	let response = await axios.post(apiUrl, body, {
		headers: {
			"Content-Type": `application/json`,
			Authorization: AuthToken,
			"ngrok-skip-browser-warning": "69420",
		},
	});

	return response.data;
};
/********************************************************
 *                 Authtication Apis End                *
 *********************************************************/

/********************************************************
 *                     Workout Apis Start                *
 *********************************************************/

/*Library APIS CRUD */

export const createWorkoutLibrary = async (
	workout_library_name,
	commaSeparatedString
) => {
	let AuthToken = localStorage.getItem("token");
	let date = new Date();

	// Parse the original date and time string and format it to the desired format
	// const currentTime = moment().tz(userTimezone).format('MMMM Do YYYY, h:mm:ss a');
	const formattedDateTime = moment(date).format("YYYY-MM-DD HH:mm:ss");
	// 2023-09-11 17:33:29
	let editDate = JSON.stringify(date);
	let body = {
		workout_library_name: workout_library_name,
		tags: commaSeparatedString,
		created_by: 1, // Replace with the actual user ID
		created_at: formattedDateTime, // Replace with the desired creation date and time
		status: 1,
	};
	const apiUrl = `${URL}/workoutLibrary/createLibrary`;

	let { data } = await axios.post(apiUrl, body, {
		headers: {
			"Content-Type": `application/json`,
			Authorization: AuthToken,
			"ngrok-skip-browser-warning": "69420",
		},
	});

	return data;
};

export const getAllWorkoutLibrary = async () => {
	let AuthToken = localStorage.getItem("token");

	const apiUrl = `${URL}/workoutLibrary/getLibrary`;

	try {
		const response = await axios.get(apiUrl, {
			headers: {
				"Content-Type": "application/json",
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		console.error("Error fetching data:", error);
		throw error; // Rethrow the error to be handled higher up in the call stack
	}
};

export const updateWorkoutLibrary = async (
	workout_library_name,
	commaSeparatedString,
	workout_library_id
) => {
	let AuthToken = localStorage.getItem("token");
	let user_ID = localStorage.getItem("userId");

	let body = {
		name: workout_library_name,
		tags: commaSeparatedString,
		created_by: user_ID,
		id: workout_library_id,
	};

	const apiUrl = `${URL}/program/updateworkoutlibrary`;

	let { data } = await axios.post(apiUrl, body, {
		headers: {
			"Content-Type": `application/json`,
			Authorization: AuthToken,
			"ngrok-skip-browser-warning": "69420",
		},
	});

	return data;
};

export const deleteWorkoutLibrary = async (workout_library_id) => {
	let AuthToken = localStorage.getItem("token");
	let body = {
		id: workout_library_id,
	};
	const apiUrl = `${URL}/program/deleteworkoutlibrary`;

	let { data } = await axios.post(apiUrl, body, {
		headers: {
			"Content-Type": `application/json`,
			Authorization: AuthToken,
			"ngrok-skip-browser-warning": "69420",
		},
	});

	return data;
};

/*inside Library Workouts APIS CRUD */

export const createWorkoutInsideLibrary = async (data) => {
	let AuthToken = localStorage.getItem("token");
	let user_ID = localStorage.getItem("userId");
	const body = { ...data, createdById: user_ID };
	const apiUrl = `${URL}/program/addworkouttoworkoutlibrary`;

	let response = await axios.post(apiUrl, body, {
		headers: {
			"Content-Type": `application/json`,
			Authorization: AuthToken,
			"ngrok-skip-browser-warning": "69420",
		},
	});

	return response.data;
};

export const getAllWorkoutsFromLibraryByLibraryId = async (library_id) => {
	let AuthToken = localStorage.getItem("token");

	const apiUrl = `${URL}/workout-master/${library_id}`;

	try {
		const response = await axios.get(apiUrl, {
			headers: {
				"Content-Type": "application/json",
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data.workouts;
	} catch (error) {
		console.error("Error fetching data:", error);
		throw error; // Rethrow the error to be handled higher up in the call stack
	}
};

export const updateWorkoutinsideLibraryByWorkoutId = async (
	body,
	workout_id
) => {
	let AuthToken = localStorage.getItem("token");

	const apiUrl = `${URL}/program/updateworkouttoworkoutlibrary`;

	let result = await axios.post(apiUrl, body, {
		headers: {
			"Content-Type": `application/json`,
			Authorization: AuthToken,
			"ngrok-skip-browser-warning": "69420",
		},
	});

	return result.data;
};

export const deleteWorkoutfromLibraryByWorkoutId = async (workout_id) => {
	let AuthToken = localStorage.getItem("token");
	const body = {
		id: workout_id,
	};
	const apiUrl = `${URL}/program/deleteworkouttoworkoutlibrary`;

	let { data } = await axios.post(apiUrl, body, {
		headers: {
			"Content-Type": `application/json`,
			Authorization: AuthToken,
			"ngrok-skip-browser-warning": "69420",
		},
	});

	return data;
};

export const getAllWorkouts = async (library_id) => {
	let AuthToken = localStorage.getItem("token");

	const apiUrl = `${URL}/workout-master`;

	try {
		const response = await axios.get(apiUrl, {
			headers: {
				"Content-Type": "application/json",
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data.workouts;
	} catch (error) {
		console.error("Error fetching data:", error);
		throw error; // Rethrow the error to be handled higher up in the call stack
	}
};
export const hideUnhideStravaWorkouts = async (body) => {
	let AuthToken = localStorage.getItem("token");
	let user_ID = localStorage.getItem("userId");

	const apiUrl = `${URL}/program/changestravaactivitystatus`;

	let response = await axios.post(apiUrl, body, {
		headers: {
			"Content-Type": `application/json`,
			Authorization: AuthToken,
			"ngrok-skip-browser-warning": "69420",
		},
	});

	return response.data;
};
export const deleteSelectedWorkouts = async (body) => {
	let AuthToken = localStorage.getItem("token");
	let user_ID = localStorage.getItem("userId");

	const apiUrl = `${URL}/program/deleteworkouts`;

	let response = await axios.post(apiUrl, body, {
		headers: {
			"Content-Type": `application/json`,
			Authorization: AuthToken,
			"ngrok-skip-browser-warning": "69420",
		},
	});

	return response.data;
};
// get phases,fitnesslevel,goallist

export const getAllPhasesFitnessLevelGoalList = async () => {
	let AuthToken = localStorage.getItem("token");
	const apiUrl = `${URL}/getBlocksList`;

	try {
		const response = await axios.get(apiUrl, {
			headers: {
				"Content-Type": "application/json",
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		console.error("Error fetching data:", error);
		throw error; // Rethrow the error to be handled higher up in the call stack
	}
};

export const getAllActivity = async () => {
	let AuthToken = localStorage.getItem("token");
	const apiUrl = `${URL}/activitySubactivity/list-activities`;

	try {
		const response = await axios.get(apiUrl, {
			headers: {
				"Content-Type": "application/json",
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data.activities;
	} catch (error) {
		console.error("Error fetching data:", error);
		throw error; // Rethrow the error to be handled higher up in the call stack
	}
};

export const getAllSubActivity = async () => {
	let AuthToken = localStorage.getItem("token");
	const apiUrl = `${URL}/Subactivity/get-all-subactivities`;

	try {
		const response = await axios.get(apiUrl, {
			headers: {
				"Content-Type": "application/json",
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		console.error("Error fetching data:", error);
		throw error; // Rethrow the error to be handled higher up in the call stack
	}
};

export const getAllTags = async () => {
	let AuthToken = localStorage.getItem("token");

	const apiUrl = `${URL}/tagCloud`;

	try {
		const response = await axios.get(apiUrl, {
			headers: {
				"Content-Type": "application/json",
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data.tagClouds;
	} catch (error) {
		console.error("Error fetching data:", error);
		throw error; // Rethrow the error to be handled higher up in the call stack
	}
};

//custom blocks api

export const getAllBlocks = async (library_id) => {
	let AuthToken = localStorage.getItem("token");
	const apiUrl = `${URL}/workoutCustomBlock`;

	try {
		const response = await axios.get(apiUrl, {
			headers: {
				"Content-Type": "application/json",
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data.workoutCustomBlocks;
	} catch (error) {
		console.error("Error fetching data:", error);
		throw error; // Rethrow the error to be handled higher up in the call stack
	}
};

export const createCustomBlocks = async (body) => {
	let AuthToken = localStorage.getItem("token");
	const apiUrl = `${URL}/workoutCustomBlock/create`;

	let response = await axios.post(apiUrl, body, {
		headers: {
			"Content-Type": `application/json`,
			Authorization: AuthToken,
			"ngrok-skip-browser-warning": "69420",
		},
	});

	return response.data;
};

export const updateCustomBlocks = async (body, block_id) => {
	let AuthToken = localStorage.getItem("token");
	const apiUrl = `${URL}/workoutCustomBlock/update/${block_id}`;

	let result = await axios.put(apiUrl, body, {
		headers: {
			"Content-Type": `application/json`,
			Authorization: AuthToken,
			"ngrok-skip-browser-warning": "69420",
		},
	});

	return result.data;
};

export const getAllBlocksByWorkoutId = async (workout_id) => {
	let AuthToken = localStorage.getItem("token");
	const apiUrl = `${URL}/workoutCustomBlockMapping/${workout_id}`;

	try {
		const response = await axios.get(apiUrl, {
			headers: {
				"Content-Type": "application/json",
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data.workoutCustomBlocksMapping;
	} catch (error) {
		console.error("Error fetching data:", error);
		throw error; // Rethrow the error to be handled higher up in the call stack
	}
};

export const copyPasteWorkoutLibrary = async (data) => {
	let AuthToken = localStorage.getItem("token");
	const apiUrl = `${URL}/workoutLibrary/pasteLibrary`;
	let body = {
		libraryId: data.libraryId,
		destinationLocationId: data.destinationId,
		operation: data.operationType,
	};

	let response = await axios.post(apiUrl, body, {
		headers: {
			"Content-Type": `application/json`,
			Authorization: AuthToken,
			"ngrok-skip-browser-warning": "69420",
		},
	});

	return response;
};
export const copyPasteWorkoutLibrari = async (data) => {
	let AuthToken = localStorage.getItem("token");
	const apiUrl = `${URL}/program/copyworkoutlibrary`;
	let body = {
		id: data.id,
		title: data.title,
	};

	let response = await axios.post(apiUrl, body, {
		headers: {
			"Content-Type": `application/json`,
			Authorization: AuthToken,
			"ngrok-skip-browser-warning": "69420",
		},
	});

	return response.data;
};
export const copyPasteWorkoutLibrariWorkout = async (data) => {
	let AuthToken = localStorage.getItem("token");
	const apiUrl = `${URL}/program/copyworkoutinworkoutlibrary`;
	let body = {
		id: data.id,
		title: data.title,
	};

	let response = await axios.post(apiUrl, body, {
		headers: {
			"Content-Type": `application/json`,
			Authorization: AuthToken,
			"ngrok-skip-browser-warning": "69420",
		},
	});

	return response.data;
};
//create workouts from button

export const createWorkout = async (body) => {
	let AuthToken = localStorage.getItem("token");

	const apiUrl = `${URL}/workoutMaster/createWorkout`;

	let response = await axios.post(apiUrl, body, {
		headers: {
			"Content-Type": `application/json`,
			Authorization: AuthToken,
			"ngrok-skip-browser-warning": "69420",
		},
	});

	return response.data;
};

export const updateworkout = async (body, block_id) => {
	let AuthToken = localStorage.getItem("token");
	const apiUrl = `${URL}/workoutMaster/updateWorkout/${block_id}`;

	let result = await axios.put(apiUrl, body, {
		headers: {
			"Content-Type": `application/json`,
			Authorization: AuthToken,
			"ngrok-skip-browser-warning": "69420",
		},
	});

	return result.data;
};

export const deleteWorkout = async (workout_id) => {
	let AuthToken = localStorage.getItem("token");

	const apiUrl = `${URL}/workoutMaster/deleteWorkout/${workout_id}`;

	let { data } = await axios.delete(apiUrl, {
		headers: {
			"Content-Type": `application/json`,
			Authorization: AuthToken,
			"ngrok-skip-browser-warning": "69420",
		},
	});

	return data;
};

export const getAllworkouts = async (library_id) => {
	let AuthToken = localStorage.getItem("token");
	const apiUrl = `${URL}/workoutMaster/getWorkouts`;

	try {
		const response = await axios.get(apiUrl, {
			headers: {
				"Content-Type": "application/json",
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data.data;
	} catch (error) {
		console.error("Error fetching data:", error);
		throw error; // Rethrow the error to be handled higher up in the call stack
	}
};

/********************************************************
 *                     Workout Apis End                  *
 *********************************************************/

/********************************************************
 *               Training Blocks Apis Start             *
 *********************************************************/

//training block library apis

export const createTrainingBlockLibrary = async (lib_details) => {
	let AuthToken = localStorage.getItem("token");
	let Tags = lib_details.tags.join(",");
	let body = {
		trainingBlockLibraryName: lib_details.trainingBlockLibraryName,
		tags: Tags,
		createdBy: 1,
		status: true,
	};
	const apiUrl = `${URL}/training-block-library/create`;

	let { data } = await axios.post(apiUrl, body, {
		headers: {
			"Content-Type": `application/json`,
			Authorization: AuthToken,
			"ngrok-skip-browser-warning": "69420",
			"ngrok-skip-browser-warning": "69420",
		},
	});

	return data;
};
export const pasteTrainingBlockLibrary = async (body) => {
	let AuthToken = localStorage.getItem("token");

	const apiUrl = `${URL}/training-block-library/paste`;

	let { data } = await axios.post(apiUrl, body, {
		headers: {
			"Content-Type": `application/json`,
			Authorization: AuthToken,
			"ngrok-skip-browser-warning": "69420",
		},
	});

	return data;
};

export const getAllTrainingBlockLibrary = async () => {
	let AuthToken = localStorage.getItem("token");
	const apiUrl = `${URL}/training-block-library`;

	try {
		const response = await axios.get(apiUrl, {
			headers: {
				"Content-Type": "application/json",
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
				"ngrok-skip-browser-warning": "69420",
			},
		});
		console.log(
			"response.data.trainingBlocks",
			response.data.trainingBlocks
		);
		return response.data.trainingBlocks;
	} catch (error) {
		console.error("Error fetching data:", error);
		throw error; // Rethrow the error to be handled higher up in the call stack
	}
};
export const getAllTraningBlocks = async (library_id) => {
	let AuthToken = localStorage.getItem("token");

	const apiUrl = `${URL}/training-block-library`;

	try {
		const response = await axios.get(apiUrl, {
			headers: {
				"Content-Type": "application/json",
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
				"ngrok-skip-browser-warning": "69420",
			},
		});
		console.log("response.data.data", response.data.data);
		return response.data.data;
	} catch (error) {
		console.error("Error fetching data:", error);
		throw error; // Rethrow the error to be handled higher up in the call stack
	}
};

export const updateTrainingBlockLibrary = async (
	lib_details,
	training_block_library_id
) => {
	let AuthToken = localStorage.getItem("token");
	let Tags = lib_details.tags.join(",");
	let body = {
		trainingBlockLibraryName: lib_details.library_name,
		tags: Tags,
		createdBy: 1,
		status: true,
	};

	const apiUrl = `${URL}/training-block-library/update/${training_block_library_id}`;

	let { data } = await axios.put(apiUrl, body, {
		headers: {
			"Content-Type": `application/json`,
			Authorization: AuthToken,
			"ngrok-skip-browser-warning": "69420",
		},
	});

	return data;
};

export const deleteTrainingBlockLibrary = async (training_block_library_id) => {
	let AuthToken = localStorage.getItem("token");

	const apiUrl = `${URL}/training-block-library/delete/${training_block_library_id}`;

	let { data } = await axios.delete(apiUrl, {
		headers: {
			"Content-Type": `application/json`,
			Authorization: AuthToken,
			"ngrok-skip-browser-warning": "69420",
		},
	});

	return data;
};
export const coppastTrainingBlock = async (body) => {
	let AuthToken = localStorage.getItem("token");
	const apiUrl = `${URL}/trainingBlock/copytrainingblock`;

	let response = await axios.post(apiUrl, body, {
		headers: {
			"Content-Type": `application/json`,
			Authorization: AuthToken,
			"ngrok-skip-browser-warning": "69420",
		},
	});

	return response.data;
};

//training blocks apis

export const createTrainingBlock = async (block_details, lib_id) => {
	let AuthToken = localStorage.getItem("token");

	let Tags = block_details.tags.join(",");
	let body = {
		trainingBlockName: block_details.block_name,
		trainingBlockDescription: block_details.block_description,
		trainingBlockLibraryId: lib_id,
		tags: Tags,
		createdBy: 1,
		status: true,
	};
	const apiUrl = `${URL}/trainingBlock/create`;

	let { data } = await axios.post(apiUrl, body, {
		headers: {
			"Content-Type": `application/json`,
			Authorization: AuthToken,
			"ngrok-skip-browser-warning": "69420",
		},
	});

	return data;
};

export const getAllTrainingBlockByTraininingLibraryID = async (lib_id) => {
	let AuthToken = localStorage.getItem("token");

	const apiUrl = `${URL}/trainingBlock/libraryId/${lib_id}`;

	try {
		const response = await axios.get(apiUrl, {
			headers: {
				"Content-Type": "application/json",
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		console.error("Error fetching data:", error);
		throw error; // Rethrow the error to be handled higher up in the call stack
	}
};

export const updateTrainingBlock = async (
	block_details,
	lib_id,
	trainingBlockID
) => {
	let AuthToken = localStorage.getItem("token");
	let Tags = block_details.tags.join(",");
	let body = {
		trainingBlockName: block_details.block_name,
		trainingBlockDescription: block_details.block_description,
		trainingBlockLibraryId: lib_id,
		tags: Tags,
		createdBy: 1,
		status: true,
	};

	const apiUrl = `${URL}/trainingBlock/update/${trainingBlockID}`;

	let { data } = await axios.put(apiUrl, body, {
		headers: {
			"Content-Type": `application/json`,
			Authorization: AuthToken,
			"ngrok-skip-browser-warning": "69420",
		},
	});

	return data;
};

export const deleteTrainingBlock = async (trainingBlockID) => {
	let AuthToken = localStorage.getItem("token");
	const apiUrl = `${URL}/trainingBlock/delete/${trainingBlockID}`;

	let { data } = await axios.delete(apiUrl, {
		headers: {
			"Content-Type": `application/json`,
			Authorization: AuthToken,
			"ngrok-skip-browser-warning": "69420",
		},
	});

	return data;
};

export const handleDeleteTraningBlock = async (id) => {
	try {
		const apiUrl = `${URL}/trainingBlock/delete/${id}`;
		let AuthToken = localStorage.getItem("token");

		let { data } = await axios.delete(apiUrl, {
			headers: {
				"Content-Type": `application/json`,
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return data;
	} catch (error) {
		console.error("Error fetching data:", error);
		throw error; // Rethrow the error to be handled higher up in the call stack
	}
};

export const handleEditTraningBlock = async (id, updateTraningBlockVal) => {
	try {
		let body = updateTraningBlockVal;
		const apiUrl = `${URL}/trainingBlock/update/${id}`;
		let AuthToken = localStorage.getItem("token");
		let { data } = await axios.put(apiUrl, body, {
			headers: {
				"Content-Type": `application/json`,
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return data;
	} catch (error) {
		console.error("Error fetching data:", error);
		throw error; // Rethrow the error to be handled higher up in the call stack
	}
};

//Training-plan-master-mapping Apis

export const createTrainingPlanMasterMaping = async (data) => {
	let AuthToken = localStorage.getItem("token");
	let user_ID = localStorage.getItem("userId");

	let body = {
		...data,
		createdBy: parseFloat(user_ID),
	};
	const apiUrl = `${URL}/training-plan-master-mapping/create`;

	let response = await axios.post(apiUrl, body, {
		headers: {
			"Content-Type": `application/json`,
			Authorization: AuthToken,
			"ngrok-skip-browser-warning": "69420",
		},
	});

	return response.data;
};
export const deleteTrainingPlanMasterMaping = async (mappingId) => {
	let AuthToken = localStorage.getItem("token");

	const apiUrl = `${URL}/training-plan-master-mapping/delete/${mappingId}`;

	let response = await axios.delete(apiUrl, {
		headers: {
			"Content-Type": `application/json`,
			Authorization: AuthToken,
			"ngrok-skip-browser-warning": "69420",
		},
	});

	return response.data;
};

export const getTrainingPlanMasterMapingByTrainingPlanID = async (
	training_plan_id
) => {
	let AuthToken = localStorage.getItem("token");

	const apiUrl = `${URL}/training-plan-master-mapping/training-plan-id/${training_plan_id}`;

	try {
		const response = await axios.get(apiUrl, {
			headers: {
				"Content-Type": "application/json",
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		console.error("Error fetching data:", error);
		throw error; // Rethrow the error to be handled higher up in the call stack
	}
};

export const DeleteWorkoutFromTrainingPlanMasterMaping = async (
	body,
	training_plan_id
) => {
	let AuthToken = localStorage.getItem("token");

	const apiUrl = `${URL}/training-plan-master-mapping/update/${training_plan_id}`;

	let response = await axios.put(apiUrl, body, {
		headers: {
			"Content-Type": `application/json`,
			Authorization: AuthToken,
			"ngrok-skip-browser-warning": "69420",
		},
	});

	return response.data;
};

export const updateTrainingPlanMasterMapingShileDND = async (
	body,
	training_block_id,
	mapping_id
) => {
	let AuthToken = localStorage.getItem("token");

	const apiUrl = `${URL}/training-plan-master-mapping/update-plan/${training_block_id}?mapping_id=${mapping_id}`;

	let response = await axios.put(apiUrl, body, {
		headers: {
			"Content-Type": `application/json`,
			Authorization: AuthToken,
			"ngrok-skip-browser-warning": "69420",
		},
	});

	return response.data;
};

export const updateTrainingPlanMasterMapingWorkoutStatusforHide = async (
	body,
	training_block_id
) => {
	let AuthToken = localStorage.getItem("token");

	const apiUrl = `${URL}/training-plan-master-mapping/update-status/${training_block_id}`;

	let response = await axios.put(apiUrl, body, {
		headers: {
			"Content-Type": `application/json`,
			Authorization: AuthToken,
			"ngrok-skip-browser-warning": "69420",
		},
	});

	return response.data;
};

//UserMappingTrainingPlan apis

export const createAssignTrainingPlanToUser = async (array) => {
	let AuthToken = localStorage.getItem("token");
	let body = {
		array: array,
	};

	const apiUrl = `${URL}/user-mapping-training-plan/assign-create`;

	let response = await axios.post(apiUrl, body, {
		headers: {
			"Content-Type": `application/json`,
			Authorization: AuthToken,
			"ngrok-skip-browser-warning": "69420",
		},
	});

	return response.data;
};

export const updateAssignTrainingPlanToUser = async (array) => {
	let AuthToken = localStorage.getItem("token");
	let body = {
		array: array,
	};

	const apiUrl = `${URL}/user-mapping-training-plan/update-assing-mapping-user`;

	let response = await axios.put(apiUrl, body, {
		headers: {
			"Content-Type": `application/json`,
			Authorization: AuthToken,
			"ngrok-skip-browser-warning": "69420",
		},
	});

	return response.data;
};

/********************************************************
 *               Training Blocks Apis End               *
 *********************************************************/

export const getAllUsers = async () => {
	let AuthToken = localStorage.getItem("token");

	const apiUrl = `${URL}/get-user`;

	try {
		const response = await axios.get(apiUrl, {
			headers: {
				"Content-Type": "application/json",
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data.users;
	} catch (error) {
		console.error("Error fetching data:", error);
		throw error; // Rethrow the error to be handled higher up in the call stack
	}
};

/********************************************************
 *               Onboarding Apis Start                   *
 *********************************************************/

export const getAllGoals = async () => {
	let AuthToken = localStorage.getItem("token");
	let AuthUserToken = localStorage.getItem("userTokentoken");

	const apiUrl = `${URL}/workoutGoal`;

	try {
		const response = await axios.get(apiUrl, {
			headers: {
				"Content-Type": "application/json",
				Authorization: AuthUserToken ? AuthUserToken : AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data.workoutGoals;
	} catch (error) {
		console.error("Error fetching data:", error);
		throw error; // Rethrow the error to be handled higher up in the call stack
	}
};

export const getAllYoskaActivities = async () => {
	let AuthToken = localStorage.getItem("token");
	let AuthUserToken = localStorage.getItem("userTokentoken");

	const apiUrl = `${URL}/yoska-activity`;

	try {
		const response = await axios.get(apiUrl, {
			headers: {
				"Content-Type": "application/json",
				Authorization: AuthUserToken ? AuthUserToken : AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data.yoskaActivities;
	} catch (error) {
		console.error("Error fetching data:", error);
		throw error; // Rethrow the error to be handled higher up in the call stack
	}
};
export const getAllYoskaActivitiesUser = async () => {
	let AuthToken = localStorage.getItem("token");
	let user_ID = localStorage.getItem("userId");

	const apiUrl = `${URL}/yoska-activity/basedonuser/${user_ID}`;

	try {
		const response = await axios.get(apiUrl, {
			headers: {
				"Content-Type": "application/json",
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data.yoskaActivities;
	} catch (error) {
		console.error("Error fetching data:", error);
		throw error; // Rethrow the error to be handled higher up in the call stack
	}
};

export const getSubscriptionPlanBYProgramID = async (program_id) => {
	let AuthToken = localStorage.getItem("token");

	const apiUrl = `${URL}/subscription-plans/program-id/${program_id}`;

	try {
		const response = await axios.get(apiUrl, {
			headers: {
				"Content-Type": "application/json",
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data.subscriptionPlan;
	} catch (error) {
		console.error("Error fetching data:", error);
		throw error; // Rethrow the error to be handled higher up in the call stack
	}
};
export const getManageSubscription = async (program_id) => {
	let AuthToken = localStorage.getItem("token");

	const apiUrl = `${URL}/program/manageusersubscriptionsdata`;

	try {
		const response = await axios.get(apiUrl, {
			headers: {
				"Content-Type": "application/json",
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data.subs;
	} catch (error) {
		console.error("Error fetching data:", error);
		throw error; // Rethrow the error to be handled higher up in the call stack
	}
};
export const getManageAllCoach = async (program_id) => {
	let AuthToken = localStorage.getItem("token");

	const apiUrl = `${URL}/program/getallcoaches`;

	try {
		const response = await axios.get(apiUrl, {
			headers: {
				"Content-Type": "application/json",
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data.coaches;
	} catch (error) {
		console.error("Error fetching data:", error);
		throw error; // Rethrow the error to be handled higher up in the call stack
	}
};
export const createAdminCoachPrivilige = async (id) => {
	let AuthToken = localStorage.getItem("token");
	let user_ID = localStorage.getItem("userId");

	const apiUrl = `${URL}/profile/createoreditadmincoachprivilige/${id}`;
	try {
		const response = await axios.get(apiUrl, {
			headers: {
				"Content-Type": "application/json",
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		console.error("Error fetching data:", error?.response?.data);
		return error?.response?.data; // Rethrow the error to be handled higher up in the call stack
	}
};
export const deleteCoachPriviliage = async (id) => {
	let AuthToken = localStorage.getItem("token");
	const apiUrl = `${URL}/profile/deleteadmincoachprivilige/${id}`;

	try {
		const response = await axios.get(apiUrl, {
			headers: {
				"Content-Type": "application/json",
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		console.error("Error fetching data:", error?.response?.data);
		return error?.response?.data; // Rethrow the error to be handled higher up in the call stack
	}
};
export const updateSubscription = async (body) => {
	let AuthToken = localStorage.getItem("token");
	const apiUrl = `${URL}/program/changesubofuser`;
	try {
		const response = await axios.post(apiUrl, body, {
			headers: {
				"Content-Type": "application/json",
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		console.error("Error fetching data:", error?.response?.data);
		return error?.response?.data; // Rethrow the error to be handled higher up in the call stack
	}
};
export const updateCoach = async (body) => {
	let AuthToken = localStorage.getItem("token");
	const apiUrl = `${URL}/program/updateCoachDetails`;
	try {
		const response = await axios.post(apiUrl, body, {
			headers: {
				"Content-Type": "application/json",
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		console.error("Error fetching data:", error?.response?.data);
		return error?.response?.data; // Rethrow the error to be handled higher up in the call stack
	}
};
export const genrateInvoice = async (id, userId) => {
	let AuthToken = localStorage.getItem("token");
	const apiUrl = `${URL}/payment/generateinvoice`;

	let body = {
		user_id: userId,
		sub_id: id,
	};
	try {
		const response = await axios.post(apiUrl, body, {
			headers: {
				"Content-Type": "application/json",
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		console.error("Error fetching data:", error?.response?.data);
		return error?.response?.data; // Rethrow the error to be handled higher up in the call stack
	}
};
export const putOnHold = async (id, userId) => {
	let AuthToken = localStorage.getItem("token");
	const apiUrl = `${URL}/user-subscription/putonhold/${id}`;

	try {
		const response = await axios.get(apiUrl, {
			headers: {
				"Content-Type": "application/json",
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		console.error("Error fetching data:", error?.response?.data);
		return error?.response?.data; // Rethrow the error to be handled higher up in the call stack
	}
};
export const coachPutOnHold = async (body) => {
	let AuthToken = localStorage.getItem("token");
	const apiUrl = `${URL}/program/putonholduser`;

	try {
		const response = await axios.post(apiUrl, body, {
			headers: {
				"Content-Type": "application/json",
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		console.error("Error fetching data:", error?.response?.data);
		return error?.response?.data; // Rethrow the error to be handled higher up in the call stack
	}
};
export const terminateCoach = async (body) => {
	let AuthToken = localStorage.getItem("token");
	const apiUrl = `${URL}/program/userTermination`;

	try {
		const response = await axios.post(apiUrl, body, {
			headers: {
				"Content-Type": "application/json",
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		console.error("Error fetching data:", error?.response?.data);
		return error?.response?.data; // Rethrow the error to be handled higher up in the call stack
	}
};
export const terminateAthelete = async (body) => {
	let AuthToken = localStorage.getItem("token");
	const apiUrl = `${URL}/program/userTermination`;

	try {
		const response = await axios.post(apiUrl, body, {
			headers: {
				"Content-Type": "application/json",
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		console.error("Error fetching data:", error?.response?.data);
		return error?.response?.data; // Rethrow the error to be handled higher up in the call stack
	}
};
export const notifyCoach = async (body) => {
	let AuthToken = localStorage.getItem("token");
	const apiUrl = `${URL}/program/sendmailtounpaiduser`;

	try {
		const response = await axios.post(apiUrl, body, {
			headers: {
				"Content-Type": "application/json",
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		console.error("Error fetching data:", error?.response?.data);
		return error?.response?.data; // Rethrow the error to be handled higher up in the call stack
	}
};
export const notifyAthletes = async (id) => {
	let AuthToken = localStorage.getItem("token");
	const apiUrl = `${URL}/profile/notifyuserofsubending/${id}`;

	try {
		const response = await axios.get(apiUrl, {
			headers: {
				"Content-Type": "application/json",
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		console.error("Error fetching data:", error?.response?.data);
		return error?.response?.data; // Rethrow the error to be handled higher up in the call stack
	}
};
export const putunHold = async (id, userId) => {
	let AuthToken = localStorage.getItem("token");
	const apiUrl = `${URL}/user-subscription/putoffhold/${id}`;

	try {
		const response = await axios.get(apiUrl, {
			headers: {
				"Content-Type": "application/json",
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		console.error("Error fetching data:", error?.response?.data);
		return error?.response?.data; // Rethrow the error to be handled higher up in the call stack
	}
};
export const getAllyoskaProgramByActivityID = async (activity_id) => {
	let AuthToken = localStorage.getItem("token");

	const apiUrl = `${URL}/yoska-program/get-by-activity`;
	let body = {
		activityIds: [activity_id], // currently we are fetching only one id that why its we have add array brakets
	};

	try {
		const response = await axios.post(apiUrl, body, {
			headers: {
				"Content-Type": "application/json",
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data.programs;
	} catch (error) {
		console.error("Error fetching data:", error?.response?.data);
		return error?.response?.data; // Rethrow the error to be handled higher up in the call stack
	}
};
export const getAllyoskaProgramFeateryID = async (activity_id) => {
	let AuthToken = localStorage.getItem("token");
	let AuthUserToken = localStorage.getItem("userTokentoken");

	const apiUrl = `${URL}/feeddata/getProgramByActivityId/${activity_id}`;

	try {
		const response = await axios.get(apiUrl, {
			headers: {
				"Content-Type": "application/json",
				Authorization: AuthUserToken ? AuthUserToken : AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		console.error("Error fetching data:", error?.response?.data);
		return error?.response?.data; // Rethrow the error to be handled higher up in the call stack
	}
};

export const createUserSubscription = async (body, id) => {
	let AuthToken = localStorage.getItem("token");
	console.log("AuthToken", AuthToken);
	let AuthUserToken = localStorage.getItem("userTokentoken");

	const apiUrl = `${URL}/user-subscription/create`;

	let response = await axios.post(apiUrl, body, {
		headers: {
			"Content-Type": `application/json`,
			Authorization: AuthUserToken ? AuthUserToken : AuthToken,
			"ngrok-skip-browser-warning": "69420",
		},
	});
	if (!id) {
		let Token = `Bearer ${response.data.token}`;
		localStorage.setItem("token", Token);
	}

	return response.data;
};
export const updateUserSubscription = async (body, id) => {
	try {
		let AuthToken = localStorage.getItem("token");
		console.log("AuthToken", AuthToken);

		const apiUrl = `${URL}/user-subscription/update/${id}`;

		let response = await axios.put(apiUrl, body, {
			headers: {
				"Content-Type": `application/json`,
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});

		let Token = `Bearer ${response.data.token}`;

		return response.data;
	} catch (error) {
		return error?.response.data;
	}
};
export const createUserDetailsSubscription = async (body) => {
	try {
		let AuthToken = localStorage.getItem("token");

		const apiUrl = `${URL}/user-subscription/create`;

		let response = await axios.post(apiUrl, body, {
			headers: {
				"Content-Type": `application/json`,
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});

		return response.data;
	} catch (error) {
		return error;
	}
};
export const ValidatePromoCode = async (promo_code, sub_id) => {
	try {
		let AuthToken = localStorage.getItem("userTokentoken")
			? localStorage.getItem("userTokentoken")
			: localStorage.getItem("token");
		let body = {
			coupon_code: promo_code,
			subscription_id: parseInt(sub_id, 10),
		};

		const apiUrl = `${URL}/coupons/checkCouponValidity`;

		let response = await axios.post(apiUrl, body, {
			headers: {
				"Content-Type": `application/json`,
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});

		return response.data;
	} catch (error) {
		return error?.response?.data?.error;
	}
};

export const registerCouponCode = async (promo_code, sub_id) => {
	try {
		let AuthToken = localStorage.getItem("userTokentoken")
			? localStorage.getItem("userTokentoken")
			: localStorage.getItem("token");
		let body = {
			coupon_code: promo_code,
			subscription_id: parseInt(sub_id, 10),
		};

		const apiUrl = `${URL}/coupons/registrationCouponCode`;

		let response = await axios.post(apiUrl, body, {
			headers: {
				"Content-Type": `application/json`,
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});

		return response.data;
	} catch (error) {
		return error?.response?.data?.error;
	}
};

export const getStravaURL = async (user_ID) => {
	let AuthToken = localStorage.getItem("token");
	let user_Id = localStorage.getItem("userId");
	console.log(user_Id,"user_Id",user_ID,"USER_ID",URL,"URL");
	const apiUrl = `${URL}/auth/strava2/${user_ID ? user_ID : user_Id}`;

	try {
		const response = await axios.get(apiUrl, {
			headers: {
				"Content-Type": "application/json",
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		console.error("Error fetching data:", error);
		throw error; // Rethrow the error to be handled higher up in the call stack
	}
};

export const getStravaDetails = async (user_ID, code, scops) => {
	let AuthToken = localStorage.getItem("token");

	const apiUrl = `${URL}/auth/strava/callback2/${user_ID}?code=${code}?scope=${scops}`;

	try {
		const response = await axios.get(apiUrl, {
			headers: {
				"Content-Type": "application/json",
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		console.error("Error fetching data:", error);
		throw error; // Rethrow the error to be handled higher up in the call stack
	}
};
export const getAllActivities = async () => {
	let AuthToken = localStorage.getItem("token");

	const apiUrl = `${URL}/program/getallactivities`;

	try {
		const response = await axios.get(apiUrl, {
			headers: {
				"Content-Type": "application/json",
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data.data;
	} catch (error) {
		console.error("Error fetching data:", error);
		throw error; // Rethrow the error to be handled higher up in the call stack
	}
};
export const getUserBadges = async (id) => {
	let AuthToken = localStorage.getItem("token");
	let user_ID = localStorage.getItem("userId");

	const apiUrl = `${URL}/program/gamification/getallbadgesbyuserid/${
		id ? id : user_ID
	}`;

	try {
		const response = await axios.get(apiUrl, {
			headers: {
				"Content-Type": "application/json",
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data.data;
	} catch (error) {
		console.error("Error fetching data:", error);
		throw error; // Rethrow the error to be handled higher up in the call stack
	}
};
export const getAllActivitiesField = async (
	activitiId,
	setFiledName,
	assignedCocahId
) => {
	let AuthToken = localStorage.getItem("token");
	let user_ID = localStorage.getItem("userId");

	setFiledName([]);
	const apiUrl = `${URL}/program/fetchsubactivityandparams/${activitiId}/${
		assignedCocahId ? assignedCocahId : user_ID
	}`;

	try {
		const response = await axios.get(apiUrl, {
			headers: {
				"Content-Type": "application/json",
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		console.error("Error fetching data:", error);
		throw error; // Rethrow the error to be handled higher up in the call stack
	}
};

export const getSuActivity = async (activitiId) => {
	let AuthToken = localStorage.getItem("token");
	let user_ID = localStorage.getItem("userId");

	const apiUrl = `${URL}/program/getallsubworkouts/${activitiId}`;

	try {
		const response = await axios.get(apiUrl, {
			headers: {
				"Content-Type": "application/json",
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data.data;
	} catch (error) {
		console.error("Error fetching data:", error);
		throw error; // Rethrow the error to be handled higher up in the call stack
	}
};

export const createWorkouts = async (data, assignedCocahId) => {
	let AuthToken = localStorage.getItem("token");
	let user_ID = localStorage.getItem("userId");
	let body = {
		...data,
		created_by_id: assignedCocahId ? assignedCocahId : user_ID,
		created_for_id: assignedCocahId ? assignedCocahId : user_ID,
	};

	const apiUrl = `${URL}/program/manualv2`;

	let response = await axios.post(apiUrl, body, {
		headers: {
			"Content-Type": `application/json`,
			Authorization: AuthToken,
			"ngrok-skip-browser-warning": "69420",
		},
	});

	return response.data;
};

export const updateWorkouts = async (data, id, assignedCocahId) => {
	let AuthToken = localStorage.getItem("token");
	let user_ID = localStorage.getItem("userId");
	let body = {
		...data,
		id: id,
		created_by_id: assignedCocahId ? assignedCocahId : user_ID,
		created_for_id: assignedCocahId ? assignedCocahId : user_ID,
	};

	const apiUrl = `${URL}/program/updatemanualv2`;

	let response = await axios.post(apiUrl, body, {
		headers: {
			"Content-Type": `application/json`,
			Authorization: AuthToken,
			"ngrok-skip-browser-warning": "69420",
		},
	});

	return response.data;
};
export const updateWorkoutsByDrag = async (data, id, assignedCocahId) => {
	let AuthToken = localStorage.getItem("token");
	let user_ID = localStorage.getItem("userId");
	let body = {
		...data,
		id: id,
		created_by_id: assignedCocahId ? assignedCocahId : user_ID,
		created_for_id: assignedCocahId ? assignedCocahId : user_ID,
	};

	const apiUrl = `${URL}/program/draganddropworkout`;

	let response = await axios.post(apiUrl, body, {
		headers: {
			"Content-Type": `application/json`,
			Authorization: AuthToken,
			"ngrok-skip-browser-warning": "69420",
		},
	});

	return response.data;
};
export const getAllworkoutsDetails = async (id) => {
	let AuthToken = localStorage.getItem("token");
	let user_ID = localStorage.getItem("userId");

	// const apiUrl = `${URL}/program/getallworkoutdata/${id ? id : user_ID}`;
	const apiUrl = `${URL}/program/getallworkoutdatav2`;

	const localtz = moment.tz.guess();
	let body = {
		user_id: id ? id : user_ID,
		timezone: localtz,
	};
	try {
		const response = await axios.post(apiUrl, body, {
			headers: {
				"Content-Type": "application/json",
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});

		let data = await getAllActivities();
		const twelveHoursAgo = new Date();
		twelveHoursAgo.setHours(twelveHoursAgo.getHours() - 12); // Calculate 12 hours ago from current time
		const modifiedData = response?.data?.workweekdata?.map((item) => {
			let newDate = item.datetf.split(" ")[0];
			let newDatewithoutstrava = item.date.split(" ")[0];
			let newTimeAm = item.datetf.split(" ")[2];
			let newTime = item.datetf.split(" ")[1];

			let showTimeDate =
				item?.process == "Strava" ? newDate : newDatewithoutstrava;
			console.log(
				"showTimeDate",
				newDate,
				newTimeAm,
				item.datetf,
				`${newTime}${newTimeAm}`
			);
			return {
				...item,
				start: showTimeDate,
				title: item.workout,
				isEdit:
					showTimeDate <= twelveHoursAgo &&
					item?.process !== "Strava",
				// isEdit: item.workout_planned_distance <=0  && item?.process !== "Strava",
				backgroundColor: "white",
				margin: "10px",
				borderColor:
					item.ispartial == 2
						? "green"
						: item.ispartial == 0 &&
						  item?.process != "Strava" &&
						  new Date(showTimeDate) <= twelveHoursAgo
						? "red"
						: item.ispartial == 1
						? "orange"
						: item?.process === "Strava"
						? "black"
						: "gray", // Add "bg-green" class if actual_duration is greater
				border:
					item.ispartial == 2
						? "green"
						: item.ispartial == 0 && showTimeDate <= twelveHoursAgo
						? "red"
						: item.ispartial == 1
						? "orange"
						: item?.process === "Strava"
						? "black"
						: "gray",
				actual_workout: item.actual_workout,
				workout_planned_duration: item.workout_planned_duration,
				id: item.id,
				badgeTpe:
					data?.length &&
					data?.filter((ele) => ele.activity_name == item.activity),
				workoutTime: `${newTime}${newTimeAm}`,
			};
		});
		console.log("modifiedData", modifiedData);
		return modifiedData;
	} catch (error) {
		console.error("Error fetching data:", error);
		throw error; // Rethrow the error to be handled higher up in the call stack
	}
};
export const getWeeklySummary = async (data, id) => {
	let AuthToken = localStorage.getItem("token");
	let user_ID = localStorage.getItem("userId");
	let body = {
		...data,
		user_id: id ? id : user_ID,
	};
	const apiUrl = `${URL}/program/getallworkoutdatainrangeweeklysummary`;

	try {
		const response = await axios.post(apiUrl, body, {
			headers: {
				"Content-Type": "application/json",
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});

		return response?.data;
	} catch (error) {
		console.error("Error fetching data:", error);
		throw error; // Rethrow the error to be handled higher up in the call stack
	}
};
export const copyPasteWorkouts = async (id, date) => {
	let AuthToken = localStorage.getItem("token");
	let user_ID = localStorage.getItem("userId");

	const apiUrl = `${URL}/program/copyworkouts`;
	let body = {
		id: id,
		date: date,
	};
	try {
		const response = await axios.post(apiUrl, body, {
			headers: {
				"Content-Type": "application/json",
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		console.error("Error fetching data:", error);
		throw error; // Rethrow the error to be handled higher up in the call stack
	}
};
export const copyPasteweeklyWorkouts = async (data, assignedCocahId) => {
	let AuthToken = localStorage.getItem("token");
	let user_ID = localStorage.getItem("userId");

	const apiUrl = `${URL}/program/copyweekworkoutsv2`;

	let body = {
		...data,
		user_id: assignedCocahId ? assignedCocahId : user_ID,
	};
	try {
		const response = await axios.post(apiUrl, body, {
			headers: {
				"Content-Type": "application/json",
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		console.error("Error fetching data:", error);
		throw error; // Rethrow the error to be handled higher up in the call stack
	}
};
export const copyPasteweeklyTrainingBlocks = async (data, assignedCocahId) => {
	let AuthToken = localStorage.getItem("token");
	let user_ID = localStorage.getItem("userId");

	const apiUrl = `${URL}/program/copyweekworkoutintrainingblock`;

	let body = {
		...data,
		user_id: assignedCocahId ? assignedCocahId : user_ID,
	};
	try {
		const response = await axios.post(apiUrl, body, {
			headers: {
				"Content-Type": "application/json",
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		console.error("Error fetching data:", error);
		throw error; // Rethrow the error to be handled higher up in the call stack
	}
};

export const getParticularWorkoutDetails = async (id, assignedCocahId) => {
	let AuthToken = localStorage.getItem("token");
	let user_ID = localStorage.getItem("userId");

	const apiUrl = `${URL}/program/getmanualworkoutbyid/${id}`;

	try {
		const response = await axios.get(apiUrl, {
			headers: {
				"Content-Type": "application/json",
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});

		return response.data.data;
	} catch (error) {
		console.error("Error fetching data:", error);
		throw error; // Rethrow the error to be handled higher up in the call stack
	}
};

export const createCommentOnworkout = async (body) => {
	let AuthToken = localStorage.getItem("token");
	let user_ID = localStorage.getItem("userId");

	const apiUrl = `${URL}/program/comments`;

	let response = await axios.post(apiUrl, body, {
		headers: {
			"Content-Type": `application/json`,
			Authorization: AuthToken,
			"ngrok-skip-browser-warning": "69420",
		},
	});

	return response.data;
};

export const getAllcomments = async (body) => {
	let AuthToken = localStorage.getItem("token");
	let user_ID = localStorage.getItem("userId");

	const apiUrl = `${URL}/program/getAllComments`;

	let response = await axios.post(apiUrl, body, {
		headers: {
			"Content-Type": `application/json`,
			Authorization: AuthToken,
			"ngrok-skip-browser-warning": "69420",
		},
	});

	return response.data;
};

export const getAllUserZones = async (zoneID, assignedCocahId) => {
	console.log("nbnbndsagfdfhg", zoneID);
	let AuthToken = localStorage.getItem("token");
	let user_ID = localStorage.getItem("userId");
	const apiUrl = `${URL}/program/getuserzones/${
		assignedCocahId ? assignedCocahId : user_ID
	}/${zoneID}`;
	console.log("apiUrl", apiUrl);
	const response = await axios.get(apiUrl, {
		headers: {
			"Content-Type": "application/json",
			Authorization: AuthToken,
			"ngrok-skip-browser-warning": "69420",
		},
	});

	return response.data;
};
export const getDistanceforactivity = async (zoneID) => {
	let AuthToken = localStorage.getItem("token");
	let user_ID = localStorage.getItem("userId");

	const apiUrl = `${URL}/program/getdistanceforactivity/${zoneID}`;

	const response = await axios.get(apiUrl, {
		headers: {
			"Content-Type": "application/json",
			Authorization: AuthToken,
			"ngrok-skip-browser-warning": "69420",
		},
	});

	return response.data;
};
export const fetchactivitylevel = async (body) => {
	console.log("body", body);
	let AuthToken = localStorage.getItem("token");
	let user_ID = localStorage.getItem("userId");

	const apiUrl = `${URL}/program/fetchactivitylevel`;

	let response = await axios.post(apiUrl, body, {
		headers: {
			"Content-Type": `application/json`,
			Authorization: AuthToken,
			"ngrok-skip-browser-warning": "69420",
		},
	});

	return response.data;
};

export const getSaveZones = async (id, data, assignedCoach) => {
	try {
		let AuthToken = localStorage.getItem("token");
		let user_ID = localStorage.getItem("userId");
		let body = {
			activity_id: id,
			heartrate: data.heartrate,
			power: data.power,
			user_id: assignedCoach ? assignedCoach : user_ID,
			fitnesslevelid: data.level.id,
			distance: data.timeTrail,
			time: data.timeValue,
			updated_by_id: assignedCoach ? assignedCoach : user_ID,
			arrayofvalues: [],
			speed: parseFloat(data.speed),
			pace: data.pace,
		};
		const apiUrl = `${URL}/program/userzones`;

		let response = await axios.post(apiUrl, body, {
			headers: {
				"Content-Type": `application/json`,
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});

		return response.data;
	} catch (error) {
		return error.message;
	}
};
export const getSaveCoreZones = async (data, id) => {
	console.log("id", id);
	try {
		let AuthToken = localStorage.getItem("token");
		let user_ID = localStorage.getItem("userId");
		let body = {
			...data,
			user_id: id ? id : user_ID,
			updated_by_id: id ? id : user_ID,
		};
		const apiUrl = `${URL}/program/userzones`;

		let response = await axios.post(apiUrl, body, {
			headers: {
				"Content-Type": `application/json`,
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});

		return response.data;
	} catch (error) {
		return error.message;
	}
};

export const getFitnessData = async () => {
	try {
		const authToken = localStorage.getItem("token");
		const authUserToken = localStorage.getItem("userTokentoken");
		const userId = localStorage.getItem("userId");
		const url = `${URL}/program/getfitnesszone/${userId}`;
		const response = await axios.get(url, {
			headers: {
				"Content-Type": `application/json`,
				Authorization: authToken ? authToken : authUserToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		return error.message;
	}
};

export const getAllCountry = async () => {
	let AuthToken = localStorage.getItem("token");

	const apiUrl = `${URL}/loc/country`;

	try {
		const response = await axios.get(apiUrl, {
			headers: {
				"Content-Type": "application/json",
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		const sortlistCountry = response.data.data.sort((a, b) =>
			a.name.localeCompare(b.name)
		);

		return sortlistCountry;
	} catch (error) {
		console.error("Error fetching data:", error);
		throw error; // Rethrow the error to be handled higher up in the call stack
	}
};

export const getAllState = async (countryId) => {
	let AuthToken = localStorage.getItem("token");

	const apiUrl = `${URL}/loc/states/${countryId}`;

	try {
		const response = await axios.get(apiUrl, {
			headers: {
				"Content-Type": "application/json",
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		const sortListState = response.data.states.sort((a, b) =>
			a.name.localeCompare(b.name)
		);

		return sortListState;
	} catch (error) {
		console.error("Error fetching data:", error);
		throw error; // Rethrow the error to be handled higher up in the call stack
	}
};
export const getAllCities = async (countryId, stateId) => {
	let AuthToken = localStorage.getItem("token");

	const apiUrl = `${URL}/loc/cities/${countryId}/${stateId}`;

	try {
		const response = await axios.get(apiUrl, {
			headers: {
				"Content-Type": "application/json",
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});

		const cities = response.data.cities;

		// Sort the cities alphabetically by name
		const sortedCities = cities.sort((a, b) =>
			a.name.localeCompare(b.name)
		);
		return sortedCities;
	} catch (error) {
		console.error("Error fetching data:", error);
		throw error; // Rethrow the error to be handled higher up in the call stack
	}
};

// *************************************User subscription api ************************************* //

export const getAlluserSubscription = async () => {
	let AuthToken = localStorage.getItem("token");
	let user_ID = localStorage.getItem("userId");

	const apiUrl = `${URL}/program/getallusersubscriptionbyuserid/${user_ID}`;

	try {
		const response = await axios.get(apiUrl, {
			headers: {
				"Content-Type": "application/json",
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});

		return response.data;
	} catch (error) {
		console.error("Error fetching data:", error);
		throw error; // Rethrow the error to be handled higher up in the call stack
	}
};

export const getFechAlGoals = async (activityID) => {
	let AuthToken = localStorage.getItem("token");
	let user_ID = localStorage.getItem("userId");
	let AuthUserToken = localStorage.getItem("userTokentoken");

	const apiUrl = `${URL}/program/fetchgoalsbyactivityid/${activityID}`;

	try {
		const response = await axios.get(apiUrl, {
			headers: {
				"Content-Type": "application/json",
				Authorization: AuthUserToken ? AuthUserToken : AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});

		return response.data;
	} catch (error) {
		console.error("Error fetching data:", error);
		throw error; // Rethrow the error to be handled higher up in the call stack
	}
};
export const getPersonlisationForProgramID = async (programID) => {
	let AuthToken = localStorage.getItem("token");
	let user_ID = localStorage.getItem("userId");
	let body = {
		user_id: user_ID,
		program_id: programID,
	};
	const apiUrl = `${URL}/program/getpresonalizationprofle2`;

	try {
		const response = await axios.post(apiUrl, body, {
			headers: {
				"Content-Type": "application/json",
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});

		return response.data.data;
	} catch (error) {
		console.error("Error fetching data:", error);
		throw error; // Rethrow the error to be handled higher up in the call stack
	}
};
export const getPersonlisation = async (id) => {
	let AuthToken = localStorage.getItem("token");
	let user_ID = localStorage.getItem("userId");
	let body = {
		user_id: id ? id : user_ID,
	};
	const apiUrl = `${URL}/program/getpresonalizationprofle`;

	try {
		const response = await axios.post(apiUrl, body, {
			headers: {
				"Content-Type": "application/json",
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});

		return response.data.data;
	} catch (error) {
		console.error("Error fetching data:", error);
		throw error; // Rethrow the error to be handled higher up in the call stack
	}
};

export const getDaysById = async (activityID) => {
	let AuthToken = localStorage.getItem("token");
	let user_ID = localStorage.getItem("userId");

	const apiUrl = `${URL}/program/fetchgoalsbyactivityid/${activityID}`;

	try {
		const response = await axios.get(apiUrl, {
			headers: {
				"Content-Type": "application/json",
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});

		return response.data.goals;
	} catch (error) {
		console.error("Error fetching data:", error);
		throw error; // Rethrow the error to be handled higher up in the call stack
	}
};

export const getDayoptionValues = async (days, activityId) => {
	let AuthToken = localStorage.getItem("token");
	let user_ID = localStorage.getItem("userId");
	let AuthUserToken = localStorage.getItem("userTokentoken");

	const apiUrl = `${URL}/program/fetchdaysoptionvalueforalloptions/${days}/${activityId}`;

	try {
		const response = await axios.get(apiUrl, {
			headers: {
				"Content-Type": "application/json",
				Authorization: AuthUserToken ? AuthUserToken : AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});

		return response.data.values;
	} catch (error) {
		console.error("Error fetching data:", error);
		throw error; // Rethrow the error to be handled higher up in the call stack
	}
};
export const createPersonolize = async (data) => {
	let AuthToken = localStorage.getItem("token");
	let user_ID = localStorage.getItem("userId");
	let AuthUserToken = localStorage.getItem("userTokentoken");

	const body = { ...data, user_id: user_ID };
	const apiUrl = `${URL}/program/createpersonalizationprofile`;

	let response = await axios.post(apiUrl, body, {
		headers: {
			"Content-Type": `application/json`,
			Authorization: AuthUserToken ? AuthUserToken : AuthToken,
			"ngrok-skip-browser-warning": "69420",
		},
	});

	return response.data;
};
export const updatePersonlise = async (data, assignedCocahId) => {
	let AuthToken = localStorage.getItem("token");
	let user_ID = localStorage.getItem("userId");
	const body = {
		...data,
		user_id: assignedCocahId ? assignedCocahId : user_ID,
	};
	const apiUrl = `${URL}/program/updatepersonalizationprofile`;

	let response = await axios.post(apiUrl, body, {
		headers: {
			"Content-Type": `application/json`,
			Authorization: AuthToken,
			"ngrok-skip-browser-warning": "69420",
		},
	});

	return response.data;
};
export const getAssignedCoach = async (id) => {
	let AuthToken = localStorage.getItem("token");
	let user_ID = localStorage.getItem("userId");

	const apiUrl = `${URL}/program/assingedatheletestocoachv2/${user_ID}/${id}`;

	let response = await axios.get(apiUrl, {
		headers: {
			"Content-Type": `application/json`,
			Authorization: AuthToken,
			"ngrok-skip-browser-warning": "69420",
		},
	});
	console.log("response", response);
	return response.data;
};
export const createWorkoutLibrary1 = async (
	workout_library_name,
	commaSeparatedString
) => {
	let AuthToken = localStorage.getItem("token");
	let user_ID = localStorage.getItem("userId");

	let body = {
		name: workout_library_name,
		tags: commaSeparatedString,
		created_by: user_ID, // Replace with the actual user ID
	};
	const apiUrl = `${URL}/program/saveworkoutlibrary`;

	let { data } = await axios.post(apiUrl, body, {
		headers: {
			"Content-Type": `application/json`,
			Authorization: AuthToken,
			"ngrok-skip-browser-warning": "69420",
		},
	});

	return data;
};
export const getAllWorkoutLibrary1 = async (activityID) => {
	let AuthToken = localStorage.getItem("token");
	let user_ID = localStorage.getItem("userId");

	const apiUrl = `${URL}/program/renderalllibrarydataapi`;

	try {
		const response = await axios.get(apiUrl, {
			headers: {
				"Content-Type": "application/json",
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});

		return response.data;
	} catch (error) {
		console.error("Error fetching data:", error);
		throw error; // Rethrow the error to be handled higher up in the call stack
	}
};
export const getAllfitnesLevel = async (workoutLibraryID) => {
	let AuthToken = localStorage.getItem("token");
	let user_ID = localStorage.getItem("userId");

	const apiUrl = `${URL}/program/renderworkoutlibrarydataapi/${workoutLibraryID}`;

	try {
		const response = await axios.get(apiUrl, {
			headers: {
				"Content-Type": "application/json",
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});

		return response.data;
	} catch (error) {
		console.error("Error fetching data:", error);
		throw error; // Rethrow the error to be handled higher up in the call stack
	}
};

export const getAllWorkoutsByLiabrary = async (library_id) => {
	let AuthToken = localStorage.getItem("token");

	const apiUrl = `${URL}/program/renderlibrarydataapibyid/${
		library_id ? library_id : 2
	}`;

	try {
		const response = await axios.get(apiUrl, {
			headers: {
				"Content-Type": "application/json",
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data.library;
	} catch (error) {
		console.error("Error fetching data:", error);
		throw error; // Rethrow the error to be handled higher up in the call stack
	}
};
export const ceateTrainingPlan = async (body) => {
	let AuthToken = localStorage.getItem("token");
	let user_ID = localStorage.getItem("userId");

	const apiUrl = `${URL}/program/createtraininplan`;

	let { data } = await axios.post(apiUrl, body, {
		headers: {
			"Content-Type": `application/json`,
			Authorization: AuthToken,
			"ngrok-skip-browser-warning": "69420",
		},
	});

	return data;
};

export const createTraningBlocksWorkout = async (body) => {
	try {
		let AuthToken = localStorage.getItem("token");
		let user_ID = localStorage.getItem("userId");

		const apiUrl = `${URL}/program/manualv2fortraininplan`;

		let response = await axios.post(apiUrl, body, {
			headers: {
				"Content-Type": `application/json`,
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});

		return response.data;
	} catch (error) {
		return error.message;
	}
};
export const getAllTrainingBlocksWorkout = async (library_id) => {
	let AuthToken = localStorage.getItem("token");

	const apiUrl = `${URL}/program/gettrainingplandata/7`;

	try {
		const response = await axios.get(apiUrl, {
			headers: {
				"Content-Type": "application/json",
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data.formattedtraininplandata;
	} catch (error) {
		console.error("Error fetching data:", error);
		throw error; // Rethrow the error to be handled higher up in the call stack
	}
};

export const getPrograms = async (id) => {
	let AuthToken = localStorage.getItem("token");
	let user_ID = localStorage.getItem("userId");

	const apiUrl = `${URL}/program/getalluseractiveprograms/${
		id ? id : user_ID
	}`;

	try {
		const response = await axios.get(apiUrl, {
			headers: {
				"Content-Type": "application/json",
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		console.error("Error fetching data:", error);
		throw error; // Rethrow the error to be handled higher up in the call stack
	}
};
export const updateProgram = async (programId) => {
	try {
		let AuthToken = localStorage.getItem("token");
		let user_ID = localStorage.getItem("userId");

		const apiUrl = `${URL}/program/changeprofile/${programId}/${user_ID}`;

		let response = await axios.get(apiUrl, {
			headers: {
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});

		return response.data;
	} catch (error) {
		return error.message;
	}
};
export const getUsersProfile = async (programID, id) => {
	let AuthToken = localStorage.getItem("token");
	let user_ID = localStorage.getItem("userId");

	const apiUrl = `${URL}/profile/getprofilev2/${
		id ? id : user_ID
	}/${programID}`;

	try {
		const response = await axios.get(apiUrl, {
			headers: {
				"Content-Type": "application/json",
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data.data;
	} catch (error) {
		console.error("Error fetching data:", error);
		throw error; // Rethrow the error to be handled higher up in the call stack
	}
};
export const updatePrfile = async (body) => {
	try {
		let AuthToken = localStorage.getItem("token");
		let AuthUserToken = localStorage.getItem("userTokentoken");

		const apiUrl = `${URL}/profile/saveprofile`;

		let response = await axios.post(apiUrl, body, {
			headers: {
				Authorization: AuthUserToken ? AuthUserToken : AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});

		return response.data;
	} catch (error) {
		return error.message;
	}
};
export const getAllUserSubscription = async () => {
	let AuthToken = localStorage.getItem("token");

	const apiUrl = `${URL}/program/getallusersubscriptions`;

	try {
		const response = await axios.get(apiUrl, {
			headers: {
				"Content-Type": "application/json",
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});

		return response.data.subsWithCoach;
	} catch (error) {
		console.error("Error fetching data:", error);
		throw error; // Rethrow the error to be handled higher up in the call stack
	}
};
export const getAllGroups = async () => {
	let AuthToken = localStorage.getItem("token");

	const apiUrl = `${URL}/profile/getallgroups`;

	try {
		const response = await axios.get(apiUrl, {
			headers: {
				"Content-Type": "application/json",
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});

		return response.data;
	} catch (error) {
		console.error("Error fetching data:", error);
		throw error; // Rethrow the error to be handled higher up in the call stack
	}
};
export const getAllUserChallenges = async () => {
	let AuthToken = localStorage.getItem("token");

	const apiUrl = `${URL}/program/gamification/leaderboarddata`;

	try {
		const response = await axios.get(apiUrl, {
			headers: {
				"Content-Type": "application/json",
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});

		return response.data.challenges;
	} catch (error) {
		console.error("Error fetching data:", error);
		throw error; // Rethrow the error to be handled higher up in the call stack
	}
};
export const getChangeLogDEtails = async (userid, prograId) => {
	let AuthToken = localStorage.getItem("token");

	const apiUrl = `${URL}/program/getassingedcoach/${userid}/${prograId}`;

	try {
		const response = await axios.get(apiUrl, {
			headers: {
				"Content-Type": "application/json",
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});

		return response.data.coachchangelogs;
	} catch (error) {
		console.error("Error fetching data:", error);
		throw error; // Rethrow the error to be handled higher up in the call stack
	}
};
export const createPostLeaderBoard = async (body) => {
	try {
		let AuthToken = localStorage.getItem("token");

		const apiUrl = `${URL}/program/gamification/leaderboard`;

		let response = await axios.post(apiUrl, body, {
			headers: {
				"Content-Type": `application/json`,
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});

		return response.data;
	} catch (error) {
		return error.message;
	}
};
export const getAllCoachesList = async () => {
	let AuthToken = localStorage.getItem("token");

	const apiUrl = `${URL}/program/getallcoaches`;

	try {
		const response = await axios.get(apiUrl, {
			headers: {
				"Content-Type": "application/json",
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});

		return response.data.coaches;
	} catch (error) {
		console.error("Error fetching data:", error);
		throw error; // Rethrow the error to be handled higher up in the call stack
	}
};
export const getAllWithoutPaymentAthelete = async () => {
	let AuthToken = localStorage.getItem("token");

	const apiUrl = `${URL}/program/userwithnopaymentforsubs`;

	try {
		const response = await axios.get(apiUrl, {
			headers: {
				"Content-Type": "application/json",
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});

		return response.data.subs;
	} catch (error) {
		console.error("Error fetching data:", error);
		throw error; // Rethrow the error to be handled higher up in the call stack
	}
};
export const expireUserList = async () => {
	let AuthToken = localStorage.getItem("token");

	const apiUrl = `${URL}/profile/getabouttoexpiresubs`;

	try {
		const response = await axios.get(apiUrl, {
			headers: {
				"Content-Type": "application/json",
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});

		return response.data.subs;
	} catch (error) {
		console.error("Error fetching data:", error);
		throw error; // Rethrow the error to be handled higher up in the call stack
	}
};
export const getAllathletes = async () => {
	let AuthToken = localStorage.getItem("token");

	const apiUrl = `${URL}/program/getallateheletes`;

	try {
		const response = await axios.get(apiUrl, {
			headers: {
				"Content-Type": "application/json",
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});

		return response.data.atheletes;
	} catch (error) {
		console.error("Error fetching data:", error);
		throw error; // Rethrow the error to be handled higher up in the call stack
	}
};
export const getAllCoachesSheduelList = async (id) => {
	let AuthToken = localStorage.getItem("token");

	const apiUrl = `${URL}/program/getcoachschedule/${id}`;

	try {
		const response = await axios.get(apiUrl, {
			headers: {
				"Content-Type": "application/json",
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		const modifiedData = response.data.slots?.map((item) => {
			return {
				...item,
				backgroundColor: "white",
			};
		});
		return modifiedData;
	} catch (error) {
		console.error("Error fetching data:", error);
		throw error; // Rethrow the error to be handled higher up in the call stack
	}
};
export const getAllCoachesSheduelListCoach = async (id) => {
	let AuthToken = localStorage.getItem("token");
	let user_ID = localStorage.getItem("userId");

	const apiUrl = `${URL}/program/coachscheduledata/${user_ID}`;

	try {
		const response = await axios.get(apiUrl, {
			headers: {
				"Content-Type": "application/json",
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		const modifiedData = response.data.slots?.map((item) => {
			return {
				...item,
				backgroundColor: "white",
			};
		});
		return modifiedData;
	} catch (error) {
		console.error("Error fetching data:", error);
		throw error; // Rethrow the error to be handled higher up in the call stack
	}
};
export const updatecoachbooking = async (id) => {
	let user_ID = localStorage.getItem("userId");
	let body = {
		user_id: user_ID,
		slotid: id,
	};
	try {
		let AuthToken = localStorage.getItem("token");
		const apiUrl = `${URL}/program/coachbooking`;
		let response = await axios.post(apiUrl, body, {
			headers: {
				"Content-Type": `application/json`,
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		return error.message;
	}
};
export const craetSlotBooking = async (data) => {
	let user_ID = localStorage.getItem("userId");
	let body = {
		...data,
		coach: user_ID,
	};
	try {
		let AuthToken = localStorage.getItem("token");
		const apiUrl = `${URL}/program/coachschedule`;
		let response = await axios.post(apiUrl, body, {
			headers: {
				"Content-Type": `application/json`,
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		return error.message;
	}
};
export const updatSlotBooking = async (data) => {
	let user_ID = localStorage.getItem("userId");
	let body = {
		...data,
		coach: user_ID,
	};
	try {
		let AuthToken = localStorage.getItem("token");
		const apiUrl = `${URL}/program/updateschedule`;
		let response = await axios.post(apiUrl, body, {
			headers: {
				"Content-Type": `application/json`,
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		return error.message;
	}
};
export const approveBooking = async (id) => {
	let user_ID = localStorage.getItem("userId");
	let body = {
		user_id: user_ID,
		slotid: id,
	};
	try {
		let AuthToken = localStorage.getItem("token");
		const apiUrl = `${URL}/program/approvecoachbooking/${id}`;
		let response = await axios.get(apiUrl, {
			headers: {
				"Content-Type": `application/json`,
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		return error.message;
	}
};
export const cancelBooking = async (id) => {
	let user_ID = localStorage.getItem("userId");

	try {
		let AuthToken = localStorage.getItem("token");
		const apiUrl = `${URL}/program/rejectcoachbooking/${id}`;
		let response = await axios.get(apiUrl, {
			headers: {
				"Content-Type": `application/json`,
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		return error.message;
	}
};
export const getCoachPayment = async () => {
	let AuthToken = localStorage.getItem("token");
	let user_ID = localStorage.getItem("userId");

	const apiUrl = `${URL}/payment/coachpaymentatsbycoachidsimdata/${user_ID}`;

	try {
		const response = await axios.get(apiUrl, {
			headers: {
				"Content-Type": "application/json",
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});

		return response.data.coachpayments;
	} catch (error) {
		console.error("Error fetching data:", error);
		throw error; // Rethrow the error to be handled higher up in the call stack
	}
};
export const getAllBookingData = async () => {
	let AuthToken = localStorage.getItem("token");
	let user_ID = localStorage.getItem("userId");

	const apiUrl = `${URL}/program/getcoachbookingdata/${user_ID}`;

	try {
		const response = await axios.get(apiUrl, {
			headers: {
				"Content-Type": "application/json",
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});

		return JSON.parse(response.data.bookings);
	} catch (error) {
		console.error("Error fetching data:", error);
		throw error; // Rethrow the error to be handled higher up in the call stack
	}
};
export const updateCoachPaymnet = async (body) => {
	try {
		let AuthToken = localStorage.getItem("token");

		const apiUrl = `${URL}/payment/updatestatusofcoachpayment`;

		let response = await axios.post(apiUrl, body, {
			headers: {
				"Content-Type": `application/json`,
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});

		return response.data;
	} catch (error) {
		return error.message;
	}
};
export const CreateAssignCoach = async (body) => {
	try {
		let AuthToken = localStorage.getItem("token");

		const apiUrl = `${URL}/program/coachassignment`;

		let response = await axios.post(apiUrl, body, {
			headers: {
				"Content-Type": `application/json`,
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});

		return response.data;
	} catch (error) {
		return error.message;
	}
};
export const updateAssignCoach = async (body) => {
	try {
		let AuthToken = localStorage.getItem("token");

		const apiUrl = `${URL}/program/coachchange`;

		let response = await axios.post(apiUrl, body, {
			headers: {
				"Content-Type": `application/json`,
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});

		return response.data;
	} catch (error) {
		return error.message;
	}
};
export const importStravaActivity = async (assignedCocahId) => {
	try {
		let user_ID = localStorage.getItem("userId");

		let body = {
			user_id: assignedCocahId ? assignedCocahId : user_ID,
		};
		let AuthToken = localStorage.getItem("token");

		const apiUrl = `${URL}/import_strava_activities`;

		let response = await axios.post(apiUrl, body, {
			headers: {
				"Content-Type": `application/json`,
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});

		return response.data;
	} catch (error) {
		console.log("error", error);
		return error?.response?.statusText;
	}
};
export const syncStravaActivity = async (assignedCocahId) => {
	try {
		let user_ID = localStorage.getItem("userId");

		let body = {
			user_id: assignedCocahId ? assignedCocahId : user_ID,
		};
		let AuthToken = localStorage.getItem("token");

		const apiUrl = `${URL}/testfetchactivitysync/${
			assignedCocahId ? assignedCocahId : user_ID
		}`;

		let response = await axios.get(apiUrl, {
			headers: {
				"Content-Type": `application/json`,
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});

		return response.data;
	} catch (error) {
		return error?.response?.statusText;
	}
};
export const syncStravaActivityCoach = async (assignedCocahId, programID) => {
	try {
		let user_ID = localStorage.getItem("userId");

		let body = {
			user_id: assignedCocahId ? assignedCocahId : user_ID,
		};
		let AuthToken = localStorage.getItem("token");

		const apiUrl = `${URL}/program/generateautomaticworkoutdatav3/${
			assignedCocahId ? assignedCocahId : user_ID
		}/${programID}`;

		let response = await axios.get(apiUrl, {
			headers: {
				"Content-Type": `application/json`,
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});

		return response.data;
	} catch (error) {
		return error?.response?.statusText;
	}
};

//  admin chllenges api
export const getAllChallenges = async () => {
	let AuthToken = localStorage.getItem("token");

	const apiUrl = `${URL}/program/gamification/getallchallenges`;

	try {
		const response = await axios.get(apiUrl, {
			headers: {
				"Content-Type": "application/json",
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data.challenges;
	} catch (error) {
		console.error("Error fetching data:", error);
		throw error; // Rethrow the error to be handled higher up in the call stack
	}
};
export const getAllRenderChallengesData = async () => {
	let AuthToken = localStorage.getItem("token");

	const apiUrl = `${URL}/program/gamification/rendercreatechallangedata`;

	try {
		const response = await axios.get(apiUrl, {
			headers: {
				"Content-Type": "application/json",
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		console.error("Error fetching data:", error);
		throw error; // Rethrow the error to be handled higher up in the call stack
	}
};

export const createChallengeActivity = async (body) => {
	try {
		let AuthToken = localStorage.getItem("token");

		const apiUrl = `${URL}/program/gamification/createchallange`;

		let response = await axios.post(apiUrl, body, {
			headers: {
				// "Content-Type": `application/json`,
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});

		return response.data;
	} catch (error) {
		return error?.response?.statusText;
	}
};
export const updateChallengeActivity = async (body) => {
	try {
		let AuthToken = localStorage.getItem("token");

		const apiUrl = `${URL}/yofit/gamification/editchallenge`;

		let response = await axios.post(apiUrl, body, {
			headers: {
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});

		return response.data;
	} catch (error) {
		return error?.response?.statusText;
	}
};
export const updateRaceData = async (body) => {
	try {
		let AuthToken = localStorage.getItem("token");

		const apiUrl = `${URL}/program/updaterace`;

		let response = await axios.post(apiUrl, body, {
			headers: {
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});

		return response.data;
	} catch (error) {
		return error?.response?.statusText;
	}
};
export const updateSwimmingPoolData = async (body) => {
	try {
		let AuthToken = localStorage.getItem("token");

		const apiUrl = `${URL}/program/updateswimmingpool`;

		let response = await axios.post(apiUrl, body, {
			headers: {
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});

		return response.data;
	} catch (error) {
		return error?.response?.statusText;
	}
};
export const deleteCraetedCahllenges = async (id) => {
	let AuthToken = localStorage.getItem("token");

	const apiUrl = `${URL}/yofit/gamification/deletechallenge/${id}`;

	let { data } = await axios.get(apiUrl, {
		headers: {
			"Content-Type": `application/json`,
			Authorization: AuthToken,
			"ngrok-skip-browser-warning": "69420",
		},
	});

	return data;
};
export const deleteRaceData = async (id) => {
	let AuthToken = localStorage.getItem("token");

	const apiUrl = `${URL}/program/deleterace/${id}`;

	let { data } = await axios.get(apiUrl, {
		headers: {
			"Content-Type": `application/json`,
			Authorization: AuthToken,
			"ngrok-skip-browser-warning": "69420",
		},
	});

	return data;
};
export const deleteswimmingPoolData = async (id) => {
	let AuthToken = localStorage.getItem("token");

	const apiUrl = `${URL}/program/deleteswimmingpool/${id}`;

	let { data } = await axios.get(apiUrl, {
		headers: {
			"Content-Type": `application/json`,
			Authorization: AuthToken,
			"ngrok-skip-browser-warning": "69420",
		},
	});

	return data;
};
export const assignChallengesToUsers = async (data) => {
	try {
		let AuthToken = localStorage.getItem("token");
		let user_ID = localStorage.getItem("userId");
		let body = {
			...data,
			assinged_by: [user_ID],
		};

		const apiUrl = `${URL}/program/gamification/assignchallangetouser`;

		let response = await axios.post(apiUrl, body, {
			headers: {
				"Content-Type": `application/json`,
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});

		return response.data;
	} catch (error) {
		return error?.response?.statusText;
	}
};
export const assignChallengesToGroup = async (data) => {
	try {
		let AuthToken = localStorage.getItem("token");
		let user_ID = localStorage.getItem("userId");
		let body = {
			...data,
			assinged_by: [user_ID],
		};

		const apiUrl = `${URL}/program/gamification/assignchallangetogroup`;

		let response = await axios.post(apiUrl, body, {
			headers: {
				"Content-Type": `application/json`,
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});

		return response.data;
	} catch (error) {
		return error?.response?.statusText;
	}
};
export const getAllRacesData = async () => {
	let AuthToken = localStorage.getItem("token");

	const apiUrl = `${URL}/program/getallracesdata`;

	try {
		const response = await axios.get(apiUrl, {
			headers: {
				"Content-Type": "application/json",
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data.races;
	} catch (error) {
		console.error("Error fetching data:", error);
		throw error; // Rethrow the error to be handled higher up in the call stack
	}
};
export const getAllRenderSaveRaceData = async () => {
	let AuthToken = localStorage.getItem("token");

	const apiUrl = `${URL}/program/addracedata`;

	try {
		const response = await axios.get(apiUrl, {
			headers: {
				"Content-Type": "application/json",
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		console.error("Error fetching data:", error);
		throw error; // Rethrow the error to be handled higher up in the call stack
	}
};

export const createRace = async (body) => {
	try {
		let AuthToken = localStorage.getItem("token");

		const apiUrl = `${URL}/program/addrace`;

		let response = await axios.post(apiUrl, body, {
			headers: {
				// "Content-Type": `application/json`,
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});

		return response.data;
	} catch (error) {
		return error?.response?.statusText;
	}
};
export const createSwim = async (body) => {
	try {
		let AuthToken = localStorage.getItem("token");

		const apiUrl = `${URL}/program/addswimmingpool`;

		let response = await axios.post(apiUrl, body, {
			headers: {
				// "Content-Type": `application/json`,
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});

		return response.data;
	} catch (error) {
		return error?.response?.statusText;
	}
};
export const getAllSwimingData = async () => {
	let AuthToken = localStorage.getItem("token");

	const apiUrl = `${URL}/program/getallswimmingpoolsdata`;

	try {
		const response = await axios.get(apiUrl, {
			headers: {
				"Content-Type": "application/json",
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data.swimmingpools;
	} catch (error) {
		console.error("Error fetching data:", error);
		throw error; // Rethrow the error to be handled higher up in the call stack
	}
};
export const updateRace = async (body) => {
	try {
		let AuthToken = localStorage.getItem("token");

		const apiUrl = `${URL}/yofit/gamification/editchallenge`;

		let response = await axios.post(apiUrl, body, {
			headers: {
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});

		return response.data;
	} catch (error) {
		return error?.response?.statusText;
	}
};

export const getWellnessQuetions = async (id) => {
	let AuthToken = localStorage.getItem("token");

	const apiUrl = `${URL}/program/renderassessmentdata/wellness`;

	let { data } = await axios.get(apiUrl, {
		headers: {
			"Content-Type": `application/json`,
			Authorization: AuthToken,
			"ngrok-skip-browser-warning": "69420",
		},
	});

	return data.wellnessAssessment;
};
export const createWellnessData = async (data) => {
	try {
		let AuthToken = localStorage.getItem("token");
		let user_ID = localStorage.getItem("userId");
		let body = {
			...data,
			user_id: user_ID,
		};

		const apiUrl = `${URL}/program/readinessindex/wellness`;

		let response = await axios.post(apiUrl, body, {
			headers: {
				"Content-Type": `application/json`,
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});

		return response.data;
	} catch (error) {
		return error?.response?.statusText;
	}
};

export const getWellnessReult = async (id) => {
	let AuthToken = localStorage.getItem("token");
	let user_ID = localStorage.getItem("userId");

	const apiUrl = `${URL}/program/readinessindex/wellness/showassessmentresultdata/${user_ID}`;

	let { data } = await axios.get(apiUrl, {
		headers: {
			"Content-Type": `application/json`,
			Authorization: AuthToken,
			"ngrok-skip-browser-warning": "69420",
		},
	});

	return data.data;
};
export const getRacesRedinessQuetions = async (id) => {
	let AuthToken = localStorage.getItem("token");
	let user_ID = localStorage.getItem("userId");

	const apiUrl = `${URL}/program/readinessindex/racereadinessdata/${user_ID}`;

	let { data } = await axios.get(apiUrl, {
		headers: {
			"Content-Type": `application/json`,
			Authorization: AuthToken,
			"ngrok-skip-browser-warning": "69420",
		},
	});

	return data;
};
export const createRaccesReadinessData = async (data) => {
	try {
		let AuthToken = localStorage.getItem("token");
		let user_ID = localStorage.getItem("userId");
		let body = {
			structuredData: data,
			user_id: user_ID,
		};

		const apiUrl = `${URL}/program/readinessindex/racereadiness`;

		let response = await axios.post(apiUrl, body, {
			headers: {
				"Content-Type": `application/json`,
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});

		return response.data;
	} catch (error) {
		return error?.response?.statusText;
	}
};
export const getRacessReadinessReult = async (id) => {
	let AuthToken = localStorage.getItem("token");
	let user_ID = localStorage.getItem("userId");

	const apiUrl = `${URL}/program/readinessindex/racereadiness/showracereadinessresultdata/${user_ID}`;

	let { data } = await axios.get(apiUrl, {
		headers: {
			"Content-Type": `application/json`,
			Authorization: AuthToken,
			"ngrok-skip-browser-warning": "69420",
		},
	});

	return data.data;
};

export const getAssignedChallenges = async (id) => {
	let AuthToken = localStorage.getItem("token");
	let user_ID = localStorage.getItem("userId");

	const apiUrl = `${URL}/program/gamification/assingedchallengesdata`;

	let { data } = await axios.get(apiUrl, {
		headers: {
			"Content-Type": `application/json`,
			Authorization: AuthToken,
			"ngrok-skip-browser-warning": "69420",
		},
	});

	return data.challenges;
};
export const getAllTrainingBlock = async (id) => {
	let AuthToken = localStorage.getItem("token");
	let user_ID = localStorage.getItem("userId");

	const apiUrl = `${URL}/trainingBlock/getAllTrainingBlocksAll`;

	let { data } = await axios.get(apiUrl, {
		headers: {
			"Content-Type": `application/json`,
			Authorization: AuthToken,
			"ngrok-skip-browser-warning": "69420",
		},
	});

	return data.trainingBlocks;
};
export const createTRackChallenge = async (body) => {
	try {
		let AuthToken = localStorage.getItem("token");
		let user_ID = localStorage.getItem("userId");

		const apiUrl = `${URL}/program/gamification/createchallengetrack`;
		console.log("vfbdvbfgsdh", apiUrl);

		let response = await axios.post(apiUrl, body, {
			headers: {
				"Content-Type": `application/json`,
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		console.log("response", response);
		return response.data;
	} catch (error) {
		return error?.response?.statusText;
	}
};

export const getAllAssignedTrack = async (id) => {
	let AuthToken = localStorage.getItem("token");
	let user_ID = localStorage.getItem("userId");

	const apiUrl = `${URL}/program/gamification/getallchallengetracksdata`;

	let { data } = await axios.get(apiUrl, {
		headers: {
			"Content-Type": `application/json`,
			Authorization: AuthToken,
			"ngrok-skip-browser-warning": "69420",
		},
	});

	return data.alltrackswithchallenges;
};

export const assignTariningPlanToUser = async (body) => {
	try {
		let AuthToken = localStorage.getItem("token");
		let user_ID = localStorage.getItem("userId");

		const apiUrl = `${URL}/program/assigntrainingplantouser`;
		console.log("vfbdvbfgsdh", apiUrl);

		let response = await axios.post(apiUrl, body, {
			headers: {
				"Content-Type": `application/json`,
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		console.log("response", response);
		return response.data;
	} catch (error) {
		return error?.response?.statusText;
	}
};
export const unLinkTariningPlanToUser = async (body) => {
	try {
		let AuthToken = localStorage.getItem("token");
		let user_ID = localStorage.getItem("userId");

		const apiUrl = `${URL}/program/unlinkassigntrainingplantouser`;
		console.log("vfbdvbfgsdh", apiUrl);

		let response = await axios.post(apiUrl, body, {
			headers: {
				"Content-Type": `application/json`,
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		console.log("response", response);
		return response.data;
	} catch (error) {
		return error?.response?.statusText;
	}
};
export const mergeWorkout = async (data, id) => {
	console.log("id", id);
	try {
		let body = {
			...data,
			workout_id: id,
		};

		let AuthToken = localStorage.getItem("token");
		let user_ID = localStorage.getItem("userId");

		const apiUrl = `${URL}/program/mergestravaactivitywithworkout`;
		console.log("vfbdvbfgsdh", apiUrl);

		let response = await axios.post(apiUrl, body, {
			headers: {
				"Content-Type": `application/json`,
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		console.log("response", response);
		return response.data;
	} catch (error) {
		return error?.response?.statusText;
	}
};
export const unMergedWorkouts = async (body) => {
	try {
		let AuthToken = localStorage.getItem("token");
		let user_ID = localStorage.getItem("userId");

		const apiUrl = `${URL}/program/unmergestravaactivitywithworkout`;
		console.log("vfbdvbfgsdh", apiUrl);

		let response = await axios.post(apiUrl, body, {
			headers: {
				"Content-Type": `application/json`,
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		console.log("response", response);
		return response.data;
	} catch (error) {
		return error?.response?.statusText;
	}
};
export const getCalculatorGoals = async (data) => {
	try {
		let AuthToken = localStorage.getItem("token");
		let user_ID = localStorage.getItem("userId");

		const apiUrl = `${URL}/program/racecalculatordata`;
		console.log("vfbdvbfgsdh", apiUrl);

		let response = await axios.get(apiUrl, {
			headers: {
				"Content-Type": `application/json`,
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		console.log("response", response);
		return response.data;
	} catch (error) {
		return error?.response?.statusText;
	}
};
export const createCalculator = async (body) => {
	try {
		let AuthToken = localStorage.getItem("token");
		let user_ID = localStorage.getItem("userId");

		//   let body = {
		//     "user_id":user_ID,
		//     "structuredData":data
		// }
		const apiUrl = `${URL}/program/racecalculatorv2`;
		console.log("vfbdvbfgsdh", apiUrl);

		let response = await axios.post(apiUrl, body, {
			headers: {
				"Content-Type": `application/json`,
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		console.log("response", response);
		return response.data;
	} catch (error) {
		return error?.response?.statusText;
	}
};

export const getCoachRevenueReport = async (body) => {
	try {
		let AuthToken = localStorage.getItem("token");
		let user_ID = localStorage.getItem("userId");

		const apiUrl = `${URL}/program/coachrevenuereportbytimerangedatav2`;
		console.log("vfbdvbfgsdh", apiUrl);

		let response = await axios.post(apiUrl, body, {
			headers: {
				"Content-Type": `application/json`,
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		console.log("response", response);
		return response.data;
	} catch (error) {
		return error?.response?.statusText;
	}
};
export const getSubscriptionPaymentData = async (body) => {
	try {
		let AuthToken = localStorage.getItem("token");
		let user_ID = localStorage.getItem("userId");

		const apiUrl = `${URL}/payment/getSubscriptionsWithPaymentdata`;
		console.log("vfbdvbfgsdh", apiUrl);

		let response = await axios.post(apiUrl, body, {
			headers: {
				"Content-Type": `application/json`,
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		console.log("response", response);
		return response.data;
	} catch (error) {
		return error?.response?.statusText;
	}
};
export const getSubscriptionPaymentFile = async (body) => {
	try {
		let AuthToken = localStorage.getItem("token");
		let user_ID = localStorage.getItem("userId");

		const apiUrl = `${URL}/payment/getSubscriptionsWithPayment`;
		console.log("vfbdvbfgsdh", apiUrl);

		let response = await axios.post(apiUrl, body, {
			headers: {
				"Content-Type": `application/json`,
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		console.log("getSubscriptionPaymentFileResponse", response);
		return response.data;
	} catch (error) {
		return error?.response?.statusText;
	}
};
export const revenueReportByTimeRange = async (body) => {
	try {
		let AuthToken = localStorage.getItem("token");
		let user_ID = localStorage.getItem("userId");

		const apiUrl = `${URL}/program/revenuereportbytimerangedata`;

		let response = await axios.post(apiUrl, body, {
			headers: {
				"Content-Type": `application/json`,
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		console.log("response", response);
		return response.data;
	} catch (error) {
		return error?.response?.statusText;
	}
};
export const UserGrowthReportData = async (body) => {
	try {
		let AuthToken = localStorage.getItem("token");
		let user_ID = localStorage.getItem("userId");

		const apiUrl = `${URL}/program/usergrowthnumbersdata`;
		console.log("vfbdvbfgsdh", apiUrl);

		let response = await axios.post(apiUrl, body, {
			headers: {
				"Content-Type": `application/json`,
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		return error?.response?.statusText;
	}
};
export const userOverallReports = async (body) => {
	try {
		let AuthToken = localStorage.getItem("token");
		let user_ID = localStorage.getItem("userId");

		const apiUrl = `${URL}/program/growthoverentirespan`;
		console.log("vfbdvbfgsdh", apiUrl);

		let response = await axios.post(
			apiUrl,
			{},
			{
				headers: {
					"Content-Type": `application/json`,
					Authorization: AuthToken,
					"ngrok-skip-browser-warning": "69420",
				},
			}
		);
		return response.data;
	} catch (error) {
		return error?.response?.statusText;
	}
};
export const formReviewCoach = async (data) => {
	try {
		let AuthToken = localStorage.getItem("token");
		let user_ID = localStorage.getItem("userId");

		let body = {
			...data,
			user_id: user_ID,
		};
		const apiUrl = `${URL}/program/formreview`;
		console.log("vfbdvbfgsdh", apiUrl);

		let response = await axios.post(apiUrl, body, {
			headers: {
				"Content-Type": `application/json`,
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		return error?.response?.statusText;
	}
};
export const createAssesmentform = async (data) => {
	try {
		let AuthToken = localStorage.getItem("token");
		let user_ID = localStorage.getItem("userId");

		let body = {
			...data,
			reviewed_by: user_ID,
		};
		const apiUrl = `${URL}/program/saveassessmentpost`;
		console.log("vfbdvbfgsdh", apiUrl);

		let response = await axios.post(apiUrl, body, {
			headers: {
				"Content-Type": `application/json`,
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		return error?.response?.statusText;
	}
};

export const fetchSelefAssesmnetGetData = async (id) => {
	try {
		let AuthToken = localStorage.getItem("token");
		let user_ID = localStorage.getItem("userId");

		const apiUrl = `${URL}/program/selfassessmentformdata/${user_ID}/${id}`;
		console.log("vfbdvbfgsdh", apiUrl);

		let response = await axios.get(apiUrl, {
			headers: {
				"Content-Type": `application/json`,
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		return error?.response?.statusText;
	}
};
export const fetchSavedAssesmnetGetData = async (id) => {
	try {
		let AuthToken = localStorage.getItem("token");
		let user_ID = localStorage.getItem("userId");

		const apiUrl = `${URL}/program/getformreviewuserdata/${user_ID}`;
		console.log("vfbdvbfgsdh", apiUrl);

		let response = await axios.get(apiUrl, {
			headers: {
				"Content-Type": `application/json`,
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		return error?.response?.statusText;
	}
};
export const fetchSavedAssesmnetGetDataCoach = async (id) => {
	try {
		let AuthToken = localStorage.getItem("token");
		let user_ID = localStorage.getItem("userId");

		const apiUrl = `${URL}/program/getformreviewcoachdata/${user_ID}`;
		console.log("vfbdvbfgsdh", apiUrl);

		let response = await axios.get(apiUrl, {
			headers: {
				"Content-Type": `application/json`,
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		return error?.response?.statusText;
	}
};
export const NotifiAtheletes = async (id) => {
	try {
		let AuthToken = localStorage.getItem("token");
		let user_ID = localStorage.getItem("userId");

		const apiUrl = `${URL}/program/notifyathleteofformreviewcompletion/${id}`;
		console.log("vfbdvbfgsdh", apiUrl);

		let response = await axios.get(apiUrl, {
			headers: {
				"Content-Type": `application/json`,
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		return error?.response?.statusText;
	}
};
export const getUserChalnnges = async (id) => {
	let AuthToken = localStorage.getItem("token");
	let user_ID = localStorage.getItem("userId");

	const apiUrl = `${URL}/program/gamification/getalluserchallengesdata/${
		id ? id : user_ID
	}`;

	try {
		const response = await axios.get(apiUrl, {
			headers: {
				"Content-Type": "application/json",
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		console.error("Error fetching data:", error);
		throw error; // Rethrow the error to be handled higher up in the call stack
	}
};
export const getProgress = async (body) => {
	let AuthToken = localStorage.getItem("token");
	let user_ID = localStorage.getItem("userId");

	const apiUrl = `${URL}/program/gamification/checkprogress`;

	try {
		const response = await axios.post(apiUrl, body, {
			headers: {
				"Content-Type": "application/json",
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		console.error("Error fetching data:", error);
		throw error; // Rethrow the error to be handled higher up in the call stack
	}
};
export const getUserOngoingChalenges = async () => {
	let AuthToken = localStorage.getItem("token");
	let user_ID = localStorage.getItem("userId");

	const apiUrl = `${URL}/program/gamification/getallongoingchallengesdata/${user_ID}`;

	try {
		const response = await axios.get(apiUrl, {
			headers: {
				"Content-Type": "application/json",
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		console.error("Error fetching data:", error);
		throw error; // Rethrow the error to be handled higher up in the call stack
	}
};
export const subsribeToChallenge = async (data) => {
	try {
		let AuthToken = localStorage.getItem("token");
		let user_ID = localStorage.getItem("userId");
		console.log(data,"Line4446");
		let body = {
			...data,
			userId: user_ID,
		};
		console.log(body,"Line4451");
		const apiUrl = `${URL}/program/gamification/subscribetochallenge`;
		console.log("vfbdvbfgsdh", apiUrl);

		let response = await axios.post(apiUrl, body, {
			headers: {
				"Content-Type": `application/json`,
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		return error?.response?.statusText;
	}
};


export const subsribeAthleteToChallenge = async (data, user_ID) => {
	try {
		let AuthToken = localStorage.getItem("token");
		let body = {
			challengeId: parseInt(data, 10),
			userId: String(user_ID),
		};
		
		const apiUrl = `${URL}/program/gamification/subscribetochallenge`;

		let response = await axios.post(apiUrl, body, {
			headers: {
				"Content-Type": `application/json`,
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		return error?.response?.statusText;
	}
};


export const testSyncChallenge = async (id) => {
	let AuthToken = localStorage.getItem("token");
	let user_ID = localStorage.getItem("userId");
	const apiUrl = `${URL}/program/gamification/nodecrontbyuseridforchallengesync/${
		id ? id : user_ID
	}`;

	try {
		const response = await axios.get(apiUrl, {
			headers: {
				"Content-Type": "application/json",
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		console.error("Error fetching data:", error);
		throw error; // Rethrow the error to be handled higher up in the call stack
	}
};
export const weeklyFeedData = async () => {
	let AuthToken = localStorage.getItem("token");
	let user_ID = localStorage.getItem("userId");

	const apiUrl = `${URL}/feeddata/getallweekpatterdata`;

	try {
		const response = await axios.get(apiUrl, {
			headers: {
				"Content-Type": "application/json",
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		console.error("Error fetching data:", error);
		throw error; // Rethrow the error to be handled higher up in the call stack
	}
};
export const weeklyFeedDataProgram = async () => {
	let AuthToken = localStorage.getItem("token");
	let user_ID = localStorage.getItem("userId");

	const apiUrl = `${URL}/feeddata/weeklypatterdata`;

	try {
		const response = await axios.get(apiUrl, {
			headers: {
				"Content-Type": "application/json",
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		console.error("Error fetching data:", error);
		throw error; // Rethrow the error to be handled higher up in the call stack
	}
};
export const weeklyFeedDataPattern = async (id) => {
	let AuthToken = localStorage.getItem("token");
	let user_ID = localStorage.getItem("userId");

	const apiUrl = `${URL}/feeddata/getheaderssavailible/${id}`;

	try {
		const response = await axios.get(apiUrl, {
			headers: {
				"Content-Type": "application/json",
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data.headers;
	} catch (error) {
		console.error("Error fetching data:", error);
		throw error; // Rethrow the error to be handled higher up in the call stack
	}
};
export const CreateFeedWeeklyDataPattern = async (body) => {
	try {
		let AuthToken = localStorage.getItem("token");
		let user_ID = localStorage.getItem("userId");

		const apiUrl = `${URL}/feeddata/saveweeklypatter`;
		console.log("vfbdvbfgsdh", apiUrl);

		let response = await axios.post(apiUrl, body, {
			headers: {
				"Content-Type": `application/json`,
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		return error?.response?.statusText;
	}
};
export const updateFeedWeeklyDataPattern = async (body) => {
	try {
		let AuthToken = localStorage.getItem("token");
		let user_ID = localStorage.getItem("userId");

		const apiUrl = `${URL}/feeddata/updateweeklypattern`;
		console.log("vfbdvbfgsdh", apiUrl);

		let response = await axios.post(apiUrl, body, {
			headers: {
				"Content-Type": `application/json`,
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		return error?.response?.statusText;
	}
};

export const getZonesClasification = async (id) => {
	let AuthToken = localStorage.getItem("token");
	let user_ID = localStorage.getItem("userId");

	const apiUrl = `${URL}/feeddata/zonesactivities`;

	try {
		const response = await axios.get(apiUrl, {
			headers: {
				"Content-Type": "application/json",
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data.data;
	} catch (error) {
		console.error("Error fetching data:", error);
		throw error; // Rethrow the error to be handled higher up in the call stack
	}
};
export const CreateZonesClasification = async (body) => {
	try {
		let AuthToken = localStorage.getItem("token");
		let user_ID = localStorage.getItem("userId");

		const apiUrl = `${URL}/feeddata/zonesactivities`;
		console.log("vfbdvbfgsdh", apiUrl);

		let response = await axios.post(apiUrl, body, {
			headers: {
				"Content-Type": `application/json`,
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		return error?.response?.statusText;
	}
};
export const updateZonesClasification = async (body) => {
	try {
		let AuthToken = localStorage.getItem("token");
		let user_ID = localStorage.getItem("userId");

		const apiUrl = `${URL}/feeddata/zonesactivities/${body?.id}`;
		console.log("vfbdvbfgsdh", apiUrl);

		let response = await axios.put(apiUrl, body, {
			headers: {
				"Content-Type": `application/json`,
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		return error?.response?.statusText;
	}
};
export const deleteZonesClasification = async (id) => {
	let AuthToken = localStorage.getItem("token");

	const apiUrl = `${URL}/feeddata/zonesactivities/${id}`;

	let { data } = await axios.delete(apiUrl, {
		headers: {
			"Content-Type": `application/json`,
			Authorization: AuthToken,
			"ngrok-skip-browser-warning": "69420",
		},
	});

	return data;
};

export const deleteWeeklyPattern = async (id) => {
	let AuthToken = localStorage.getItem("token");

	const apiUrl = `${URL}/feeddata/deleteweeklypattern/${id}`;

	let { data } = await axios.delete(apiUrl, {
		headers: {
			"Content-Type": `application/json`,
			Authorization: AuthToken,
			"ngrok-skip-browser-warning": "69420",
		},
	});

	return data;
};

export const getBounds = async (id) => {
	let AuthToken = localStorage.getItem("token");
	let user_ID = localStorage.getItem("userId");

	const apiUrl = `${URL}/program/bounds`;

	try {
		const response = await axios.get(apiUrl, {
			headers: {
				"Content-Type": "application/json",
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data.allBounds;
	} catch (error) {
		console.error("Error fetching data:", error);
		throw error; // Rethrow the error to be handled higher up in the call stack
	}
};
export const createBound = async (body) => {
	try {
		let AuthToken = localStorage.getItem("token");
		let user_ID = localStorage.getItem("userId");

		const apiUrl = `${URL}/program/bounds`;
		console.log("vfbdvbfgsdh", apiUrl);

		let response = await axios.post(apiUrl, body, {
			headers: {
				"Content-Type": `application/json`,
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		return error?.response?.statusText;
	}
};
export const updateBounds = async (body) => {
	try {
		let AuthToken = localStorage.getItem("token");
		let user_ID = localStorage.getItem("userId");

		const apiUrl = `${URL}/program/bounds/${body?.id}`;
		console.log("vfbdvbfgsdh", apiUrl);

		let response = await axios.put(apiUrl, body, {
			headers: {
				"Content-Type": `application/json`,
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		return error?.response?.statusText;
	}
};
export const deleteBounds = async (id) => {
	let AuthToken = localStorage.getItem("token");

	const apiUrl = `${URL}/program/bounds/${id}`;

	let { data } = await axios.delete(apiUrl, {
		headers: {
			"Content-Type": `application/json`,
			Authorization: AuthToken,
			"ngrok-skip-browser-warning": "69420",
		},
	});

	return data;
};
export const getWorkoutDistribution = async (id) => {
	let AuthToken = localStorage.getItem("token");
	let user_ID = localStorage.getItem("userId");

	const apiUrl = `${URL}/feeddata/workoutdistribution`;

	try {
		const response = await axios.get(apiUrl, {
			headers: {
				"Content-Type": "application/json",
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data.allWorkoutDistributions;
	} catch (error) {
		console.error("Error fetching data:", error);
		throw error; // Rethrow the error to be handled higher up in the call stack
	}
};
export const createWorkoutDistribution = async (body) => {
	try {
		let AuthToken = localStorage.getItem("token");
		let user_ID = localStorage.getItem("userId");

		const apiUrl = `${URL}/feeddata/workoutdistribution`;
		console.log("vfbdvbfgsdh", apiUrl);

		let response = await axios.post(apiUrl, body, {
			headers: {
				"Content-Type": `application/json`,
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		return error?.response?.statusText;
	}
};
export const updateWorkoutDistribution = async (body) => {
	try {
		let AuthToken = localStorage.getItem("token");
		let user_ID = localStorage.getItem("userId");

		const apiUrl = `${URL}/feeddata/workoutdistribution/${body?.id}`;
		console.log("vfbdvbfgsdh", apiUrl);

		let response = await axios.put(apiUrl, body, {
			headers: {
				"Content-Type": `application/json`,
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		return error?.response?.statusText;
	}
};
export const deleteWorkoutDistribution = async (id) => {
	let AuthToken = localStorage.getItem("token");

	const apiUrl = `${URL}/feeddata/workoutdistribution/${id}`;

	let { data } = await axios.delete(apiUrl, {
		headers: {
			"Content-Type": `application/json`,
			Authorization: AuthToken,
			"ngrok-skip-browser-warning": "69420",
		},
	});

	return data;
};
export const getSaveAutomationData = async (id) => {
	let AuthToken = localStorage.getItem("token");
	let user_ID = localStorage.getItem("userId");

	const apiUrl = `${URL}/program/getallautomationtrainingblock`;

	try {
		const response = await axios.get(apiUrl, {
			headers: {
				"Content-Type": "application/json",
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		console.error("Error fetching data:", error);
		throw error; // Rethrow the error to be handled higher up in the call stack
	}
};
export const getAllPrograms = async (id) => {
	let AuthToken = localStorage.getItem("token");
	let user_ID = localStorage.getItem("userId");

	const apiUrl = `${URL}/feeddata/programs`;

	try {
		const response = await axios.get(apiUrl, {
			headers: {
				"Content-Type": "application/json",
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		console.error("Error fetching data:", error);
		throw error; // Rethrow the error to be handled higher up in the call stack
	}
};
export const getAllTrainingBlockData = async (id) => {
	let AuthToken = localStorage.getItem("token");
	let user_ID = localStorage.getItem("userId");

	const apiUrl = `${URL}/trainingBlock/data/getalltrainingblocks`;

	try {
		const response = await axios.get(apiUrl, {
			headers: {
				"Content-Type": "application/json",
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data.getAllTrainingBlocks;
	} catch (error) {
		console.error("Error fetching data:", error);
		throw error; // Rethrow the error to be handled higher up in the call stack
	}
};

export const CreatePrograms = async (body) => {
	try {
		let AuthToken = localStorage.getItem("token");
		let user_ID = localStorage.getItem("userId");

		const apiUrl = `${URL}/feeddata/programs`;
		console.log("vfbdvbfgsdh", apiUrl);

		let response = await axios.post(apiUrl, body, {
			headers: {
				"Content-Type": `application/json`,
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		return error?.response?.statusText;
	}
};
export const CreateSaveAutomation = async (body) => {
	try {
		let AuthToken = localStorage.getItem("token");
		let user_ID = localStorage.getItem("userId");
		const apiUrl = `${URL}/program/saveautomationtrainingblock`;
		console.log("vfbdvbfgsdh", apiUrl);

		let response = await axios.post(apiUrl, body, {
			headers: {
				"Content-Type": `application/json`,
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		return error?.response?.statusText;
	}
};
export const updatePrograms = async (body) => {
	try {
		let AuthToken = localStorage.getItem("token");
		let user_ID = localStorage.getItem("userId");

		const apiUrl = `${URL}/feeddata/programs/${body?.program_id}`;
		console.log("vfbdvbfgsdh", apiUrl);

		let response = await axios.put(apiUrl, body, {
			headers: {
				"Content-Type": `application/json`,
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		return error?.response?.statusText;
	}
};
export const deletePrograms = async (id) => {
	try {
		let AuthToken = localStorage.getItem("token");

		const apiUrl = `${URL}/feeddata/programs/${id}`;

		let { data } = await axios.delete(apiUrl, {
			headers: {
				"Content-Type": `application/json`,
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});

		return data;
	} catch (error) {
		return error?.response?.data;
	}
};
export const deleteAutomation = async (id) => {
	try {
		let AuthToken = localStorage.getItem("token");

		const apiUrl = `${URL}/program/deleteautomationtrainingblock/${id}`;

		let { data } = await axios.get(apiUrl, {
			headers: {
				"Content-Type": `application/json`,
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});

		return data;
	} catch (error) {
		return error?.response?.data;
	}
};

export const getAlllevels = async (id) => {
	let AuthToken = localStorage.getItem("token");
	let user_ID = localStorage.getItem("userId");

	const apiUrl = `${URL}/feeddata/levels`;

	try {
		const response = await axios.get(apiUrl, {
			headers: {
				"Content-Type": "application/json",
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		console.error("Error fetching data:", error);
		throw error; // Rethrow the error to be handled higher up in the call stack
	}
};
export const CreateLevels = async (body) => {
	try {
		let AuthToken = localStorage.getItem("token");
		let user_ID = localStorage.getItem("userId");

		const apiUrl = `${URL}/feeddata/levels`;
		console.log("vfbdvbfgsdh", apiUrl);

		let response = await axios.post(apiUrl, body, {
			headers: {
				"Content-Type": `application/json`,
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		return error?.response?.statusText;
	}
};
export const updateLevel = async (body) => {
	try {
		let AuthToken = localStorage.getItem("token");
		let user_ID = localStorage.getItem("userId");

		const apiUrl = `${URL}/feeddata/levels/${body?.id}`;
		console.log("vfbdvbfgsdh", apiUrl);

		let response = await axios.put(apiUrl, body, {
			headers: {
				"Content-Type": `application/json`,
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		return error?.response?.statusText;
	}
};
export const deleteLevel = async (id) => {
	let AuthToken = localStorage.getItem("token");

	const apiUrl = `${URL}/feeddata/levels/${id}`;

	let { data } = await axios.delete(apiUrl, {
		headers: {
			"Content-Type": `application/json`,
			Authorization: AuthToken,
			"ngrok-skip-browser-warning": "69420",
		},
	});

	return data;
};

export const getAllReviewSection = async (id) => {
	let AuthToken = localStorage.getItem("token");
	let user_ID = localStorage.getItem("userId");

	const apiUrl = `${URL}/program/formreviewsection`;

	try {
		const response = await axios.get(apiUrl, {
			headers: {
				"Content-Type": "application/json",
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		console.error("Error fetching data:", error);
		throw error; // Rethrow the error to be handled higher up in the call stack
	}
};
export const getAllReviewSectionByID = async (id) => {
	let AuthToken = localStorage.getItem("token");
	let user_ID = localStorage.getItem("userId");

	const apiUrl = `${URL}/program/formreviewsectionbyactivityid/${id}`;

	try {
		const response = await axios.get(apiUrl, {
			headers: {
				"Content-Type": "application/json",
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		console.error("Error fetching data:", error);
		throw error; // Rethrow the error to be handled higher up in the call stack
	}
};
export const CreateReviewSection = async (body) => {
	try {
		let AuthToken = localStorage.getItem("token");
		let user_ID = localStorage.getItem("userId");

		const apiUrl = `${URL}/program/formreviewsection`;
		console.log("vfbdvbfgsdh", apiUrl);

		let response = await axios.post(apiUrl, body, {
			headers: {
				"Content-Type": `application/json`,
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		return error?.response?.statusText;
	}
};
export const updateRevieSection = async (body) => {
	try {
		let AuthToken = localStorage.getItem("token");
		let user_ID = localStorage.getItem("userId");

		const apiUrl = `${URL}/program/formreviewsection/${body?.id}`;
		console.log("vfbdvbfgsdh", apiUrl);

		let response = await axios.put(apiUrl, body, {
			headers: {
				"Content-Type": `application/json`,
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		return error?.response?.statusText;
	}
};
export const deleteRevieSection = async (id) => {
	let AuthToken = localStorage.getItem("token");

	const apiUrl = `${URL}/program/formreviewsection/${id}`;

	let { data } = await axios.delete(apiUrl, {
		headers: {
			"Content-Type": `application/json`,
			Authorization: AuthToken,
			"ngrok-skip-browser-warning": "69420",
		},
	});

	return data;
};

export const getAllReviewSectionOption = async (id) => {
	let AuthToken = localStorage.getItem("token");
	let user_ID = localStorage.getItem("userId");

	const apiUrl = `${URL}/program/formreviewsectionoptions`;

	try {
		const response = await axios.get(apiUrl, {
			headers: {
				"Content-Type": "application/json",
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		console.error("Error fetching data:", error);
		throw error; // Rethrow the error to be handled higher up in the call stack
	}
};
export const CreateReviewSectionOption = async (body) => {
	try {
		let AuthToken = localStorage.getItem("token");
		let user_ID = localStorage.getItem("userId");

		const apiUrl = `${URL}/program/formreviewsectionoptions`;
		console.log("vfbdvbfgsdh", apiUrl);

		let response = await axios.post(apiUrl, body, {
			headers: {
				"Content-Type": `application/json`,
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		return error?.response?.statusText;
	}
};
export const updateRevieSectionOption = async (body) => {
	try {
		let AuthToken = localStorage.getItem("token");
		let user_ID = localStorage.getItem("userId");

		const apiUrl = `${URL}/program/formreviewsectionoptions/${body?.id}`;
		console.log("vfbdvbfgsdh", apiUrl);

		let response = await axios.put(apiUrl, body, {
			headers: {
				"Content-Type": `application/json`,
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		return error?.response?.statusText;
	}
};
export const deleteRevieSectionOption = async (id) => {
	let AuthToken = localStorage.getItem("token");

	const apiUrl = `${URL}/program/formreviewsectionoptions/${id}`;

	let { data } = await axios.delete(apiUrl, {
		headers: {
			"Content-Type": `application/json`,
			Authorization: AuthToken,
			"ngrok-skip-browser-warning": "69420",
		},
	});

	return data;
};
export const getAllRaces = async (id) => {
	let AuthToken = localStorage.getItem("token");
	let user_ID = localStorage.getItem("userId");

	const apiUrl = `${URL}/program/getracetype`;

	try {
		const response = await axios.get(apiUrl, {
			headers: {
				"Content-Type": "application/json",
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data?.racetype;
	} catch (error) {
		console.error("Error fetching data:", error);
		throw error; // Rethrow the error to be handled higher up in the call stack
	}
};
export const CreateRaces = async (body) => {
	try {
		let AuthToken = localStorage.getItem("token");
		let user_ID = localStorage.getItem("userId");

		const apiUrl = `${URL}/program/addracetype`;
		console.log("vfbdvbfgsdh", apiUrl);

		let response = await axios.post(apiUrl, body, {
			headers: {
				"Content-Type": `application/json`,
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		return error?.response?.statusText;
	}
};
export const updateRaces = async (body) => {
	try {
		let AuthToken = localStorage.getItem("token");
		let user_ID = localStorage.getItem("userId");

		const apiUrl = `${URL}/program/updateracetype`;
		console.log("vfbdvbfgsdh", apiUrl);

		let response = await axios.post(apiUrl, body, {
			headers: {
				"Content-Type": `application/json`,
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		return error?.response?.statusText;
	}
};
export const deleteRaces = async (id) => {
	let AuthToken = localStorage.getItem("token");

	const apiUrl = `${URL}/program/deleteracetype/${id}`;

	let { data } = await axios.get(apiUrl, {
		headers: {
			"Content-Type": `application/json`,
			Authorization: AuthToken,
			"ngrok-skip-browser-warning": "69420",
		},
	});

	return data;
};
export const getAllActivityData = async (id) => {
	let AuthToken = localStorage.getItem("token");
	let user_ID = localStorage.getItem("userId");

	const apiUrl = `${URL}/feeddata/activities`;

	try {
		const response = await axios.get(apiUrl, {
			headers: {
				"Content-Type": "application/json",
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		console.error("Error fetching data:", error);
		throw error; // Rethrow the error to be handled higher up in the call stack
	}
};
export const createActivity = async (body) => {
	try {
		let AuthToken = localStorage.getItem("token");
		let user_ID = localStorage.getItem("userId");

		const apiUrl = `${URL}/feeddata/activities`;
		console.log("vfbdvbfgsdh", apiUrl);

		let response = await axios.post(apiUrl, body, {
			headers: {
				"Content-Type": `application/json`,
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		return error?.response?.statusText;
	}
};
export const updateActivity = async (body) => {
	try {
		let AuthToken = localStorage.getItem("token");
		let user_ID = localStorage.getItem("userId");

		const apiUrl = `${URL}/feeddata/activities/${body?.id}`;
		console.log("vfbdvbfgsdh", apiUrl);

		let response = await axios.put(apiUrl, body, {
			headers: {
				"Content-Type": `application/json`,
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		return error?.response?.statusText;
	}
};
export const deleteActivity = async (id) => {
	console.log("id", id);
	let AuthToken = localStorage.getItem("token");

	const apiUrl = `${URL}/feeddata/activities/${id}`;

	let { data } = await axios.delete(apiUrl, {
		headers: {
			"Content-Type": `application/json`,
			Authorization: AuthToken,
			"ngrok-skip-browser-warning": "69420",
		},
	});

	return data;
};
export const getAllRaceCalculation = async (id) => {
	let AuthToken = localStorage.getItem("token");
	let user_ID = localStorage.getItem("userId");

	const apiUrl = `${URL}/feeddata/goals`;

	try {
		const response = await axios.get(apiUrl, {
			headers: {
				"Content-Type": "application/json",
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		console.error("Error fetching data:", error);
		throw error; // Rethrow the error to be handled higher up in the call stack
	}
};
export const createRaceCalculation = async (body) => {
	try {
		let AuthToken = localStorage.getItem("token");
		let user_ID = localStorage.getItem("userId");

		const apiUrl = `${URL}/feeddata/goals`;
		console.log("vfbdvbfgsdh", apiUrl);

		let response = await axios.post(apiUrl, body, {
			headers: {
				"Content-Type": `application/json`,
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		return error?.response?.statusText;
	}
};
export const updateRaceCalculation = async (body) => {
	try {
		let AuthToken = localStorage.getItem("token");
		let user_ID = localStorage.getItem("userId");

		const apiUrl = `${URL}/feeddata/goals/${body?.id}`;
		console.log("vfbdvbfgsdh", apiUrl);

		let response = await axios.put(apiUrl, body, {
			headers: {
				"Content-Type": `application/json`,
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		return error?.response?.statusText;
	}
};
export const deleteRaceCalculation = async (id) => {
	console.log("id", id);
	let AuthToken = localStorage.getItem("token");

	const apiUrl = `${URL}/feeddata/goals/${id}`;

	let { data } = await axios.delete(apiUrl, {
		headers: {
			"Content-Type": `application/json`,
			Authorization: AuthToken,
			"ngrok-skip-browser-warning": "69420",
		},
	});

	return data;
};
export const getAllMultipleRaceCalculation = async (id) => {
	let AuthToken = localStorage.getItem("token");
	let user_ID = localStorage.getItem("userId");

	const apiUrl = `${URL}/feeddata/multisport`;

	try {
		const response = await axios.get(apiUrl, {
			headers: {
				"Content-Type": "application/json",
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		console.error("Error fetching data:", error);
		throw error; // Rethrow the error to be handled higher up in the call stack
	}
};
export const createMultipleRaceCalculation = async (body) => {
	try {
		let AuthToken = localStorage.getItem("token");
		let user_ID = localStorage.getItem("userId");

		const apiUrl = `${URL}/feeddata/multisport`;
		console.log("vfbdvbfgsdh", apiUrl);

		let response = await axios.post(apiUrl, body, {
			headers: {
				"Content-Type": `application/json`,
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		return error?.response?.statusText;
	}
};
export const updateMultipleRaceCalculation = async (body) => {
	try {
		let AuthToken = localStorage.getItem("token");
		let user_ID = localStorage.getItem("userId");

		const apiUrl = `${URL}/feeddata/multisport/${body?.id}`;
		console.log("vfbdvbfgsdh", apiUrl);

		let response = await axios.put(apiUrl, body, {
			headers: {
				"Content-Type": `application/json`,
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		return error?.response?.statusText;
	}
};
export const deleteMultipleRaceCalculation = async (id) => {
	console.log("id", id);
	let AuthToken = localStorage.getItem("token");

	const apiUrl = `${URL}/feeddata/multisport/${id}`;

	let { data } = await axios.delete(apiUrl, {
		headers: {
			"Content-Type": `application/json`,
			Authorization: AuthToken,
			"ngrok-skip-browser-warning": "69420",
		},
	});

	return data;
};

export const getAllGoalNameForYTA = async (id) => {
	let AuthToken = localStorage.getItem("token");
	let user_ID = localStorage.getItem("userId");

	const apiUrl = `${URL}/feeddata/goalnamesforyta`;

	try {
		const response = await axios.get(apiUrl, {
			headers: {
				"Content-Type": "application/json",
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		console.error("Error fetching data:", error);
		throw error; // Rethrow the error to be handled higher up in the call stack
	}
};
export const createGoalNameYTA = async (body) => {
	try {
		let AuthToken = localStorage.getItem("token");
		let user_ID = localStorage.getItem("userId");

		const apiUrl = `${URL}/feeddata/goalnamesforyta`;
		console.log("vfbdvbfgsdh", apiUrl);

		let response = await axios.post(apiUrl, body, {
			headers: {
				"Content-Type": `application/json`,
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		return error?.response?.statusText;
	}
};
export const updateGoalNameYTA = async (body) => {
	try {
		let AuthToken = localStorage.getItem("token");
		let user_ID = localStorage.getItem("userId");

		const apiUrl = `${URL}/feeddata/goalnamesforyta/${body?.id}`;
		console.log("vfbdvbfgsdh", apiUrl);

		let response = await axios.put(apiUrl, body, {
			headers: {
				"Content-Type": `application/json`,
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		return error?.response?.statusText;
	}
};
export const deleteGoalNameYTA = async (id) => {
	console.log("id", id);
	let AuthToken = localStorage.getItem("token");

	const apiUrl = `${URL}/feeddata/goalnamesforyta/${id}`;

	let { data } = await axios.delete(apiUrl, {
		headers: {
			"Content-Type": `application/json`,
			Authorization: AuthToken,
			"ngrok-skip-browser-warning": "69420",
		},
	});

	return data;
};
export const getAllGoalsData = async (id) => {
	let AuthToken = localStorage.getItem("token");
	let user_ID = localStorage.getItem("userId");

	const apiUrl = `${URL}/feeddata/goalsdata`;

	try {
		const response = await axios.get(apiUrl, {
			headers: {
				"Content-Type": "application/json",
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		console.error("Error fetching data:", error);
		throw error; // Rethrow the error to be handled higher up in the call stack
	}
};
export const createGoalsdata = async (body) => {
	try {
		let AuthToken = localStorage.getItem("token");
		let user_ID = localStorage.getItem("userId");

		const apiUrl = `${URL}/feeddata/goalsdata`;
		console.log("vfbdvbfgsdh", apiUrl);

		let response = await axios.post(apiUrl, body, {
			headers: {
				"Content-Type": `application/json`,
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		return error?.response?.statusText;
	}
};
export const updateGoalsdata = async (body) => {
	try {
		let AuthToken = localStorage.getItem("token");
		let user_ID = localStorage.getItem("userId");

		const apiUrl = `${URL}/feeddata/goalsdata/${body?.id}`;
		console.log("vfbdvbfgsdh", apiUrl);

		let response = await axios.put(apiUrl, body, {
			headers: {
				"Content-Type": `application/json`,
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		return error?.response?.statusText;
	}
};
export const deleteGoalsData = async (id) => {
	console.log("id", id);
	let AuthToken = localStorage.getItem("token");

	const apiUrl = `${URL}/feeddata/goalsdata/${id}`;

	let { data } = await axios.delete(apiUrl, {
		headers: {
			"Content-Type": `application/json`,
			Authorization: AuthToken,
			"ngrok-skip-browser-warning": "69420",
		},
	});

	return data;
};
export const getAlPhaseBlockData = async (id) => {
	let AuthToken = localStorage.getItem("token");
	let user_ID = localStorage.getItem("userId");

	const apiUrl = `${URL}/feeddata/phase`;

	try {
		const response = await axios.get(apiUrl, {
			headers: {
				"Content-Type": "application/json",
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		console.error("Error fetching data:", error);
		throw error; // Rethrow the error to be handled higher up in the call stack
	}
};
export const createPhaseBloackdata = async (body) => {
	try {
		let AuthToken = localStorage.getItem("token");
		let user_ID = localStorage.getItem("userId");

		const apiUrl = `${URL}/feeddata/phase`;
		console.log("vfbdvbfgsdh", apiUrl);

		let response = await axios.post(apiUrl, body, {
			headers: {
				"Content-Type": `application/json`,
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		return error?.response?.statusText;
	}
};
export const updatePhaseBloackdata = async (body) => {
	try {
		let AuthToken = localStorage.getItem("token");
		let user_ID = localStorage.getItem("userId");

		const apiUrl = `${URL}/feeddata/phase/${body?.id}`;
		console.log("vfbdvbfgsdh", apiUrl);

		let response = await axios.put(apiUrl, body, {
			headers: {
				"Content-Type": `application/json`,
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		return error?.response?.statusText;
	}
};
export const deletePhaseBloackData = async (id) => {
	console.log("id", id);
	let AuthToken = localStorage.getItem("token");

	const apiUrl = `${URL}/feeddata/phase/${id}`;

	let { data } = await axios.delete(apiUrl, {
		headers: {
			"Content-Type": `application/json`,
			Authorization: AuthToken,
			"ngrok-skip-browser-warning": "69420",
		},
	});

	return data;
};
export const getAllPhaseNameData = async (id) => {
	let AuthToken = localStorage.getItem("token");
	let user_ID = localStorage.getItem("userId");

	const apiUrl = `${URL}/feeddata/phasename`;

	try {
		const response = await axios.get(apiUrl, {
			headers: {
				"Content-Type": "application/json",
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		console.error("Error fetching data:", error);
		throw error; // Rethrow the error to be handled higher up in the call stack
	}
};
export const createPhaseNamedata = async (body) => {
	try {
		let AuthToken = localStorage.getItem("token");
		let user_ID = localStorage.getItem("userId");

		const apiUrl = `${URL}/feeddata/phasename`;
		console.log("vfbdvbfgsdh", apiUrl);

		let response = await axios.post(apiUrl, body, {
			headers: {
				"Content-Type": `application/json`,
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		return error?.response?.statusText;
	}
};
export const updatePhaseNamedata = async (body) => {
	try {
		let AuthToken = localStorage.getItem("token");
		let user_ID = localStorage.getItem("userId");

		const apiUrl = `${URL}/feeddata/phasename/${body?.id}`;
		console.log("vfbdvbfgsdh", apiUrl);

		let response = await axios.put(apiUrl, body, {
			headers: {
				"Content-Type": `application/json`,
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		return error?.response?.statusText;
	}
};
export const deletePhaseNameData = async (id) => {
	console.log("id", id);
	let AuthToken = localStorage.getItem("token");

	const apiUrl = `${URL}/feeddata/phasename/${id}`;

	let { data } = await axios.delete(apiUrl, {
		headers: {
			"Content-Type": `application/json`,
			Authorization: AuthToken,
			"ngrok-skip-browser-warning": "69420",
		},
	});

	return data;
};

export const getAllYTAVolumeData = async (id) => {
	let AuthToken = localStorage.getItem("token");
	let user_ID = localStorage.getItem("userId");

	const apiUrl = `${URL}/feeddata/ytagoalsvolume`;

	try {
		const response = await axios.get(apiUrl, {
			headers: {
				"Content-Type": "application/json",
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		console.error("Error fetching data:", error);
		throw error; // Rethrow the error to be handled higher up in the call stack
	}
};
export const createYTAVolumedata = async (body) => {
	try {
		let AuthToken = localStorage.getItem("token");
		let user_ID = localStorage.getItem("userId");

		const apiUrl = `${URL}/feeddata/ytagoalsvolume`;
		console.log("vfbdvbfgsdh", apiUrl);

		let response = await axios.post(apiUrl, body, {
			headers: {
				"Content-Type": `application/json`,
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		return error?.response?.statusText;
	}
};
export const updateYTAVolumedata = async (body) => {
	try {
		let AuthToken = localStorage.getItem("token");
		let user_ID = localStorage.getItem("userId");

		const apiUrl = `${URL}/feeddata/ytagoalsvolume/${body?.id}`;
		console.log("vfbdvbfgsdh", apiUrl);

		let response = await axios.put(apiUrl, body, {
			headers: {
				"Content-Type": `application/json`,
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		return error?.response?.statusText;
	}
};
export const deleteYTAVolumeData = async (id) => {
	console.log("id", id);
	let AuthToken = localStorage.getItem("token");

	const apiUrl = `${URL}/feeddata/ytagoalsvolume/${id}`;

	let { data } = await axios.delete(apiUrl, {
		headers: {
			"Content-Type": `application/json`,
			Authorization: AuthToken,
			"ngrok-skip-browser-warning": "69420",
		},
	});

	return data;
};
export const getAllUserGoalData = async (id) => {
	let AuthToken = localStorage.getItem("token");
	let user_ID = localStorage.getItem("userId");

	const apiUrl = `${URL}/usergoals/getallusergoals/${user_ID}`;

	try {
		const response = await axios.get(apiUrl, {
			headers: {
				"Content-Type": "application/json",
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		console.error("Error fetching data:", error);
		throw error; // Rethrow the error to be handled higher up in the call stack
	}
};
export const createUserGoaldata = async (data) => {
	try {
		let AuthToken = localStorage.getItem("token");
		let user_ID = localStorage.getItem("userId");

		const body = { ...data, user_id: user_ID };
		const apiUrl = `${URL}/usergoals/creategoals`;
		console.log("vfbdvbfgsdh", apiUrl);

		let response = await axios.post(apiUrl, body, {
			headers: {
				"Content-Type": `application/json`,
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		return error?.response?.statusText;
	}
};
export const updateUserGoaldata = async (data) => {
	try {
		let AuthToken = localStorage.getItem("token");
		let user_ID = localStorage.getItem("userId");
		const body = { ...data, user_id: user_ID };

		const apiUrl = `${URL}/usergoals/creategoals`;
		console.log("vfbdvbfgsdh", apiUrl);

		let response = await axios.post(apiUrl, body, {
			headers: {
				"Content-Type": `application/json`,
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		return error?.response?.statusText;
	}
};
export const deleteUserGoalData = async (id) => {
	console.log("id", id);
	let AuthToken = localStorage.getItem("token");

	const apiUrl = `${URL}/usergoals/deletegoals/${id}`;

	let { data } = await axios.get(apiUrl, {
		headers: {
			"Content-Type": `application/json`,
			Authorization: AuthToken,
			"ngrok-skip-browser-warning": "69420",
		},
	});

	return data;
};
export const getAllUserAdminGoalData = async (id) => {
	let AuthToken = localStorage.getItem("token");
	let user_ID = localStorage.getItem("userId");

	const apiUrl = `${URL}/usergoals/getallgoalscreatedbyadmin`;

	try {
		const response = await axios.get(apiUrl, {
			headers: {
				"Content-Type": "application/json",
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		console.error("Error fetching data:", error);
		throw error; // Rethrow the error to be handled higher up in the call stack
	}
};
export const createUserAdminGoaldata = async (body) => {
	try {
		let AuthToken = localStorage.getItem("token");
		let user_ID = localStorage.getItem("userId");

		const apiUrl = `${URL}/usergoals/creategoalbyadmin`;
		console.log("vfbdvbfgsdh", apiUrl);

		let response = await axios.post(apiUrl, body, {
			headers: {
				"Content-Type": `application/json`,
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		return error?.response?.statusText;
	}
};
export const updateUserAdminGoaldata = async (body) => {
	try {
		let AuthToken = localStorage.getItem("token");
		let user_ID = localStorage.getItem("userId");

		const apiUrl = `${URL}/usergoals/creategoalbyadmin`;
		console.log("vfbdvbfgsdh", apiUrl);

		let response = await axios.post(apiUrl, body, {
			headers: {
				"Content-Type": `application/json`,
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		return error?.response?.statusText;
	}
};
export const deleteUserAdminGoalData = async (id) => {
	console.log("id", id);
	let AuthToken = localStorage.getItem("token");

	const apiUrl = `${URL}/usergoals/deletegoalbyadmin/${id}`;

	let { data } = await axios.delete(apiUrl, {
		headers: {
			"Content-Type": `application/json`,
			Authorization: AuthToken,
			"ngrok-skip-browser-warning": "69420",
		},
	});

	return data;
};
export const getAllYTAData = async (id) => {
	let AuthToken = localStorage.getItem("token");
	let user_ID = localStorage.getItem("userId");

	const apiUrl = `${URL}/feeddata/ytagoals`;

	try {
		const response = await axios.get(apiUrl, {
			headers: {
				"Content-Type": "application/json",
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		console.error("Error fetching data:", error);
		throw error; // Rethrow the error to be handled higher up in the call stack
	}
};
export const createYTAdata = async (body) => {
	try {
		let AuthToken = localStorage.getItem("token");
		let user_ID = localStorage.getItem("userId");

		const apiUrl = `${URL}/feeddata/ytagoals`;
		console.log("vfbdvbfgsdh", apiUrl);

		let response = await axios.post(apiUrl, body, {
			headers: {
				"Content-Type": `application/json`,
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		return error?.response?.statusText;
	}
};
export const updateYTAdata = async (body) => {
	try {
		let AuthToken = localStorage.getItem("token");
		let user_ID = localStorage.getItem("userId");

		const apiUrl = `${URL}/feeddata/ytagoals/${body?.id}`;
		console.log("vfbdvbfgsdh", apiUrl);

		let response = await axios.put(apiUrl, body, {
			headers: {
				"Content-Type": `application/json`,
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		return error?.response?.statusText;
	}
};
export const deleteYTAData = async (id) => {
	console.log("id", id);
	let AuthToken = localStorage.getItem("token");

	const apiUrl = `${URL}/feeddata/ytagoals/${id}`;

	let { data } = await axios.delete(apiUrl, {
		headers: {
			"Content-Type": `application/json`,
			Authorization: AuthToken,
			"ngrok-skip-browser-warning": "69420",
		},
	});

	return data;
};
export const getAllPhaseSubData = async (id) => {
	let AuthToken = localStorage.getItem("token");
	let user_ID = localStorage.getItem("userId");

	const apiUrl = `${URL}/feeddata/phasesubactivity`;

	try {
		const response = await axios.get(apiUrl, {
			headers: {
				"Content-Type": "application/json",
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		console.error("Error fetching data:", error);
		throw error; // Rethrow the error to be handled higher up in the call stack
	}
};
export const createPhaseSubdata = async (body) => {
	try {
		let AuthToken = localStorage.getItem("token");
		let user_ID = localStorage.getItem("userId");

		const apiUrl = `${URL}/feeddata/phasesubactivity`;
		console.log("vfbdvbfgsdh", apiUrl);

		let response = await axios.post(apiUrl, body, {
			headers: {
				"Content-Type": `application/json`,
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		return error?.response?.statusText;
	}
};
export const updatePhaseSubdata = async (body) => {
	try {
		let AuthToken = localStorage.getItem("token");
		let user_ID = localStorage.getItem("userId");

		const apiUrl = `${URL}/feeddata/phasesubactivity/${body?.id}`;
		console.log("vfbdvbfgsdh", apiUrl);

		let response = await axios.put(apiUrl, body, {
			headers: {
				"Content-Type": `application/json`,
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		return error?.response?.statusText;
	}
};
export const deletePhaseSubData = async (id) => {
	console.log("id", id);
	let AuthToken = localStorage.getItem("token");

	const apiUrl = `${URL}/feeddata/phasesubactivity/${id}`;

	let { data } = await axios.delete(apiUrl, {
		headers: {
			"Content-Type": `application/json`,
			Authorization: AuthToken,
			"ngrok-skip-browser-warning": "69420",
		},
	});

	return data;
};

export const getAllsubactivityData = async (id) => {
	let AuthToken = localStorage.getItem("token");
	let user_ID = localStorage.getItem("userId");

	const apiUrl = `${URL}/feeddata/subworkout`;

	try {
		const response = await axios.get(apiUrl, {
			headers: {
				"Content-Type": "application/json",
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		console.error("Error fetching data:", error);
		throw error; // Rethrow the error to be handled higher up in the call stack
	}
};
export const createsubactivitydata = async (body) => {
	try {
		let AuthToken = localStorage.getItem("token");
		let user_ID = localStorage.getItem("userId");

		const apiUrl = `${URL}/feeddata/subworkout`;
		console.log("vfbdvbfgsdh", apiUrl);

		let response = await axios.post(apiUrl, body, {
			headers: {
				"Content-Type": `application/json`,
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		return error?.response?.statusText;
	}
};
export const updatesubactivitydata = async (body) => {
	try {
		let AuthToken = localStorage.getItem("token");
		let user_ID = localStorage.getItem("userId");

		const apiUrl = `${URL}/feeddata/subworkout/${body?.id}`;
		console.log("vfbdvbfgsdh", apiUrl);

		let response = await axios.put(apiUrl, body, {
			headers: {
				"Content-Type": `application/json`,
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		return error?.response?.statusText;
	}
};
export const deletesubactivityData = async (id) => {
	console.log("id", id);
	let AuthToken = localStorage.getItem("token");

	const apiUrl = `${URL}/feeddata/subworkout/${id}`;

	let { data } = await axios.delete(apiUrl, {
		headers: {
			"Content-Type": `application/json`,
			Authorization: AuthToken,
			"ngrok-skip-browser-warning": "69420",
		},
	});

	return data;
};
export const getAllworkoutData = async (id) => {
	let AuthToken = localStorage.getItem("token");
	let user_ID = localStorage.getItem("userId");

	const apiUrl = `${URL}/feeddata/workout`;

	try {
		const response = await axios.get(apiUrl, {
			headers: {
				"Content-Type": "application/json",
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		console.error("Error fetching data:", error);
		throw error; // Rethrow the error to be handled higher up in the call stack
	}
};
export const createworkoutdata = async (body) => {
	try {
		let AuthToken = localStorage.getItem("token");
		let user_ID = localStorage.getItem("userId");

		const apiUrl = `${URL}/feeddata/workout`;
		console.log("vfbdvbfgsdh", apiUrl);

		let response = await axios.post(apiUrl, body, {
			headers: {
				"Content-Type": `application/json`,
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		return error?.response?.statusText;
	}
};
export const updateworkoutdata = async (body) => {
	try {
		let AuthToken = localStorage.getItem("token");
		let user_ID = localStorage.getItem("userId");

		const apiUrl = `${URL}/feeddata/workout/${body?.id}`;
		console.log("vfbdvbfgsdh", apiUrl);

		let response = await axios.put(apiUrl, body, {
			headers: {
				"Content-Type": `application/json`,
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		return error?.response?.statusText;
	}
};
export const deleteworkoutData = async (id) => {
	console.log("id", id);
	let AuthToken = localStorage.getItem("token");

	const apiUrl = `${URL}/feeddata/workout/${id}`;

	let { data } = await axios.delete(apiUrl, {
		headers: {
			"Content-Type": `application/json`,
			Authorization: AuthToken,
			"ngrok-skip-browser-warning": "69420",
		},
	});

	return data;
};
export const getAllAssesmentData = async (id) => {
	let AuthToken = localStorage.getItem("token");
	let user_ID = localStorage.getItem("userId");

	const apiUrl = `${URL}/feeddata/assessments`;

	try {
		const response = await axios.get(apiUrl, {
			headers: {
				"Content-Type": "application/json",
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		console.error("Error fetching data:", error);
		throw error; // Rethrow the error to be handled higher up in the call stack
	}
};
export const createAssesmentdata = async (body) => {
	try {
		let AuthToken = localStorage.getItem("token");
		let user_ID = localStorage.getItem("userId");

		const apiUrl = `${URL}/feeddata/assessments`;
		console.log("vfbdvbfgsdh", apiUrl);

		let response = await axios.post(apiUrl, body, {
			headers: {
				"Content-Type": `application/json`,
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		return error?.response?.statusText;
	}
};
export const updateAssesmentdata = async (body) => {
	try {
		let AuthToken = localStorage.getItem("token");
		let user_ID = localStorage.getItem("userId");

		const apiUrl = `${URL}/feeddata/assessments/${body?.id}`;
		console.log("vfbdvbfgsdh", apiUrl);

		let response = await axios.put(apiUrl, body, {
			headers: {
				"Content-Type": `application/json`,
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		return error?.response?.statusText;
	}
};
export const deleteAssesmentData = async (id) => {
	console.log("id", id);
	let AuthToken = localStorage.getItem("token");

	const apiUrl = `${URL}/feeddata/assessments/${id}`;

	let { data } = await axios.delete(apiUrl, {
		headers: {
			"Content-Type": `application/json`,
			Authorization: AuthToken,
			"ngrok-skip-browser-warning": "69420",
		},
	});

	return data;
};

export const getAllSugmentData = async (id) => {
	let AuthToken = localStorage.getItem("token");
	let user_ID = localStorage.getItem("userId");

	const apiUrl = `${URL}/feeddata/segments`;

	try {
		const response = await axios.get(apiUrl, {
			headers: {
				"Content-Type": "application/json",
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		console.error("Error fetching data:", error);
		throw error; // Rethrow the error to be handled higher up in the call stack
	}
};
export const createSugmentdata = async (body) => {
	try {
		let AuthToken = localStorage.getItem("token");
		let user_ID = localStorage.getItem("userId");

		const apiUrl = `${URL}/feeddata/segments`;
		console.log("vfbdvbfgsdh", apiUrl);

		let response = await axios.post(apiUrl, body, {
			headers: {
				"Content-Type": `application/json`,
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		return error?.response?.statusText;
	}
};
export const updateSugmentdata = async (body) => {
	try {
		let AuthToken = localStorage.getItem("token");
		let user_ID = localStorage.getItem("userId");

		const apiUrl = `${URL}/feeddata/segments/${body?.id}`;
		console.log("vfbdvbfgsdh", apiUrl);

		let response = await axios.put(apiUrl, body, {
			headers: {
				"Content-Type": `application/json`,
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		return error?.response?.statusText;
	}
};
export const deleteSugmentData = async (id) => {
	console.log("id", id);
	let AuthToken = localStorage.getItem("token");

	const apiUrl = `${URL}/feeddata/segments/${id}`;

	let { data } = await axios.delete(apiUrl, {
		headers: {
			"Content-Type": `application/json`,
			Authorization: AuthToken,
			"ngrok-skip-browser-warning": "69420",
		},
	});

	return data;
};
export const getAllSubSugmentData = async (id) => {
	let AuthToken = localStorage.getItem("token");
	let user_ID = localStorage.getItem("userId");

	const apiUrl = `${URL}/feeddata/subsegments`;

	try {
		const response = await axios.get(apiUrl, {
			headers: {
				"Content-Type": "application/json",
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		console.error("Error fetching data:", error);
		throw error; // Rethrow the error to be handled higher up in the call stack
	}
};
export const createSubSugmentdata = async (body) => {
	try {
		let AuthToken = localStorage.getItem("token");
		let user_ID = localStorage.getItem("userId");

		const apiUrl = `${URL}/feeddata/subsegments`;
		console.log("vfbdvbfgsdh", apiUrl);

		let response = await axios.post(apiUrl, body, {
			headers: {
				"Content-Type": `application/json`,
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		return error?.response?.statusText;
	}
};
export const updateSubSugmentdata = async (body) => {
	try {
		let AuthToken = localStorage.getItem("token");
		let user_ID = localStorage.getItem("userId");

		const apiUrl = `${URL}/feeddata/subsegments/${body?.id}`;
		console.log("vfbdvbfgsdh", apiUrl);

		let response = await axios.put(apiUrl, body, {
			headers: {
				"Content-Type": `application/json`,
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		return error?.response?.statusText;
	}
};
export const deleteSubSugmentData = async (id) => {
	console.log("id", id);
	let AuthToken = localStorage.getItem("token");

	const apiUrl = `${URL}/feeddata/subsegments/${id}`;

	let { data } = await axios.delete(apiUrl, {
		headers: {
			"Content-Type": `application/json`,
			Authorization: AuthToken,
			"ngrok-skip-browser-warning": "69420",
		},
	});

	return data;
};
export const getAllQuestionData = async (id) => {
	let AuthToken = localStorage.getItem("token");
	let user_ID = localStorage.getItem("userId");

	const apiUrl = `${URL}/feeddata/questions`;

	try {
		const response = await axios.get(apiUrl, {
			headers: {
				"Content-Type": "application/json",
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		console.error("Error fetching data:", error);
		throw error; // Rethrow the error to be handled higher up in the call stack
	}
};
export const createQuestiondata = async (body) => {
	try {
		let AuthToken = localStorage.getItem("token");
		let user_ID = localStorage.getItem("userId");

		const apiUrl = `${URL}/feeddata/questions`;
		console.log("vfbdvbfgsdh", apiUrl);

		let response = await axios.post(apiUrl, body, {
			headers: {
				"Content-Type": `application/json`,
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		return error?.response?.statusText;
	}
};
export const updateQuestiondata = async (body) => {
	try {
		let AuthToken = localStorage.getItem("token");
		let user_ID = localStorage.getItem("userId");

		const apiUrl = `${URL}/feeddata/questions/${body?.id}`;
		console.log("vfbdvbfgsdh", apiUrl);

		let response = await axios.put(apiUrl, body, {
			headers: {
				"Content-Type": `application/json`,
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		return error?.response?.statusText;
	}
};
export const deleteQuestionData = async (id) => {
	console.log("id", id);
	let AuthToken = localStorage.getItem("token");

	const apiUrl = `${URL}/feeddata/questions/${id}`;

	let { data } = await axios.delete(apiUrl, {
		headers: {
			"Content-Type": `application/json`,
			Authorization: AuthToken,
			"ngrok-skip-browser-warning": "69420",
		},
	});

	return data;
};
export const getAllOptionData = async (id) => {
	let AuthToken = localStorage.getItem("token");
	let user_ID = localStorage.getItem("userId");

	const apiUrl = `${URL}/feeddata/options`;

	try {
		const response = await axios.get(apiUrl, {
			headers: {
				"Content-Type": "application/json",
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		console.error("Error fetching data:", error);
		throw error; // Rethrow the error to be handled higher up in the call stack
	}
};
export const createOptiondata = async (body) => {
	try {
		let AuthToken = localStorage.getItem("token");
		let user_ID = localStorage.getItem("userId");

		const apiUrl = `${URL}/feeddata/options`;
		console.log("vfbdvbfgsdh", apiUrl);

		let response = await axios.post(apiUrl, body, {
			headers: {
				"Content-Type": `application/json`,
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		return error?.response?.statusText;
	}
};
export const updateOptiondata = async (body) => {
	try {
		let AuthToken = localStorage.getItem("token");
		let user_ID = localStorage.getItem("userId");

		const apiUrl = `${URL}/feeddata/options/${body?.id}`;
		console.log("vfbdvbfgsdh", apiUrl);

		let response = await axios.put(apiUrl, body, {
			headers: {
				"Content-Type": `application/json`,
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		return error?.response?.statusText;
	}
};
export const deleteOptionData = async (id) => {
	console.log("id", id);
	let AuthToken = localStorage.getItem("token");

	const apiUrl = `${URL}/feeddata/options/${id}`;

	let { data } = await axios.delete(apiUrl, {
		headers: {
			"Content-Type": `application/json`,
			Authorization: AuthToken,
			"ngrok-skip-browser-warning": "69420",
		},
	});

	return data;
};
export const getAllrenderAssesment = async (id) => {
	let AuthToken = localStorage.getItem("token");
	let user_ID = localStorage.getItem("userId");

	const apiUrl = `${URL}/program/renderassessmentdata/wellness`;

	try {
		const response = await axios.get(apiUrl, {
			headers: {
				"Content-Type": "application/json",
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data.wellnessAssessment;
	} catch (error) {
		console.error("Error fetching data:", error);
		throw error; // Rethrow the error to be handled higher up in the call stack
	}
};
export const getAllraceReadnessdata = async (id) => {
	let AuthToken = localStorage.getItem("token");
	let user_ID = localStorage.getItem("userId");

	const apiUrl = `${URL}/feeddata/race-readiness`;

	try {
		const response = await axios.get(apiUrl, {
			headers: {
				"Content-Type": "application/json",
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		console.error("Error fetching data:", error);
		throw error; // Rethrow the error to be handled higher up in the call stack
	}
};
export const getAllSegmentReadnessData = async (id) => {
	let AuthToken = localStorage.getItem("token");
	let user_ID = localStorage.getItem("userId");

	const apiUrl = `${URL}/feeddata/segments-readiness`;

	try {
		const response = await axios.get(apiUrl, {
			headers: {
				"Content-Type": "application/json",
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		console.error("Error fetching data:", error);
		throw error; // Rethrow the error to be handled higher up in the call stack
	}
};
export const createSegmentReadnessdata = async (body) => {
	try {
		let AuthToken = localStorage.getItem("token");
		let user_ID = localStorage.getItem("userId");

		const apiUrl = `${URL}/feeddata/segments-readiness`;
		console.log("vfbdvbfgsdh", apiUrl);

		let response = await axios.post(apiUrl, body, {
			headers: {
				"Content-Type": `application/json`,
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		return error?.response?.statusText;
	}
};
export const updateSegmentReadnessdata = async (body) => {
	try {
		let AuthToken = localStorage.getItem("token");
		let user_ID = localStorage.getItem("userId");

		const apiUrl = `${URL}/feeddata/segments-readiness/${body?.id}`;
		console.log("vfbdvbfgsdh", apiUrl);

		let response = await axios.put(apiUrl, body, {
			headers: {
				"Content-Type": `application/json`,
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		return error?.response?.statusText;
	}
};
export const deleteSegmentReadnessData = async (id) => {
	console.log("id", id);
	let AuthToken = localStorage.getItem("token");

	const apiUrl = `${URL}/feeddata/segments-readiness/${id}`;

	let { data } = await axios.delete(apiUrl, {
		headers: {
			"Content-Type": `application/json`,
			Authorization: AuthToken,
			"ngrok-skip-browser-warning": "69420",
		},
	});

	return data;
};
export const getAllsubSegmentReadnessData = async (id) => {
	let AuthToken = localStorage.getItem("token");
	let user_ID = localStorage.getItem("userId");

	const apiUrl = `${URL}/feeddata/subsegments-readiness`;

	try {
		const response = await axios.get(apiUrl, {
			headers: {
				"Content-Type": "application/json",
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		console.error("Error fetching data:", error);
		throw error; // Rethrow the error to be handled higher up in the call stack
	}
};
export const createsuSegmentReadnessdata = async (body) => {
	try {
		let AuthToken = localStorage.getItem("token");
		let user_ID = localStorage.getItem("userId");

		const apiUrl = `${URL}/feeddata/subsegments-readiness`;
		console.log("vfbdvbfgsdh", apiUrl);

		let response = await axios.post(apiUrl, body, {
			headers: {
				"Content-Type": `application/json`,
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		return error?.response?.statusText;
	}
};
export const updatesuSegmentReadnessdata = async (body) => {
	try {
		let AuthToken = localStorage.getItem("token");
		let user_ID = localStorage.getItem("userId");

		const apiUrl = `${URL}/feeddata/subsegments-readiness/${body?.id}`;
		console.log("vfbdvbfgsdh", apiUrl);

		let response = await axios.put(apiUrl, body, {
			headers: {
				"Content-Type": `application/json`,
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		return error?.response?.statusText;
	}
};
export const deletesuSegmentReadnessData = async (id) => {
	console.log("id", id);
	let AuthToken = localStorage.getItem("token");

	const apiUrl = `${URL}/feeddata/subsegments-readiness/${id}`;

	let { data } = await axios.delete(apiUrl, {
		headers: {
			"Content-Type": `application/json`,
			Authorization: AuthToken,
			"ngrok-skip-browser-warning": "69420",
		},
	});

	return data;
};

export const getAllOptionReadnessData = async (id) => {
	let AuthToken = localStorage.getItem("token");
	let user_ID = localStorage.getItem("userId");

	const apiUrl = `${URL}/feeddata/options-readiness`;

	try {
		const response = await axios.get(apiUrl, {
			headers: {
				"Content-Type": "application/json",
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		console.error("Error fetching data:", error);
		throw error; // Rethrow the error to be handled higher up in the call stack
	}
};
export const createOptionReadnessdata = async (body) => {
	try {
		let AuthToken = localStorage.getItem("token");
		let user_ID = localStorage.getItem("userId");

		const apiUrl = `${URL}/feeddata/options-readiness`;
		console.log("vfbdvbfgsdh", apiUrl);

		let response = await axios.post(apiUrl, body, {
			headers: {
				"Content-Type": `application/json`,
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		return error?.response?.statusText;
	}
};
export const updateOptionReadnessdata = async (body) => {
	try {
		let AuthToken = localStorage.getItem("token");
		let user_ID = localStorage.getItem("userId");

		const apiUrl = `${URL}/feeddata/options-readiness/${body?.id}`;
		console.log("vfbdvbfgsdh", apiUrl);

		let response = await axios.put(apiUrl, body, {
			headers: {
				"Content-Type": `application/json`,
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		return error?.response?.statusText;
	}
};
export const deleteOptionReadnessData = async (id) => {
	console.log("id", id);
	let AuthToken = localStorage.getItem("token");

	const apiUrl = `${URL}/feeddata/options-readiness/${id}`;

	let { data } = await axios.delete(apiUrl, {
		headers: {
			"Content-Type": `application/json`,
			Authorization: AuthToken,
			"ngrok-skip-browser-warning": "69420",
		},
	});

	return data;
};
export const getAllScoreReadnessData = async (id) => {
	let AuthToken = localStorage.getItem("token");
	let user_ID = localStorage.getItem("userId");

	const apiUrl = `${URL}/feeddata/readiness-scores`;

	try {
		const response = await axios.get(apiUrl, {
			headers: {
				"Content-Type": "application/json",
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		console.error("Error fetching data:", error);
		throw error; // Rethrow the error to be handled higher up in the call stack
	}
};
export const createScoreReadnessdata = async (body) => {
	try {
		let AuthToken = localStorage.getItem("token");
		let user_ID = localStorage.getItem("userId");

		const apiUrl = `${URL}/feeddata/readiness-scores`;
		console.log("vfbdvbfgsdh", apiUrl);

		let response = await axios.post(apiUrl, body, {
			headers: {
				"Content-Type": `application/json`,
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		return error?.response?.statusText;
	}
};
export const updateScoreReadnessdata = async (body) => {
	try {
		let AuthToken = localStorage.getItem("token");
		let user_ID = localStorage.getItem("userId");

		const apiUrl = `${URL}/feeddata/readiness-scores/${body?.id}`;
		console.log("vfbdvbfgsdh", apiUrl);

		let response = await axios.put(apiUrl, body, {
			headers: {
				"Content-Type": `application/json`,
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		return error?.response?.statusText;
	}
};
export const deleteScoreReadnessData = async (id) => {
	console.log("id", id);
	let AuthToken = localStorage.getItem("token");

	const apiUrl = `${URL}/feeddata/readiness-scores/${id}`;

	let { data } = await axios.delete(apiUrl, {
		headers: {
			"Content-Type": `application/json`,
			Authorization: AuthToken,
			"ngrok-skip-browser-warning": "69420",
		},
	});

	return data;
};
export const getAllActivityGroupData = async (id) => {
	let AuthToken = localStorage.getItem("token");
	let user_ID = localStorage.getItem("userId");

	const apiUrl = `${URL}/feeddata/activitygroup`;

	try {
		const response = await axios.get(apiUrl, {
			headers: {
				"Content-Type": "application/json",
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		console.error("Error fetching data:", error);
		throw error; // Rethrow the error to be handled higher up in the call stack
	}
};
export const createActivityGroupdata = async (body) => {
	try {
		let AuthToken = localStorage.getItem("token");
		let user_ID = localStorage.getItem("userId");

		const apiUrl = `${URL}/feeddata/activitygroup`;
		console.log("vfbdvbfgsdh", apiUrl);

		let response = await axios.post(apiUrl, body, {
			headers: {
				"Content-Type": `application/json`,
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		return error?.response?.statusText;
	}
};
export const updateActivityGroupdata = async (body) => {
	try {
		let AuthToken = localStorage.getItem("token");
		let user_ID = localStorage.getItem("userId");

		const apiUrl = `${URL}/feeddata/activitygroup/${body?.id}`;
		console.log("vfbdvbfgsdh", apiUrl);

		let response = await axios.put(apiUrl, body, {
			headers: {
				"Content-Type": `application/json`,
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		return error?.response?.statusText;
	}
};
export const deleteActivityGroupData = async (id) => {
	console.log("id", id);
	let AuthToken = localStorage.getItem("token");

	const apiUrl = `${URL}/feeddata/activitygroup/${id}`;

	let { data } = await axios.delete(apiUrl, {
		headers: {
			"Content-Type": `application/json`,
			Authorization: AuthToken,
			"ngrok-skip-browser-warning": "69420",
		},
	});

	return data;
};
export const getChallengeData = async (id) => {
	let AuthToken = localStorage.getItem("token");
	let user_ID = localStorage.getItem("userId");

	const apiUrl = `${URL}/feeddata/gamelevel`;

	try {
		const response = await axios.get(apiUrl, {
			headers: {
				"Content-Type": "application/json",
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		console.error("Error fetching data:", error);
		throw error; // Rethrow the error to be handled higher up in the call stack
	}
};
export const createChallengedata = async (body) => {
	try {
		let AuthToken = localStorage.getItem("token");
		let user_ID = localStorage.getItem("userId");

		const apiUrl = `${URL}/feeddata/gamelevel`;
		console.log("vfbdvbfgsdh", apiUrl);

		let response = await axios.post(apiUrl, body, {
			headers: {
				"Content-Type": `application/json`,
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		return error?.response?.statusText;
	}
};
export const updateChallengedata = async (body) => {
	try {
		let AuthToken = localStorage.getItem("token");
		let user_ID = localStorage.getItem("userId");

		const apiUrl = `${URL}/feeddata/gamelevel/${body?.id}`;
		console.log("vfbdvbfgsdh", apiUrl);

		let response = await axios.put(apiUrl, body, {
			headers: {
				"Content-Type": `application/json`,
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		return error?.response?.statusText;
	}
};
export const deleteChallengeData = async (id) => {
	console.log("id", id);
	let AuthToken = localStorage.getItem("token");

	const apiUrl = `${URL}/feeddata/gamelevel/${id}`;

	let { data } = await axios.delete(apiUrl, {
		headers: {
			"Content-Type": `application/json`,
			Authorization: AuthToken,
			"ngrok-skip-browser-warning": "69420",
		},
	});

	return data;
};
export const getGoalWithoutVolumeData = async (id) => {
	let AuthToken = localStorage.getItem("token");
	let user_ID = localStorage.getItem("userId");

	const apiUrl = `${URL}/feeddata/GoalsWithoutVolume`;

	try {
		const response = await axios.get(apiUrl, {
			headers: {
				"Content-Type": "application/json",
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		console.error("Error fetching data:", error);
		throw error; // Rethrow the error to be handled higher up in the call stack
	}
};
export const createGoalWithoutVolumedata = async (body) => {
	try {
		let AuthToken = localStorage.getItem("token");
		let user_ID = localStorage.getItem("userId");

		const apiUrl = `${URL}/feeddata/GoalsWithoutVolume`;
		console.log("vfbdvbfgsdh", apiUrl);

		let response = await axios.post(apiUrl, body, {
			headers: {
				"Content-Type": `application/json`,
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		return error?.response?.statusText;
	}
};
export const updateGoalWithoutVolumedata = async (body) => {
	try {
		let AuthToken = localStorage.getItem("token");
		let user_ID = localStorage.getItem("userId");

		const apiUrl = `${URL}/feeddata/GoalsWithoutVolume/${body?.id}`;
		console.log("vfbdvbfgsdh", apiUrl);

		let response = await axios.put(apiUrl, body, {
			headers: {
				"Content-Type": `application/json`,
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		return error?.response?.statusText;
	}
};
export const deleteGoalWithoutVolumeData = async (id) => {
	console.log("id", id);
	let AuthToken = localStorage.getItem("token");

	const apiUrl = `${URL}/feeddata/GoalsWithoutVolume/${id}`;

	let { data } = await axios.delete(apiUrl, {
		headers: {
			"Content-Type": `application/json`,
			Authorization: AuthToken,
			"ngrok-skip-browser-warning": "69420",
		},
	});

	return data;
};
export const getPowerData = async (id) => {
	let AuthToken = localStorage.getItem("token");
	let user_ID = localStorage.getItem("userId");

	const apiUrl = `${URL}/feeddata/zonespower`;

	try {
		const response = await axios.get(apiUrl, {
			headers: {
				"Content-Type": "application/json",
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		console.error("Error fetching data:", error);
		throw error; // Rethrow the error to be handled higher up in the call stack
	}
};
export const getZonesDataById = async (id) => {
	let AuthToken = localStorage.getItem("token");
	let user_ID = localStorage.getItem("userId");

	const apiUrl = `${URL}/feeddata/getallzonesnamesbyactivityid/${id}`;

	try {
		const response = await axios.get(apiUrl, {
			headers: {
				"Content-Type": "application/json",
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		console.error("Error fetching data:", error);
		throw error; // Rethrow the error to be handled higher up in the call stack
	}
};
export const createPowerdata = async (body) => {
	try {
		let AuthToken = localStorage.getItem("token");
		let user_ID = localStorage.getItem("userId");

		const apiUrl = `${URL}/feeddata/zonespower`;
		console.log("vfbdvbfgsdh", apiUrl);

		let response = await axios.post(apiUrl, body, {
			headers: {
				"Content-Type": `application/json`,
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		return error?.response?.statusText;
	}
};
export const updatePowerdata = async (body) => {
	try {
		let AuthToken = localStorage.getItem("token");
		let user_ID = localStorage.getItem("userId");

		const apiUrl = `${URL}/feeddata/zonespower/${body?.id}`;
		console.log("vfbdvbfgsdh", apiUrl);

		let response = await axios.put(apiUrl, body, {
			headers: {
				"Content-Type": `application/json`,
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		return error?.response?.statusText;
	}
};
export const deletePowerData = async (id) => {
	console.log("id", id);
	let AuthToken = localStorage.getItem("token");

	const apiUrl = `${URL}/feeddata/zonespower/${id}`;

	let { data } = await axios.delete(apiUrl, {
		headers: {
			"Content-Type": `application/json`,
			Authorization: AuthToken,
			"ngrok-skip-browser-warning": "69420",
		},
	});

	return data;
};
export const getZoneHeartData = async (id) => {
	let AuthToken = localStorage.getItem("token");
	let user_ID = localStorage.getItem("userId");

	const apiUrl = `${URL}/feeddata/zonesheartrate`;

	try {
		const response = await axios.get(apiUrl, {
			headers: {
				"Content-Type": "application/json",
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		console.error("Error fetching data:", error);
		throw error; // Rethrow the error to be handled higher up in the call stack
	}
};
export const createZoneHeartdata = async (body) => {
	try {
		let AuthToken = localStorage.getItem("token");
		let user_ID = localStorage.getItem("userId");

		const apiUrl = `${URL}/feeddata/zonesheartrate`;
		console.log("vfbdvbfgsdh", apiUrl);

		let response = await axios.post(apiUrl, body, {
			headers: {
				"Content-Type": `application/json`,
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		return error?.response?.statusText;
	}
};
export const updateZoneHeartdata = async (body) => {
	try {
		let AuthToken = localStorage.getItem("token");
		let user_ID = localStorage.getItem("userId");

		const apiUrl = `${URL}/feeddata/zonesheartrate/${body?.id}`;
		console.log("vfbdvbfgsdh", apiUrl);

		let response = await axios.put(apiUrl, body, {
			headers: {
				"Content-Type": `application/json`,
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		return error?.response?.statusText;
	}
};
export const deleteZoneHeartData = async (id) => {
	console.log("id", id);
	let AuthToken = localStorage.getItem("token");

	const apiUrl = `${URL}/feeddata/zonesheartrate/${id}`;

	let { data } = await axios.delete(apiUrl, {
		headers: {
			"Content-Type": `application/json`,
			Authorization: AuthToken,
			"ngrok-skip-browser-warning": "69420",
		},
	});

	return data;
};
export const uploadsaveFile = async (body) => {
	try {
		let AuthToken = localStorage.getItem("token");
		let user_ID = localStorage.getItem("userId");

		const apiUrl = `${URL}/feeddata/savefile`;
		console.log("vfbdvbfgsdh", apiUrl);

		let response = await axios.post(apiUrl, body, {
			headers: {
				// "Content-Type": `application/json`,
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		return error?.response?.statusText;
	}
};
export const getImageURLData = async (id) => {
	let AuthToken = localStorage.getItem("token");
	let user_ID = localStorage.getItem("userId");

	const apiUrl = `${URL}/feeddata/formimages`;

	try {
		const response = await axios.get(apiUrl, {
			headers: {
				"Content-Type": "application/json",
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		console.error("Error fetching data:", error);
		throw error; // Rethrow the error to be handled higher up in the call stack
	}
};
export const createImageURLdata = async (body) => {
	try {
		let AuthToken = localStorage.getItem("token");
		let user_ID = localStorage.getItem("userId");

		const apiUrl = `${URL}/feeddata/formimages`;
		console.log("vfbdvbfgsdh", apiUrl);

		let response = await axios.post(apiUrl, body, {
			headers: {
				"Content-Type": `application/json`,
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		return error?.response?.statusText;
	}
};
export const updateImageURLdata = async (body) => {
	try {
		let AuthToken = localStorage.getItem("token");
		let user_ID = localStorage.getItem("userId");

		const apiUrl = `${URL}/feeddata/formimages/${body?.id}`;
		console.log("vfbdvbfgsdh", apiUrl);

		let response = await axios.put(apiUrl, body, {
			headers: {
				"Content-Type": `application/json`,
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		return error?.response?.statusText;
	}
};
export const deleteImageURLData = async (id) => {
	console.log("id", id);
	let AuthToken = localStorage.getItem("token");

	const apiUrl = `${URL}/feeddata/formimages/${id}`;

	let { data } = await axios.delete(apiUrl, {
		headers: {
			"Content-Type": `application/json`,
			Authorization: AuthToken,
			"ngrok-skip-browser-warning": "69420",
		},
	});

	return data;
};
export const getActivityTrackData = async (id) => {
	let AuthToken = localStorage.getItem("token");
	let user_ID = localStorage.getItem("userId");

	const apiUrl = `${URL}/feeddata/activitytrack`;

	try {
		const response = await axios.get(apiUrl, {
			headers: {
				"Content-Type": "application/json",
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		console.error("Error fetching data:", error);
		throw error; // Rethrow the error to be handled higher up in the call stack
	}
};
export const createActivityTrackdata = async (body) => {
	try {
		let AuthToken = localStorage.getItem("token");
		let user_ID = localStorage.getItem("userId");

		const apiUrl = `${URL}/feeddata/activitytrack`;
		console.log("vfbdvbfgsdh", apiUrl);

		let response = await axios.post(apiUrl, body, {
			headers: {
				"Content-Type": `application/json`,
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		return error?.response?.statusText;
	}
};
export const updateActivityTrackdata = async (body) => {
	try {
		let AuthToken = localStorage.getItem("token");
		let user_ID = localStorage.getItem("userId");

		const apiUrl = `${URL}/feeddata/activitytrack/${body?.id}`;
		console.log("vfbdvbfgsdh", apiUrl);

		let response = await axios.put(apiUrl, body, {
			headers: {
				"Content-Type": `application/json`,
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		return error?.response?.statusText;
	}
};
export const deleteActivityTrackData = async (id) => {
	console.log("id", id);
	let AuthToken = localStorage.getItem("token");

	const apiUrl = `${URL}/feeddata/activitytrack/${id}`;

	let { data } = await axios.delete(apiUrl, {
		headers: {
			"Content-Type": `application/json`,
			Authorization: AuthToken,
			"ngrok-skip-browser-warning": "69420",
		},
	});

	return data;
};
export const getPromotorsData = async (id) => {
	let AuthToken = localStorage.getItem("token");
	let user_ID = localStorage.getItem("userId");

	const apiUrl = `${URL}/feeddata/promotors`;

	try {
		const response = await axios.get(apiUrl, {
			headers: {
				"Content-Type": "application/json",
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		console.error("Error fetching data:", error);
		throw error; // Rethrow the error to be handled higher up in the call stack
	}
};
export const createPromotorsdata = async (body) => {
	try {
		let AuthToken = localStorage.getItem("token");
		let user_ID = localStorage.getItem("userId");

		const apiUrl = `${URL}/feeddata/promotors`;
		console.log("vfbdvbfgsdh", apiUrl);

		let response = await axios.post(apiUrl, body, {
			headers: {
				"Content-Type": `application/json`,
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		return error?.response?.statusText;
	}
};
export const updatePromotorsdata = async (body) => {
	try {
		let AuthToken = localStorage.getItem("token");
		let user_ID = localStorage.getItem("userId");

		const apiUrl = `${URL}/feeddata/promotors/${body?.id}`;
		console.log("vfbdvbfgsdh", apiUrl);

		let response = await axios.put(apiUrl, body, {
			headers: {
				"Content-Type": `application/json`,
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		return error?.response?.statusText;
	}
};
export const deletePromotorsData = async (id) => {
	console.log("id", id);
	let AuthToken = localStorage.getItem("token");

	const apiUrl = `${URL}/feeddata/promotors/${id}`;

	let { data } = await axios.delete(apiUrl, {
		headers: {
			"Content-Type": `application/json`,
			Authorization: AuthToken,
			"ngrok-skip-browser-warning": "69420",
		},
	});

	return data;
};

export const getPaymentCredData = async (id) => {
	let AuthToken = localStorage.getItem("token");
	let user_ID = localStorage.getItem("userId");
	let AuthUserToken = localStorage.getItem("userTokentoken");

	const apiUrl = `${URL}/feeddata/paymentcreds`;

	try {
		const response = await axios.get(apiUrl, {
			headers: {
				"Content-Type": "application/json",
				Authorization: AuthUserToken ? AuthUserToken : AuthToken,

				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		console.error("Error fetching data:", error);
		throw error; // Rethrow the error to be handled higher up in the call stack
	}
};

export const getPaymentCredDataForPayment = async () => {
	let AuthToken = localStorage.getItem("token");
	let AuthUserToken = localStorage.getItem("userTokentoken");

	const apiUrl = `${URL}/payment-razorpay`;

	try {
		const response = await axios.get(apiUrl, {
			headers: {
				"Content-Type": "application/json",
				Authorization: AuthUserToken ? AuthUserToken : AuthToken,

				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		console.error("Error fetching data:", error);
		throw error; // Rethrow the error to be handled higher up in the call stack
	}
};
export const createPaymentCreddata = async (body) => {
	try {
		let AuthToken = localStorage.getItem("token");
		let user_ID = localStorage.getItem("userId");

		const apiUrl = `${URL}/feeddata/paymentcreds`;
		console.log("vfbdvbfgsdh", apiUrl);

		let response = await axios.post(apiUrl, body, {
			headers: {
				"Content-Type": `application/json`,
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		return error?.response?.statusText;
	}
};
export const updatePaymentCreddata = async (body) => {
	try {
		let AuthToken = localStorage.getItem("token");
		let user_ID = localStorage.getItem("userId");

		const apiUrl = `${URL}/feeddata/paymentcreds/${body?.id}`;
		console.log("vfbdvbfgsdh", apiUrl);

		let response = await axios.put(apiUrl, body, {
			headers: {
				"Content-Type": `application/json`,
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		return error?.response?.statusText;
	}
};
export const deletePaymentCredData = async (id) => {
	console.log("id", id);
	let AuthToken = localStorage.getItem("token");

	const apiUrl = `${URL}/feeddata/paymentcreds/${id}`;

	let { data } = await axios.delete(apiUrl, {
		headers: {
			"Content-Type": `application/json`,
			Authorization: AuthToken,
			"ngrok-skip-browser-warning": "69420",
		},
	});

	return data;
};

export const getdiscountcouponData = async (id) => {
	let AuthToken = localStorage.getItem("token");
	let user_ID = localStorage.getItem("userId");
	let AuthUserToken = localStorage.getItem("userTokentoken");

	const apiUrl = `${URL}/feeddata/discountcoupon`;

	try {
		const response = await axios.get(apiUrl, {
			headers: {
				"Content-Type": "application/json",
				Authorization: AuthUserToken ? AuthUserToken : AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		console.error("Error fetching data:", error);
		throw error; // Rethrow the error to be handled higher up in the call stack
	}
};
export const creatediscountcoupondata = async (body) => {
	try {
		let AuthToken = localStorage.getItem("token");
		let user_ID = localStorage.getItem("userId");

		const apiUrl = `${URL}/feeddata/discountcoupon`;
		console.log("vfbdvbfgsdh", apiUrl);

		let response = await axios.post(apiUrl, body, {
			headers: {
				"Content-Type": `application/json`,
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		return error?.response?.statusText;
	}
};
export const updatediscountcoupondata = async (body) => {
	try {
		let AuthToken = localStorage.getItem("token");
		let user_ID = localStorage.getItem("userId");

		const apiUrl = `${URL}/feeddata/discountcoupon/${body?.id}`;
		console.log("vfbdvbfgsdh", apiUrl);

		let response = await axios.put(apiUrl, body, {
			headers: {
				"Content-Type": `application/json`,
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		return error?.response?.statusText;
	}
};
export const deletediscountcouponData = async (id) => {
	console.log("id", id);
	let AuthToken = localStorage.getItem("token");

	const apiUrl = `${URL}/feeddata/discountcoupon/${id}`;

	let { data } = await axios.delete(apiUrl, {
		headers: {
			"Content-Type": `application/json`,
			Authorization: AuthToken,
			"ngrok-skip-browser-warning": "69420",
		},
	});

	return data;
};

export const getsubspackageData = async (id) => {
	let AuthToken = localStorage.getItem("token");
	let user_ID = localStorage.getItem("userId");

	const apiUrl = `${URL}/feeddata/subspackage`;

	try {
		const response = await axios.get(apiUrl, {
			headers: {
				"Content-Type": "application/json",
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		console.error("Error fetching data:", error);
		throw error; // Rethrow the error to be handled higher up in the call stack
	}
};
export const createsubspackagedata = async (body) => {
	try {
		let AuthToken = localStorage.getItem("token");
		let user_ID = localStorage.getItem("userId");

		const apiUrl = `${URL}/feeddata/subspackage`;
		console.log("vfbdvbfgsdh", apiUrl);

		let response = await axios.post(apiUrl, body, {
			headers: {
				"Content-Type": `application/json`,
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		return error?.response?.statusText;
	}
};
export const updatesubspackagedata = async (body) => {
	try {
		let AuthToken = localStorage.getItem("token");
		let user_ID = localStorage.getItem("userId");

		const apiUrl = `${URL}/feeddata/subspackage/${body?.id}`;
		console.log("vfbdvbfgsdh", apiUrl);

		let response = await axios.put(apiUrl, body, {
			headers: {
				"Content-Type": `application/json`,
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		return error?.response?.statusText;
	}
};
export const deletesubspackageData = async (id) => {
	console.log("id", id);
	let AuthToken = localStorage.getItem("token");

	const apiUrl = `${URL}/feeddata/subspackage/${id}`;

	let { data } = await axios.delete(apiUrl, {
		headers: {
			"Content-Type": `application/json`,
			Authorization: AuthToken,
			"ngrok-skip-browser-warning": "69420",
		},
	});

	return data;
};

export const getuomsData = async (id) => {
	let AuthToken = localStorage.getItem("token");
	let user_ID = localStorage.getItem("userId");

	const apiUrl = `${URL}/feeddata/uoms`;

	try {
		const response = await axios.get(apiUrl, {
			headers: {
				"Content-Type": "application/json",
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		console.error("Error fetching data:", error);
		throw error; // Rethrow the error to be handled higher up in the call stack
	}
};
export const createuomsdata = async (body) => {
	try {
		let AuthToken = localStorage.getItem("token");
		let user_ID = localStorage.getItem("userId");

		const apiUrl = `${URL}/feeddata/uoms`;
		console.log("vfbdvbfgsdh", apiUrl);

		let response = await axios.post(apiUrl, body, {
			headers: {
				"Content-Type": `application/json`,
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		return error?.response?.statusText;
	}
};
export const updateuomsdata = async (body) => {
	try {
		let AuthToken = localStorage.getItem("token");
		let user_ID = localStorage.getItem("userId");

		const apiUrl = `${URL}/feeddata/uoms/${body?.uom_id}`;
		console.log("vfbdvbfgsdh", apiUrl);

		let response = await axios.put(apiUrl, body, {
			headers: {
				"Content-Type": `application/json`,
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		return error?.response?.statusText;
	}
};
export const deleteuomsData = async (id) => {
	console.log("id", id);
	let AuthToken = localStorage.getItem("token");

	const apiUrl = `${URL}/feeddata/uoms/${id}`;

	let { data } = await axios.delete(apiUrl, {
		headers: {
			"Content-Type": `application/json`,
			Authorization: AuthToken,
			"ngrok-skip-browser-warning": "69420",
		},
	});

	return data;
};
export const getTagCloudData = async (id) => {
	let AuthToken = localStorage.getItem("token");
	let user_ID = localStorage.getItem("userId");

	const apiUrl = `${URL}/feeddata/tag-clouds`;

	try {
		const response = await axios.get(apiUrl, {
			headers: {
				"Content-Type": "application/json",
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		console.error("Error fetching data:", error);
		throw error; // Rethrow the error to be handled higher up in the call stack
	}
};
export const creatTagClouddata = async (body) => {
	try {
		let AuthToken = localStorage.getItem("token");
		let user_ID = localStorage.getItem("userId");

		const apiUrl = `${URL}/feeddata/tag-clouds`;
		console.log("vfbdvbfgsdh", apiUrl);

		let response = await axios.post(apiUrl, body, {
			headers: {
				"Content-Type": `application/json`,
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		return error?.response?.statusText;
	}
};
export const updateTagClouddata = async (body) => {
	try {
		let AuthToken = localStorage.getItem("token");
		let user_ID = localStorage.getItem("userId");

		const apiUrl = `${URL}/feeddata/tag-clouds/${body["tag-cloud-id"]}`;
		console.log("vfbdvbfgsdh", apiUrl);

		let response = await axios.put(apiUrl, body, {
			headers: {
				"Content-Type": `application/json`,
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		return error?.response?.statusText;
	}
};
export const deleteTagCloudData = async (id) => {
	console.log("id", id);
	let AuthToken = localStorage.getItem("token");

	const apiUrl = `${URL}/feeddata/tag-clouds/${id}`;

	let { data } = await axios.delete(apiUrl, {
		headers: {
			"Content-Type": `application/json`,
			Authorization: AuthToken,
			"ngrok-skip-browser-warning": "69420",
		},
	});

	return data;
};

export const getSystemConfigrationData = async (id) => {
	let AuthToken = localStorage.getItem("token");
	let user_ID = localStorage.getItem("userId");
	let AuthUserToken = localStorage.getItem("userTokentoken");

	const apiUrl = `${URL}/feeddata/system-configs`;

	try {
		const response = await axios.get(apiUrl, {
			headers: {
				"Content-Type": "application/json",
				Authorization: AuthUserToken ? AuthUserToken : AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		console.error("Error fetching data:", error);
		throw error; // Rethrow the error to be handled higher up in the call stack
	}
};
export const createSystemConfigrationdata = async (body) => {
	try {
		let AuthToken = localStorage.getItem("token");
		let user_ID = localStorage.getItem("userId");

		const apiUrl = `${URL}/feeddata/system-configs`;
		console.log("vfbdvbfgsdh", apiUrl);

		let response = await axios.post(apiUrl, body, {
			headers: {
				"Content-Type": `application/json`,
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		return error?.response?.statusText;
	}
};
export const updateSystemConfigrationdata = async (body) => {
	try {
		let AuthToken = localStorage.getItem("token");
		let user_ID = localStorage.getItem("userId");

		const apiUrl = `${URL}/feeddata/system-configs/${body?.id}`;
		console.log("vfbdvbfgsdh", apiUrl);

		let response = await axios.put(apiUrl, body, {
			headers: {
				"Content-Type": `application/json`,
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		return error?.response?.statusText;
	}
};
export const deleteSystemConfigrationData = async (id) => {
	console.log("id", id);
	let AuthToken = localStorage.getItem("token");

	const apiUrl = `${URL}/feeddata/system-configs/${id}`;

	let { data } = await axios.delete(apiUrl, {
		headers: {
			"Content-Type": `application/json`,
			Authorization: AuthToken,
			"ngrok-skip-browser-warning": "69420",
		},
	});

	return data;
};

export const getActivityMetricData = async (id) => {
	let AuthToken = localStorage.getItem("token");
	let user_ID = localStorage.getItem("userId");

	const apiUrl = `${URL}/feeddata/activity-metrics`;

	try {
		const response = await axios.get(apiUrl, {
			headers: {
				"Content-Type": "application/json",
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		console.error("Error fetching data:", error);
		throw error; // Rethrow the error to be handled higher up in the call stack
	}
};
export const createActivityMetricdata = async (body) => {
	try {
		let AuthToken = localStorage.getItem("token");
		let user_ID = localStorage.getItem("userId");

		const apiUrl = `${URL}/feeddata/activity-metrics`;
		console.log("vfbdvbfgsdh", apiUrl);

		let response = await axios.post(apiUrl, body, {
			headers: {
				"Content-Type": `application/json`,
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		return error?.response?.statusText;
	}
};
export const updateActivityMetricdata = async (body) => {
	try {
		let AuthToken = localStorage.getItem("token");
		let user_ID = localStorage.getItem("userId");

		const apiUrl = `${URL}/feeddata/activity-metrics/${body?.id}`;
		console.log("vfbdvbfgsdh", apiUrl);

		let response = await axios.put(apiUrl, body, {
			headers: {
				"Content-Type": `application/json`,
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		return error?.response?.statusText;
	}
};
export const deleteActivityMetricData = async (id) => {
	console.log("id", id);
	let AuthToken = localStorage.getItem("token");

	const apiUrl = `${URL}/feeddata/activity-metrics/${id}`;

	let { data } = await axios.delete(apiUrl, {
		headers: {
			"Content-Type": `application/json`,
			Authorization: AuthToken,
			"ngrok-skip-browser-warning": "69420",
		},
	});

	return data;
};
export const CreateGroupRegistrationData = async (body) => {
	try {
		let AuthToken = localStorage.getItem("token");
		let user_ID = localStorage.getItem("userId");

		const apiUrl = `${URL}/profile/createusergroup`;
		console.log("vfbdvbfgsdh", apiUrl);

		let response = await axios.post(apiUrl, body, {
			headers: {
				// "Content-Type": `application/json`,
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		return error?.response?.statusText;
	}
};

export const DisconnectStrava = async (assignedCocahId) => {
	let AuthToken = localStorage.getItem("token").split(" ")[1];
	console.log("AuthToken", AuthToken);
	let token = `auth ${AuthToken}`;
	console.log("token", token, AuthToken);
	let user_ID = localStorage.getItem("userId");
	let body = {
		user_ID: assignedCocahId ? assignedCocahId : user_ID,
	};
	const apiUrl = `${URL}/strava/disconnect`;

	try {
		const response = await axios.post(apiUrl, body, {
			headers: {
				"Content-Type": "application/json",
				Authorization: token,
				"ngrok-skip-browser-warning": "69420",
			},
		});

		return response.data;
	} catch (error) {
		console.error("Error fetching data:", error);
		throw error; // Rethrow the error to be handled higher up in the call stack
	}
};

export const getCadenceData = async (id, assignedCocahId) => {
	let AuthToken = localStorage.getItem("token");
	let user_ID = localStorage.getItem("userId");
	const apiUrl = `${URL}/streams`;
	let body = {
		activityId: id,
		user_id: assignedCocahId ? assignedCocahId : user_ID,
	};
	try {
		const response = await axios.post(apiUrl, body, {
			headers: {
				"Content-Type": "application/json",
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		console.error("Error fetching data:", error);
		throw error; // Rethrow the error to be handled higher up in the call stack
	}
};

export const getHeartZoneData = async (id) => {
	let AuthToken = localStorage.getItem("token");
	let user_ID = localStorage.getItem("userId");

	const apiUrl = `${URL}/program/userzonesdata/${user_ID}`;

	try {
		const response = await axios.get(apiUrl, {
			headers: {
				"Content-Type": "application/json",
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data.subactivitymetric;
	} catch (error) {
		console.error("Error fetching data:", error);
		throw error; // Rethrow the error to be handled higher up in the call stack
	}
};

export const registerCoach = async (body) => {
	let AuthToken = localStorage.getItem("token");
	const apiUrl = `${URL}/program/register
  `;

	try {
		const response = await axios.post(apiUrl, body, {
			headers: {
				"Content-Type": "application/json",
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});

		return response.data;
	} catch (error) {
		console.error("Error fetching data:", error);
		throw error; // Rethrow the error to be handled higher up in the call stack
	}
};

export const updatedusers = async () => {
	let AuthToken = localStorage.getItem("token");
	let userid = localStorage.getItem("userId");
	const apiUrl = `${URL}/profile/getonlycoachesforatheletesv2`;
	let result = await axios.post(
		apiUrl,
		{
			user_id: userid,
		},
		{
			headers: {
				"Content-Type": `application/json`,
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		}
	);
	console.log("oooodata", result.data?.updatedusers);
	return result.data?.updatedusers;

	//   const uniqueEmails = {};
	//   const filteredBody = {};
	//   const removedBody = {};

	//   for (const [key, value] of Object.entries(body)) {
	//     if (value.email) {
	//       const email = value.email.toLowerCase();
	//       if (!uniqueEmails[email] ) {
	//         uniqueEmails[email] = true;
	//         filteredBody[value.uid] = value;
	//       } else {
	//         removedBody[key] = value;
	//       }
	//     }
	//   }
	//  console.log("666",filteredBody);
	//   let AuthToken = localStorage.getItem("token");
	//   let userid = localStorage.getItem("userId")
	//   const apiUrl = `${URL}/profile/getonlycoachesforatheletes`;
	//   let { data } = await axios.post(apiUrl, {

	//     users: filteredBody,
	//     user_id: userid,
	//   }, {
	//     headers: {
	//       "Content-Type": `application/json`,
	//       Authorization: AuthToken,
	//       "ngrok-skip-browser-warning": "69420",
	//     },
	//   });
	//   console.log("oooo",Object.values(data))
	//   return Object.values(data);
};

export const getzonesupdation = async (userId, prgramId) => {
	let AuthToken = localStorage.getItem("token");

	const apiUrl = `${URL}/program/checkifzonesupdationscreenshouldbeshown/${userId}/${prgramId}`;

	try {
		const response = await axios.get(apiUrl, {
			headers: {
				"Content-Type": "application/json",
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});

		return response.data;
	} catch (error) {
		console.error("Error fetching data:", error);
		throw error; // Rethrow the error to be handled higher up in the call stack
	}
};
export const getCoachProfile = async (assignedCocahId) => {
	let AuthToken = localStorage.getItem("token");
	let userid = localStorage.getItem("userId");

	const apiUrl = `${URL}/profile/getcoachprofile/${
		assignedCocahId ? assignedCocahId : userid
	}`;

	try {
		const response = await axios.get(apiUrl, {
			headers: {
				"Content-Type": "application/json",
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});

		return response.data;
	} catch (error) {
		console.error("Error fetching data:", error);
		throw error; // Rethrow the error to be handled higher up in the call stack
	}
};
export const getcoachGrantAccess = async (assignedCocahId) => {
	let AuthToken = localStorage.getItem("token");
	let userid = localStorage.getItem("userId");

	const apiUrl = `${URL}/profile/getadmincoachprivilige/${userid}`;

	try {
		const response = await axios.get(apiUrl, {
			headers: {
				"Content-Type": "application/json",
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});

		return response.data;
	} catch (error) {
		console.error("Error fetching data:", error);
		throw error; // Rethrow the error to be handled higher up in the call stack
	}
};
export const getUpdateAutomation = async (userid, programID, weekno) => {
	let AuthToken = localStorage.getItem("token");

	const apiUrl = `${URL}/program/checkautomationcapturestatusweekbyweek/${userid}/${programID}/${weekno}`;

	try {
		const response = await axios.get(apiUrl, {
			headers: {
				"Content-Type": "application/json",
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});

		return response.data;
	} catch (error) {
		console.error("Error fetching data:", error);
		throw error; // Rethrow the error to be handled higher up in the call stack
	}
};
export const saveCoachProfile = async (programID) => {
	try {
		let AuthToken = localStorage.getItem("token");
		let user_ID = localStorage.getItem("userId");
		const apiUrl = `${URL}/profile/setcoachprofile/${user_ID}/${programID}`;

		let response = await axios.get(apiUrl, {
			headers: {
				"Content-Type": `application/json`,
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		return error?.response?.statusText;
	}
};
export const getPartculatWorkouts = async (id) => {
	let AuthToken = localStorage.getItem("token");

	const apiUrl = `${URL}/workoutmaster/getWorkoutMastersbyid/${id}`;

	try {
		const response = await axios.get(apiUrl, {
			headers: {
				"Content-Type": "application/json",
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		console.error("Error fetching data:", error);
		throw error; // Rethrow the error to be handled higher up in the call stack
	}
};

export const getAllProgramFeature = async (id) => {
	let AuthToken = localStorage.getItem("token");
	let user_ID = localStorage.getItem("userId");

	const apiUrl = `${URL}/feeddata/ProgramFeature`;

	try {
		const response = await axios.get(apiUrl, {
			headers: {
				"Content-Type": "application/json",
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		console.error("Error fetching data:", error);
		throw error; // Rethrow the error to be handled higher up in the call stack
	}
};
export const createprogramFeature = async (body) => {
	try {
		let AuthToken = localStorage.getItem("token");
		let user_ID = localStorage.getItem("userId");

		const apiUrl = `${URL}/feeddata/ProgramFeature`;
		console.log("vfbdvbfgsdh", apiUrl);

		let response = await axios.post(apiUrl, body, {
			headers: {
				"Content-Type": `application/json`,
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		return error?.response?.statusText;
	}
};
export const updateProgramFeature = async (body) => {
	try {
		let AuthToken = localStorage.getItem("token");
		let user_ID = localStorage.getItem("userId");

		const apiUrl = `${URL}/feeddata/ProgramFeature/${body?.id}`;
		console.log("vfbdvbfgsdh", apiUrl);

		let response = await axios.put(apiUrl, body, {
			headers: {
				"Content-Type": `application/json`,
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		return error?.response?.statusText;
	}
};
export const deleteProgramFeature = async (id) => {
	console.log("id", id);
	let AuthToken = localStorage.getItem("token");

	const apiUrl = `${URL}/feeddata/ProgramFeature/${id}`;

	let { data } = await axios.delete(apiUrl, {
		headers: {
			"Content-Type": `application/json`,
			Authorization: AuthToken,
			"ngrok-skip-browser-warning": "69420",
		},
	});

	return data;
};
export const getAllProgramPoints = async (id) => {
	let AuthToken = localStorage.getItem("token");
	let user_ID = localStorage.getItem("userId");

	const apiUrl = `${URL}/feeddata/OverallPoints`;

	try {
		const response = await axios.get(apiUrl, {
			headers: {
				"Content-Type": "application/json",
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		console.error("Error fetching data:", error);
		throw error; // Rethrow the error to be handled higher up in the call stack
	}
};
export const createprogramOverallPoints = async (body) => {
	try {
		let AuthToken = localStorage.getItem("token");
		let user_ID = localStorage.getItem("userId");

		const apiUrl = `${URL}/feeddata/OverallPoints`;
		console.log("vfbdvbfgsdh", apiUrl);

		let response = await axios.post(apiUrl, body, {
			headers: {
				"Content-Type": `application/json`,
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		return error?.response?.statusText;
	}
};
export const updateProgramOverallPoints = async (body) => {
	try {
		let AuthToken = localStorage.getItem("token");
		let user_ID = localStorage.getItem("userId");

		const apiUrl = `${URL}/feeddata/OverallPoints/${body?.id}`;
		console.log("vfbdvbfgsdh", apiUrl);

		let response = await axios.put(apiUrl, body, {
			headers: {
				"Content-Type": `application/json`,
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		return error?.response?.statusText;
	}
};
export const deleteProgramOverallPoints = async (id) => {
	console.log("id", id);
	let AuthToken = localStorage.getItem("token");

	const apiUrl = `${URL}/feeddata/OverallPoints/${id}`;

	let { data } = await axios.delete(apiUrl, {
		headers: {
			"Content-Type": `application/json`,
			Authorization: AuthToken,
			"ngrok-skip-browser-warning": "69420",
		},
	});

	return data;
};

export const filterUsersProgram = async (data) => {
	try {
		let AuthToken = localStorage.getItem("token");
		let user_ID = localStorage.getItem("userId");
		let programId = localStorage.getItem("programID");
		let body = {
			program_id: programId,
			firebaseusers: data,
		};

		const apiUrl = `${URL}/sign-up/exportsallusersfromprogram`;

		let response = await axios.post(apiUrl, body, {
			headers: {
				"Content-Type": `application/json`,
				Authorization: AuthToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});

		return response.data;
	} catch (error) {
		return error?.response?.statusText;
	}
};

export const generateOrderForPayment = async (data) => {
	try {
		const { amount, currency, receipt } = data;
		const payment_capture = 1;
		const requestObj = {
			amount,
			currency,
			receipt,
			payment_capture,
		};
		const url = `${URL}/payment-razorpay/createOrder`;
		let authToken = localStorage.getItem("token");
		let authUserToken = localStorage.getItem("userTokentoken");
		const response = await axios.post(url, requestObj, {
			headers: {
				"Content-Type": `application/json`,
				Authorization: authUserToken ? authUserToken : authToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		throw error;
	}
};
/********************************************************
 *               Onboarding Apis End                     *
 *********************************************************/

/********************************************************
 *               Feedback Apis Start                     *
 *********************************************************/

export const getAllFeedbackOption = async () => {
	try {
		const url = `${URL}/feedback-option`;
		const authToken = localStorage.getItem("token");
		const authUserToken = localStorage.getItem("userTokentoken");
		const response = await axios.get(url, {
			headers: {
				"Content-Type": `application/json`,
				Authorization: authUserToken ? authUserToken : authToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		throw error;
	}
};

export const recordFeedbackForWorkout = async (
	workoutId,
	feedbackOptionId,
	workoutType,
	feedbackText
) => {
	try {
		const url = `${URL}/workout-feedback`;
		const authToken = localStorage.getItem("token");
		const authUserToken = localStorage.getItem("userTokentoken");
		const dataObj = {
			workoutId,
			feedbackOptionId,
			workoutType,
			feedbackText,
		};
		const response = await axios.post(url, dataObj, {
			headers: {
				"Content-Type": `application/json`,
				Authorization: authUserToken ? authUserToken : authToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		if (response?.data?.status === 201) {
			const getResponse = await getFeedbackForWorkout(
				workoutId,
				workoutType
			);
			console.log(getResponse);
			return getResponse?.data;
		} else {
			alert("Feedback for workout not recorded.");
		}
	} catch (error) {
		alert("Feedback for workout not recorded.");
		throw error;
	}
};

export const getFeedbackForWorkout = async (workoutId, workoutType) => {
	try {
		const url = `${URL}/workout-feedback/${workoutId}/${workoutType}`;
		const authToken = localStorage.getItem("token");
		const authUserToken = localStorage.getItem("userTokentoken");
		const response = await axios.get(url, {
			headers: {
				"Content-Type": `application/json`,
				Authorization: authUserToken ? authUserToken : authToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		throw error;
	}
};

/********************************************************
 *               Feedback Apis End                     *
 *********************************************************/

/********************************************************
 *            Community Athlete Onboarding Apis start   *
 *********************************************************/
export const getInviteListForGroup = async (groupId) => {
	try {
		const url = `${URL}/athlete-community/group-invite/group/${groupId}`;
		const authToken = localStorage.getItem("token");
		const authUserToken = localStorage.getItem("userTokentoken");
		const response = await axios.get(url, {
			headers: {
				"Content-Type": `application/json`,
				Authorization: authUserToken ? authUserToken : authToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		throw error;
	}
};

export const sentInviteEmailForGroup = async (emailList, groupId) => {
	try {
		const url = `${URL}/athlete-community/group-invite`;
		const authToken = localStorage.getItem("token");
		const authUserToken = localStorage.getItem("userTokentoken");
		const dataObj = {
			emails: emailList,
			athleteGroupId: groupId,
		};
		const response = await axios.post(url, dataObj, {
			headers: {
				"Content-Type": `application/json`,
				Authorization: authUserToken ? authUserToken : authToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		throw error;
	}
};

export const deleteInviteForEmailForGroup = async (id) => {
	try {
		const url = `${URL}/athlete-community/group-invite/${id}`;
		const authToken = localStorage.getItem("token");
		const authUserToken = localStorage.getItem("userTokentoken");
		const response = await axios.delete(url, {
			headers: {
				"Content-Type": `application/json`,
				Authorization: authUserToken ? authUserToken : authToken,
				"ngrok-skip-browser-warning": "69420",
			},
		});
		return response.data;
	} catch (error) {
		throw error;
	}
};

export const createCommunityGroup = async (formData) => {
	const token = localStorage.getItem("token");
	const response = await axios.post(
		`${URL}/athlete-community/group`,
		formData,
		{
			headers: {
				"Content-Type": "multipart/form-data",
				Authorization: token,
			},
		}
	);
	return response;
};

export const fetchCommunityGroups = async () => {
	const token = localStorage.getItem("token");
	const response = await axios.get(`${URL}/athlete-community/group`, {
		headers: { Authorization: token },
	});
	return response;
};

export const updateCommunityGroup = async (editData, formData) => {
	const token = localStorage.getItem("token");
	const response = await axios.put(
		`${URL}/athlete-community/group/${editData}`,
		formData,
		{
			headers: {
				"Content-Type": "multipart/form-data",
				Authorization: token,
			},
		}
	);
	return response;
};

export const deleteCommunityGroup = async (record) => {
	const token = localStorage.getItem("token");
	const response = await axios.delete(
		`${URL}/athlete-community/group/${record.id}`,
		{
			headers: {
				"Content-Type": "multipart/form-data",
				Authorization: token,
			},
		}
	);
	return response;
};

export const fetchCommunityGroupChallenges = async (id) => {
	const token = localStorage.getItem("token");
	const response = await axios.get(
		`${URL}/athlete-community/group-challenge/group/${id}`,
		{
			headers: {
				"Content-Type": "application/json",
				Authorization: token,
			},
		}
	);
	return response;
};

export const fetchCommunityGroupChallengesLevel = async () => {
	const token = localStorage.getItem("token");
	const response = await axios.get(
		`${URL}/athlete-community/group-challenge-level`,
		{
			headers: {
				"Content-Type": "application/json",
				Authorization: token,
			},
		}
	);
	return response;
};

export const fetchCommunityGroupChallengesTarget = async () => {
	const token = localStorage.getItem("token");
	const response = await axios.get(
		`${URL}/athlete-community/group-challenge-target`,
		{
			headers: {
				"Content-Type": "application/json",
				Authorization: token,
			},
		}
	);
	return response;
};

export const fetchCommunityGroupEnrolment = async () => {
	let groupID = 0;
	const groupDetailString = localStorage.getItem("groupDetail");

	if (groupDetailString) {
		const groupDetail = JSON.parse(groupDetailString);
		groupID = parseInt(groupDetail.id);
	}

	const token = localStorage.getItem("token");
	const response = await axios.get(
		`${URL}/athlete-community/group-enrolment/${groupID}`,
		{
			headers: {
				"Content-Type": "application/json",
				Authorization: token,
			},
		}
	);
	console.log(response, "responseAA");

	return response;
};

export const creategroupChallengeEnrollment = async (formik, userId) => {
	try {
		const token = localStorage.getItem("token");
		const response = await axios.post(
			`${URL}/athlete-community/group-challenge-enrolment`,
			{
				athleteCommunityGroupChallengeId: formik.values.challenges,
				userId: userId,
			},
			{
				headers: { Authorization: token },
			}
		);
		return response;
	} catch (error) {
		return `Error fetching community group challenges: ${error.message}`;
	}
};

export const createAthleteCommunityGroupMember = async (payload) => {
	const token = localStorage.getItem("token");
	const response = await axios.post(
		`${URL}/athlete-community/group-mentor`,
		payload,
		{
			headers: { Authorization: token },
		}
	);
	return response;
};

export const listGroupMentors = async () => {
	try {
		const token = localStorage.getItem("token");
		const response = await axios.get(
			`${URL}/athlete-community/group-mentor`,
			{
				headers: { Authorization: token },
			}
		);
		return response;
	} catch (error) {
		throw error;
	}
};

export const fetchGroupMentors = async (groupId) => {
	try {
		const token = localStorage.getItem("token");
		const response = await axios.get(
			`${URL}/athlete-community/group-mentor/${groupId}`,
			{
				headers: { Authorization: token },
			}
		);
		return response;
	} catch (error) {
		throw error;
	}
};

export const deleteMentor = async (id) => {
	try {
		const token = localStorage.getItem("token");
		const response = await axios.delete(
			`${URL}/athlete-community/group-mentor/${id}`,
			{
				headers: { Authorization: token },
			}
		);
		return response;
	} catch (error) {
		throw error;
	}
};

export const listActivities = async () => {
	try {
		const token = localStorage.getItem("token");
		const response = await axios.get(`${URL}/yoska-activity/`, {
			headers: {
				"Content-Type": "multipart/form-data",
				Authorization: token,
			},
		});
		// setActivity(response.data.yoskaActivities);
		return response?.data?.yoskaActivities;
	} catch (error) {
		// console.error("Error fetching activities:", error);
		return `Error fetching activities: ${error}`;
	}
};

export const updateInviteStatusForEmail = async (email) => {
  try {
    const url = `${URL}/athlete-community/group-invite/status/${email}`;
    const authToken = localStorage.getItem("token");
    const authUserToken = localStorage.getItem("userTokentoken");
    const response = await axios.put(url, {
      headers: {
        "Content-Type": `application/json`,
        Authorization: authUserToken ? authUserToken : authToken,
        "ngrok-skip-browser-warning": "69420",
      }
    });
    return response.data;

  } catch (error) {
    throw error;
  }
}

export const listChatUser = async () => {
  try {
    const token = localStorage.getItem("token");
    const response = await axios.get(`${URL}/user-chat`, {
      headers: {
        "Content-Type": "multipart/form-data",
        Authorization: token,
      },
    });
    return response?.data
  } catch (error) {
    console.error("Error fetching user chats:", error);
    return `Error fetching activities: ${error}`
  }
}

export const createChallengeLevel = async (payload) => {
  try {
	const token = localStorage.getItem("token");
	const response = await axios.post(
		`${URL}/athlete-community/group-challenge-level`,
		payload,
		{
			headers: { Authorization: token },
		}
	);
	return response;
  } catch (error) {
    console.error("Error creating challenge level:", error);
    return `Error creating challenge level: ${error}`
  }
}

export const listChallengeLevel = async () => {
  try {
    const token = localStorage.getItem("token");
    const response = await axios.get(`${URL}/athlete-community/group-challenge-level`, {
		headers: {
        "Content-Type": "multipart/form-data",
        Authorization: token,
      },
    });
    return response
  } catch (error) {
    console.error("Error fetching challenge levels:", error);
    return `Error fetching challenge levels: ${error}`
  }
}

export const updateChallengeLevel = async (payload, id) => {
  try {
	const token = localStorage.getItem("token");
	const response = await axios.put(
		`${URL}/athlete-community/group-challenge-level/${id}`,
		payload,
		{	
			"Content-Type": `application/json`,
			headers: { Authorization: token },
		}
	);
	console.log(response,"Updated challenge level");
	return response;
  } catch (error) {
    console.error("Error updating challenge level:", error);
    return `Error updating challenge level: ${error}`
  }
}

export const deleteChallengeLevel = async (id) => {
	try {
		const token = localStorage.getItem("token");
		const response = await axios.delete(`${URL}/athlete-community/group-challenge-level/${id}`,{
			headers: { Authorization: token },
		});
		return response;
	}catch (error) {
		console.error("Error deleting challenge level:", error);
    	return `Error deleting challenge level: ${error}`
	}
}

export const createChallengetarget = async (payload) => {
  try {
	const token = localStorage.getItem("token");
	const response = await axios.post(
		`${URL}/athlete-community/group-challenge-target`,
		payload,
		{
			headers: { Authorization: token },
		}
	);
	return response;
  } catch (error) {
    console.error("Error creating challenge target:", error);
    return `Error creating challenge target: ${error}`
  }
}

export const updateChallengeTarget = async (payload, id) => {
  try {
	const token = localStorage.getItem("token");
	const response = await axios.put(
		`${URL}/athlete-community/group-challenge-target/${id}`,
		payload,
		{	
			"Content-Type": `application/json`,
			headers: { Authorization: token },
		}
	);
	console.log(response,"Updated challenge level");
	return response;
  } catch (error) {
    console.error("Error updating challenge target:", error);
    return `Error updating challenge target: ${error}`
  }
}

export const listChallengeTarget = async () => {
  try {
    const token = localStorage.getItem("token");
    const response = await axios.get(`${URL}/athlete-community/group-challenge-target`, {
		headers: {
        "Content-Type": "multipart/form-data",
        Authorization: token,
      },
    });
    return response
  } catch (error) {
    console.error("Error fetching challenge levels:", error);
    return `Error fetching challenge levels: ${error}`
  }
}

export const deleteChallengeTarget = async (id) => {
	try {
		const token = localStorage.getItem("token");
		const response = await axios.delete(`${URL}/athlete-community/group-challenge-target/${id}`,{
			headers: { Authorization: token },
		});
		return response;
	}catch (error) {
		console.error("Error deleting challenge target:", error);
    	return `Error deleting challenge target: ${error}`
	}
}

export const fetchleaderBoard = async (selectChallenge) => {
  try {
    const token = localStorage.getItem("token");
    const response = await axios.get(`${URL}/athlete-community/group-challenge-leaderboard/${selectChallenge}`, {
		headers: {
        "Content-Type": "multipart/form-data",
        Authorization: token,
      },
    });
    return response.data;
  } catch (error) {
    console.error("Error fetching LeaderBoard data:", error);
    return `Error fetching LeaderBoard data: ${error}`
  }
}

export const checkStrava = async (token) => {
	try {
	const response = await axios.get('https://www.strava.com/api/v3/athlete', {
		headers: {
		Authorization: `Bearer ${token}`
		}
	});
	 return response;
	} catch (error) {
	console.log("Error connecting to strava line9356:", error);
	return `Error connecting to strava: ${error}`
	}
}

export const syncChallengeLeaderboard = async (challengeId) => {
	try {
		const token = localStorage.getItem("token");
		const response = await axios.get(`${URL}/athlete-community/group-challenge-leaderboard/sync/${challengeId}`, {
			headers: { Authorization: token },
		});
	 return response;
	} catch (error) {
	console.log("Error:", error);
	return `Error: ${error}`
	}
}

export const generateLeaderboardData = async (challengeId) => {
	try {
		const token = localStorage.getItem("token");
		const response = await axios.get(`${URL}/athlete-community/group-challenge-leaderboard/report/${challengeId}`, {
			headers: { Authorization: token },
		});
	 return response;
	} catch (error) {
	console.log("Error:", error);
	return `Error: ${error}`
	}
}

export const deleteAthleteChallengeEnrollment = async (challengeId, athleteId) => {
	try {
		const token = localStorage.getItem("token");
		const response = await axios.delete(`${URL}/athlete-community/group-challenge-enrolment/remove/${challengeId}/${athleteId}`, {
			headers: { Authorization: token },
		});
	 return response;
	} catch (error) {
	console.log("Error:", error);
	return `Error: ${error}`
	}
}

/********************************************************
 *            Community Athlete Onboarding Apis end   *
 *********************************************************/
