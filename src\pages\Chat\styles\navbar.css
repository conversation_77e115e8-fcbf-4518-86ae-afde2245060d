.appBar {
	padding: 0.6rem !important;
	justify-content: space-between !important;
	top: 0 !important;
	position: sticky !important;
	background-color: #f5f5f5;
	z-index: 1000 !important;
	border-radius: 0 8px 0 0;
}

.headingOfPageContent {
	height: 27px;

	font-family: "Poppins";
	font-style: normal;
	font-weight: 500;
	font-size: 18px;
	line-height: 27px;
	/* identical to box height */

	color: #000000;
}

.chatTitle {
	color: var(--black-2, #111) !important;
	font-size: 16px !important;
	font-style: normal !important;
	font-weight: 700 !important;
	line-height: normal !important;
	letter-spacing: 0.16px !important;
	padding-left: 0.5rem;
}

.readUnreadButton {
	margin-top: 3px;
	font-weight: 700 !important;
	font-size: 14px;
	color: gray;
	margin-left: -5px;
}

.nav-icon:hover {
	cursor: pointer;
}

.active-inactive {
	color: var(--cyan-2, #056b6b) !important;
	font-size: 10px !important;
	font-style: normal !important;
	font-weight: 400 !important;
	line-height: normal !important;
	letter-spacing: 0.4px !important;
	display: "inline-block" !important;
	padding-left: 4px;
}

.search-message-input {
	width: 100% !important;
	margin-left: 10px !important;
	padding: 8px 12px !important;
	border: 1px solid #ddd !important;
	border-radius: 6px !important;
	outline: none !important;
}

/* Mobile specific navbar styling */
@media (max-width: 430px) {
	.appBar {
		position: sticky !important;
		top: 0 !important;
		z-index: 1001 !important;
		background-color: #f5f5f5 !important;
		box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
		border-radius: 0 !important;
	}
}
