import { Avatar, Box, CircularProgress, Paper, Typography } from '@mui/material'
import React, { useEffect, useRef, useState } from 'react'
import NavIndividualChat from '../../components/NavIndividualChat';
import PhotoOutlinedIcon from '@mui/icons-material/PhotoOutlined';
import EastOutlinedIcon from '@mui/icons-material/EastOutlined';
import FlightTakeoffOutlinedIcon from '@mui/icons-material/FlightTakeoffOutlined';
import "./../../styles/mobile/individualChat.css"
import FooterFunctions from '../../components/FooterFunctions';
import { useDispatch, useSelector } from 'react-redux';
import { getChatMessagesAction } from '../../redux/action/chatsActions';
import { get, getDatabase, onValue, push, ref, serverTimestamp, set, update,off } from 'firebase/database';
import AcceptMessage from '../../components/prompt/AcceptMessage';
import InsertEmoticonIcon from "@mui/icons-material/InsertEmoticon";
import CameraAltIcon from "@mui/icons-material/CameraAlt";
import AddBoxIcon from "@mui/icons-material/AddBox";
import SendIcon from "@mui/icons-material/Send";
import { getStorage, ref as storageRef, uploadBytes, getDownloadURL } from 'firebase/storage';
import { ref as rtdbRef, set as rtdbSet } from 'firebase/database';


import ChatMsg from './ChatMsg';

import EmojiPicker from 'emoji-picker-react';
import { useNavigate } from 'react-router-dom';
import ReplyMessage from '../../components/preview/ReplyMessage';
import Cookies from 'js-cookie';
import LinkPreview from '../../components/preview/LinkPreview';
import Header from '../../../../components/Header';
import Swal from 'sweetalert2';

export default function IndividualChat({setOpenPage, setOpenPageTwo}) {
    const paper = {
        maxWidth: "100%",
        // backgroundImage: 'url("https://cdn.wallpapersafari.com/80/83/K7l2qB.jpg")',
        boxShadow: "unset",
      };
    
    
      const [typedMessage, setTypedMessage] = useState('')
      const [isMessageReqAccepted, setIsMessageReqAccepted] = useState(true)
      const [url, setUrl] = useState(null);
      const [previewData, setPreviewData] = useState(null);
      const [showToolBox, setShowToolBox] = useState(false)
      const [isMobile, setIsMobile] = useState(false);
      const [showEmojis, setShowEmojis] = useState(false)
      const [isReplyMsg, setIsReplyMsg] = useState(false)
      const [isLoading, setIsLoading] = useState(false)
      const [isSearchActive, setIsSearchActive] = useState(false);
      const [searchKeyword, setSearchKeyword] = useState("");
      const inputRef = useRef(null);
      const dispatch = useDispatch()
      const navigate = useNavigate()
      const userData = JSON.parse(localStorage.getItem('user'));
      const { openedUser } = useSelector(state => state.users)
      const { currentUser } = useSelector(state => state.auth)
      const { chatMessages } = useSelector(state => state.chats)
    
      const handleSendMessage = async (msgType, msg, docType, fileName, thumbnailURL) => {
        const db = getDatabase();
    
        setTypedMessage('')
        setShowEmojis(false)
        console.log(thumbnailURL)
        const combinedId =
          currentUser.uid > openedUser.uid
            ? currentUser.uid + openedUser.uid
            : openedUser.uid + currentUser.uid;
    
        try {
          const chatRef = ref(db, `chats/${combinedId}/messages`);
    
          const updatedMessages =
          {
            isDeleted: false,
            isStarred: false,
            senderId: currentUser.uid,
            type: msgType,
            isSeen: false,
            timeStamp: serverTimestamp(),
            message: msg,
            docType: docType ? docType : null,
            fileName: fileName ? fileName : null,
            thumbnailURL: thumbnailURL ? thumbnailURL : null
          }
    
          await push(chatRef, updatedMessages);
          window.scrollTo({
            top: document.documentElement.scrollHeight,
            behavior: 'smooth'
          });
    
          // dispatch(getChatMessagesAction(combinedId))
          console.log(msgType, 'sent!');
          return;
        } catch (error) {
          console.error('Failed to send message:', error);
        }
      };
    
      const handleReplyMessage = async (msgType, msg, docType, fileName) => {
        const db = getDatabase();
    
        setTypedMessage('')
        setShowEmojis(false)
        setIsReplyMsg(false)
    
        const combinedId =
          currentUser.uid > openedUser.uid
            ? currentUser.uid + openedUser.uid
            : openedUser.uid + currentUser.uid;
    
        const replyMsgInfo = JSON.parse(Cookies.get('replyMsgInfo'));
        const { timeStamp, message, senderId, type } = replyMsgInfo
    
        try {
          const chatRef = ref(db, `chats/${combinedId}/messages`);
    
          const updatedMessages =
          {
            isDeleted: false,
            isStarred: false,
            isReply: true,
            repliedMsgInfo: { timeStamp, message, senderId, type },
            senderId: currentUser.uid,
            type: msgType,
            isSeen: false,
            timeStamp: serverTimestamp(),
            message: msg,
            docType: docType ? docType : null,
            fileName: fileName ? fileName : null
          }
    
          await push(chatRef, updatedMessages);
          window.scrollTo({
            top: document.documentElement.scrollHeight,
            behavior: 'smooth'
          });
    
          // dispatch(getChatMessagesAction(combinedId))
          console.log('MESSAGE REPLIED!');
          Cookies.remove('replyMsgInfo')
          return;
        } catch (error) {
          console.error('Failed to reply message:', error);
        }
      };
    
      function filterMessagesByDateRange(messages) {
        const today = new Date().toDateString();
        const yesterday = new Date();
        yesterday.setDate(yesterday.getDate() - 1);
        const yesterdayDateString = yesterday.toDateString();
      
        const groupedMessages = {};
        
        for (const message of messages) {
          const date = new Date(message.timeStamp);
          if (isNaN(date.getTime())) {
            continue; // Skip invalid date messages
          }
          const dateString = date.toDateString();
      
          if (!groupedMessages[dateString]) {
            groupedMessages[dateString] = [];
          }
      
          groupedMessages[dateString].push(message);
        }
      
        // Sort messages inside each group
        for (const date in groupedMessages) {
          groupedMessages[date].sort((a, b) => new Date(a.timeStamp) - new Date(b.timeStamp));
        }
      
        // Sort dates to ensure latest ones appear at the bottom
        const sortedDates = Object.keys(groupedMessages).sort((a, b) => new Date(a) - new Date(b));
      
        const formattedArray = sortedDates.map((date) => ({
          filteredDate: date === today ? "Today" : date === yesterdayDateString ? "Yesterday" : date,
          messages: groupedMessages[date],
        }));
      
        return formattedArray;
      }
    
      const checkMessageReqAccepted = async (combinedId) => {
        try {
          const db = getDatabase();
          const userInfoPath = `userChats/${currentUser.uid}/${combinedId}`;
          const userInfoRef = ref(db, userInfoPath);
    
          onValue(userInfoRef, (snapshot) => {
            const userInfo = snapshot.val();
            setIsMessageReqAccepted(userInfo?.isMsgReqAccepted);
          });
        } catch (error) {
          console.log(error)
        }
      }
    
      const handleUploadVdoThumbnailAndGetLink = async (thumbnailData) => {
        const storage = getStorage();
        const storageReference = storageRef(storage, 'thumbnail/' + thumbnailData.name);
        try {
          await uploadBytes(storageReference, thumbnailData);
          const downloadURL = await getDownloadURL(storageReference);
          console.log("VDO thumbnailURL : ", downloadURL)
          return downloadURL;
        } catch (error) {
          console.error('Error uploading media:', error);
          throw error;
        }
      };
    
      const uploadImageAndGetLink = async (file, isVideo, thumbnailData) => {
        const storage = getStorage();
        const storageReference = isVideo
          ? storageRef(storage, 'videos/' + file.name)
          : storageRef(storage, 'images/' + file.name);
    
        try {
          console.log(thumbnailData)
          const thumbnailURL = thumbnailData ? await handleUploadVdoThumbnailAndGetLink(thumbnailData) : null
    
          console.log(thumbnailURL)
          // Upload the image to Firebase Storage
          await uploadBytes(storageReference, file);
          // Get the download URL of the uploaded image
          const downloadURL = await getDownloadURL(storageReference);
    
          // Save the download URL in Firestore
          // (your Firestore code here)
    
          await handleSendMessage(isVideo ? 'video' : 'photo', downloadURL, null, null, thumbnailURL)
    
          console.log('Media ploaded and URL saved successfully.');
          setIsLoading(false)
    
          return downloadURL;
        } catch (error) {
          setIsLoading(false)
          Swal.fire({
            title: "Error",
            text: error,
            icon: "error",
          });
          console.error('Error uploading media:', error);
          throw error;
        }
      };
    
      const uploadDocumentAndGetURL = async (file) => {
        const storage = getStorage();
        const storageReference = storageRef(storage, 'documents/' + file.name);
    
        const database = getDatabase();
        const databaseReference = rtdbRef(database, 'documents');
    
        try {
          // Upload the image to Firebase Storage
          await uploadBytes(storageReference, file);
    
          // Get the download URL of the uploaded image
          const downloadURL = await getDownloadURL(storageReference);
    
          // Save the download URL in Firestore
          // (your Firestore code here)
    
          await handleSendMessage('document', downloadURL, file.name.split('.').pop(), file.name)
    
          console.log('Document uploaded and URL saved successfully.');
    
          return downloadURL;
        } catch (error) {
          console.error('Error uploading document:', error);
          throw error;
        }
      };
    
      const handleImageUpload = async (event) => {
        setShowToolBox(false);
        if (event.target.files[0]) {
          const acceptedTypes = [
            'image/jpeg',
            'image/jpg',
            'image/png',
            'image/gif',
            'image/svg+xml',
            'video/mp4',
            'video/webm',
            'video/mkv',
            'video/ogg',
          ];
      
          const fileType = event.target.files[0].type;
      
          if (acceptedTypes.includes(fileType)) {
            const isVideo = fileType.startsWith('video/');
      
            try {
              if (isVideo) {
                setIsLoading(true);
                console.log("Uploading video...");
                const video = document.createElement('video');
                video.preload = 'metadata';
                video.src = URL.createObjectURL(event.target.files[0]);
      
                console.log('Video duration:', video.duration, video.src);
                await new Promise((resolve) => {
                  video.onloadedmetadata = resolve;
                });
      
                if (!isFinite(video.duration) || video.duration <= 0) {
                  throw new Error('Invalid video duration.');
                }
      
                // Seek to the middle of the video
                const seekTime = video.duration / 2;
                video.currentTime = seekTime;
      
                await new Promise((resolve) => {
                  video.onseeked = resolve;
                });
      
                const canvas = document.createElement('canvas');
                canvas.width = video.videoWidth;
                canvas.height = video.videoHeight;
                const ctx = canvas.getContext('2d');
      
                ctx.drawImage(video, 0, 0, canvas.width, canvas.height);
      
                const thumbnailBlob = await new Promise((resolve) =>
                  canvas.toBlob(resolve, 'image/jpeg', 0.8)
                );
      
                const thumbnailFile = new File([thumbnailBlob], 'thumbnail.jpg', {
                  type: 'image/jpeg',
                });
      
                // Pass the thumbnailFile to the next arrow function or perform any desired operations
                console.log('Thumbnail File:', thumbnailFile);
      
                const downloadURL = await uploadImageAndGetLink(
                  event.target.files[0],
                  isVideo,
                  thumbnailFile
                );
                console.log('Download URL:', downloadURL);
      
                // Show success message
                console.log("Video uploaded successfully.");
                Swal.fire({
                  title: "Success",
                  text: "Video Uploaded Successfully.",
                  icon: "success",
                });
              } else {
                // Handle image upload
                const downloadURL = await uploadImageAndGetLink(event.target.files[0]);
                console.log('Download URL:', downloadURL);
      
                // Show success message
                console.log("Image uploaded successfully.");
                Swal.fire({
                  title: "Success",
                  text: "Image Uploaded Successfully.",
                  icon: "success",
                });
              }
            } catch (error) {
              console.error('Error uploading image:', error);
              setIsLoading(false);
              // Show error message
              console.log("Error uploading image:", error);
              Swal.fire({
                title: "Error",
                text: error.message || error,
                icon: "error",
              });
            }
          } else {
            console.log('File is not from the given options.');
            Swal.fire({
              title: "Error",
              text: "File Type Doesn't match",
              icon: "error",
            });
          }
        }
      };
      
      const handleDocumentsUpload = async (event) => {
        setShowToolBox(false);
        if (event.target.files[0]) {
          const allowedFileTypes = [".csv", ".doc", ".docx", ".pdf", ".ppt", ".txt"];
          const selectedFileType = event.target.files[0].name.substring(
            event.target.files[0].name.lastIndexOf(".")
          );
          if (allowedFileTypes.includes(selectedFileType)) {
            try {
              const downloadURL = await uploadDocumentAndGetURL(event.target.files[0]);
              console.log('Download URL:', downloadURL);
            } catch (error) {
              console.error('Error uploading image:', error);
            }
          } else {
            console.log('Selected file is not from the allowed list.');
          }
        }
      };
      useEffect(() => {
        console.log('openedUser:', openedUser);
      }, [openedUser]);
    
    
      const handleTypeChange = event => {
        setTypedMessage(event.target.value)
    
        const inputText = event.target.value;
    
        // Regular expression to match URLs
        const urlRegex = /(https?:\/\/[^\s]+)/g;
        const match = inputText.match(urlRegex);
    
        if (match) {
          // If URL found, set it to the state variable
          const foundUrl = match[0];
          console.log(foundUrl);
          if (foundUrl !== url) setUrl(foundUrl);
        } else {
          // If no URL found, set the state variable to null
          setUrl(null);
        }
      }
    
      const navigateToGoogleMaps = () => {
        if (navigator.geolocation) {
          navigator.geolocation.getCurrentPosition(position => {
            const { latitude, longitude } = position.coords;
            const googleMapsUrl = `https://www.google.com/maps/search/?api=1&query=${latitude},${longitude}`;
            window.open(googleMapsUrl, '_blank');
          }, error => {
            console.error('Error getting current location:', error);
          });
        } else {
          console.error('Geolocation is not supported by this browser');
        }
      };
    
      const handleEmojiClick = (emojiObject) => {
        // Assuming emojiObject is an object with a property 'emoji'
        setTypedMessage((prevTypedMessage) => {
          // Append the clicked emoji to the existing message
          return prevTypedMessage + emojiObject.emoji;
        });
      };
    
      useEffect(() => {
        window.scrollTo({
          top: document.documentElement.scrollHeight + 10,
        });
      }, [chatMessages])
    
      useEffect(() => {
        // inputRef.current.focus();
        const combinedId =
          currentUser.uid > openedUser.uid
            ? currentUser.uid + openedUser.uid
            : openedUser.uid + currentUser.uid;
    
        checkMessageReqAccepted(combinedId)
    
        dispatch(getChatMessagesAction(combinedId))
      }, [openedUser.uid])
    
      useEffect(() => {
        const checkScreenWidth = () => {
          const isMobileScreen = window.innerWidth <= 768;
          setIsMobile(isMobileScreen);
        };
        window.addEventListener('resize', checkScreenWidth);
        checkScreenWidth();
        return () => {
          window.removeEventListener('resize', checkScreenWidth);
        };
      }, []);
    
      useEffect(() => {
        console.log("isLoadingindividual",isLoading);
    }, [isLoading]);
    
      const chatContainerRef = useRef(null);
    
      useEffect(() => {
          // Scroll to the bottom of the chat container when chatMessages state changes
          if (chatContainerRef.current) {
              chatContainerRef.current.scrollTop = chatContainerRef.current.scrollHeight;
          }
      }, [chatMessages]);
    
      const [isOnline, setIsOnline] = useState(false);
      useEffect(() => {
        const db = getDatabase();
        const isOnlineRef = ref(db, `/users/${openedUser.uid}/onlineStatus/isOnline`);
      
        // This will fire every time `isOnline` changes in Firebase
        const listener = onValue(isOnlineRef, (snapshot) => {
          setIsOnline(snapshot.val());
        });
      
        // Clean up the listener on unmount
        return () => {
          off(isOnlineRef, listener);
        };
      }, [openedUser.uid]);

    return (
        <div>
        <Header />
  
        <Box
          sx={{ display: "flex", justifyContent: "center", alignItems: "center" , maxWidth:"100%"}}
        >
          <Paper
  sx={{ flexGrow: 1 }}
  elevation={10}
  style={{ ...paper, display: 'flex', flexDirection: 'column', height: 'calc(100vh - 64px)' }}
>
            {userData.onboardingState === "community" ? (
              <NavIndividualChat 
              backButtonPath={'/chat'} 
              isOnline={isOnline}
               photoURL={openedUser.photoURL} 
               displayName={openedUser.name}
               isSearchActive={isSearchActive}
              setIsSearchActive={setIsSearchActive}
              searchKeyword={searchKeyword}
              setSearchKeyword={setSearchKeyword}
              setOpenPage={setOpenPage}
              setOpenPageTwo={setOpenPageTwo} />
            ) : (
              <NavIndividualChat 
              backButtonPath={'/chat'} 
              isOnline={isOnline}
               photoURL={openedUser.photoURL} 
               displayName={openedUser.displayName}
               isSearchActive={isSearchActive}
              setIsSearchActive={setIsSearchActive}
              searchKeyword={searchKeyword}
              setSearchKeyword={setSearchKeyword}
              setOpenPage={setOpenPage}
              setOpenPageTwo={setOpenPageTwo} />
            )}
   <div
    ref={chatContainerRef}
    style={{ overflowY: "scroll", flex: 1 }}
    className="chatPaperIndividual"
  >
            {filterMessagesByDateRange(chatMessages)?.map((item, index) => {
              return (
                <Box key={index} >
                  <Box className="date-box-parent" mt={1}>
                    <Box className="date-box">
                      <Typography className="ind-chat-date">
                        {item.filteredDate}
                      </Typography>
                    </Box>
                  </Box>
  
                  {item.messages?.map((msg, mindex) => (
                    <ChatMsg
                      isLoading={isLoading}
                      onClick={() => setShowToolBox(false)}
                      key={msg.timeStamp}
                      currentUser={currentUser}
                      openedUser={openedUser}
                      isMessageReqAccepted={isMessageReqAccepted}
                      msg={msg}
                      setIsReplyMsg={setIsReplyMsg}
                      searchKeyword={searchKeyword}
                      isSearchActive={isSearchActive}
                    />
                  ))}
                </Box>
              );
            })}
            </div>
  
            {isLoading   ? (
              <CircularProgress className="m-6" />) : (
            null )}
            {/*---- <FooterFunctions/> ----*/}
              {showToolBox && (
                <Box className="plus-bar-content">
                  <Box className="plus-bar-icon-div">
                    <img
                      onClick={() => document.getElementById("selectDocumentsInput").click()}
                      style={{ cursor: "pointer" }}
                      height={32}
                      src="/images/document-icon.png"
                      alt="emoji"
                    />
                    <Typography
                      onClick={() => document.getElementById("selectDocumentsInput").click()}
                      style={{ cursor: "pointer" }}
                      className="plus-bar-icon-label"
                    >
                      Document
                    </Typography>
                  </Box>
                  <Box className="line-plus-icon-menu"></Box>
                  <Box className="plus-bar-icon-div">
                    <img
                      onClick={() => setOpenPageTwo("camera")}
                      height={32}
                      src="/images/camera-icon.png"
                      alt="emoji"
                      style={{ cursor: "pointer" }}
                    />
                    <Typography
                      onClick={() => setOpenPageTwo("camera")}
                      className="plus-bar-icon-label"
                      style={{ cursor: "pointer" }}
                    >
                      Camera
                    </Typography>
                  </Box>
                  <Box className="plus-bar-icon-div">
                    <img
                      onClick={() => document.getElementById("selectImgInput").click()}
                      height={32}
                      src="/images/gallery-icon.png"
                      style={{ cursor: "pointer" }}
                      alt="emoji"
                    />
                    <Typography
                      onClick={() => document.getElementById("selectImgInput").click()}
                      className="plus-bar-icon-label"
                      style={{ cursor: "pointer" }}
                    >
                      Gallery
                    </Typography>
                  </Box>
                </Box>
              )}
  
              <input
                id="selectImgInput"
                hidden
                type="file"
                accept=".jpeg, .jpg, .png, .gif, .svg, video/*"
                onChange={handleImageUpload}
              />
              <input
                id="selectDocumentsInput"
                hidden
                type="file"
                accept=".csv, .doc, .docx, .pdf, .ppt, .txt"
                onChange={handleDocumentsUpload}
              />
  
              {isMessageReqAccepted ? (
                <div className="chat-footer">
                  <form
                    onSubmit={(e) => {
                      e.preventDefault();
                      if (typedMessage.trim()) {
                        handleSendMessage("text", typedMessage);
                      }
                    }}
                    className="input-form"
                  >
                    <div className="input-container">
                      <InsertEmoticonIcon
                        onClick={() => setShowEmojis((prev) => !prev)}
                        style={{ cursor: "pointer" }}
                      />
                      <input
                        ref={inputRef}
                        value={typedMessage}
                        onChange={handleTypeChange}
                        type="text"
                        placeholder="Type here..."
                        className="type-msg"
                      />
                      <CameraAltIcon
                        onClick={() => setOpenPageTwo("camera")}
                        style={{ cursor: "pointer" }}
                      />
                      <label className="file-input-button">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          viewBox="0 0 20 20"
                          id="attachment"
                          width="24"
                          height="24"
                          fill="currentColor"
                        >
                          <path d="M5.602 19.8c-1.293 0-2.504-.555-3.378-1.44-1.695-1.716-2.167-4.711.209-7.116l9.748-9.87c.988-1 2.245-1.387 3.448-1.06 1.183.32 2.151 1.301 2.468 2.498.322 1.22-.059 2.493-1.046 3.493l-9.323 9.44c-.532.539-1.134.858-1.738.922-.599.064-1.17-.13-1.57-.535-.724-.736-.828-2.117.378-3.337l6.548-6.63c.269-.272.705-.272.974 0s.269.714 0 .986l-6.549 6.631c-.566.572-.618 1.119-.377 1.364.106.106.266.155.451.134.283-.029.606-.216.909-.521l9.323-9.439c.64-.648.885-1.41.69-2.145a2.162 2.162 0 0 0-1.493-1.513c-.726-.197-1.48.052-2.12.7l-9.748 9.87c-1.816 1.839-1.381 3.956-.209 5.143 1.173 1.187 3.262 1.629 5.079-.212l9.748-9.87a.683.683 0 0 1 .974 0 .704.704 0 0 1 0 .987L9.25 18.15c-1.149 1.162-2.436 1.65-3.648 1.65z"></path>
                        </svg>
                      </label>
                      <button className="chat-btn" type="submit">
                        <SendIcon />
                      </button>
                    </div>
                  </form>
                </div>
              ) : (
                <AcceptMessage
                  currentUserId={currentUser.uid}
                  combinedId={
                    currentUser.uid > openedUser.uid
                      ? currentUser.uid + openedUser.uid
                      : openedUser.uid + currentUser.uid
                  }
                  openedUserId={openedUser.uid}
                />
              )}
          </Paper>
        </Box>
      </div>
    )
}