import { Chip, FormControl, FormLabel, Grid, MenuItem, OutlinedInput, Select, TextField } from '@mui/material';
import { Button, Modal } from 'antd'
import React, { useEffect, useState } from 'react'
import { CreateLevels, CreatePrograms, CreateZonesClasification ,createAssesmentdata,createSegmentReadnessdata,createSugmentdata,getAllAssesmentData,getAllPrograms, getAlllevels, updateAssesmentdata, updateLevel, updatePrograms, updateSegmentReadnessdata, updateSugmentdata, updateZonesClasification, weeklyFeedDataPattern, weeklyFeedDataProgram } from '../../API/api-endpoint';
import { useFormik } from 'formik';
import Swal from 'sweetalert2';
import SlickCarousel from '../../pages/SlickCarousel';
const scoreData = [1, 2, 3, 4, 5]
const CreateSegmentReadness = ({ fetchReport, setShowAssesmentModal, showAssesmentModal,editData ,setEditData}) => {
    const [assesmentList, setAssesmentList] = useState([]);
console.log("editData",assesmentList);
    const formik = useFormik({
        initialValues: {
             name: "", 
        }, validate: (values) => {
            const errors = {};
            if (!values.name) {
                errors.name = "Name is required";
            }
            return errors;
        },
        // validationSchema: {},
        onSubmit: (values, { resetForm }) => {
            handleSubmitAssesmentForm(values, resetForm)

        },
    });
    console.log("formik", formik?.values);
    const assementdata = async () => {
        const response = await getAllAssesmentData()
        console.log("response", response);
        setAssesmentList(response)
    }
    useEffect(() => {
        assementdata()
    }, [])
  
    const handleSubmitAssesmentForm = async (data,resetForm) => {
        let response =""
        if (editData?.id) {

         response = await updateSegmentReadnessdata(data)
            
        }else{
         response = await createSegmentReadnessdata(data)

        }
        if (response?.status) {
            Swal.fire({
                title: "Success",
                text: response.message,
                icon: "success",
            });
            setShowAssesmentModal(false)
            fetchReport()
            resetForm()
            setEditData({})
            formik?.setValues({name:""})
        } else {
            Swal.fire({
                title: "Error",
                text: response.message,
                icon: "error",
            });
        }
        console.log("response", response);
    }
    useEffect(()=>{
        if (editData?.id) {
            const {srID,...data}=editData
            console.log("data",data);
            formik?.setValues(data)
        }else{
            setEditData({})
        }
    },[editData?.id])

   
    return (
        <Modal
            width={1200}
            open={showAssesmentModal}
            onCancel={() => {setShowAssesmentModal(false)
                setEditData({})
        formik.resetForm()
                formik?.setValues({name:""})}}
            footer={
                <div >
                    </div>
                //   loading={isLoading}
            }
        >

<div className="headingCont">
        <span className="heading">{editData?.id ? "Edit " : "Create"}</span>{" "}
        <span className="orange heading">Segment Readiness</span>
      </div>
      {/* <h1>{editData ? editData.challengeId : values.challengeId}</h1> */}
      <div className="parentCont">
        <form className="form1" onSubmit={formik.handleSubmit}>
          
                <Grid container spacing={2}>
                
                    <Grid item xs={12} sm={11}>
                        <FormLabel >Wellness Name<span className="text-[red]">*</span></FormLabel>
                    
                        <TextField
                            fullWidth
                            placeholder="Wellness name"
                            size="small"
                            type="text"
                            name="name"
                            value={formik?.values?.name}
                            onChange={formik.handleChange}
                            error={formik.touched.name && formik.errors.name}
                            helperText={
                                formik.touched.name && formik.errors.name
                            }

                        />
                    </Grid>
                    <Grid item xs={12} sm={6}>
              <Button
                className="btn"
                key="submit"
                type="primary"
                onClick={() => formik.handleSubmit()}
              >
                Submit
              </Button>
            </Grid>
                </Grid>
            </form>
            <div className="slick-container">
          <SlickCarousel />
        </div>
            </div>
        </Modal>
    )
}
export default CreateSegmentReadness
