// validationSchema.js
import * as Yup from 'yup';
export  const initialValues = {
  // Define your initial form field values here
  workout_planned_title:"",
  workout_description:"",
  workout_planned_speed:0,
  workout_planned_for_date_time:new Date(),
  postworkoutfeel_planned:"",
  postworkoutfeel_actual:"good",
  rpe_actual:0,
  zone_actual:0,
  training_plan_id:1,
  workout_planned_distance:"",
  workout_actual_distance:'',
  workout_planned_duration:'',
  workout_actual_duration:'',
  workout_planned_heart_unit:"bpm",
  workout_actual_heart_unit:"bpm",
  workout_planned_cadence_unit:"spm",
  workout_actual_cadence_unit:"spm",
  workout_planned_unit:"km",
  activity_id:"",
  unit:"km",
  sub_activity_id:""

};
export const addworkoutValidation = Yup.object({
  workout_planned_title: Yup.string().required('Title is required'),
  activity_id: Yup.string().required('Activity  is required'),
  sub_activity_id: Yup.string().required('Sub Activity  is required'),
  workout_planned_distance: Yup.number()
    .nullable()
    .positive('Distance must be a positive number')
    .transform((value, originalValue) => (originalValue === '' ? null : value)),
    workout_actual_distance: Yup.number()
    .nullable()
    .positive('Distance must be a positive number')
    .transform((value, originalValue) => (originalValue === '' ? null : value)),


  // workout_planned_distance:Yup.string().required('Distance  is required'),
  // workout_planned_duration:Yup.string().required('Duration is required'),



});
export const addworkoutMealValidation = Yup.object({
  workout_planned_title: Yup.string().required('Title is required'),
  sub_activity_id: Yup.string().required('Sub Activity  is required'),
  activity_id: Yup.string().required('Activity  is required'),
  adherence: Yup.number()
    .max(100, 'Percentage should be equal or less than 100'),
    actual_aadherence: Yup.number()
    .max(100, 'Percentage should be equal or less than 100')

});
export  const profileinitialValue = {
  firstname:"",
  dateofbirth:""

};
export const profileScema = Yup.object({
  firstname: Yup.string().required('First name is required'),
  dateofbirth: Yup.date()
    .max(new Date(), 'Date of birth must be in the past')
    .test('age', 'Age must be less than 12 years', function(value) {
      const dob = new Date(value);
      const currentDate = new Date();
  const minDOBDate = new Date(currentDate.getFullYear() - 12, currentDate.getMonth(), currentDate.getDate()); // Calculate minimum DOB date (12 years ago)

      console.log("age",minDOBDate,dob, dob >= minDOBDate);
      return dob <= minDOBDate;
    })
    .required('Date of birth is required'),
});
export  const assignAthleteinitialValue = {
  atheleteid:""

};
export const assignAthleteScema = Yup.object({

});

export  const createchallenge = {
  challangeTarget: [],
};
export const challengeScema = Yup.object({

});
export const traithlonScema = Yup.object({

});

