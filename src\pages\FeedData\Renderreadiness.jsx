import { useEffect, useState, use<PERSON>emo, useCallback } from "react";
import { <PERSON><PERSON> } from "../../components/ui/button";
import { Input } from "../../components/ui/input";
import { Card, CardContent, CardHeader } from "../../components/ui/card";
import {
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableHeader,
	TableRow,
} from "../../components/ui/table";
import { ArrowRight } from "lucide-react";
import Header from "../../components/Header";
import { getAllraceReadnessdata } from "../../API/api-endpoint";
import Swal from "sweetalert2";
import { Link } from "react-router-dom";

const RenderReadiness = () => {
	const [readinessData, setReadinessData] = useState([]);
	const [isLoading, setIsLoading] = useState(true);
	const [searchTerm, setSearchTerm] = useState("");
	const [currentPage, setCurrentPage] = useState(1);
	const pageSize = 10;

	const fetchData = useCallback(async () => {
		try {
			setIsLoading(true);
			const response = await getAllraceReadnessdata();
			if (response) {
				setReadinessData(response || []);
			} else {
				console.log("Response is empty or invalid");
				setReadinessData([]);
			}
		} catch (error) {
			console.error("Error fetching readiness data:", error);
			Swal.fire({
				title: "Error",
				text: "Failed to fetch readiness data. Please try again.",
				icon: "error",
				timer: 3000,
				showConfirmButton: false,
			});
			setReadinessData([]);
		} finally {
			setIsLoading(false);
		}
	}, []);

	useEffect(() => {
		fetchData();
	}, [fetchData]);

	const filteredData = useMemo(() => {
		if (!searchTerm.trim()) return readinessData;

		return readinessData.filter((item) => {
			return item?.name?.toLowerCase().includes(searchTerm.toLowerCase());
		});
	}, [readinessData, searchTerm]);

	const paginatedData = useMemo(() => {
		const startIndex = (currentPage - 1) * pageSize;
		const endIndex = startIndex + pageSize;
		return filteredData.slice(startIndex, endIndex);
	}, [filteredData, currentPage, pageSize]);

	const totalPages = Math.ceil(filteredData.length / pageSize);

	const handleSearch = (value) => {
		setSearchTerm(value);
		setCurrentPage(1);
	};

	const navigationButtons = [
		{ href: "/score-readness", label: "Get Score" },
		{ href: "/segments-readness", label: "Get Segments" },
		{ href: "/subsegments-readness", label: "Get Sub Segments" },
		{ href: "/option-readness", label: "Get Option" },
	];

	return (
		<div>
			<Header />
			<div className='mx-auto p-6 max-w-[1300px] mt-16'>
				<Card>
					<CardHeader className='bg-orange-50 border-b flex flex-col gap-4'>
						<div className='flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4'>
							<div>
								<h1 className='text-2xl font-bold text-orange-900'>
									Race Readiness
								</h1>
								<p className='text-orange-700 mt-1 text-sm'>
									Manage race readiness assessments and data
								</p>
							</div>
						</div>

						<div className='flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4'>
							<div className='flex flex-wrap gap-3'>
								{navigationButtons.map((button, index) => (
									<Link to={button.href} key={index}>
										<Button
											variant='outline'
											className='border-orange-300 text-orange-700 hover:bg-orange-100'
										>
											{button.label}
										</Button>
									</Link>
								))}
							</div>

							<div className='flex gap-3'>
								<Input
									placeholder='Search by name...'
									value={searchTerm}
									onChange={(e) =>
										handleSearch(e.target.value)
									}
									className='w-full text-sm'
								/>
							</div>
						</div>
					</CardHeader>
					<CardContent className='p-0'>
						<div className='overflow-x-auto'>
							<Table className='min-w-full w-full'>
								<TableHeader>
									<TableRow className='bg-blue-600 hover:bg-blue-600'>
										<TableHead className='text-white font-semibold'>
											Sr No
										</TableHead>
										<TableHead className='text-white font-semibold'>
											Name
										</TableHead>
									</TableRow>
								</TableHeader>
								<TableBody>
									{isLoading ? (
										<TableRow>
											<TableCell
												colSpan={2}
												className='text-center py-8'
											>
												Loading...
											</TableCell>
										</TableRow>
									) : paginatedData.length > 0 ? (
										paginatedData.map((item, index) => (
											<TableRow
												key={item.srID || index}
												className='hover:bg-gray-50'
											>
												<TableCell className='font-medium'>
													{item?.srID ||
														(currentPage - 1) *
															pageSize +
															index +
															1}
												</TableCell>
												<TableCell>
													{item?.name || "N/A"}
												</TableCell>
											</TableRow>
										))
									) : (
										<TableRow>
											<TableCell
												colSpan={2}
												className='text-center py-8 text-gray-500'
											>
												No readiness data found
											</TableCell>
										</TableRow>
									)}
								</TableBody>
							</Table>
						</div>

						{totalPages > 1 && (
							<div className='flex justify-center gap-2 p-4 border-t'>
								<Button
									variant='outline'
									size='sm'
									onClick={() =>
										setCurrentPage(
											Math.max(1, currentPage - 1)
										)
									}
									disabled={currentPage === 1}
								>
									Previous
								</Button>

								{Array.from({ length: 5 }, (_, i) => {
									const startPage =
										Math.floor((currentPage - 1) / 5) * 5 +
										1;
									const page = startPage + i;
									if (page > totalPages) return null;

									return (
										<Button
											key={page}
											variant={
												currentPage === page
													? "default"
													: "outline"
											}
											size='sm'
											onClick={() => setCurrentPage(page)}
											className={
												currentPage === page
													? "bg-orange-500 hover:bg-orange-600 text-white"
													: ""
											}
										>
											{page}
										</Button>
									);
								})}

								<Button
									variant='outline'
									size='sm'
									onClick={() =>
										setCurrentPage(
											Math.min(
												totalPages,
												currentPage + 1
											)
										)
									}
									disabled={currentPage === totalPages}
								>
									Next
								</Button>
							</div>
						)}
					</CardContent>
				</Card>
			</div>
		</div>
	);
};

export default RenderReadiness;
