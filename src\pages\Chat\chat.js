import React, { useEffect, useState } from "react";
import ChatLanding from "./screens/ChatLanding";
import IndividualChat from "./screens/individual/IndividualChat";
import IndividualChatSecond from "./screens/individual/IndividualChatSecond";
import IndividualChatThird from "./screens/individual/IndividualChatThird";
import NewChat from "./screens/newChat/NewChat";
import CreateNewGroup from "./screens/group/CreateNewGroup";
import { BrowserRouter, Route, Routes } from "react-router-dom";
import CreateNewGroupStepTwo from "./screens/group/CreateNewGroupStepTwo";
import GroupChatCreator from "./screens/group/chat/GroupChatCreator";
import GroupInfo from "./screens/group/GroupInfo";
import CameraIndividualChat from "./components/camera/CameraIndividualChat";
import CameraGroupChat from "./components/camera/CameraGroupChat";
import StartNewChat from "./screens/group/chat/StartNewChat";
import AddNewMember from "./components/group/AddNewMember";
import ShowPost from "./screens/posts/ShowPost";
import { ShareWith } from "./screens/posts/ShareWith";
import TempChatPage from "./screens/TempChatPage";
import SelectMsgForwardUsers from "./screens/individual/SelectMsgForwardUsers";
import { auth, db, ref } from "../../API/firebase.config";
import { onAuthStateChanged } from "firebase/auth";
import { onDisconnect, onValue, update } from "firebase/database";
import { useSelector } from "react-redux";

export default function Chat() {
  const { currentUser } = useSelector((state) => state.auth);

  useEffect(() => {
    const updateOnlineStatus = (userId, isOnline) => {
      const onlineStatusRef = ref(db, `users/${userId}/onlineStatus`);
      update(onlineStatusRef, { isOnline }); // Update only the isOnline key
    };

    onAuthStateChanged(auth, (user) => {
      if (user && currentUser?.uid) {
        const userId = user.uid;
        const userStatusRef = ref(db, ".info/connected");
        const userRef = ref(db, `users/${userId}/onlineStatus/isOnline`);

        updateOnlineStatus(userId, true);

        onDisconnect(userRef).set(false);

        onValue(userStatusRef, (snapshot) => {
          const isOnline = snapshot.val();

          if (!isOnline) {
            updateOnlineStatus(userId, true);
          }
        });
      }
    });
  }, []);

  return (
    <>
      <Routes>
        <Route path="/" element={<ChatLanding />} />
        <Route path="/start-new-chat" element={<StartNewChat />} />

        <Route path="/individual-chat" element={<IndividualChat />} />
        <Route
          path="/individual-chat/camera"
          element={<CameraIndividualChat />}
        />
        <Route
          path="/individual-chat/forward-message"
          element={<SelectMsgForwardUsers />}
        />
        <Route
          path="/individual-chat/next"
          element={<IndividualChatSecond />}
        />
        <Route
          path="/individual-chat/third"
          element={<IndividualChatThird />}
        />
        <Route path="/new-chat" element={<NewChat />} />
        <Route
          path="/create-new-group/step-first"
          element={<CreateNewGroup />}
        />
        <Route
          path="/create-new-group/step-final"
          element={<CreateNewGroupStepTwo />}
        />
        <Route path="/group-chat/creator" element={<GroupChatCreator />} />
        <Route path="/group-information" element={<GroupInfo />} />
        <Route
          path="/group-information/add-new-member"
          element={<AddNewMember />}
        />
        <Route path="/group-chat/camera" element={<CameraGroupChat />} />
        <Route path="/view-post" element={<ShowPost />} />
        <Route path="/show-post/pick-chats" element={<ShareWith />} />
        <Route path="/temp-users-chats" element={<TempChatPage />} />
      </Routes>
    </>
  );
}
