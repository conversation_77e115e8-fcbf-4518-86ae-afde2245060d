import React, { useState, useEffect } from "react";
import { useDrag } from "react-dnd";
import { workOutGroup } from "./constants";
import KeyboardArrowDownIcon from "@mui/icons-material/KeyboardArrowDown";
import KeyboardArrowUpIcon from "@mui/icons-material/KeyboardArrowUp";
import { styled, alpha } from "@mui/material/styles";
import InputBase from "@mui/material/InputBase";
import SearchIcon from "@mui/icons-material/Search";
import { Button, Card, Dialog, DialogActions, DialogTitle, Grid, Menu, MenuItem, TextField } from "@mui/material";
import AddTraningBlockLibrary from "./AddTrainingBlockLibrary";
import EditTrainingBlockLibrary from "./EditTrainingBlockLibrary";
import AddTrainingBlock from "./AddTrainingBlock";
import { useDispatch, useSelector } from "react-redux";
import {
  getAllTrainingBlocksLibrary,
  getAllTraningBlocksbyLibraryId,
  getTrainingPlanMasterMappingByTrainingId,
  handleCreateTraningBlockDialogState,
  handleEditTrainingBlockDialogState,
  handlecurrentTrainingBlockData,
  updateColumnsinTrainingData,
  updateLastColumnDayInRow,
} from "../../store/slices/MultiTrainingBlocksSlice";
import { Dropdown, Space } from "antd";
import { IconAntennaBars1 } from "@tabler/icons";
import {
  coppastTrainingBlock,
  deleteTrainingBlockLibrary,
  handleDeleteTraningBlock,
  handleEditTraningBlock,
  pasteTrainingBlockLibrary,
} from "../../API/api-endpoint";

import Box from "@mui/material/Box";
import Typography from "@mui/material/Typography";
import Modal from "@mui/material/Modal";
import { Textarea } from "@mantine/core";
import TextArea from "antd/es/input/TextArea";
import axios from "axios";
import Swal from "sweetalert2";
import { heIL } from "@mui/x-date-pickers";

let URL = "http://localhost:3000";

const Modelstyle = {
  position: "absolute",
  top: "50%",
  left: "50%",
  transform: "translate(-50%, -50%)",
  width: 400,
  bgcolor: "background.paper",
  boxShadow: 24,
  p: 4,
};

const Search = styled("div")(({ theme }) => ({
  position: "relative",
  border: "1px solid black",
  backgroundColor: alpha(theme.palette.common.white, 0.15),
  "&:hover": {
    backgroundColor: alpha(theme.palette.common.white, 0.25),
  },
  width: "100%",
}));

const SearchIconWrapper = styled("div")(({ theme }) => ({
  padding: theme.spacing(0, 2),
  height: "100%",
  position: "absolute",
  pointerEvents: "none",
  display: "flex",
  alignItems: "center",
  justifyContent: "center",
}));

const StyledInputBase = styled(InputBase)(({ theme }) => ({
  color: "inherit",
  "& .MuiInputBase-input": {
    padding: theme.spacing(1, 1, 1, 0),
    // vertical padding + font size from searchIcon
    paddingLeft: `calc(1em + ${theme.spacing(4)})`,
    transition: theme.transitions.create("width"),
    width: "100%",
    [theme.breakpoints.up("md")]: {
      width: "20ch",
    },
  },
}));

const DraggableItem = ({
  blocks,
  setOpen,
  setTrainingBlock,
  setUpdateTraningBlockVal,
  selectedTrainingBlockLibrary,
  updateTraningBlockVal,
  library, setIsOpenDeletedBox
}) => {
  console.log(selectedTrainingBlockLibrary.libraryID, "selectedTrainingBlockLibrary");

  const [anchorEl, setAnchorEl] = useState(null);
  const [selectedtrainingBlock, setSelectedtrainingBlock] = useState(null);
  console.log("blocks", blocks["training-block-name"], "selectedtrainingBlock", selectedtrainingBlock);

  const anchorElopen = Boolean(anchorEl);
  const handleanchorElClick = (event) => {
    setAnchorEl(event.currentTarget);
  };
  const handleanchorElClose = (id) => {
    setAnchorEl(null);
  };
  const handleCopy = (id) => {
    setAnchorEl(null);
    localStorage.setItem("id", id)
    setSelectedtrainingBlock(id)
  };
  const handleanchorPasteElClose = async (id) => {
    // setAnchorEl(null);
    // setSelectedtrainingBlock(id)
    let blockId = localStorage.getItem("id")
    let data = {
      trainingBlockId: blockId,
      libraryid: selectedTrainingBlockLibrary.libraryID
    }
    const response = await coppastTrainingBlock(data)
    console.log("response", response);
    if (response.message ===
      "Training block copied successfully") {
      Swal.fire({
        title: "Success",
        text: "Training Block Paste Succesfully",
        icon: "success",
      });
      localStorage.removeItem("id")
      dispatch(
        getAllTraningBlocksbyLibraryId(library["training-block-library-id"])
      );
    }

  };

  let dispatch = useDispatch();

  const handleSetBlock = () => {
    setAnchorEl(null);
    setOpen(true);
    setTrainingBlock(blocks);
    setUpdateTraningBlockVal({
      ...updateTraningBlockVal,
      trainingBlockName: blocks["training-block-name"],
      trainingBlockDescription: blocks["training-block-description"],
      trainingBlockLibraryId: blocks["training-block-library-id"],
      tags: blocks["tags"],
      createdBy: blocks["created-by"],
      status: blocks["status"],
    });
  };
  const handleDeleteTraningBlockFun = () => {
    const data = handleDeleteTraningBlock(blocks["training-block-id"]);

    data
      .then((result) => {
        console.log(result, "res");
        if (result?.message === "Training block deleted successfully") {
          setIsOpenDeletedBox({ open: false, message: "", addfunction: "" })
          Swal.fire({
            title: "Success",
            text: "Training Block Deleted Successfully",
            icon: "success",
          });
          dispatch(
            getAllTraningBlocksbyLibraryId(library["training-block-library-id"])
          );
        }
      })
      .catch((error) => {
        console.error(error);
      });
  };
  let newObject = {
    ...blocks,
    id: blocks["training-block-id"],
    type: "BLOCK",
    component: {
      type: blocks["training-block-name"],
      content: blocks["training-block-name"],
    },
    isHidden: false,
  };

  const [, ref] = useDrag({
    item: newObject,
  });

  const handleCurrentTrainingBlock = () => {
    dispatch(
      handlecurrentTrainingBlockData({
        id: blocks["training-block-id"],
        name: blocks["training-block-name"],
      })
    );
    dispatch(
      getTrainingPlanMasterMappingByTrainingId(blocks["training-block-id"])
    );

    //its update the layout when select any other training plan
    setTimeout(() => {
      dispatch(updateColumnsinTrainingData());
      dispatch(updateLastColumnDayInRow());
    }, 300);
  };
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const timer = setTimeout(() => {
      setIsVisible(true);
    }, 500); // 3 seconds

    return () => clearTimeout(timer); // cleanup on unmount
  }, []);

  if (!isVisible) return null; 

  return (
    <div
      //ref={ref}
      style={{fontSize:"13px",backgroundColor:"white",borderColor:"white"}}
      className="bg-[white] p-2 m-2 pl-2 ml-4 pt-3 text-gray-600 cursor-pointer"
    >
      <div
        style={{ display: "flex", justifyContent: "space-between" }}
        onClick={handleCurrentTrainingBlock}
      >

        {/*
     // show traning block list in sidebar 
    */}
        <span>{blocks["training-block-name"]}</span>
        <IconAntennaBars1
          style={{ transform: "rotate(90deg)",color:"#E67E22" }}
          id="basic-button"
          aria-controls={anchorElopen ? "basic-menu" : undefined}
          aria-haspopup="true"
          aria-expanded={anchorElopen ? "true" : undefined}
          onClick={handleanchorElClick}
        />
      </div>
      <Menu
        id="basic-menu"
        anchorEl={anchorEl}
        open={anchorElopen}
        onClose={handleanchorElClose}
        MenuListProps={{
          "aria-labelledby": "basic-button",
        }}
      >
        <MenuItem onClick={() => handleCopy(blocks["training-block-id"])}>Copy Training Block</MenuItem>
        <MenuItem onClick={handleanchorPasteElClose}>Paste Training Block</MenuItem>
        <MenuItem
          onClick={handleSetBlock}
        >Edit Training Block</MenuItem>
        <MenuItem
          onClick={() => setIsOpenDeletedBox({ open: true, message: "training block", addfunction: handleDeleteTraningBlockFun })}
        >Delete Training Block</MenuItem>
      </Menu>
    </div>
  );
};

const DragOnlyList = ({
  dispatch,
  library,
  isExpanded,
  searchQuery,
  setOpen,
  setTrainingBlock,
  setUpdateTraningBlockVal,
  updateTraningBlockVal,
  selectedTrainingBlockLibrary,
  setIsOpenDeletedBox
}) => {
  let trainingBlocksbyLibraryId = useSelector(
    (state) => state.MultiTrainingBlocksSlice.trainingBlocksbyLibraryId
  );
  console.log("trainingBlocksbyLibraryId", trainingBlocksbyLibraryId);
  // useEffect(() => {
  //   dispatch(
  //     getAllTraningBlocksbyLibraryId(library["training-block-library-id"])
  //   );
  // }, [dispatch, library, isExpanded]);

  if (!isExpanded) {
    return null;
  }

  //console.log(library["training-block-library-id"]);
  // const filteredWorkouts = library.workouts.filter((workout) => {
  //   if (
  //     workout.component &&
  //     workout.component.content &&
  //     searchQuery &&
  //     searchQuery.trim() !== "" // Check if searchQuery is not empty or whitespace
  //   ) {
  //     return workout.component.content
  //       .toLowerCase()
  //       .includes(searchQuery.toLowerCase());
  //   }
  //   return true; // If searchQuery is empty or undefined, show all workouts
  // });

  return (
    <div>
      {/* <DraggableItem key={library["training-block-library-id"]} /> */}
      {trainingBlocksbyLibraryId.map((blocks) => (
        <>
          <DraggableItem
            key={blocks["training-block-id"]}
            setIsOpenDeletedBox={setIsOpenDeletedBox}
            blocks={blocks}
            library={library}
            setOpen={setOpen}
            selectedTrainingBlockLibrary={selectedTrainingBlockLibrary}
            setTrainingBlock={setTrainingBlock}
            setUpdateTraningBlockVal={setUpdateTraningBlockVal}
            updateTraningBlockVal={updateTraningBlockVal}
          />
        </>
      ))}
    </div>
  );
};

const SideBar = (props) => {
  const { setTogglesidebar } = props;
  let dispatch = useDispatch();
  let trainingBlockLibrary = useSelector(
    (state) => state.MultiTrainingBlocksSlice.trainingBlockLibrary
  );
  console.log("trainingBlockLibrary", trainingBlockLibrary);
  // Use your workOutGroup data here
  const [list, setList] = useState(trainingBlockLibrary); //workOutGroup  workout nu structure jevu che avu strcuture traningblocklib krvu pdse drag and drop mate
  const [expandedBlock, setExpandedBlock] = useState(null);
  const [searchQuery, setSearchQuery] = useState("");
  const [isLibraryUpdated, setisLibraryUpdated] = useState(false);
  const [liabraryIdForCopy, setLibraryIdForCopy] = useState();
  const [isOpenDeletedBox, setIsOpenDeletedBox] = useState({ open: false, message: "", addfunction: "" });

  const [selectedTrainingBlockLibrary, setSelectedTrainingBlockLibrary] =
    useState({});
  console.log("expandedBlock", selectedTrainingBlockLibrary);

  const [open, setOpen] = useState(false);
  const [trainingBlock, setTrainingBlock] = useState();
  const initialTrainingValues = {
    trainingBlockName: "",
    trainingBlockDescription: "",
    trainingBlockLibraryId: "",
    tags: "",
    createdBy: "",
    status: "",
  };
  const handleUpadteBlockChange = (e) => {
    const { name, value } = e.target;
    const trimmedValue = value.trimStart(); // Trim white space only at the beginning

    setUpdateTraningBlockVal({
      ...updateTraningBlockVal,
      [name]: trimmedValue,
    });
  };
  const [updateTraningBlockVal, setUpdateTraningBlockVal] = useState(
    initialTrainingValues
  );
  const handleClose = () => setOpen(false);

  const handleEditTraningBlockFun = () => {
    console.log("updateTraningBlockVal", updateTraningBlockVal);
    if (updateTraningBlockVal?.trainingBlockName && updateTraningBlockVal?.trainingBlockDescription) {
      let body = updateTraningBlockVal;
      const data = handleEditTraningBlock(
        trainingBlock["training-block-id"],
        body
      );

      data
        .then((result) => {
          console.log(result, "res");
          if (result?.message === "Training block updated successfully") {
            handleClose();
            Swal.fire({
              title: "Success",
              text: result.message,
              icon: "success",
            });
            dispatch(
              getAllTraningBlocksbyLibraryId(updateTraningBlockVal.trainingBlockLibraryId)
            );
          }
        })
        .catch((error) => {
          console.error(error);
        });

    }
  };

  const handleDeleteTraningBlockFun = () => {
    const data = handleDeleteTraningBlock(trainingBlock["training-block-id"]);

    data
      .then((result) => {
        console.log(result, "res");
        if (result?.message === "Training block deleted successfully") {
          handleClose();
        }
      })
      .catch((error) => {
        console.error(error);
      });
  };

  // const filteredList = list.filter(
  //   (block) =>
  //     block.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
  //     block.workouts.some((workout) =>
  //       workout.component.content
  //         .toLowerCase()
  //         .includes(searchQuery.toLowerCase())
  //     )
  // );

  const handleOpenCreateTrainingBlockDialog = () => {
    dispatch(
      handleCreateTraningBlockDialogState({
        bool: true,
        libraryID: selectedTrainingBlockLibrary.libraryID,
      })
    );
  };

  const handleOpenEditTrainingBlockLibraryDialog = () => {
    dispatch(
      handleEditTrainingBlockDialogState({
        bool: true,
        library: selectedTrainingBlockLibrary,
      })
    );
  };


  const handleDeleteTrainingBlockLibrary = async () => {
    //selectedTrainingBlock.libraryID
    let result = await deleteTrainingBlockLibrary(
      selectedTrainingBlockLibrary.libraryID
    );
    if (result.message == "Training block library deleted successfully") {
      setIsOpenDeletedBox({ open: false, message: "", addfunction: "" })
      setisLibraryUpdated(true);
      Swal.fire({
        title: "Success",
        text: result.message,
        icon: "success",
      });
    }
    setTimeout(() => {
      setisLibraryUpdated(false);
    }, 300);
  };
  const handlePasteTrainingBlockLibrary = async () => {
    //selectedTrainingBlock.libraryID
    let data = {
      sourceLibraryId: liabraryIdForCopy,
      targetLibraryId: selectedTrainingBlockLibrary?.libraryID,
      operations: "copy"
    }
    let result = await pasteTrainingBlockLibrary(
      data
    );
    console.log("result", result);
    if (result.message == "Training block created successfully") {
      Swal.fire({
        title: "Success",
        text: "Pasted Successfully",
        icon: "success",
      });
      // setLibraryIdForCopy("")
      setisLibraryUpdated(true);
    }
    setTimeout(() => {
      setisLibraryUpdated(false);
    }, 300);
  };

  const items = [
    {
      label: (
        <div
          onClick={() => setLibraryIdForCopy(selectedTrainingBlockLibrary?.libraryID)}
        >
          Copy Training Block Library
        </div>
      ),
      key: "0",
    },
    liabraryIdForCopy &&
    {
      label: liabraryIdForCopy ? (
        <div onClick={() => handlePasteTrainingBlockLibrary()}>
          Paste Training Block Library
        </div>
      ) : null,
      key: "3",
    },
    {
      label: (
        <div
          onClick={() => handleOpenEditTrainingBlockLibraryDialog()}
        >
          Edit Training Block Library
        </div>
      ),
      key: "4",
    },
    {
      label: (
        <div
          onClick={() => setIsOpenDeletedBox({ open: true, message: "training block library", addfunction: handleDeleteTrainingBlockLibrary })}
        >
          Delete Training Block Library
        </div>
      ),
      key: "5",
    },
    {
      label: (
        <div onClick={() => handleOpenCreateTrainingBlockDialog()}>
          Add Training Blocks
        </div>
      ),
      key: "6",
    },
  ];

  const handleBlockClick = (blockName) => {
    setExpandedBlock((prevBlock) =>
      prevBlock === blockName ? null : blockName
    );
  };

  useEffect(() => {
    dispatch(getAllTrainingBlocksLibrary());
  }, [isLibraryUpdated]);

  useEffect(() => {
    localStorage.setItem("training_page", "training-blocks-1 page visited");
  }, []);
  const filteredUsers = trainingBlockLibrary.filter((user) => {
    const fullName = user["training-block-library-name"];
    return fullName.toLowerCase().includes(searchQuery?.toLowerCase());
  });
  return (
    <div className="h-[100%] ">
      <AddTraningBlockLibrary
        setisLibraryUpdated={setisLibraryUpdated}
        setTogglesidebar={setTogglesidebar}
      />
      <AddTrainingBlock />
      <EditTrainingBlockLibrary setisLibraryUpdated={setisLibraryUpdated} />
      <Search className="mb-2"  style={{backgroundColor:"white",borderColor:"white"}}>
        <SearchIconWrapper>
          <SearchIcon style={{color:"#E67E22"}} />
        </SearchIconWrapper>
        <StyledInputBase
          placeholder="Search…"
          inputProps={{ "aria-label": "search" }}
          value={searchQuery}
          onChange={(e) => {
            setSearchQuery(e.target.value);
            setExpandedBlock(null); // Reset expandedBlock when search query changes
          }}
        />
      </Search>
      <div>
        {filteredUsers !== undefined &&
          filteredUsers?.map((library, index) => (
            <Grid container className="ml-2" style={{ alignItems: "center",borderBottom:"1px solid lightGray"  }}>
              <Grid item xs={12} sm={9}>
                <h2 style={{ fontSize: "14px" }}>{library["training-block-library-name"]}</h2>
              </Grid>
              <Grid item xs={12} sm={2}>
                <span
                  className="p-2"
                  onClick={() => {
                    dispatch(
                      getAllTraningBlocksbyLibraryId()
                    )

                    setSelectedTrainingBlockLibrary({
                      // ...library,
                      libraryID: library["training-block-library-id"],
                      isEditForm: true,
                    });
                    dispatch(
                      getAllTraningBlocksbyLibraryId(library["training-block-library-id"])
                    );
                    handleBlockClick(library["training-block-library-id"])
                  }
                  }
                >
                  {" "}
                  {expandedBlock === library["training-block-library-id"] ? (
                    <KeyboardArrowUpIcon style={{color:"#E67E22"}}/>
                  ) : (
                    <KeyboardArrowDownIcon style={{color:"#E67E22"}}/>
                  )}
                </span>
              </Grid>
              <Grid item xs={12} sm={1}>
                <span className="p-2">
                  <Dropdown
                  
                    menu={{
                      items,
                    }}
                    trigger={["click"]}
                    onOpenChange={() => {
                      setSelectedTrainingBlockLibrary({
                        ...library,
                        libraryID: library["training-block-library-id"],
                        isEditForm: true,
                      });
                    }}
                  >
                    <div>
                      <Space>
                        <IconAntennaBars1
                          style={{ transform: "rotate(90deg)" ,color:"#E67E22"}}
                        />
                      </Space>
                    </div>
                  </Dropdown>
                </span>
              </Grid>
              <Grid item xs={12} sm={12}>
                <DragOnlyList
                  setIsOpenDeletedBox={setIsOpenDeletedBox}
                  dispatch={dispatch}
                  setOpen={setOpen}
                  selectedTrainingBlockLibrary={selectedTrainingBlockLibrary}
                  setTrainingBlock={setTrainingBlock}
                  setUpdateTraningBlockVal={setUpdateTraningBlockVal}
                  updateTraningBlockVal={updateTraningBlockVal}
                  library={library} //here we have to share a training blocks details
                  isExpanded={
                    expandedBlock === library["training-block-library-id"]
                  }
                />
              </Grid>

            </Grid>

          ))}
        <Dialog
          open={isOpenDeletedBox?.open}
          onClose={() => setIsOpenDeletedBox({ open: false, message: "", addfunction: "" })}
          aria-labelledby="alert-dialog-title"
          aria-describedby="alert-dialog-description"
        >
          <DialogTitle id="alert-dialog-title">
            {`Are you sure want to delete this ${isOpenDeletedBox?.message}`}
          </DialogTitle>

          <DialogActions>
            <Button style={{color:"white"}} onClick={()=>setIsOpenDeletedBox({open:false,message:"",addfunction:""})}>No</Button>
            <Button onClick={isOpenDeletedBox?.addfunction} style={{backgroundColor:"#E67E22"}} variant="contained" autoFocus>
              Yes
            </Button>
          </DialogActions>
        </Dialog>
        <Modal
          open={open}
          onClose={handleClose}
          aria-labelledby="modal-modal-title"
          aria-describedby="modal-modal-description"
        >
          <Box sx={Modelstyle}>
            <Typography variant="h5">Edit Training Block</Typography>
            <Box sx={{ marginTop: "10px" }}>
              <lable>Training Block Name</lable> <br />
              <TextField
                fullWidth
                name="trainingBlockName"
                value={updateTraningBlockVal.trainingBlockName}
                onChange={handleUpadteBlockChange}
              />
            </Box>
            <br />
            <Box sx={{ marginTop: "5px" }}>
              <lable>Training Block Description</lable>
              <TextArea
                name="trainingBlockDescription"
                value={updateTraningBlockVal.trainingBlockDescription}
                onChange={handleUpadteBlockChange}
              />
            </Box>

            <div className="mt-5">
              {trainingBlock &&
                trainingBlock["training-block-name"] ===
                updateTraningBlockVal.trainingBlockName &&
                trainingBlock["training-block-description"] ===
                updateTraningBlockVal.trainingBlockDescription ? (
                <Button style={{color:"white"}} variant="outlined" onClick={handleClose}>
                  Cancel
                </Button>
              ) : (
                <Button
                  variant="outlined"
                  disabled={!updateTraningBlockVal?.trainingBlockName && !updateTraningBlockVal?.trainingBlockDescription}
                  style={{ backgroundColor: "#2563EB", color: "white" }}
                  onClick={handleEditTraningBlockFun}
                >
                  Edit
                </Button>
              )}

              {/*
             <Button
                variant="contained"
                color="error"
                sx={{ marginLeft: "20px" }}
                onClick={handleDeleteTraningBlockFun}
              >
                Delete
              </Button>
            */}
            </div>
          </Box>
        </Modal>
      </div>
    </div>
  );
};

export default SideBar;
