import React from "react";
import { Bar } from "react-chartjs-2";
import {
    Chart as ChartJS,
    CategoryScale,
    LinearScale,
    BarElement,
    Title,
    Tooltip,
    Legend,
} from "chart.js";
import { parseSpeed } from "../../../../utils/metricConversion";

// Register Chart.js components
ChartJS.register(CategoryScale, LinearScale, BarElement, Title, Tooltip, Legend);

const SpeedGraph = ({ activity, data }) => {
    const { plannedSpeed, actualSpeed, speedunit } = data;
    console.log("activity --> ", activity);

    // Convert speed values
    const planned = parseSpeed(plannedSpeed);
    const actual = parseSpeed(actualSpeed);

    // Chart Data
    const chartData = {
        labels: ["Speed"],
        datasets: [
            {
                label: "Planned",
                data: [planned],
                backgroundColor: "rgba(54, 162, 235, 0.7)",
                barThickness: 20
            },
            {
                label: "Actual",
                data: [actual],
                backgroundColor: "rgba(255, 99, 132, 0.7)",
                barThickness: 20
            },
        ],
    };

    // Chart Options
    const options = {
        responsive: true,
        plugins: {
            title: {
                display: true,
                text: `Speed [planned(${planned}${speedunit}) v/s actual(${planned}${speedunit})]`,
            },
            tooltip: {
                callbacks: {
                    label: function (context) {
                        return `${context.raw} ${speedunit}`;
                    },
                },
            },
        },
        scales: {
            x: {
                barPercentage: 0.8, // Adjust bar width (0.1 to 1.0)
                categoryPercentage: 0.6, // Adjust category spacing (0.1 to 1.0)
            },
            y: {
                beginAtZero: true,
                suggestedMax: 50, // Adjust based on expected values
                title: {
                    display: true,
                    text: `Speed (${speedunit})`,
                },
            },
        },
    };

    return <Bar data={chartData} options={options} />;
};

export default SpeedGraph;
