import NewGroupMemberNav from '../../components/navbar/NewGroupMemberNav';
import { Avatar, Box, Button, Checkbox, Paper, Tabs, Typography } from '@mui/material'
import React, { useEffect, useMemo, useState } from 'react'
import ArrowOutwardSharpIcon from '@mui/icons-material/ArrowOutwardSharp';
import "../../styles/chatlanding.css"
import "./../../styles/newchat/newChat.css"
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import CheckCircleOutlineIcon from '@mui/icons-material/CheckCircleOutline';
import EastOutlinedIcon from '@mui/icons-material/EastOutlined';
import { useDispatch, useSelector } from 'react-redux';
import { getAllUsersAction, storeUsersToCreateGroup } from '../../redux/action/usersAction';
import { storeOpenedGroupInfoAction } from '../../redux/action/groupAction';
import SearchIcon from '@mui/icons-material/Search';
import { URL } from '../../../../API/api-endpoint';
import Swal from "sweetalert2";
import axios from 'axios';
import {
    get,
    getDatabase,
    push,
    ref,
    serverTimestamp,
    set,
    update,
  } from "firebase/database";

import {
    getStorage,
    ref as storageRef,
    uploadBytes,
    getDownloadURL,
} from "firebase/storage";

export default function AddNewGroupMember ({ setOpenPageOne }) {

    const paper = {
        padding: "0rem",
        // maxWidth: '33vw',
        overflowY : "scroll",
    };

    const dispatch = useDispatch()

    const [profilePicture, setProfilePicture] = useState(null);
    const [groupName, setGroupName] = useState("");
    const [groupDescription, setGroupDescription] = useState("");

    const { allUsers, storedUserToCreateGroup } = useSelector(state => state.users)
    const { currentUser } = useSelector(state => state.auth)
    const [filteredUsers, seTfilteredUsers] = useState([])
    const [searchTerm, setSearchTerm] = useState('');
    const roleID = localStorage.getItem("roleID")

    const [email, setEmail] = useState("");
    const [selectedEmails, setSelectedEmails] = useState([]);

    const [checked, setChecked] = useState(false);
    const [selectedProfiles, setSelectedProfile] = useState(storedUserToCreateGroup)
    const { openedGroupData } = useSelector((state) => state.group);

    const addToSelected = user => {
        const copiedSelected = [...selectedProfiles];

        const userIndex = copiedSelected.findIndex(obj => obj.uid === user.uid);
        if (userIndex !== -1) {
            copiedSelected.splice(userIndex, 1);
        } else {
            copiedSelected.push(user);
        }

        setSelectedProfile(copiedSelected);
    };

    const removeSelected = uid => {
        const arr = [...selectedProfiles]

        const index = arr.findIndex(obj => obj.uid === uid);
        if (index !== -1) {
            arr.splice(index, 1);
        }
        setSelectedProfile(arr);
        return;
    }

    const filterAndSortData = searchTerm => {
        const filteredData = allUsers.filter(obj =>
            obj.displayName.toLowerCase().includes(searchTerm.toLowerCase())
        );

        const sortedData = [...filteredData].sort((a, b) =>
            a.displayName.localeCompare(b.displayName)
        );

        return sortedData;
    };

    const filteredAndSortedData = useMemo(() => {
        const filteredData = allUsers?.filter(obj =>
            obj.displayName?.toLowerCase().includes(searchTerm?.toLowerCase())
        );

        const sortedData = [...filteredData].sort((a, b) =>
            a.displayName.localeCompare(b.displayName)
        );

        return sortedData;
    }, [allUsers, searchTerm]);

    console.log("allUsers", allUsers, filteredAndSortedData);

    const handleSearchChange = event => {
        setSearchTerm(event.target.value);
    };
    console.log(selectedProfiles)

    const uploadImageAndGetLink = async (file) => {
        const storage = getStorage();
        const storageReference = storageRef(storage, "images/" + file.name);
    
        const database = getDatabase();
        const databaseReference = ref(database, "images");
    
        try {
          await uploadBytes(storageReference, file);
    
          const downloadURL = await getDownloadURL(storageReference);
    
          console.log("Image uploaded and URL saved successfully.");
    
          return downloadURL;
        } catch (error) {
          console.error("Error uploading image:", error);
          throw error;
        }
      };
    // const handleAddMemberInGroup = async () => {
    //     const usersUIDs = storedUserToCreateGroup
    //       .filter((user) => user.uid)
    //       .map((user) => user.uid);
        
    //     const db = getDatabase();
    //     const newRef = push(ref(db, "groups"));
    //     const groupId = newRef.key;
      
    //     const downloadURL =
    //       profilePicture && (await uploadImageAndGetLink(profilePicture));
      
    //     function formatDate(date) {
    //       const options = { year: "numeric", month: "2-digit", day: "2-digit" };
    //       const dateString = date.toLocaleDateString("en-US", options);
      
    //       const timeOptions = { hour: "numeric", minute: "2-digit", hour12: true };
    //       const timeString = date.toLocaleTimeString("en-US", timeOptions);
      
    //       return `${dateString} ${timeString}`;
    //     }
      
    //     const currentDate = new Date();
    //     const formattedDate = formatDate(currentDate);
    //     const openedGroup = openedGroupData.groupDetail
      
    //     const apiData = {
    //         "groupId": openedGroup.id,
    //         "displayName": openedGroup.channelName
    //     };
        
    //     try {
    //       const authToken = localStorage.getItem("token");
          
    //       const response = await axios.post(`${URL}/subscribe-group`, apiData, {
    //         headers: {
    //           'Content-Type': 'application/json',
    //           'Authorization': `${authToken}`
    //         }
    //       });
          
    //       // dispatch(SetGroupData({
    //       //   "GroupId": response.data.data.id
    //       // }));
          
    //       if (response.status === 200) {
    //         console.log("Added to the group successful:", response.data);
      
    //         const groupData = {
    //           groupId,
    //           createdById: currentUser.uid,
    //           createdByName: currentUser.displayName,
    //           createdAt: formattedDate,
    //           groupProfileURL: downloadURL,
    //           groupName: groupName,
    //           groupDescription: groupDescription,
    //           groupMembers: storedUserToCreateGroup.some(
    //             (obj) => obj.uid === currentUser.uid
    //           )
    //             ? usersUIDs
    //             : [currentUser.uid, ...usersUIDs],
    //         };
      
    //         await set(ref(db, `groups/${groupId}`), groupData);
      
    //         storedUserToCreateGroup.map(async (item) => {
    //           const grpRef = ref(db, "usersGroups/" + item.uid);
    //           const grpSnapshot = await get(grpRef);
    //           const grpPreData = grpSnapshot.val();
    //           const existingGrps = grpPreData || [];
      
    //           await set(ref(db, `usersGroups/${item.uid}`), [
    //             ...existingGrps,
    //             groupData,
    //           ]);
    //         });
      
    //         storedUserToCreateGroup.map(async (item) => {
    //           const userChatsRef = ref(db, `userChats/${item.uid}`);
      
    //           await update(userChatsRef, {
    //             [`${groupId}/chatType`]: "group",
    //             [`${groupId}/groupId`]: groupId,
    //             [`${groupId}/date`]: serverTimestamp(),
    //             [`${groupId}/isMsgReqAccepted`]: true,
    //           });
    //         });
      
    //         if (selectedEmails.length > 0) {
    //           console.log("groupId", groupId);
              
    //           const inviteData = {
    //             emails: selectedEmails,
    //             groupId: openedGroup.id,
    //           };
    //           const authToken = localStorage.getItem("token");
    //           await axios.post(`${URL}/group-invite`, inviteData, {
    //             headers: {
    //               'Content-Type': 'application/json',
    //               'Authorization': `${authToken}`
    //             }
    //           });
    //         }
    
    //         Swal.fire({
    //           title: "Success",
    //           text: "Group invitation send!",
    //           icon: "success",
    //         });
    
    //         setOpenPageOne("home");
    //       } else {
    //         console.log("Failed to create group via API:", response.statusText);
    //       }
    //     } catch (error) {
    //       console.log("Error in group creation:", error);
    //     }
    // };
    
  const handleAddMemberInGroup = async () => {
        // const usersUIDs = storedUserToCreateGroup
        //   .filter((user) => user.uid)
        //   .map((user) => user.uid);
        
        // const db = getDatabase();
        // const newRef = push(ref(db, "groups"));
        // const groupId = newRef.key;
      
        // const downloadURL =
        //   profilePicture && (await uploadImageAndGetLink(profilePicture));
      
        // function formatDate(date) {
        //   const options = { year: "numeric", month: "2-digit", day: "2-digit" };
        //   const dateString = date.toLocaleDateString("en-US", options);
      
        //   const timeOptions = { hour: "numeric", minute: "2-digit", hour12: true };
        //   const timeString = date.toLocaleTimeString("en-US", timeOptions);
      
        //   return `${dateString} ${timeString}`;
        // }
      
        // const currentDate = new Date();
        // const formattedDate = formatDate(currentDate);
        const openedGroup = openedGroupData.groupDetail
      
        // const apiData = {
        //     "groupId": openedGroup.id,
        //     "emails": openedGroup.channelName
        // };
        
        // try {
        //   const authToken = localStorage.getItem("token");
          
        //   const response = await axios.post(`${URL}/group-invite`, apiData, {
        //     headers: {
        //       'Content-Type': 'application/json',
        //       'Authorization': `${authToken}`
        //     }
        //   });
          
        //   // dispatch(SetGroupData({
        //   //   "GroupId": response.data.data.id
        //   // }));
          
        //   if (response.status === 200) {
        //     console.log("Added to the group successful:", response.data);
      
            // const groupData = {
            //   groupId,
            //   createdById: currentUser.uid,
            //   createdByName: currentUser.displayName,
            //   createdAt: formattedDate,
            //   groupProfileURL: downloadURL,
            //   groupName: groupName,
            //   groupDescription: groupDescription,
            //   groupMembers: storedUserToCreateGroup.some(
            //     (obj) => obj.uid === currentUser.uid
            //   )
            //     ? usersUIDs
            //     : [currentUser.uid, ...usersUIDs],
            // };
      
            // await set(ref(db, `groups/${groupId}`), groupData);
      
            // storedUserToCreateGroup.map(async (item) => {
            //   const grpRef = ref(db, "usersGroups/" + item.uid);
            //   const grpSnapshot = await get(grpRef);
            //   const grpPreData = grpSnapshot.val();
            //   const existingGrps = grpPreData || [];
      
            //   await set(ref(db, `usersGroups/${item.uid}`), [
            //     ...existingGrps,
            //     groupData,
            //   ]);
            // });
      
            // storedUserToCreateGroup.map(async (item) => {
            //   const userChatsRef = ref(db, `userChats/${item.uid}`);
      
            //   await update(userChatsRef, {
            //     [`${groupId}/chatType`]: "group",
            //     [`${groupId}/groupId`]: groupId,
            //     [`${groupId}/date`]: serverTimestamp(),
            //     [`${groupId}/isMsgReqAccepted`]: true,
            //   });
            // });
      
            if (selectedEmails.length > 0) {
            //   console.log("groupId", groupId);
              
              const inviteData = {
                emails: selectedEmails,
                groupId: openedGroup.id,
              };
              const authToken = localStorage.getItem("token");
              await axios.post(`${URL}/group-invite`, inviteData, {
                headers: {
                  'Content-Type': 'application/json',
                  'Authorization': `${authToken}`
                }
              });
            }
    
            Swal.fire({
              title: "Success",
              text: "Group invitation send!",
              icon: "success",
            });
    
            setOpenPageOne("home");
        //   } else {
        //     console.log("Failed to create group via API:", response.statusText);
        //   }
        // } catch (error) {
        //   console.log("Error in group creation:", error);
        // }
    };

    console.log("setOpenPageOne",setOpenPageOne);

    useEffect(() => {
        dispatch(getAllUsersAction(roleID))
    }, [])

    const addEmail = () => {
    if (email) {
        setSelectedEmails([...selectedEmails, email]);
        setEmail("");
    }
    };

    const removeEmail = (emailToRemove) => {
    setSelectedEmails(selectedEmails.filter((email) => email !== emailToRemove));
    };


    return (
    <Box>
        <Paper className='addGroupMember chatPaper' sx={{ flexGrow: 1 }} elevation={10} style={paper}>
        <NewGroupMemberNav setOpenPageOne={setOpenPageOne} />
        {/* <Box sx={{ backgroundColor: "#F5F5F5", padding: "8px", display: "flex", justifyContent: 'space-between', alignItems: 'center',marginTop: "5px" }}> */}
                {/* <Box sx={{ width: "100%", display: "flex", alignItems: 'center', padding: '6px', overflowX: "auto" }}>

                    {
                        selectedProfiles.length === 0 && <Box
                            style={{ position: "relative", marginRight: "1.5rem", overflowX: "hidden" }}>
                            <Avatar sx={{ height: 58, width: 58 }} alt="Remy Sharp" src="https://cdn.pixabay.com/photo/2015/10/05/22/37/blank-profile-picture-973460_1280.png" />
                            <Typography className='selectects-avatar-name'>Buddy</Typography>
                        </Box>
                    }

                    <Box scroll style={{
                        display: 'flex',
                        scrollbarWidth: 'thin',
                        scrollbarColor: 'transparent transparent',
                        '-ms-overflow-style': 'none',
                        'scrollbar-width': 'thin',
                        '&::-webkit-scrollbar': {
                            width: '6px',
                        },
                        '&::-webkit-scrollbar-track': {
                            backgroundColor: 'transparent',
                        },
                        '&::-webkit-scrollbar-thumb': {
                            backgroundColor: 'transparent', 
                        },
                    }}>

                        {
                            selectedProfiles?.slice(0).reverse().map((user, index) => {
                                if (user.uid === currentUser.uid) return;
                                return <Box
                                    onClick={() => removeSelected(user.uid)}
                                    key={index} style={{ position: "relative", marginRight: "1.5rem", overflowX: "hidden" }}>
                                    <Avatar sx={{ height: 58, width: 58 }} alt={user?.displayName} src={`${URL}/static/public/userimages/${user?.photoURL}`} />
                                    <Typography className='selectects-avatar-name'>
                                        {user.displayName.length > 8 ? `${user.displayName.slice(0, 8)}...` : user.displayName}
                                    </Typography>
                                    <Box className="cross-button">
                                        <img height={7} width={7} src="/images/cross-icon.png" alt="remove" />
                                    </Box>
                                </Box>
                            })
                        }

                    </Box>
                </Box> */}
                {/* <ArrowOutwardSharpIcon sx={{ paddingRight: "0.4rem" }} /> */}
            {/* </Box> */}

            <div>
                <Typography className='select-buddies-text'>Send email invitations to add to your Group</Typography>
                <div className="email-input-container">
                <input
                    type="email"
                    placeholder="Enter email"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                />
                <button onClick={addEmail}>Add</button>
                </div>
                <div className="email-badge-container">
                {selectedEmails.map((email, index) => (
                    <span key={index} className="email-badge">
                    {email}
                    <button onClick={() => removeEmail(email)}>X</button>
                    </span>
                ))}
                </div>
            </div>

            {/* <Box>
                <Typography className='select-buddies-text'>Select Buddies to add to your Group</Typography>

                <Box style={{ position: 'relative', textAlign: "center" }}>
                    <input onChange={handleSearchChange} placeholder='Search Buddies' type="text" className='search-buddy-input' />
                    <Box className="search-icon-parent" style={{ textAlign: "center",backgroundColor: "#E67E22",color:'white' }}>
                        <SearchIcon />
                    </Box>
                </Box>
            </Box>
            {filteredAndSortedData?.length > 0 ? filteredAndSortedData?.map((item, index) => {
                if (item.uid === currentUser.uid) return;
                return <Box className="profile-row" onClick={() => {
                    addToSelected(item)
                    document.getElementById(`check-profile-${index}`).click()
                }} key={index}
                    sx={{ padding: "8px", paddingTop: "12px", display: "flex", justifyContent: 'space-between', alignItems: 'center' }}>
                    <Box sx={{ display: "flex", alignItems: 'center', padding: '6px' }}>
                        <Avatar  alt={item?.displayName} src={`${URL}/static/public/userimages/${item?.photoURL}`} />
                        <Box>
                            <Typography className='userName' fontWeight="fontWeightBold" sx={{ paddingLeft: '1.2rem', fontSize: '14px' }}>{item.displayName}</Typography>
                        </Box>
                    </Box>
                    <Checkbox
                        id={`check-profile-${index}`}
                        sx={{
                            color: "#F4F4F4",
                            '&.Mui-checked': {
                                color: '#E67E22',
                            }
                        }}
                        checked={selectedProfiles.some(obj => obj.uid === item.uid)}
                        icon={<CheckCircleOutlineIcon />}
                        checkedIcon={<CheckCircleIcon />}
                    />
                </Box>
            }) : ( <Box>
                <Typography className='userName' fontWeight="fontWeightBold" sx={{ paddingLeft: '3.0rem',paddingTop:"20px", fontSize: '14px' }}>No Data Found</Typography>
            </Box>)} */}
            <Box className="sticky-next-btn">
                <Button
                    disabled={!(selectedProfiles.length > 0 || selectedEmails.length > 0)} 
                    onClick={handleAddMemberInGroup}
                    style={{ boxShadow: "-2px 4px 10px rgba(0, 0, 0, 0.25)", fontSize: 10, letterSpacing: "0.16px", display: "flex" }} className={selectedProfiles.length > 0 || selectedEmails.length > 0 ? 'next-mui-btn-selected' : 'next-mui-btn'}>
                    Create
                    <EastOutlinedIcon sx={
                        selectedProfiles.length > 0
                            ? { fontSize: '14px', paddingLeft: '0.24rem', color: "white" }
                            : { fontSize: '14px', paddingLeft: '0.24rem', color: "#FFFFFF", }
                    } />
                </Button>
            </Box>
        </Paper>
    </Box>
    );
};
  