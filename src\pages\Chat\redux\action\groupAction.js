import { SET_OPENED_GROUP_MEMBERS, SET_OPENED_GROUP_INFO, SET_GROUP_DATA, GET_GROUP_INFO_FAIL, GET_GROUP_INFO_INIT, GET_GROUP_INFO_SUCCESS, GET_GROUP_MESSAGES_FAIL, GET_GROUP_MESSAGES_INIT, GET_GROUP_MESSAGES_SUCCESS } from "../constant/groupConstants";
import { getDatabase, onValue, ref } from "firebase/database";

export const getGroupInfoAction = (groupId) => async dispatch => {
    try {

        if (groupId === undefined) {
            dispatch({ type: GET_GROUP_INFO_SUCCESS, payload: undefined });
        } else {
            dispatch({ type: GET_GROUP_INFO_INIT })

            const db = getDatabase();
            const starCountRef = ref(db, `/groups/${groupId}`);

            const datetime = new Date().toLocaleString();
            await onValue(starCountRef, (snapshot) => {
                const data = snapshot.val();
                data && dispatch({ type: GET_GROUP_INFO_SUCCESS, payload: { ...data, datetime } })
            });
        }

    } catch (error) {
        dispatch({ type: GET_GROUP_INFO_FAIL, payload: error })
    }
}

export const storeOpenedGroupMembers = (foundGroup) => (dispatch) => {
    try {
        dispatch({ type: SET_OPENED_GROUP_MEMBERS, payload: foundGroup });
    } catch (error) {
        console.log("StoreOpenedGroupMembers error ",error);
    }
};

export const storeOpenedGroupInfoAction = (foundGroup) => (dispatch) => {
    try {
        dispatch({ type: SET_OPENED_GROUP_INFO, payload: foundGroup });
    } catch (error) {
        console.log("StoreOpenedGroupInfoAction error ",error);
    }
};

export const getGroupChatMessagesAction = (groupId) => async (dispatch) => {
    try {
        dispatch({ type: GET_GROUP_MESSAGES_INIT });

        const db = getDatabase();
        const starCountRef = ref(db, `/chats/${groupId}/messages`);

        await onValue(starCountRef, (snapshot) => {
            const data = snapshot.val();
            if (data) {
                const messages = Object.values(data).filter(
                    (message) => !message.isDeleted || message.isDeleted === false
                );
                const sortedMessages = messages.sort(
                    (a, b) => a.timeStamp - b.timeStamp
                );
                dispatch({
                    type: GET_GROUP_MESSAGES_SUCCESS,
                    payload: sortedMessages,
                });
            } else {
                dispatch({ type: GET_GROUP_MESSAGES_SUCCESS, payload: [] });
            }
        });
    } catch (error) {
        dispatch({ type: GET_GROUP_MESSAGES_FAIL, payload: error });
    }
};

export const SetGroupData = (groupData) => (dispatch) => { 
    try {
        dispatch({ type: SET_GROUP_DATA, payload: groupData });
    } catch (error) {
        console.log("error ",error);
        
    }
};

// export const getGroupChatMessagesAction = (groupId) => async dispatch => {
//     try {

//         dispatch({ type: GET_GROUP_MESSAGES_INIT })

//         const db = getDatabase();
//         const starCountRef = ref(db, `/chats/${groupId}/messages`);

//         await onValue(starCountRef, (snapshot) => {
//             const data = snapshot.val();
//             data
//                 ? dispatch({ type: GET_GROUP_MESSAGES_SUCCESS, payload: Object.values(data).sort((a, b) => a.timeStamp - b.timeStamp) })
//                 : dispatch({ type: GET_GROUP_MESSAGES_SUCCESS, payload: [] })
//         });

//     } catch (error) {

//         dispatch({ type: GET_GROUP_MESSAGES_FAIL, payload: error })

//     }
// }
