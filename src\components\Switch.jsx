import React from 'react'

const Switch = ({ id }) => {
  return (
    <>
      <label
        htmlFor={id}
        className='w-fit flex justify-between items-center cursor-pointer'
      >
        <div className='relative'>
          <input type='checkbox' id={id} className='sr-only' />
          <div className='block w-12 h-7 rounded-full bg'></div>
          <div className='dot absolute left-1 top-1 bg-white w-5 h-5 rounded-full transition'></div>
        </div>
      </label>
    </>
  )
}

export default Switch