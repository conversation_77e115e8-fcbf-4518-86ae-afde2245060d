import { LOGIN_USER_FAIL, LOGIN_USER_INIT, LOGIN_USER_SUCCESS } from "../constant/authConstants";

export const storeUserCreds = (uid, displayName, email, profileImageUrl) => async dispatch => {
    try {
        dispatch({ type: LOGIN_USER_INIT })

        const vals = {
            uid, displayName, email, profileImageUrl
        }
        
        localStorage.setItem("currentUser", JSON.stringify(vals))

        dispatch({ type: LOGIN_USER_SUCCESS, payload: { uid, displayName, email, profileImageUrl } })

    } catch (error) {

        dispatch({ type: LOGIN_USER_FAIL, payload: error })

    }
}