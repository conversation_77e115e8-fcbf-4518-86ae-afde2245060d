import React, { useEffect, useState } from "react";
import DialogTitle from "@mui/material/DialogTitle";
import Dialog from "@mui/material/Dialog";
import { Line } from "react-chartjs-2";
import "chart.js/auto";
import moment from "moment";
import CloseIcon from "@mui/icons-material/Close";
import { BsFillHeartPulseFill } from "react-icons/bs";
import Paper from "@mui/material/Paper";
import Table from "@mui/material/Table";
import TableBody from "@mui/material/TableBody";
import TableContainer from "@mui/material/TableContainer";
import TableHead from "@mui/material/TableHead";
import TableRow from "@mui/material/TableRow";
import Grid from "@mui/material/Grid";
import TableCell, { tableCellClasses } from "@mui/material/TableCell";
import { styled } from "@mui/material/styles";

import VolunteerActivismIcon from "@mui/icons-material/VolunteerActivism";
import { getCadenceData, getPartculatWorkouts } from "../../API/api-endpoint";
import { CircularProgress, FormControl, MenuItem, OutlinedInput, Select, ToggleButton, ToggleButtonGroup } from "@mui/material";
import { calculatePace, calculateSpeed } from "../../utils/Resubale";
const selectPaceArray = [{ name: "Cadence", value: "cadence" }, { name: "Altitude", value: "altitude" },
{ name: "Pace", value: "pace" },
{ name: "Power", value: "power" },
]
const selectSpeedArray = [{ name: "Cadence", value: "cadence" }, { name: "Altitude", value: "altitude" },
{ name: "Speed", value: "speed" },
{ name: "Power", value: "power" },
]
const StyledTableCell = styled(TableCell)(({ theme }) => ({
  [`&.${tableCellClasses.head}`]: {
    backgroundColor: "white",
    color: theme.palette.common.black,
    border: "1px solid #dbd6d6"
  },
  [`&.${tableCellClasses.body}`]: {
    fontSize: 14,
  },
}));
const StyledTableRow = styled(TableRow)(({ theme }) => ({
  "&:nth-of-type(odd)": {
    backgroundColor: theme.palette.action.hover,
    border: "1px solid #dbd6d6"

  },
  // hide last border
  "&:last-child td, &:last-child th": {
    border: "1px solid #dbd6d6"

  },
}));





const ViewTrainingBlock = ({ selectedValue, onClose, open }) => {
  const [workoutData, setWorkoutData] = useState({});

  console.log("selectedValue",workoutData);
  const getWorkout = async(id)=>{
    const response = await getPartculatWorkouts(id)
    console.log("response",response );
    if (response.status ==200) {
      setWorkoutData(response?.data[0])
      
    }
  }
  useEffect(()=>{
    if (selectedValue) {
      getWorkout(selectedValue)
    }
  },[selectedValue])

  return (
    <Dialog
      maxWidth="lg"
      minWidth="lg"
      onClose={() => onClose(false)}
      open={open}
      PaperProps={{
        className: 'thin-scrollbar overflowXNone'
      }}
      style={{ overflowY: "auto" }}
    >
  <div className="p-4">
    <div className="flex justify-between ">
        <DialogTitle>
          {workoutData?.workout_planned_title} |{" "}
          {moment(workoutData?.date).format("dddd, DD-MMM-YYYY")}
        </DialogTitle>
        <DialogTitle className=" cursor-pointer" onClick={() => onClose(false)}>
          {" "}
          <CloseIcon />
        </DialogTitle>


      </div>
      <div className="flex justify-start text-sm">
      <div  className="p-4 m0 trainingBlockView" style={{"border":"2px solid #d2caca","margin":"7px","borderRadius":"10px","boxShadow":"rgba(0, 0, 0, 0.15) 0px 15px 25px, rgba(0, 0, 0, 0.05) 0px 5px 10px", width:"100%"}}>Description
      <br/>
      <label dangerouslySetInnerHTML={{
        __html: workoutData?.workout_description,
      }}></label>
      </div>
    </div>
      </div>
    
    </Dialog>
  );
};

export default ViewTrainingBlock
