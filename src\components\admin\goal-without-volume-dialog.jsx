import { useState, useEffect } from "react";
import { But<PERSON> } from "../ui/button";
import { Input } from "../ui/input";
import { Label } from "../ui/label";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "../ui/select";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "../ui/dialog";
import {
	createGoalWithoutVolumedata,
	updateGoalWithoutVolumedata,
	getAllActivityData,
	getAllGoalNameForYTA,
	getAllworkoutData,
} from "../../API/api-endpoint";
import Swal from "sweetalert2";

export const GoalWithoutVolumeDialog = ({ open, onClose, onSuccess, editingItem }) => {
	const [formData, setFormData] = useState({
		activity_id: "",
		goal: "",
		first: "",
		second: "",
		third: "",
		fourth: "",
		fifth: "",
		sixth: "",
		seventh: "",
	});
	const [isLoading, setIsLoading] = useState(false);
	const [activityList, setActivityList] = useState([]);
	const [goalList, setGoalList] = useState([]);
	const [workoutList, setWorkoutList] = useState([]);

	// Fetch dropdown data
	useEffect(() => {
		const fetchDropdownData = async () => {
			try {
				const [activityResponse, goalResponse, workoutResponse] = await Promise.all([
					getAllActivityData(),
					getAllGoalNameForYTA(),
					getAllworkoutData(),
				]);

				console.log("Activity response:", activityResponse);
				console.log("Goal response:", goalResponse);
				console.log("Workout response:", workoutResponse);

				setActivityList(activityResponse?.rows || []);
				setGoalList(goalResponse || []);
				setWorkoutList(workoutResponse || []);
			} catch (error) {
				console.error("Error fetching dropdown data:", error);
			}
		};

		if (open) {
			fetchDropdownData();
		}
	}, [open]);

	// Reset form when dialog opens/closes or when editing different item
	useEffect(() => {
		if (open) {
			if (editingItem?.id) {
				// Editing existing goal without volume
				const editData = {
					activity_id: editingItem.activity_id || "",
					goal: editingItem.goal || "",
					first: editingItem["1"]?.subworkout || "",
					second: editingItem["2"]?.subworkout || "",
					third: editingItem["3"]?.subworkout || "",
					fourth: editingItem["4"]?.subworkout || "",
					fifth: editingItem["5"]?.subworkout || "",
					sixth: editingItem["6"]?.subworkout || "",
					seventh: editingItem["7"]?.subworkout || "",
				};
				setFormData(editData);
			} else {
				// Creating new goal without volume
				const newData = {
					activity_id: "",
					goal: "",
					first: "",
					second: "",
					third: "",
					fourth: "",
					fifth: "",
					sixth: "",
					seventh: "",
				};
				setFormData(newData);
			}
		}
	}, [open, editingItem]);

	const handleInputChange = (field, value) => {
		setFormData((prev) => ({
			...prev,
			[field]: value,
		}));
	};

	const handleSubmit = async (e) => {
		e.preventDefault();

		// Validation
		if (!formData.activity_id) {
			Swal.fire({
				title: "Error",
				text: "Activity is required",
				icon: "error",
				timer: 1800,
				showConfirmButton: false,
			});
			return;
		}

		if (!formData.goal.trim()) {
			Swal.fire({
				title: "Error",
				text: "Goal is required",
				icon: "error",
				timer: 1800,
				showConfirmButton: false,
			});
			return;
		}

		if (!formData.first.trim()) {
			Swal.fire({
				title: "Error",
				text: "First workout is required",
				icon: "error",
				timer: 1800,
				showConfirmButton: false,
			});
			return;
		}

		if (!formData.second.trim()) {
			Swal.fire({
				title: "Error",
				text: "Second workout is required",
				icon: "error",
				timer: 1800,
				showConfirmButton: false,
			});
			return;
		}

		if (!formData.third.trim()) {
			Swal.fire({
				title: "Error",
				text: "Third workout is required",
				icon: "error",
				timer: 1800,
				showConfirmButton: false,
			});
			return;
		}

		if (!formData.fourth.trim()) {
			Swal.fire({
				title: "Error",
				text: "Fourth workout is required",
				icon: "error",
				timer: 1800,
				showConfirmButton: false,
			});
			return;
		}

		if (!formData.fifth.trim()) {
			Swal.fire({
				title: "Error",
				text: "Fifth workout is required",
				icon: "error",
				timer: 1800,
				showConfirmButton: false,
			});
			return;
		}

		try {
			setIsLoading(true);

			// Prepare data for API
			const apiData = {
				activity_id: parseInt(formData.activity_id),
				goal: formData.goal.trim(),
				first: formData.first.trim(),
				second: formData.second.trim(),
				third: formData.third.trim(),
				fourth: formData.fourth.trim(),
				fifth: formData.fifth.trim(),
				sixth: formData.sixth.trim() || "",
				seventh: formData.seventh.trim() || "",
			};

			let response;
			if (editingItem?.id) {
				// Add ID for update
				apiData.id = editingItem.id;
				response = await updateGoalWithoutVolumedata(apiData);
			} else {
				response = await createGoalWithoutVolumedata(apiData);
			}

			console.log("API response:", response);

			if (response?.status) {
				Swal.fire({
					title: "Success",
					text: response.message || `Goal without volume ${editingItem?.id ? 'updated' : 'created'} successfully`,
					icon: "success",
					timer: 2000,
					showConfirmButton: false,
				});
				onSuccess();
			} else {
				Swal.fire({
					title: "Error",
					text: response?.message || "Failed to save goal without volume",
					icon: "error",
					timer: 3000,
					showConfirmButton: false,
				});
			}
		} catch (error) {
			console.error("Error saving goal without volume:", error);
			Swal.fire({
				title: "Error",
				text: "An error occurred while saving the goal without volume",
				icon: "error",
				timer: 3000,
				showConfirmButton: false,
			});
		} finally {
			setIsLoading(false);
		}
	};

	return (
		<Dialog open={open} onOpenChange={onClose}>
			<DialogContent className='sm:max-w-4xl bg-white max-h-[90vh] overflow-y-auto'>
				<DialogHeader>
					<DialogTitle className='text-lg font-semibold text-gray-900'>
						{editingItem?.id ? "Edit Goal Without Volume" : "Create Goal Without Volume"}
					</DialogTitle>
				</DialogHeader>

				<form onSubmit={handleSubmit} className='space-y-4'>
					<div className='grid gap-4'>
						<div className='grid grid-cols-1 sm:grid-cols-2 gap-4'>
							<div className='space-y-2'>
								<Label
									htmlFor='activity_id'
									className='text-sm font-semibold'
								>
									Activity <span className='text-red-500'>*</span>
								</Label>
								<Select
									value={formData.activity_id}
									onValueChange={(value) =>
										handleInputChange("activity_id", value)
									}
									disabled={isLoading}
								>
									<SelectTrigger className='w-full text-sm'>
										<SelectValue placeholder='Select Activity' />
									</SelectTrigger>
									<SelectContent className='bg-white'>
										{activityList.map((activity) => (
											<SelectItem
												key={activity.id}
												value={activity.id.toString()}
											>
												{activity.activity_name}
											</SelectItem>
										))}
									</SelectContent>
								</Select>
							</div>

							<div className='space-y-2'>
								<Label
									htmlFor='goal'
									className='text-sm font-semibold'
								>
									Goal <span className='text-red-500'>*</span>
								</Label>
								<Select
									value={formData.goal}
									onValueChange={(value) =>
										handleInputChange("goal", value)
									}
									disabled={isLoading}
								>
									<SelectTrigger className='w-full text-sm'>
										<SelectValue placeholder='Select Goal' />
									</SelectTrigger>
									<SelectContent className='bg-white'>
										{goalList.map((goal, index) => (
											<SelectItem
												key={index}
												value={goal.goal_name}
											>
												{goal.goal_name}
											</SelectItem>
										))}
									</SelectContent>
								</Select>
							</div>
						</div>

						{/* Workout Sequence Fields */}
						<div className='grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4'>
							{[
								{ key: "first", label: "First Workout", required: true },
								{ key: "second", label: "Second Workout", required: true },
								{ key: "third", label: "Third Workout", required: true },
								{ key: "fourth", label: "Fourth Workout", required: true },
								{ key: "fifth", label: "Fifth Workout", required: true },
								{ key: "sixth", label: "Sixth Workout", required: false },
								{ key: "seventh", label: "Seventh Workout", required: false },
							].map((field) => (
								<div key={field.key} className='space-y-2'>
									<Label
										htmlFor={field.key}
										className='text-sm font-semibold'
									>
										{field.label} {field.required && <span className='text-red-500'>*</span>}
									</Label>
									<Input
										id={field.key}
										className='w-full text-sm'
										value={formData[field.key]}
										onChange={(e) =>
											handleInputChange(field.key, e.target.value)
										}
										placeholder={`Enter ${field.label.toLowerCase()}`}
										disabled={isLoading}
										required={field.required}
									/>
								</div>
							))}
						</div>
					</div>

					<div className='flex justify-end gap-3 pt-4'>
						<Button
							type='button'
							variant='outline'
							onClick={onClose}
							disabled={isLoading}
						>
							Cancel
						</Button>
						<Button
							type='submit'
							className='bg-orange-600 hover:bg-orange-700 text-white'
							disabled={isLoading}
						>
							{isLoading
								? "Saving..."
								: editingItem?.id
								? "Update Goal"
								: "Create Goal"}
						</Button>
					</div>
				</form>
			</DialogContent>
		</Dialog>
	);
};
