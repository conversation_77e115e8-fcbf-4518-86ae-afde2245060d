import { useState, useEffect } from "react";
import {
	<PERSON><PERSON>,
	DialogContent,
	DialogHeader,
	DialogTitle,
} from "../../components/ui/dialog";
import { Button } from "../../components/ui/button";
import { Input } from "../../components/ui/input";
import { Label } from "../../components/ui/label";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "../../components/ui/select";
import {
	getAllActivityData,
	weeklyFeedDataPattern,
	CreateFeedWeeklyDataPattern,
	updateFeedWeeklyDataPattern,
} from "../../API/api-endpoint";
import Swal from "sweetalert2";

const days = [
	"monday",
	"tuesday",
	"wednesday",
	"thursday",
	"friday",
	"saturday",
	"sunday",
];

export function WeeklyFeedDialog({ open, onOpenChange, editData, onSave }) {
	const [formData, setFormData] = useState({
		activity: "",
		options: 1,
		run_days_per_week: "",
		monday: "",
		tuesday: "",
		wednesday: "",
		thursday: "",
		friday: "",
		saturday: "",
		sunday: "",
	});
	const [activities, setActivities] = useState([]);
	const [workoutTypes, setWorkoutTypes] = useState([]);
	const [isInitialized, setIsInitialized] = useState(false);
	const [activityChanged, setActivityChanged] = useState(false);

	useEffect(() => {
		if (editData) {
			setFormData(editData);
		} else {
			setFormData({
				activity: "",
				options: 1,
				run_days_per_week: "",
				monday: "",
				tuesday: "",
				wednesday: "",
				thursday: "",
				friday: "",
				saturday: "",
				sunday: "",
			});
		}
	}, [editData, open]);

	useEffect(() => {
		async function fetchActivities() {
			const response = await getAllActivityData();
			setActivities(response?.rows || []);
		}
		fetchActivities();
	}, []);

	const fetchWorkoutTypesForActivity = async (activityId) => {
		const response = await weeklyFeedDataPattern(activityId);
		setWorkoutTypes(response || []);
	};

	// When editData changes, set formData fields to IDs
	useEffect(() => {
		if (editData && activities.length && !isInitialized) {
			setFormData({
				activity:
					editData.activity && editData.activity.id
						? editData.activity.id
						: "",
				options: editData.options || 1,
				run_days_per_week: editData.run_days_per_week
					? String(editData.run_days_per_week)
					: "",
				monday:
					editData.monday && editData.monday.id
						? String(editData.monday.id)
						: "",
				tuesday:
					editData.tuesday && editData.tuesday.id
						? String(editData.tuesday.id)
						: "",
				wednesday:
					editData.wednesday && editData.wednesday.id
						? String(editData.wednesday.id)
						: "",
				thursday:
					editData.thursday && editData.thursday.id
						? String(editData.thursday.id)
						: "",
				friday:
					editData.friday && editData.friday.id
						? String(editData.friday.id)
						: "",
				saturday:
					editData.saturday && editData.saturday.id
						? String(editData.saturday.id)
						: "",
				sunday:
					editData.sunday && editData.sunday.id
						? String(editData.sunday.id)
						: "",
			});
			if (editData.activity && editData.activity.id) {
				fetchWorkoutTypesForActivity(editData.activity.id);
			}
			setIsInitialized(true);
		}
	}, [editData, activities]);

	// When activity changes, fetch workout types
	useEffect(() => {
		if (formData.activity) {
			fetchWorkoutTypesForActivity(formData.activity);
		}
	}, [formData.activity]);

	// Reset all day fields when activity changes and not in edit mode
	useEffect(() => {
		if ((activityChanged || !editData) && formData.activity) {
			setFormData((prev) => {
				const clearedDays = {};
				days.forEach((day) => (clearedDays[day] = ""));
				return { ...prev, ...clearedDays };
			});
		}
	}, [formData.activity]);

	// Handle input changes
	const handleInputChange = (e) => {
		const { name, value } = e.target;
		setFormData((prev) => ({ ...prev, [name]: value }));
	};

	// On submit, send formData directly to API
	const handleSubmit = async (e) => {
		e.preventDefault();
		// Validation: all fields required
		if (
			!formData.activity ||
			!formData.options ||
			!formData.run_days_per_week ||
			!formData.monday ||
			!formData.tuesday ||
			!formData.wednesday ||
			!formData.thursday ||
			!formData.friday ||
			!formData.saturday ||
			!formData.sunday
		) {
			Swal.fire({
				title: "Error",
				text: "All fields are required. Please fill in every field.",
				icon: "error",
				timer: 1800,
				showConfirmButton: false,
			});
			return;
		}
		const payload = {
			program: formData.activity,
			option: formData.options,
			daysaweek: Number(formData.run_days_per_week),
			monday: formData.monday,
			tuesday: formData.tuesday,
			wednesday: formData.wednesday,
			thursday: formData.thursday,
			friday: formData.friday,
			saturday: formData.saturday,
			sunday: formData.sunday,
		};
		if (!editData) {
			const response = await CreateFeedWeeklyDataPattern(payload);
			if (response?.status) {
				Swal.fire({
					title: "Success",
					text: "Weekly-FeedData saved successfully!",
					icon: "success",
					timer: 1800,
					showConfirmButton: false,
				});
				onOpenChange(false);
				onSave();
			} else {
				Swal.fire({
					title: "Error",
					text: response?.message || "Failed to create data",
					icon: "error",
				});
			}
		} else {
			payload.id = editData.id;
			const response = await updateFeedWeeklyDataPattern(payload);
			if (response?.status) {
				Swal.fire({
					title: "Success",
					text: "Weekly-FeedData updated successfully!",
					icon: "success",
					timer: 1800,
					showConfirmButton: false,
				});
				onOpenChange(false);
				onSave();
			} else {
				Swal.fire({
					title: "Error",
					text: response?.message || "Failed to update data",
					icon: "error",
				});
			}
		}
	};

	useEffect(() => {
		if (workoutTypes.length > 0) {
			setFormData((prev) => {
				const updated = { ...prev };
				days.forEach((day) => {
					if (
						updated[day] &&
						!workoutTypes.some(
							(w) => String(w.id) === String(updated[day])
						)
					) {
						updated[day] = "";
					}
				});
				return updated;
			});
		}
	}, [workoutTypes]);

	useEffect(() => {
		if (!open) setActivityChanged(false);
	}, [open]);

	useEffect(() => {
		setActivityChanged(false);
	}, [editData]);

	return (
		<Dialog open={open} onOpenChange={onOpenChange}>
			<DialogContent className='max-w-4xl max-h-[90vh] overflow-y-auto bg-white'>
				<DialogHeader>
					<DialogTitle>
						{editData
							? "Edit Weekly Feed Data"
							: "Create Weekly Feed Data"}
					</DialogTitle>
				</DialogHeader>

				<form onSubmit={handleSubmit} className='space-y-6'>
					<div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
						<div className='space-y-2'>
							<Label htmlFor='activity' className='font-semibold'>
								Activity
							</Label>
							<Select
								name='activity'
								value={formData.activity}
								onValueChange={(value) => {
									console.log(
										"[DEBUG] Activity selected:",
										value,
										typeof value
									);
									setFormData((prev) => ({
										...prev,
										activity: Number(value),
									}));
									setActivityChanged(true);
								}}
							>
								<SelectTrigger>
									<SelectValue placeholder='Select activity' />
								</SelectTrigger>
								<SelectContent className='bg-white'>
									{activities.map((activity) => (
										<SelectItem
											key={activity.id}
											value={activity.id}
										>
											{activity.activity_name}
										</SelectItem>
									))}
								</SelectContent>
							</Select>
						</div>

						<div className='space-y-2'>
							<Label htmlFor='options' className='font-semibold'>
								Options
							</Label>
							<Input
								id='options'
								type='number'
								min='1'
								className='w-full text-sm'
								value={formData.options}
								onChange={handleInputChange}
								name='options'
							/>
						</div>
						<div className='space-y-2'>
							<Label
								htmlFor='run_days_per_week'
								className='font-semibold'
							>
								Run Days Per Week
							</Label>
							<Select
								name='run_days_per_week'
								value={formData.run_days_per_week}
								onValueChange={(value) =>
									setFormData((prev) => ({
										...prev,
										run_days_per_week: value,
									}))
								}
							>
								<SelectTrigger>
									<SelectValue placeholder='Select Day' />
								</SelectTrigger>
								<SelectContent className='bg-white'>
									{[1, 2, 3, 4, 5, 6, 7].map((day) => (
										<SelectItem
											key={day}
											value={String(day)}
										>
											{day}
										</SelectItem>
									))}
								</SelectContent>
							</Select>
						</div>
					</div>

					<div className='space-y-4'>
						<h3 className='text-lg font-semibold'>
							Weekly Schedule
						</h3>
						<div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4'>
							{days.map((day) => (
								<div key={day} className='space-y-2'>
									<Label
										htmlFor={day}
										className='capitalize font-semibold'
									>
										{day}
									</Label>
									<Select
										name={day}
										value={formData[day]}
										onValueChange={(value) =>
											setFormData((prev) => ({
												...prev,
												[day]: value
													? String(value)
													: "",
											}))
										}
									>
										<SelectTrigger>
											<SelectValue
												placeholder={`Select ${day} workout`}
											/>
										</SelectTrigger>
										<SelectContent className='bg-white'>
											{workoutTypes.map((workout) => (
												<SelectItem
													key={workout.id}
													value={String(workout.id)}
												>
													{workout.workout}
												</SelectItem>
											))}
										</SelectContent>
									</Select>
								</div>
							))}
						</div>
					</div>

					<div className='flex justify-end gap-2 pt-4'>
						<Button
							type='button'
							variant='outline'
							onClick={() => onOpenChange(false)}
						>
							Cancel
						</Button>
						<Button
							type='submit'
							className='bg-orange-600 hover:bg-orange-700 text-white'
						>
							{editData ? "Update" : "Create"}
						</Button>
					</div>
				</form>
			</DialogContent>
		</Dialog>
	);
}
