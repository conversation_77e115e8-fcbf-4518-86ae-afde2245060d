import React from 'react'
import Header from '../components/Header'

const LastFeedbackPage = () => {
  const athletes = [
    { id: 1, name: 'JAF<PERSON> <PERSON>', date: '2021-21-27' },
    { id: 2, name: '	<PERSON><PERSON><PERSON>', date: '2021-21-27' },
    { id: 3, name: '<PERSON><PERSON>', date: '2021-10-205' },
    { id: 4, name: '<PERSON><PERSON><PERSON>', date: '2021-05-12' },
    { id: 5, name: '<PERSON><PERSON>', date: '2021-12-24' },
    { id: 6, name: '<PERSON><PERSON>', date: '2021-07-9' }
  ]
  return (
    <>
      <Header />
      <div className='p-6'>
        <div className='flex items-end gap-x-2'>
          <h1 className='font-medium text-xl'>Athlete plan Interactions</h1>
        </div>
        <br />
        <p className='text-sm'>
          Enter the number of days at which you need to communicate:
        </p>
        <br />
        <div className='flex flex-col md:flex-row gap-8 items-start md:items-center mb-6'>
          <div>
            <input
              type='text'
              placeholder='Search'
              className='w-full md:w-64 border-b-2 py-2 rounded-md focus:outline-none text-slate-600 px-1 text-sm'
            />
          </div>
          <div>
            <button className='py-1.5 px-5 bg-green-600 text-slate-50 rounded text-base'>
              Show all athletes
            </button>
          </div>
        </div>
        {/* Table */}
        <div className='w-full mb-6'>
          <div className='my-6'>
            <div className='flex flex-col'>
              <div className='overflow-x-auto shadow-md'>
                <div className='inline-block min-w-full align-middle'>
                  <div className='overflow-hidden '>
                    <table className='min-w-full divide-y divide-gray-200 table-fixed dark:divide-gray-700'>
                      <thead className='bg-gray-100 '>
                        <tr>
                          <th
                            scope='col'
                            className='py-3 px-6 text-xs font-medium tracking-wider text-left text-gray-700 uppercase dark:text-gray-400'
                          >
                            Name
                          </th>
                          <th
                            scope='col'
                            className='py-3 px-6 text-xs font-medium tracking-wider text-left text-gray-700 uppercase dark:text-gray-400'
                          >
                            Date
                          </th>
                        </tr>
                      </thead>
                      <tbody className='bg-white divide-y divide-gray-200 '>
                        {athletes.map((item, index) => (
                          <tr
                            className='hover:bg-gray-100 text-slate-600'
                            key={index}
                          >
                            <td className='py-4 px-6 text-sm font-medium text-gray-500 whitespace-nowrap'>
                              {item.name}
                            </td>
                            <td className='py-4 px-6 text-sm font-medium text-gray-500 whitespace-nowrap'>
                              {item.date}
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  )
}

export default LastFeedbackPage
