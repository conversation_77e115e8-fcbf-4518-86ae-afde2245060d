import React, { useEffect, useState } from "react";
import { useTheme } from "@mui/material/styles";
import Typography from "@mui/material/Typography";
import Box from "@mui/material/Box";
import MobileStepper from "@mui/material/MobileStepper";
import Button from "@mui/material/Button";
import KeyboardArrowLeft from "@mui/icons-material/KeyboardArrowLeft";
import KeyboardArrowRight from "@mui/icons-material/KeyboardArrowRight";
import DirectionsRunIcon from "@mui/icons-material/DirectionsRun";
import DirectionsBikeIcon from "@mui/icons-material/DirectionsBike";
import AttractionsIcon from "@mui/icons-material/Attractions";
import SleddingIcon from "@mui/icons-material/Sledding";
import FitnessCenterIcon from "@mui/icons-material/FitnessCenter";
import "./Flow.css";
import Swal from "sweetalert2";
import {
  createUserSubscription,
  getAllCountry,
  getAllGoals,
  getSubscriptionPlanBYProgramID,
  getAllYoskaActivities,
  getAllyoskaProgramByActivityID,
  ValidatePromoCode,
} from "../../API/api-endpoint";
import { styled } from "@mui/material/styles";
import { useNavigate } from "react-router-dom";
import MuiCard from "@mui/material/Card";
import Background from "../../Images/Background.png";

// ** Styled Components
const Card = styled(MuiCard)(({ theme }) => ({
  [theme.breakpoints.up("sm")]: { width: "28rem" },
}));

const Flow = () => {
  const navigate = useNavigate();

  const [amount, setAmount] = useState("73500");
  const [activity, setActivity] = useState("");
  const [selectActivity, setSelectActivity] = useState("");
  const [selectProgram, setselectProgram] = useState("");
  const [price, setPrice] = useState("");
  const [goalsList, setGoalsList] = useState([]);
  const [yoskaActivitieList, setYoskaActivitieList] = useState([]);
  const [yoskaProgramList, setYoskaProgramList] = useState([]);
  const [SubscriptionPlanList, setSubscriptionPlanList] = useState([]);
  const [CountryList, setCountryList] = useState([]);
  const [discountPrice, setDiscountPrice] = useState(0);
  const [formValue, setFormValue] = useState({
    name: "",
    address: "",
    email: "",
    phoneNumber: "",
    country: undefined,
    state: undefined,
    city: undefined,
    pincode: "",
    company_name: "",
    promocode: "",
  });

  const FullName = localStorage.getItem("fullname");
  const email = localStorage.getItem("email");
  const phoneNumber = localStorage.getItem("phonenumber");

  useEffect(() => {
    getSubscriptionPlan();
  }, [selectProgram]);

  useEffect(() => {
    console.log(FullName, email, phoneNumber);
    setFormValue({
      ...formValue,
      name: FullName,
      phoneNumber: phoneNumber,
      email: email,
    });
  }, []);

  //===========================get data from apis start================

  const getGoals = async () => {
    let response = await getAllGoals();
    setGoalsList(response);
  };

  const getYoskaActivities = async () => {
    let response = await getAllYoskaActivities();
    setYoskaActivitieList(response);
  };

  const getSubscriptionPlan = async () => {
    console.log("selectProgram", selectProgram);
    let response = await getSubscriptionPlanBYProgramID(
      selectProgram["program_id"]
    ); //we have to send program_id here
    setSubscriptionPlanList(response);
  };

  const getAllProgramByActivityID = async () => {
    let response = await getAllyoskaProgramByActivityID(
      activity["yoska-activity-Id"]
    );
    setselectProgram(response[0]);
    setYoskaProgramList(response);
  };

  const getCountry = async () => {
    let response = await getAllCountry();
    setCountryList(response);
  };

  useEffect(() => {
    getGoals();
    getYoskaActivities();
    // getCountry();
  }, []);

  //===========================get data from apis end==================

  const createUserSubscriptionplan = async (body) => {
    let result = await createUserSubscription(body);
    if (result.status) {
      navigate("/training-blocks-1");
    } else {
      Swal.fire({
        title: "Error",
        text: "SomeThing Went Wrong",
        icon: "error",
      });
    }
  };

  // ====================== razorPay ================================
  // "rzp_test_BLhLdnM1fwW5wV",
  const options = {
    key: "rzp_live_DPreuEqStTNuXQ",
    amount: (amount * 12 - discountPrice) * 100, //  = INR 1
    name: "Yoska",
    description: "Yoska training programs.",
    image: "https://cdn.razorpay.com/logos/7K3b6d18wHwKzL_medium.png",
    handler: function (response) {
      createUserSubscriptionplan(response);
    },
    prefill: {
      name: FullName,
      contact: phoneNumber, //"+919876543210", // phoneNumber,
      email: email,
    },
    notes: {
      address: `${formValue.company_name} ${formValue.address} 
                ${formValue.city} ${formValue.pincode} 
                ${formValue.state} ${formValue.country}`,
    },
    theme: {
      color: "#1e40af",
      hide_topbar: false,
    },
  };

  const openPayModal = (options) => {
    var rzp1 = new window.Razorpay(options);
    rzp1.open();
  };

  useEffect(() => {
    const script = document.createElement("script");
    script.src = "https://checkout.razorpay.com/v1/checkout.js";
    script.async = true;
    document.body.appendChild(script);
  }, []);
  // ====================== razorPay ================================

  const applyPromoCode = async () => {
    if (formValue.promocode.length == 0) {
      Swal.fire({
        title: "Error",
        text: "Please Enter the Promocode",
        icon: "error",
      });
    } else {
      let response = await ValidatePromoCode(formValue.promocode);
      if (response.status) {
        Swal.fire({
          title: "Success",
          text: "Coupen is Valid",
          icon: "success",
        });
        setDiscountPrice(parseInt(response?.coupon["discount_amount"]));
      } else {
        Swal.fire({
          title: "Error",
          text: "Coupen is Not Valid",
          icon: "error",
        });
      }
    }
  };

  // ================================== sttper form ==================================

  const renderWelcomeScreen = () => {
    return (
      <div style={{ textAlign: "center", fontSize: "18px" }}>
        <p>
          Welcome to the world of fitness, where we embark on a healthier and
          storger you!
        </p>
        <br />
        <p>
          Whether you are a seasoned athlete or just starting out, Iam here to
          guide you every step of the way,
        </p>
      </div>
    );
  };

  const renderSelectActivity = () => {
    return (
      <div
        style={{
          display: "flex",
          flexDirection: "column",
          gap: "20px",
          width: "100%",
          paddingBottom: "20px",
        }}
      >
        {goalsList.map((item, index) => (
          <Box
            key={index}
            onClick={() => setSelectActivity(item["workout-goal-id"])}
            sx={{
              border:
                selectActivity === item["workout-goal-id"]
                  ? "solid 1px #1e40af"
                  : "solid 1px lightgray",
              borderRadius: "10px",
              p: 1,
            }}
          >
            <p
              style={{
                margin: "0",
                color:
                  selectActivity === item["workout-goal-id"] ? "#1e40af" : "",
              }}
            >
              {item["workout-goal-name"]}
            </p>
          </Box>
        ))}
      </div>
    );
  };

  const renderActivity = () => {
    return (
      <div
        style={{
          display: "flex",
          flexDirection: "column",
          gap: "20px",
          width: "100%",
          paddingBottom: "20px",
        }}
      >
        {yoskaActivitieList.map((item, index) => (
          <Box
            sx={{
              display: "flex",
              alignItems: "center",
              border:
                activity["yoska-activity-Id"] === item["yoska-activity-Id"]
                  ? "solid 1px #1e40af"
                  : "solid 1px lightgray",
              borderRadius: "10px",
              p: 1,
            }}
            key={index}
            onClick={() => setActivity(item)}
          >
            <DirectionsRunIcon />
            {/* <img src={item["yosk-activity-icon"]} className="icon" /> */}
            <p
              style={{
                margin: "0",
                paddingLeft: "10px",
                color:
                  activity["yoska-activity-Id"] === item["yoska-activity-Id"]
                    ? "#1e40af"
                    : "",
              }}
            >
              {item["yoska-activity-name"]}
            </p>
          </Box>
        ))}
      </div>
    );
  };

  const renderRunningLable = () => {
    return (
      <Box
        sx={{
          width: "100%",
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
        }}
      >
        {activity?.icon}
        <DirectionsRunIcon />
        <p style={{ margin: "0", paddingLeft: "10px" }}>
          {activity["yoska-activity-name"]}
        </p>
      </Box>
    );
  };

  const renderRunningDiscription = () => {
    return (
      <div>
        <ul>
          <li>{activity["description"]}</li>
          <li>
            the training plan will be deliverd through Yoska's online platform
            (web & mobaile app) the only one of its kind in india.
          </li>
        </ul>
      </div>
    );
  };

  const renderRunningPricing = () => {
    return (
      <div
        style={{
          display: "flex",
          flexDirection: "column",
          gap: "10px",
          width: "100%",
          paddingBottom: "20px",
        }}
      >
        {SubscriptionPlanList.map((item, index) => (
          <Box
            key={index}
            sx={{
              display: "flex",
              alignItems: "center",
              border: price === item.price && "solid 1px #1e40af",
              borderBottom: price !== item.price && "solid 2px gray",
              flexDirection: "column",
              borderRadius: "10px",
              p: 1,
            }}
            onClick={() => {
              setPrice(item.price);
              setAmount(`${item.price}`);
            }}
          >
            <p
              style={{
                margin: "0",
                color: price === item.price ? "#1e40af" : "",
              }}
            >
              {item.plan_name}
            </p>
            <p
              style={{
                margin: "0",
                color: price === item.price ? "#1e40af" : "",
              }}
            >
              {item.price}/{item.billing_cycle}
            </p>
          </Box>
        ))}
      </div>
    );
  };

  const renderForm = () => {
    return (
      <div
        style={{
          width: "100%",
          paddingBottom: "20px",
        }}
      >
        <div
          style={{
            width: "90%",
            padding: "0px 20px",
            display: "flex",
            flexDirection: "column",
            gap: "10px",
          }}
        >
          <div style={{ borderBottom: "solid 1px lightgray", padding: "10px" }}>
            Program : {selectProgram["program_name"]}
          </div>
          <div
            style={{
              borderBottom: "solid 1px lightgray",
              padding: "10px",
              display: "flex",
              justifyContent: "space-between",
            }}
          >
            <div>Monthly Price : </div>
            <div>₹{amount}</div>
          </div>
          <div
            style={{
              display: "flex",
              padding: "10px",
              justifyContent: "space-between",
            }}
          >
            <div>Discount Price </div>
            <div>₹{discountPrice}</div>
          </div>
          <div
            style={{
              display: "flex",
              padding: "10px",
              justifyContent: "space-between",
            }}
          >
            <div>Total Price </div>
            <div>₹{amount * 12 - discountPrice}</div>
          </div>
          <div
            style={{
              borderRadius: "10px",
              width: "100%",
              display: "flex",
              flexDirection: "column",
              justifyContent: "center",
              alignItems: "center",
              gap: "10px",
            }}
            className="flow-running-form"
          >
            <div className="flow-flex-input">
              <input
                className={
                  formValue?.name == "null"
                    ? "running-step-input"
                    : "running-step-input" // running-step-input disabled-input
                }
                type="text"
                placeholder="Full Name"
                value={formValue?.name == "null" ? "" : formValue.name}
                onChange={(e) => {
                  setFormValue({ ...formValue, name: e.target.value });
                }}
                disabled={formValue?.name == "null" ? false : false}
              />
              <input
                className="running-step-input"
                type="text"
                placeholder="Address"
                value={formValue.address}
                onChange={(e) => {
                  setFormValue({ ...formValue, address: e.target.value });
                }}
              />
            </div>
            <div className="flow-flex-input">
              <input
                className={
                  formValue?.email === null
                    ? "running-step-input"
                    : "running-step-input disabled-input"
                }
                type="email"
                placeholder="Email id"
                value={formValue.email}
                onChange={(e) => {
                  setFormValue({ ...formValue, email: e.target.value });
                }}
                disabled={formValue?.email === null ? false : true}
              />
              <input
                className={
                  formValue?.phoneNumber == null
                    ? "running-step-input "
                    : "running-step-input" // running-step-input disabled-input
                }
                type="text"
                placeholder="Mobile Number"
                value={formValue.phoneNumber}
                onChange={(e) => {
                  setFormValue({ ...formValue, phoneNumber: e.target.value });
                }}
              //disabled={formValue?.phoneNumber === null ? false : true}
              />
            </div>
            <div className="flow-flex-input">
              <select
                className="running-step-input"
                name="Country"
                id="country"
                value={formValue.country}
                onChange={(e) => {
                  setFormValue({ ...formValue, country: e.target.value });
                }}
              >
                <option value="country">Country</option>
                <option value="india">India</option>
                <option value="US">US</option>
                <option value="UK">UK</option>
              </select>
              <select
                className="running-step-input"
                name="State"
                id="state"
                value={formValue.state}
                onChange={(e) => {
                  setFormValue({ ...formValue, state: e.target.value });
                }}
              >
                <option value="state">State</option>
                <option value="gujrat">Gujrat</option>
                <option value="Gujrat">TamilNadu</option>
                <option value="Goa">Goa</option>
              </select>
            </div>
            <div className="flow-flex-input">
              <select
                className="running-step-input"
                name="City"
                id="City"
                value={formValue.city}
                onChange={(e) => {
                  setFormValue({ ...formValue, city: e.target.value });
                }}
              >
                <option value="city">City</option>
                <option value="Rajkot">Ahemdabad</option>
                <option value="Rajkot">Rajkot</option>
                <option value="Surat">Surat</option>
              </select>
              <input
                className="running-step-input"
                type="number"
                placeholder="Pin code"
                value={formValue.pincode}
                onChange={(e) => {
                  setFormValue({ ...formValue, pincode: e.target.value });
                }}
              />
            </div>
            <div className="flow-flex-input">
              <input
                className="running-step-input"
                type="text"
                placeholder="Compny Name"
                value={formValue.company_name}
                onChange={(e) => {
                  setFormValue({ ...formValue, company_name: e.target.value });
                }}
              />
              <input
                className="running-step-input"
                type="text"
                placeholder="Promo code"
                value={formValue.promocode}
                onChange={(e) => {
                  setFormValue({ ...formValue, promocode: e.target.value });
                }}
              />
            </div>
            <div style={{ display: "flex", justifyContent: "right" }}>
              <button
                style={{
                  width: "100%",
                  background: "black",
                  padding: "5px 10px",
                  borderRadius: "20px",
                  color: "white",
                }}
                onClick={applyPromoCode}
              >
                Apply PromoCode
              </button>
            </div>

            {/* <input
              className="running-step-input promo"
              type="text"
              placeholder="Promo Code"
            /> */}
            <div style={{ display: "flex" }}>
              <input
                type="checkbox"
                placeholder="Company Name"
                name="termsandcondition"
                id="termsandcondition"
                defaultChecked
              />
              <lable className="ml-2">I agree all terms and conditions</lable>
            </div>
          </div>
        </div>
      </div>
    );
  };

  const renderPayment = () => {
    return (
      <div
        style={{
          width: "100%",
          display: "flex",
          justifyContent: "center",
          paddingBottom: "20px",
        }}
      >
        <div
          style={{
            width: "80%",
            padding: "0px 20px",
            display: "flex",
            flexDirection: "column",
            gap: "10px",
            justifyContent: "center",
          }}
        >
          <div style={{ borderBottom: "solid 1px lightgray", padding: "10px" }}>
            Program : {selectProgram["program_name"]}
          </div>
          <div
            style={{
              borderBottom: "solid 1px lightgray",
              padding: "10px",
              display: "flex",
              justifyContent: "space-between",
            }}
          >
            <div>Monthly Price : </div>
            <div>₹{amount}</div>
          </div>
          <div
            style={{
              borderBottom: "solid 1px lightgray",
              padding: "10px",
              display: "flex",
              justifyContent: "space-between",
            }}
          >
            <div>Discount Price : </div>
            <div>₹{discountPrice}</div>
          </div>
          <div
            style={{
              display: "flex",
              padding: "10px",
              justifyContent: "space-between",
            }}
          >
            <div>Total Price </div>
            <div>₹{amount * 12 - discountPrice}</div>
          </div>
          <div style={{ display: "flex", justifyContent: "center" }}>
            <button
              style={{
                width: "50%",
                background: "black",
                padding: "10px 10px",
                borderRadius: "20px",
                color: "white",
              }}
              onClick={() => openPayModal(options)}
            >
              Click here to Pay
            </button>
          </div>
        </div>
      </div>
    );
  };

  const renderPrograms = () => {
    return (
      <>
        <p>Select Program For Getting Subscription Plans</p>
        <select
          className="running-step-input"
          name="Program"
          id="program"
          onChange={(e) => {
            setselectProgram(yoskaProgramList[e.target.value]);
          }}
        >
          {yoskaProgramList.map((program, index) => {
            return <option value={index}>{program.program_name}</option>;
          })}
        </select>
      </>
    );
  };

  const steps = [
    {
      label: <h3 style={{ color: "#1e40af" }}>Welcome</h3>,
      description: renderWelcomeScreen(),
    },
    {
      label:
        "Let's find a perfact activity that suits your goals, choose from the following? ",
      description: renderSelectActivity(),
    },
    {
      label: "Here are some activities to choose",
      description: renderActivity(),
    },
    {
      label: renderRunningLable(),
      description: renderRunningDiscription(),
    },
    {
      label: renderPrograms(),
      description: renderRunningPricing(),
    },
    {
      label: "",
      description: renderForm(),
    },
    {
      label: "Make a Payment",
      description: renderPayment(),
    },
  ];

  const theme = useTheme();
  const [activeStep, setActiveStep] = React.useState(0);
  const maxSteps = steps.length;

  const handleNext = () => {
    console.log(activeStep);
    if (activeStep === 2) {
      if (activity.length === 0) {
        Swal.fire({
          title: "Error",
          text: "Please Select Activity",
          icon: "error",
        });
      } else {
        getAllProgramByActivityID();
        setActiveStep((prevActiveStep) => prevActiveStep + 1);
      }
    } else if (activeStep === 4) {
      if (selectProgram.length === 0) {
        Swal.fire({
          title: "Error",
          text: "Please Select Any Plans",
          icon: "error",
        });
      } else {
        setActiveStep((prevActiveStep) => prevActiveStep + 1);
      }
    } else if (activeStep === 5) {
      let response = validateData(); //its valdiate terms and conditions

      if (response) {
        setActiveStep((prevActiveStep) => prevActiveStep + 1);
      } else {
        Swal.fire({
          title: "Error",
          text: "Please Fill All Data and accept the terms and conditions ",
          icon: "error",
        });
      }
    } else {
      setActiveStep((prevActiveStep) => prevActiveStep + 1);
    }
  };

  const handleBack = () => {
    setActiveStep((prevActiveStep) => prevActiveStep - 1);
  };

  const validateData = () => {
    let checkBoxValue = document.getElementById("termsandcondition");
    // console.log(
    //   formValue?.name?.length,
    //   formValue?.address?.length,
    //   formValue?.company_name?.length,
    //   formValue?.country,
    //   formValue?.state,
    //   formValue?.city,
    //   formValue?.pincode?.length,
    //   formValue?.phoneNumber?.length,
    //   formValue?.email?.length
    // );
    if (
      formValue?.name?.length === 0 ||
      formValue?.address?.length === 0 ||
      formValue?.company_name?.length === 0 ||
      formValue?.country?.length === undefined ||
      formValue?.state?.length === undefined ||
      formValue?.city?.length === undefined ||
      formValue?.pincode?.length === 0 ||
      formValue?.phoneNumber?.length === 0 ||
      formValue?.email?.length === 0
    ) {
      return false;
    } else if (checkBoxValue.checked) {
      return true;
    } else {
      return false;
    }
  };

  return (
    <Box
      className="flow-section"
      style={{
        display: "flex",
        justifyContent: "center",
        alignItems: "center",
        height: "100vh", // 100% of the viewport height
        background: `url(${Background}) center no-repeat`, // Replace 'your-background-image.jpg' with the path to your background image
        backgroundSize: "100% 100vh",
      }}
    >
      <Card
        sx={{
          maxWidth: 500,
          flexGrow: 1,
          border: "solid 1px lightgray",
          borderRadius: "10px",
          p: 2,
          zIndex: 1,
        }}
      >
        <Typography
          variant="h6"
          sx={{
            padding: "0",
            margin: "0",
            textAlign: "center",
            color: "#1e40af",
            paddingBottom: "20px",
          }}
        >
          {steps[activeStep].label}
        </Typography>
        <Box sx={{ height: "100%", maxWidth: 500, width: "100%" }}>
          {steps[activeStep].description}
        </Box>
        <MobileStepper
          variant="text"
          steps={maxSteps}
          position="static"
          activeStep={activeStep}
          nextButton={
            <Button
              size="small"
              onClick={handleNext}
              disabled={activeStep === maxSteps - 1}
            >
              Next
              {theme.direction === "rtl" ? (
                <KeyboardArrowLeft />
              ) : (
                <KeyboardArrowRight />
              )}
            </Button>
          }
          backButton={
            <Button
              size="small"
              onClick={handleBack}
              disabled={activeStep === 0}
            >
              {theme.direction === "rtl" ? (
                <KeyboardArrowRight />
              ) : (
                <KeyboardArrowLeft />
              )}
              Back
            </Button>
          }
        />
      </Card>
    </Box>
  );
};

export default Flow;
