import { FormLabel, Grid, MenuItem, OutlinedInput, TextField } from '@mui/material';
import { Button, Modal, TimePicker } from 'antd'
import React, { useEffect, useState } from 'react'
import { createActivityMetricdata,  getAllActivityData, getAllQuestionData, getAlllevels,  getuomsData,  updateActivityMetricdata } from '../../API/api-endpoint';
import { useFormik } from 'formik';
import Swal from 'sweetalert2';
import SlickCarousel from '../../pages/SlickCarousel';
import { capitalizeFirstLetter } from '../../utils/Resubale';

const CreateActivitymetric = ({ fetchReport, setShowAssesmentModal, showAssesmentModal, editData, setEditData }) => {
    const [metricList, setMetricList] = useState([]);
    const [programList, setProgramList] = useState([]);
    const [allLevelList, setAlllevelList] = useState([]);

    console.log("editData", programList);
    const formik = useFormik({
        initialValues: {
            activityid: "",
            metricid: "",
            status: 1,
        }, validate: (values) => {
            const errors = {};
            if (!values.activityid) {
                errors.activityid = "Activity is required";
            }
            if (!values.metricid) {
                errors.metricid = "Metric name is required";
            }
            return errors;
        },
        // validationSchema: {},
        onSubmit: (values, { resetForm }) => {
            handleSubmitAssesmentForm(values, resetForm)

        },
    });
    console.log("formik", formik?.values, formik?.errors);
    const QuestionData = async () => {
        const response = await getuomsData()
        setMetricList(response)
    }
    useEffect(() => {
        QuestionData()
        getAllProgramsData()
        getAllLevelsData()
    }, [])
    const getAllProgramsData = async () => {
        const response = await getAllActivityData()
        console.log("response", response);
        setProgramList(response?.rows)
    }
    const getAllLevelsData = async () => {
        const response = await getAlllevels()
        console.log("response", response);
        setAlllevelList(response?.rows)
    }

    const handleSubmitAssesmentForm = async (data, resetForm) => {
        let response = ""
        if (editData?.id) {

            response = await updateActivityMetricdata(data)

        } else {
            response = await createActivityMetricdata(data)

        }
        if (response?.status) {
            Swal.fire({
                title: "Success",
                text: capitalizeFirstLetter(response.message),
                icon: "success",
            });
            setEditData({})
            setShowAssesmentModal(false)
            fetchReport()
            formik.resetForm()
            formik?.setValues({
                activityid: "",
                metricid: "",
                status: 1,
            })
        } else {
            Swal.fire({
                title: "Error",
                text: response.message,
                icon: "error",
            });
        }
        console.log("response", response);
    }
    useEffect(() => {
        if (editData?.id) {
            const { srID, ...data } = editData
            formik?.setValues(data)
        } 
    }, [editData?.id])


    return (
        <Modal
        width={1200}
            open={showAssesmentModal}
            onCancel={() => {
                setShowAssesmentModal(false)
                formik.resetForm()
                setEditData({})
                formik?.setValues({
                    activityid: "",
                    metricid: "",
                    status: 1,
                })
            }}
            footer={
                <div></div>
              }
        >
            <div className="headingCont">
                  <span className="heading">{editData?.id ? "Edit " : "Create"}</span>{" "}
                  <span className="orange heading">Activity Metric</span>
                  
                </div>

                <div className="parentCont">
            <form  className="form1" onSubmit={formik.handleSubmit}>
                <Grid container spacing={2}>
                    <Grid item xs={12} sm={11}>
                        <FormLabel >Activity Name<span className="text-[red]">*</span></FormLabel>
                        <TextField
                            fullWidth
                            size="small"
                            select
                            SelectProps={{
                                MenuProps: {
                                  PaperProps: {
                                    style: {
                                       scrollbarColor:"#E67E22 white",
                                       scrollbarWidth:"thin"
                                    },
                                  },
                                },
                              }}
                            name="activityid"
                            value={formik?.values?.activityid}
                            onChange={formik.handleChange}
                            error={formik.touched.activityid && formik.errors.activityid}
                            helperText={
                                formik.touched.activityid && formik.errors.activityid
                            }

                            id="form-layouts-separator-select"
                            labelId="form-layouts-separator-select-label"
                            input={<OutlinedInput id="select-multiple-language" />}
                        >
                            <MenuItem value={""} disabled>
                                Select Activity
                            </MenuItem>
                            {programList?.map((value, index) => {
                                return (
                                    <MenuItem value={value?.id}>
                                        {value?.activity_name}
                                    </MenuItem>
                                );
                            })}
                        </TextField>
                    </Grid>
                    <Grid item xs={12} sm={11}>
                        <FormLabel >Metric Name<span className="text-[red]">*</span></FormLabel>
                    
                        <TextField
                            fullWidth
                            size="small"
                            select
                            SelectProps={{
                                MenuProps: {
                                  PaperProps: {
                                    style: {
                                       scrollbarColor:"#E67E22 white",
                                       scrollbarWidth:"thin"
                                    },
                                  },
                                },
                              }}
                            name="metricid"
                            value={formik?.values?.metricid}
                            onChange={formik.handleChange}
                            error={formik.touched.metricid && formik.errors.metricid}
                            helperText={
                                formik.touched.metricid && formik.errors.metricid
                            }

                            id="form-layouts-separator-select"
                            labelId="form-layouts-separator-select-label"
                            input={<OutlinedInput id="select-multiple-language" />}
                        >
                            <MenuItem value={""} disabled>
                                Select Metric
                            </MenuItem>
                            {metricList?.map((value, index) => {
                                return (
                                    <MenuItem value={value?.uom_id}>
                                        {value?.uom_name}
                                    </MenuItem>
                                );
                            })}
                        </TextField>
                    </Grid>
                    <Grid item xs={12}  sm={11}>
                        <FormLabel >Status</FormLabel>
                    
                        <TextField
                            fullWidth
                            size="small"
                            select
                            name="status"
                            value={formik?.values?.status}
                            onChange={formik.handleChange}
                            error={formik.touched.status && formik.errors.status}
                            helperText={
                                formik.touched.status && formik.errors.status
                            }

                            id="form-layouts-separator-select"
                            labelId="form-layouts-separator-select-label"
                            input={<OutlinedInput id="select-multiple-language" />}
                        >
                            <MenuItem value={1}>
                                Yes
                            </MenuItem>
                            <MenuItem value={0}>
                                No
                            </MenuItem>
                        </TextField>
                    </Grid>
                    <Grid item xs={12} sm={6}>
                <Button 
                className="btn"
                key="submit"
                type="primary"
                
                onClick={() => formik.handleSubmit()}
              >
                Submit
              </Button>
              </Grid>
                </Grid>
            </form>

            <div className="slick-container">
          <SlickCarousel />
        </div>
            </div>
        </Modal>
    )
}
export default CreateActivitymetric
