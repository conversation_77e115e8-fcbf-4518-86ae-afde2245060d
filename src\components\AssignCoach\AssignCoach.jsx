import React, { useEffect, useMemo, useState } from "react";
import "./Assign.css";
import Paper from "@mui/material/Paper";
import Table from "@mui/material/Table";
import TableBody from "@mui/material/TableBody";
import TableContainer from "@mui/material/TableContainer";
import TableHead from "@mui/material/TableHead";
import TableRow from "@mui/material/TableRow";
import Grid from "@mui/material/Grid";
import TableCell, { tableCellClasses } from "@mui/material/TableCell";
import { styled } from "@mui/material/styles";
import { getAllUserSubscription, getChangeLogDEtails } from "../../API/api-endpoint";
import Header from "../Header";
import { Box } from "@mui/system";
import { Button, CircularProgress, Pagination, TextField } from "@mui/material";
import { useNavigate } from "react-router";
import { Modal } from "antd";
import { sowWarn } from "../Messages";
let PageSize = 15;

const StyledTableCell = styled(TableCell)(({ theme }) => ({
  [`&.${tableCellClasses.head}`]: {
    backgroundColor: "#1e40af",
    color: theme.palette.common.white,
  },
  [`&.${tableCellClasses.body}`]: {
    fontSize: 14,
  },
}));
const StyledTableRow = styled(TableRow)(({ theme }) => ({
  "&:nth-of-type(odd)": {
    backgroundColor: theme.palette.action.hover,
  },
  // hide last border
  "&:last-child td, &:last-child th": {
    border: 0,
  },
}));

const AssignCoach = () => {
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('');
  const [showLogsDetails, setShowLogsDetails] = useState({ isOpen: false, logs: [] });
  console.log("showLogsDetails", showLogsDetails);
  const [getList, setGetList] = useState()
  useEffect(() => {
    allAtheletes()

  }, [])
  const allAtheletes = async () => {

    const response = await getAllUserSubscription()
    setIsLoading(false)

    setGetList(response)
  }
  const [currentPage, setCurrentPage] = useState(1);
  const filteredList = useMemo(() => {
    return getList?.filter((row) => {
      // Implement your search logic here
      // For example, if you want to search by a specific property like 'name':
      const firstnameMatches = row?.subscription?.user?.firstname?.toLowerCase().includes(searchTerm?.toLowerCase());
      const emailMatches = row?.subscription?.user?.email?.toLowerCase().includes(searchTerm?.toLowerCase());
      return firstnameMatches || emailMatches;
      
    });
  }, [getList, searchTerm]);
  const checkLastPage = useMemo(() => {
    let frstPgae = (currentPage - 1) * PageSize;
    let lastPage = frstPgae + PageSize;
    return filteredList?.slice(frstPgae, lastPage)?.map((row, index) => ({
      ...row,
      // Adjusting index on the first page and adding count from the second page onward
      srID: index + 1 + (currentPage > 1 ? frstPgae : 0),
    }));
  }, [currentPage, filteredList]);
  const handleSearchChange = (event) => {
    setSearchTerm(event.target.value);
    setCurrentPage(1); // Reset the current page when the search term changes
  };
  const handlePageChange = (event, page) => {
    setCurrentPage(page);
  };
  const getcoachLogs = async (id, programID) => {
    const response = await getChangeLogDEtails(id, programID)
    console.log("sdsaa", response);
    if (response?.length > 0) {

      setShowLogsDetails({ isOpen: true, logs: response })
    } else {
      sowWarn("NO details found")
    }
    console.log("response", response);
  }
  return (
    <div className="zones-form-container absolute top-20 w-[100vw]">
      <Header />

      {/* <div
        className="zones-form-main"
        style={{ fontSize: "2rem", textAlign: "center" }}
      >
        Assigned Coach
      </div> */}

      <Grid xs={12} md={12}>
        <div className="table-section">
          <div className="zone-table-title">

          </div>
          <div className="zone-table">
            <TableContainer component={Paper}>
              <div style={{ fontSize: "18px", background: "#FFEADC", width: "100%", display: "flex",justifyContent:"space-between" }}>
              <div style={{marginLeft:"10px",marginTop: "15px"}}>Assigned Coach</div>
                <div style={{ padding: "10px", margin: "0" }}>
                  <TextField type="text" size="small" value={searchTerm} onChange={handleSearchChange} placeholder="Search by Username or Email.." />
                </div>
              </div>
              <Table sx={{ minWidth: 700, padding: "10px" }} aria-label="customized table">
                <TableHead>
                  <TableRow>
                    <StyledTableCell align="left">Sr ID</StyledTableCell>
                    <StyledTableCell align="left">Program Name</StyledTableCell>
                    <StyledTableCell align="left">User Name</StyledTableCell>
                    <StyledTableCell align="left">Email </StyledTableCell>
                    <StyledTableCell align="left">Current Coach </StyledTableCell>
                    <StyledTableCell align="left">Action </StyledTableCell>

                  </TableRow>
                </TableHead>
                <TableBody>
                  {isLoading ? (
                    <CircularProgress className="m-6" />) : (
                    <>
                      {checkLastPage?.length > 0 ? (
                        <>
                          {checkLastPage?.map((row,index) => (
                            <StyledTableRow key={row?.subscription?.subscription_id}>
                              <StyledTableCell align="left">
                                {index+1}
                              </StyledTableCell>
                              <StyledTableCell align="left">
                                {row?.subscription?.program?.program_name}

                              </StyledTableCell>
                              <StyledTableCell align="left">
                                {row?.subscription?.user?.firstname}

                              </StyledTableCell>
                              <StyledTableCell align="left">
                                {row?.subscription?.user?.email ? row?.subscription?.user?.email : "NA"}
                              </StyledTableCell>
                              <StyledTableCell align="left">
                                {row?.coaches[0]?.coach?.firstname ? `${row?.coaches[0]?.coach?.firstname} ${row?.coaches[0]?.coach?.lastname}` : "NA"}
                              </StyledTableCell>
                              <StyledTableCell align="left">
                                {row?.coaches?.length > 0 ?

                                  <Button className="ant-btn-primary" variant="contained"
                                    onClick={() => {
                                      navigate("/coach-page");
                                      let data = {
                                        athlete_id: row?.subscription?.user_id,
                                        program_id: row?.subscription?.program?.program_id,
                                        oldcoach_id: row?.coaches[0]
                                      }
                                      localStorage.setItem("athleteDetails", JSON.stringify(data))

                                      // ?userId=${row?.subscription?.user_id}
                                    }}
                                  >Change Coach </Button>
                                  :
                                  <Button className="ant-btn-primary" variant="contained" onClick={() => {
                                    navigate("/coach-page");
                                    let data = {
                                      atheleteid: row?.subscription?.user_id,
                                      activityid: row?.subscription?.program?.program_id
                                    }
                                    localStorage.setItem("athleteDetails", JSON.stringify(data))

                                    // ?userId=${row?.subscription?.user_id}
                                  }} >Assign Coach</Button>
                                }
                                &nbsp;
                                {row?.coaches?.length > 0 &&

                                  <Button className="ant-btn-primary" variant="contained"
                                    onClick={() => getcoachLogs(row?.subscription?.user_id, row?.subscription?.program?.program_id)}
                                  >Coach change logs</Button>}


                              </StyledTableCell>
                            </StyledTableRow>
                          ))}
                        </>
                      ) : (
                        <div className="p-4">No data found</div>
                      )}
                    </>
                  )}
                </TableBody>
              </Table>
            </TableContainer>
            &nbsp;
            <div className="flex justify-end">
              <Pagination
                count={Math.ceil(filteredList?.length / PageSize)} // Calculate total number of pages
                color="primary"
                page={currentPage}
                onChange={handlePageChange}
              />
            </div>
          </div>

        </div>
      </Grid>

      <Modal
        title="Show logs details"
        centered
        open={showLogsDetails?.isOpen}
        onCancel={() => setShowLogsDetails({ isOpen: false, logs: [] })}
        footer={null}
      >
        <div>
          {showLogsDetails?.logs?.map((logsDetails) => {
            return (

              <div style={{ borderBottom: "1px solid gray", paddingBottom: "24px" }}>

                <div style={{ display: "flex" }}>
                  <h3 style={{ fontWeight: 600 }}>Old Coach Name :</h3>
                  <h3>{logsDetails?.old_coach?.firstname ? `${logsDetails?.old_coach?.firstname} ${logsDetails?.old_coach?.lastname}` : "NA"}</h3>
                </div>
                <div style={{ display: "flex" }}>
                <h3 style={{ fontWeight: 600 }}>Old Coach Email :</h3>
                <h3>{logsDetails?.old_coach?.email ? `${logsDetails?.old_coach?.email}` : "NA"}</h3>
              </div>
                <div style={{ display: "flex" }}>
                  <h3 style={{ fontWeight: 600 }}>New Coach Name :</h3>
                  <h3>{logsDetails?.new_coach?.firstname ? `${logsDetails?.new_coach?.firstname} ${logsDetails?.new_coach?.lastname}` : "NA"}</h3>
                </div>
              
                <div style={{ display: "flex" }}>
                  <h3 style={{ fontWeight: 600 }}>New Coach Email :</h3>
                  <h3>{logsDetails?.new_coach?.email ? `${logsDetails?.new_coach?.email}` : "NA"}</h3>
                </div>
              </div>
            )
          })}

        </div>
      </Modal>


    </div>
  );
};



export default AssignCoach
