import { useState, useEffect } from "react";
import { Button } from "../ui/button";
import { Input } from "../ui/input";
import { Label } from "../ui/label";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "../ui/dialog";
import {
	createActivityGroupdata,
	updateActivityGroupdata,
	uploadsaveFile,
} from "../../API/api-endpoint";
import Swal from "sweetalert2";

export const ActivityGroupDialog = ({
	open,
	onClose,
	onSuccess,
	editingItem,
}) => {
	const [formData, setFormData] = useState({
		activity_group: "",
		badge: "",
	});
	const [isLoading, setIsLoading] = useState(false);
	const [previewImage, setPreviewImage] = useState(null);
	const [selectedFile, setSelectedFile] = useState(null);

	useEffect(() => {
		if (open) {
			if (editingItem?.id) {
				const editData = {
					activity_group: editingItem.activity_group || "",
					badge: editingItem.badge || "",
				};
				setFormData(editData);
				setSelectedFile(null);

				if (editingItem.badge) {
					setPreviewImage(
						`${process.env.REACT_APP_API_BASE_URL}/static/public/userimages/${editingItem.badge}`
					);
				} else {
					setPreviewImage(null);
				}
			} else {
				const newData = {
					activity_group: "",
					badge: "",
				};
				setFormData(newData);
				setSelectedFile(null);
				setPreviewImage(null);
			}
		}
	}, [open, editingItem]);

	const handleInputChange = (field, value) => {
		setFormData((prev) => ({
			...prev,
			[field]: value,
		}));
	};

	const handleFileChange = async (e) => {
		const file = e.target.files[0];
		if (file) {
			setSelectedFile(file);

			const reader = new FileReader();
			reader.onload = (e) => {
				setPreviewImage(e.target.result);
			};
			reader.readAsDataURL(file);

			try {
				const formData = new FormData();
				formData.append("file", file);

				const response = await uploadsaveFile(formData);
				if (response?.status) {
					setFormData((prev) => ({
						...prev,
						badge: response.file,
					}));
				}
			} catch (error) {
				console.error("Error uploading file:", error);
				Swal.fire({
					title: "Error",
					text: "Failed to upload image. Please try again.",
					icon: "error",
					timer: 3000,
					showConfirmButton: false,
				});
			}
		}
	};

	const handleSubmit = async (e) => {
		e.preventDefault();

		if (!formData.activity_group.trim()) {
			Swal.fire({
				title: "Error",
				text: "Activity group name is required",
				icon: "error",
				timer: 1800,
				showConfirmButton: false,
			});
			return;
		}

		if (!editingItem?.id && !formData.badge) {
			Swal.fire({
				title: "Error",
				text: "Badge image is required",
				icon: "error",
				timer: 1800,
				showConfirmButton: false,
			});
			return;
		}

		try {
			setIsLoading(true);

			const apiData = {
				activity_group: formData.activity_group.trim(),
				badge: formData.badge,
			};

			let response;
			if (editingItem?.id) {
				apiData.id = editingItem.id;
				response = await updateActivityGroupdata(apiData);
			} else {
				response = await createActivityGroupdata(apiData);
			}

			if (response?.status) {
				Swal.fire({
					title: "Success",
					text:
						response.message ||
						`Activity group ${
							editingItem?.id ? "updated" : "created"
						} successfully`,
					icon: "success",
					timer: 2000,
					showConfirmButton: false,
				});
				onSuccess();
			} else {
				Swal.fire({
					title: "Error",
					text: response?.message || "Failed to save activity group",
					icon: "error",
					timer: 3000,
					showConfirmButton: false,
				});
			}
		} catch (error) {
			console.error("Error saving activity group:", error);
			Swal.fire({
				title: "Error",
				text: "An error occurred while saving the activity group",
				icon: "error",
				timer: 3000,
				showConfirmButton: false,
			});
		} finally {
			setIsLoading(false);
		}
	};

	return (
		<Dialog open={open} onOpenChange={onClose}>
			<DialogContent className='sm:max-w-2xl bg-white max-h-[90vh] overflow-y-auto'>
				<DialogHeader>
					<DialogTitle className='text-lg font-semibold text-gray-900'>
						{editingItem?.id
							? "Edit Activity Group"
							: "Create Activity Group"}
					</DialogTitle>
				</DialogHeader>

				<form onSubmit={handleSubmit} className='space-y-4'>
					<div className='grid gap-4'>
						<div className='space-y-2'>
							<Label
								htmlFor='activity_group'
								className='text-sm font-semibold'
							>
								Activity Group Name{" "}
								<span className='text-red-500'>*</span>
							</Label>
							<Input
								id='activity_group'
								className='w-full text-sm'
								value={formData.activity_group}
								onChange={(e) =>
									handleInputChange(
										"activity_group",
										e.target.value
									)
								}
								placeholder='Enter activity group name'
								disabled={isLoading}
								required
							/>
						</div>

						<div className='space-y-2'>
							<Label
								htmlFor='badge'
								className='text-sm font-semibold'
							>
								Badge Image{" "}
								{!editingItem?.id && (
									<span className='text-red-500'>*</span>
								)}
							</Label>
							<Input
								id='badge'
								type='file'
								accept='image/*'
								className='w-full text-sm'
								onChange={handleFileChange}
								disabled={isLoading}
								required={!editingItem?.id}
							/>
						</div>
					</div>

					<div className='flex justify-end gap-3 pt-4'>
						<Button
							type='button'
							variant='outline'
							onClick={onClose}
							disabled={isLoading}
						>
							Cancel
						</Button>
						<Button
							type='submit'
							className='bg-orange-600 hover:bg-orange-700 text-white'
							disabled={isLoading}
						>
							{isLoading
								? "Saving..."
								: editingItem?.id
								? "Update Activity Group"
								: "Create Activity Group"}
						</Button>
					</div>
				</form>
			</DialogContent>
		</Dialog>
	);
};
