import React, { useEffect, useMemo, useState } from "react";
import Paper from "@mui/material/Paper";
import Table from "@mui/material/Table";
import TableBody from "@mui/material/TableBody";
import TableContainer from "@mui/material/TableContainer";
import TableHead from "@mui/material/TableHead";
import TableRow from "@mui/material/TableRow";
import TableCell, { tableCellClasses } from "@mui/material/TableCell";
import { styled } from "@mui/material/styles";
import { Button, CircularProgress, FormLabel, Pagination, TextField } from "@mui/material";
import Header from "../../components/Header";
import { createAdminCoachPrivilige, deleteCoachPriviliage, genrateInvoice, getManageAllCoach, getManageSubscription, putOnHold, putunHold, updateSubscription } from "../../API/api-endpoint";
import { Modal } from "antd";
import { ExclamationCircleFilled } from '@ant-design/icons';
import { showError, showSuccess } from "../../components/Messages";
import Swal from "sweetalert2";
import moment from "moment";

const StyledTableCell = styled(TableCell)(({ theme }) => ({
    [`&.${tableCellClasses.head}`]: {
        backgroundColor: "#1e40af",
        color: theme.palette.common.white,
    },
    [`&.${tableCellClasses.body}`]: {
        fontSize: 14,
    },
}));
const StyledTableRow = styled(TableRow)(({ theme }) => ({
    "&:nth-of-type(odd)": {
        backgroundColor: theme.palette.action.hover,
    },
    // hide last border
    "&:last-child td, &:last-child th": {
        border: 0,
    },
}));
const CoachAccess = () => {
    const { confirm } = Modal;
    let PageSize = 15;

    const [formValue, setFormValue] = useState({})
    const [isOpenModal, setIsOpenModal] = useState({ isOpen: false, id: "" })
    const [searchTerm, setSearchTerm] = useState('');
    const [isLoading, setIsLoading] = useState(true)

    const [reportData, setReportData] = useState()
    console.log("reportData",reportData);
    const [currentPage, setCurrentPage] = useState(1);

    const fetchReport = async () => {
        const response = await getManageAllCoach()
        setIsLoading(false)
        setReportData(response)
    }
    useEffect(() => {
        fetchReport()
    }, [])
    const handlUpdateSubscrtion = async (id) => {
        if (formValue?.endDate) {
            let data = {
                sub_id: isOpenModal?.id,
                endDate: formValue?.endDate
            }
            const response = await updateSubscription(data)
            if (response?.status) {
                fetchReport()
                setIsOpenModal({ isOpen: false, id: "" })
            }
        }
        else {
            showError("Please add date")
        }


    }
    const handleCreateAdmin = async (id) => {
        const response = await createAdminCoachPrivilige(id)
        if (response?.status) {
            fetchReport()
            Swal.fire({
                title: "Success",
                text: response.message,
                icon: "success",
              });
        }
        console.log("rseadrsad", response);
    }
    const handleRevoke = async (id) => {
        const response = await deleteCoachPriviliage(id)
        if (response?.status) {
            showSuccess(response?.message)
            fetchReport()
        }else{
            showError(response?.message)
        }
        console.log("rseadrsad", response);
    }

    const showConfirmPutOnunhold = (id) => {
        confirm({
            title: 'Do you Want to revoke this coach?',
            icon: <ExclamationCircleFilled />,
            onOk() {
                handleRevoke(id)
            },
            onCancel() {
                console.log('Cancel');
            },
        });
    };
    const showConfirmCreate = (id) => {
        confirm({
            title: 'Do you Want to give grant access of this coach?',
            icon: <ExclamationCircleFilled />,
            onOk() {
                handleCreateAdmin(id)
            },
            onCancel() {
                console.log('Cancel');
            },
        });
    };
    const filteredList = useMemo(() => {
        return reportData?.filter((row) => {
            // Implement your search logic here
            // For example, if you want to search by a specific property like 'name':
            const firstnameMatches = row?.firstname?.toLowerCase().includes(searchTerm?.toLowerCase());
            const emailMatches = row?.email?.toLowerCase().includes(searchTerm?.toLowerCase());
            return firstnameMatches || emailMatches;
        });
    }, [reportData, searchTerm]);
    
    const checkLastPage = useMemo(() => {
        let frstPgae = (currentPage - 1) * PageSize;
        let lastPage = frstPgae + PageSize;
        return filteredList?.slice(frstPgae, lastPage)?.map((row, index) => ({
            ...row,
            // Adjusting index on the first page and adding count from the second page onward
            srID: index + 1 + (currentPage > 1 ? frstPgae : 0),
        }));
    }, [currentPage, filteredList]);
    const handleSearchChange = (event) => {
        setSearchTerm(event.target.value);
        setCurrentPage(1); // Reset the current page when the search term changes
    };
    const handlePageChange = (event, page) => {
        setCurrentPage(page);
    };
    return (
        <div>
            <Header />
            <div className="grid grid-cols-1 xl:grid-cols-5 items-start gap-x-4"></div>
            <div style={{ marginTop: "100px", padding: "20px" }}>
                <TableContainer component={Paper}>
                    <div style={{ fontSize: "18px", background: "#FFEADC", width: "100%", padding: "10px",display:"flex",justifyContent: "space-between" }}>
                        <div style={{marginLeft:"10px",marginTop: "15px"}}>Coach Admin Privileges</div>
                        <div style={{ padding: "10px", margin: "0" }}>
                            <TextField type="text" size="small" value={searchTerm} onChange={handleSearchChange} placeholder="Search By Name or Email.." />
                        </div>
                    </div>
                    <Table sx={{ minWidth: 700, padding: "10px" }} aria-label="customized table">
                        <TableHead>
                            <TableRow>
                                <StyledTableCell align="left">Sr ID</StyledTableCell>
                                <StyledTableCell align="left">Coach Name</StyledTableCell>
                                <StyledTableCell align="left">Email</StyledTableCell>
                                <StyledTableCell align="left">Actions</StyledTableCell>
                            </TableRow>
                        </TableHead>
                        <TableBody>
                        {isLoading ? (
                            <CircularProgress className="m-6" />) : (
                            <>
                            {checkLastPage?.length > 0 ? (
                                <>
                                    {checkLastPage?.map((row, index) => (
                                        <StyledTableRow key={index}>
                                            <StyledTableCell align="left">
                                                {index + 1}
                                            </StyledTableCell>
                                            <StyledTableCell align="left">
                                                {row?.firstname} {row?.lastname}

                                            </StyledTableCell>

                                            <StyledTableCell align="left">
                                                {row?.email}
                                            </StyledTableCell>
                                            <StyledTableCell align="left">
                                            <Button disabled={row?.adminprivileges?.coach_id} variant="contained" style={{ marginBottom: "10px",backgroundColor:"#E67E22" }}
                                            onClick={() => showConfirmCreate(row?.id)}
                                        >Grant </Button> &nbsp;
                                                <Button disabled={!row?.adminprivileges?.coach_id} variant="contained" style={{ marginBottom: "10px",backgroundColor:"#E67E22" }} 
                                                    onClick={() => showConfirmPutOnunhold(row?.id)}
                                                >Revoke</Button>
                                            </StyledTableCell>
                                        </StyledTableRow>
                                    ))}
                                </>
                            ) : (
                                <div className="p-4">No data found</div>
                            )}
                            </>
                            )}
                        </TableBody>
                    </Table>
                </TableContainer>
                &nbsp;
                <div className="flex justify-end">
                    <Pagination
                        count={Math.ceil(filteredList?.length / PageSize)} // Calculate total number of pages
                        color="primary"
                        page={currentPage}
                        onChange={handlePageChange}
                    />
                </div>
            </div>
            <Modal
                title="Edit Subscription Dates"
                centered
                open={isOpenModal?.isOpen}
                onOk={() => handlUpdateSubscrtion()}
                onCancel={() => setIsOpenModal({ isOpen: false, id: "" })}
            >
                
                <div style={{ borderBottom: "1px solid gray", paddingBottom: "24px" }}>
                    <FormLabel style={{ textAlign: "center", fontWeight: 600 }} >End Date:</FormLabel>
                    <br />
                    <TextField
                        placeholder="Added Date and Time"
                        size="small"
                        type="date"
                        name="added_datetime"
                        value={formValue?.endDate}
                        onChange={(e) => {
                            setFormValue({ ...formValue, endDate: e.target.value })
                        }}
                        inputProps={{
                            min: formValue?.endDate ? formValue?.endDate : new Date().toISOString().split('T')[0], // Set min date as today
                        }}
                    />
                </div>
            </Modal>
        </div>
    )
}

export default CoachAccess
