import React from 'react'
import { DatePicker } from '@mantine/dates'
import Header from '../components/Header'

const CoachPaymentPage = () => {
  return (
    <>
      <Header />
      {/* Topbar */}
      <div className='p-6'>
        <div className='flex items-end gap-x-2'>
          <h1 className='font-medium text-xl'>Coach Payment</h1>
        </div>
        <br />
        <div className='flex flex-col md:flex-row gap-4 md:gap-8 items-start md:items-end mb-6'>
          <div>
            <DatePicker placeholder='Pick date' label='Select from month' />
          </div>
          <div>
            <DatePicker placeholder='Pick date' label='Select from year' />
          </div>
          <div>
            <DatePicker placeholder='Pick date' label='Select to month' />
          </div>
          <div>
            <DatePicker placeholder='Pick date' label='Select to year' />
          </div>
          <div>
            <button className='py-1.5 px-5 bg-blue-500 text-slate-50 rounded text-base'>
              Show payslip
            </button>
          </div>
        </div>
        {/* Table */}
        <div className='w-full mb-6'>
          <div className='my-6'>
            <div className='flex flex-col'>
              <div className='overflow-x-auto shadow-md'>
                <div className='inline-block min-w-full align-middle'>
                  <div className='overflow-hidden '>
                    <table className='min-w-full divide-y divide-gray-200 table-fixed dark:divide-gray-700'>
                      <thead className='bg-gray-100 '>
                        <tr>
                          <th
                            scope='col'
                            className='py-3 px-6 text-xs font-medium tracking-wider text-left text-gray-700 uppercase dark:text-gray-400'
                          >
                            Coach
                          </th>
                          <th
                            scope='col'
                            className='py-3 px-6 text-xs font-medium tracking-wider text-left text-gray-700 uppercase dark:text-gray-400'
                          >
                            Payment details
                          </th>
                          <th
                            scope='col'
                            className='py-3 px-6 text-xs font-medium tracking-wider text-left text-gray-700 uppercase dark:text-gray-400'
                          >
                            Date
                          </th>
                        </tr>
                      </thead>
                      <tbody className='bg-white divide-y divide-gray-200 '></tbody>
                    </table>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  )
}

export default CoachPaymentPage
