import { useEffect, useState, useMemo, useCallback } from "react";
import { <PERSON><PERSON> } from "../../components/ui/button";
import { Input } from "../../components/ui/input";
import { Card, CardContent, CardHeader } from "../../components/ui/card";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "../../components/ui/select";
import {
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableHeader,
	TableRow,
} from "../../components/ui/table";
import { Edit, Trash2, Plus } from "lucide-react";
import { DiscountCouponDialog } from "../../components/admin/discount-coupon-dialog";
import { DeleteConfirmDialog } from "../../components/admin/delete-confirm-dialog";
import Header from "../../components/Header";
import {
	getdiscountcouponData,
	deletediscountcouponData,
} from "../../API/api-endpoint";
import Swal from "sweetalert2";
import moment from "moment";

const DiscountCoupon = () => {
	const [couponData, setCouponData] = useState([]);
	const [isLoading, setIsLoading] = useState(true);
	const [searchTerm, setSearchTerm] = useState("");
	const [selectedTypeFilter, setSelectedTypeFilter] = useState("All");
	const [currentPage, setCurrentPage] = useState(1);
	const [showDialog, setShowDialog] = useState(false);
	const [editingItem, setEditingItem] = useState(null);
	const [deleteId, setDeleteId] = useState(null);
	const pageSize = 10;

	const fetchData = useCallback(async () => {
		try {
			setIsLoading(true);
			const response = await getdiscountcouponData();

			if (response) {
				setCouponData(response || []);
			} else {
				setCouponData([]);
			}
		} catch (error) {
			console.error("Error fetching discount coupon data:", error);
			Swal.fire({
				title: "Error",
				text: "Failed to fetch discount coupon data. Please try again.",
				icon: "error",
				timer: 3000,
				showConfirmButton: false,
			});
			setCouponData([]);
		} finally {
			setIsLoading(false);
		}
	}, []);

	useEffect(() => {
		fetchData();
	}, [fetchData]);

	const filteredData = useMemo(() => {
		return couponData.filter((item) => {
			const matchesSearch =
				!searchTerm.trim() ||
				item?.coupon?.toLowerCase().includes(searchTerm.toLowerCase());

			const matchesType =
				selectedTypeFilter === "All" ||
				item?.type?.toLowerCase().trim() ===
					selectedTypeFilter.toLowerCase();

			return matchesSearch && matchesType;
		});
	}, [couponData, searchTerm, selectedTypeFilter]);

	const paginatedData = useMemo(() => {
		const startIndex = (currentPage - 1) * pageSize;
		const endIndex = startIndex + pageSize;
		return filteredData.slice(startIndex, endIndex);
	}, [filteredData, currentPage, pageSize]);

	const totalPages = Math.ceil(filteredData.length / pageSize);

	const handleSearch = (value) => {
		setSearchTerm(value);
		setCurrentPage(1);
	};

	const handleTypeFilterChange = (value) => {
		setSelectedTypeFilter(value);
		setCurrentPage(1);
	};

	const handleCreate = () => {
		setEditingItem(null);
		setShowDialog(true);
	};

	const handleEdit = (item) => {
		setEditingItem(item);
		setShowDialog(true);
	};

	const handleDelete = async (id) => {
		try {
			const response = await deletediscountcouponData(id);

			Swal.fire({
				title: "Success",
				text: "Discount coupon deleted successfully",
				icon: "success",
				timer: 2000,
				showConfirmButton: false,
			});

			setCurrentPage(1);
			fetchData();
		} catch (error) {
			console.error("Error deleting discount coupon:", error);
			Swal.fire({
				title: "Error",
				text: "Failed to delete discount coupon. Please try again.",
				icon: "error",
				timer: 3000,
				showConfirmButton: false,
			});
		}
	};

	const handleDialogSuccess = () => {
		setShowDialog(false);
		setEditingItem(null);
		fetchData();
	};

	return (
		<div>
			<Header />
			<div className='mx-auto p-6 max-w-[1400px] mt-16'>
				<Card>
					<CardHeader className='bg-orange-50 border-b flex flex-col gap-4'>
						<div className='flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4'>
							<div>
								<h1 className='text-2xl font-bold text-orange-900'>
									Discount Coupons
								</h1>
								<p className='text-orange-700 mt-1 text-sm'>
									Manage discount coupons for subscription
									packages
								</p>
							</div>
						</div>
						<div className='flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4'>
							<div className='flex gap-3'>
								<Button
									onClick={handleCreate}
									className='bg-orange-600 hover:bg-orange-700 text-white'
								>
									<Plus className='h-4 w-4 mr-2' />
									Create Discount Coupon
								</Button>
							</div>

							<div className='flex gap-3'>
								<Input
									placeholder='Search by coupon code...'
									value={searchTerm}
									onChange={(e) =>
										handleSearch(e.target.value)
									}
									className='w-full text-sm'
								/>
							</div>
						</div>
					</CardHeader>
					<CardContent className='p-0'>
						<div className='overflow-x-auto'>
							<Table className='min-w-full w-full'>
								<TableHeader>
									<TableRow className='bg-blue-600 hover:bg-blue-600'>
										<TableHead className='text-white font-semibold'>
											Sr No
										</TableHead>
										<TableHead className='text-white font-semibold'>
											Coupon
										</TableHead>
										<TableHead className='text-white font-semibold'>
											Discount
										</TableHead>
										<TableHead className='text-white font-semibold'>
											Subscription Name
										</TableHead>
										<TableHead className='text-white font-semibold'>
											Type
										</TableHead>
										<TableHead className='text-white font-semibold'>
											Current Usage
										</TableHead>
										<TableHead className='text-white font-semibold'>
											Max Usage
										</TableHead>
										<TableHead className='text-white font-semibold'>
											Valid To
										</TableHead>
										<TableHead className='text-white font-semibold'>
											Actions
										</TableHead>
									</TableRow>
								</TableHeader>
								<TableBody>
									{isLoading ? (
										<TableRow>
											<TableCell
												colSpan={9}
												className='text-center py-8'
											>
												Loading...
											</TableCell>
										</TableRow>
									) : paginatedData.length > 0 ? (
										paginatedData.map((item, index) => (
											<TableRow
												key={item.id || index}
												className='hover:bg-gray-50'
											>
												<TableCell className='font-medium'>
													{(currentPage - 1) *
														pageSize +
														index +
														1}
												</TableCell>
												<TableCell>
													{item?.coupon || "N/A"}
												</TableCell>
												<TableCell>
													{item?.discount
														? `${item.discount}%`
														: "N/A"}
												</TableCell>
												<TableCell>
													{item?.subscriptionpackage
														?.name || "N/A"}
												</TableCell>
												<TableCell>
													<span
														className={`px-2 py-1 rounded-full text-xs font-medium ${
															item?.type ===
															"percentage"
																? "bg-green-100 text-green-800"
																: "bg-blue-100 text-blue-800"
														}`}
													>
														{item?.type || "N/A"}
													</span>
												</TableCell>
												<TableCell>
													{item?.current_usage || "0"}
												</TableCell>
												<TableCell>
													{item?.max_usage || "N/A"}
												</TableCell>
												<TableCell>
													{item?.valid_to
														? moment(
																item.valid_to
														  ).format("DD-MM-YYYY")
														: "N/A"}
												</TableCell>
												<TableCell>
													<div className='flex gap-2'>
														<Button
															variant='ghost'
															size='sm'
															onClick={() =>
																handleEdit(item)
															}
															className='text-blue-600 hover:text-blue-800'
														>
															<Edit className='h-4 w-4' />
														</Button>
														<Button
															variant='ghost'
															size='sm'
															onClick={() =>
																setDeleteId(
																	item.id
																)
															}
															className='text-red-600 hover:text-red-800'
														>
															<Trash2 className='h-4 w-4' />
														</Button>
													</div>
												</TableCell>
											</TableRow>
										))
									) : (
										<TableRow>
											<TableCell
												colSpan={9}
												className='text-center py-8 text-gray-500'
											>
												No discount coupons found
											</TableCell>
										</TableRow>
									)}
								</TableBody>
							</Table>
						</div>

						{totalPages > 1 && (
							<div className='flex justify-center gap-2 p-4 border-t'>
								<Button
									variant='outline'
									size='sm'
									onClick={() =>
										setCurrentPage(
											Math.max(1, currentPage - 1)
										)
									}
									disabled={currentPage === 1}
								>
									Previous
								</Button>

								{Array.from({ length: 5 }, (_, i) => {
									const startPage =
										Math.floor((currentPage - 1) / 5) * 5 +
										1;
									const page = startPage + i;
									if (page > totalPages) return null;

									return (
										<Button
											key={page}
											variant={
												currentPage === page
													? "default"
													: "outline"
											}
											size='sm'
											onClick={() => setCurrentPage(page)}
											className={
												currentPage === page
													? "bg-orange-500 hover:bg-orange-600 text-white"
													: ""
											}
										>
											{page}
										</Button>
									);
								})}

								<Button
									variant='outline'
									size='sm'
									onClick={() =>
										setCurrentPage(
											Math.min(
												totalPages,
												currentPage + 1
											)
										)
									}
									disabled={currentPage === totalPages}
								>
									Next
								</Button>
							</div>
						)}
					</CardContent>
				</Card>
			</div>

			<DiscountCouponDialog
				open={showDialog}
				onClose={() => setShowDialog(false)}
				onSuccess={handleDialogSuccess}
				editingItem={editingItem}
			/>

			<DeleteConfirmDialog
				open={!!deleteId}
				onOpenChange={(open) => !open && setDeleteId(null)}
				onConfirm={() => {
					handleDelete(deleteId);
					setDeleteId(null);
				}}
				title='Delete Discount Coupon'
				description='Are you sure you want to delete this discount coupon? This action cannot be undone.'
			/>
		</div>
	);
};

export default DiscountCoupon;
