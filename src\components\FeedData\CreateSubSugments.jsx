import {
    Chip,
    FormControl,
    FormLabel,
    Grid,
    MenuItem,
    OutlinedInput,
    Select,
    TextField,
  } from "@mui/material";
  import { <PERSON><PERSON>, Modal } from "antd";
  import React, { useEffect, useState } from "react";
  import {
    CreateLevels,
    CreatePrograms,
    CreateZonesClasification,
    createAssesmentdata,
    createSubSugmentdata,
    createSugmentdata,
    getAllAssesmentData,
    getAllPrograms,
    getAllSugmentData,
    getAlllevels,
    updateAssesmentdata,
    updateLevel,
    updatePrograms,
    updateSubSugmentdata,
    updateSugmentdata,
    updateZonesClasification,
    weeklyFeedDataPattern,
    weeklyFeedDataProgram,
  } from "../../API/api-endpoint";
  import { useFormik } from "formik";
  import Swal from "sweetalert2";
  import SlickCarousel from "../../pages/SlickCarousel";
  const scoreData = [1, 2, 3, 4, 5];
  const CreateSubSugments = ({
    fetchReport,
    setShowAssesmentModal,
    showAssesmentModal,
    editData,
    setEditData,
  }) => {
    const [sugmentList, setSugmentList] = useState([]);
    console.log("editData", sugmentList);
    const formik = useFormik({
      initialValues: {
        name: "",
        segment_id: "",
        subsegment_percentage: "",
      },
      validate: (values) => {
        const errors = {};
        if (!values.segment_id) {
          errors.segment_id = "Sub segment name is required";
        }
        if (!values.name) {
          errors.name = "Name is required";
        }
        if (!values.subsegment_percentage) {
          errors.subsegment_percentage = "Percentage is required";
        }
        return errors;
      },
      // validationSchema: {},
      onSubmit: (values, { resetForm }) => {
        handleSubmitAssesmentForm(values, resetForm);
      },
    });
    console.log("formik", formik?.values);
    const sugmentData = async () => {
      const response = await getAllSugmentData();
      console.log("response", response);
      setSugmentList(response);
    };
    useEffect(() => {
      sugmentData();
    }, []);
  
    const handleSubmitAssesmentForm = async (data, resetForm) => {
      let response = "";
      if (editData?.id) {
        response = await updateSubSugmentdata(data);
      } else {
        response = await createSubSugmentdata(data);
      }
      if (response?.status) {
        Swal.fire({
          title: "Success",
          text: response.message,
          icon: "success",
        });
        setShowAssesmentModal(false);
        setEditData({});
        fetchReport();
        resetForm();
        formik?.setValues({ name: "", subsegment_percentage: "", comments: "" });
      } else {
        Swal.fire({
          title: "Error",
          text: response.message,
          icon: "error",
        });
      }
      console.log("response", response);
    };
    useEffect(() => {
      if (editData?.id) {
        const { srID, ...data } = editData;
        console.log("data", data);
  
        formik?.setValues(data);
        formik?.setFieldValue("comments", data?.comments ? data?.comments : "");
      } else {
        setEditData({});
      }
    }, [editData?.id]);
  
    return (
      <Modal
        width={1200}
        open={showAssesmentModal}
        onCancel={() => {
          setShowAssesmentModal(false);
          setEditData({});
          formik.resetForm();
          formik?.setValues({
            name: "",
            subsegment_percentage: "",
            comments: "",
          });
        }}
        footer={
          <div></div>
          //   loading={isLoading}
        }
      >
        <div className="headingCont">
          <span className="heading">{editData?.id ? "Edit " : "Create"}</span>{" "}
          <span className="orange heading">Sub-Segments</span>
        </div>
        {/* <h1>{editData ? editData.challengeId : values.challengeId}</h1> */}
        <div className="parentCont">
          <form className="form1" onSubmit={formik.handleSubmit}>
            <Grid container spacing={2}>
              <Grid item xs={12} sm={11}>
                <FormLabel>Sub Segment Name<span className="text-[red]">*</span></FormLabel>
  
                <TextField
                  fullWidth
                  size="small"
                  select
                  name="segment_id"
                  value={
                    formik?.values?.segment_id ? formik?.values?.segment_id : ""
                  }
                  onChange={formik.handleChange}
                  error={formik.touched.segment_id && formik.errors.segment_id}
                  helperText={
                    formik.touched.segment_id && formik.errors.segment_id
                  }
                  id="form-layouts-separator-select"
                  labelId="form-layouts-separator-select-label"
                  input={<OutlinedInput id="select-multiple-language" />}
                >
                  <MenuItem value={""} disabled>
                    Select Name
                  </MenuItem>
                  {sugmentList?.map((value, index) => {
                    return <MenuItem value={value?.id}>{value?.name}</MenuItem>;
                  })}
                </TextField>
              </Grid>
              <Grid item xs={12} sm={11}>
                <FormLabel>Name<span className="text-[red]">*</span></FormLabel>
  
                <TextField
                  fullWidth
                  placeholder="name"
                  size="small"
                  type="text"
                  name="name"
                  value={formik?.values?.name}
                  onChange={formik.handleChange}
                  error={formik.touched.name && formik.errors.name}
                  helperText={formik.touched.name && formik.errors.name}
                />
              </Grid>
              <Grid item xs={12} sm={11}>
                <FormLabel>Percentage<span className="text-[red]">*</span></FormLabel>
  
                <TextField
                  fullWidth
                  placeholder="Percentage"
                  size="small"
                  type="number"
                  name="subsegment_percentage"
                  value={formik?.values?.subsegment_percentage}
                  onChange={formik.handleChange}
                  error={
                    formik.touched.subsegment_percentage &&
                    formik.errors.subsegment_percentage
                  }
                  helperText={
                    formik.touched.subsegment_percentage &&
                    formik.errors.subsegment_percentage
                  }
                />
              </Grid>
              <Grid item xs={12} sm={11}>
                <FormLabel>Comments</FormLabel>
  
                <TextField
                  fullWidth
                  placeholder="comments"
                  size="small"
                  type="text"
                  name="comments"
                  value={formik?.values?.comments}
                  onChange={formik.handleChange}
                  error={formik.touched.comments && formik.errors.comments}
                  helperText={formik.touched.comments && formik.errors.comments}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <Button
                  className="btn"
                  key="submit"
                  type="primary"
                  onClick={() => formik.handleSubmit()}
                >
                  Submit
                </Button>
              </Grid>
            </Grid>
          </form>
          <div className="slick-container">
            <SlickCarousel />
          </div>
        </div>
      </Modal>
    );
  };
  export default CreateSubSugments;
  