import React from "react";
import { Bar } from "react-chartjs-2";
import {
    Chart as ChartJS,
    CategoryScale,
    LinearScale,
    BarElement,
    Title,
    Tooltip,
    Legend,
} from "chart.js";
import { timeToDecimalMinutes } from "../../../../utils/metricConversion";

// Register necessary Chart.js components
ChartJS.register(CategoryScale, LinearScale, BarElement, Title, Tooltip, Legend);

const PaceMetric = ({ activity, data }) => {
    const { plannedPace, actualPace, paceUnit } = data;
    console.log("activity --> ", activity);

    // Convert time values to decimal minutes
    const plannedMinutes = timeToDecimalMinutes(plannedPace);
    const actualMinutes = timeToDecimalMinutes(actualPace);

    // Chart Data
    const chartData = {
        labels: ["Pace"],
        datasets: [
            {
                label: "Planned",
                data: [plannedMinutes],
                backgroundColor: "rgba(54, 162, 235, 0.7)",
                barThickness: 20
            },
            {
                label: "Actual",
                data: [actualMinutes],
                backgroundColor: "rgba(255, 99, 132, 0.7)",
                barThickness: 20
            },
        ],
    };

    // Chart Options
    const options = {
        responsive: true,
        plugins: {
            title: {
                display: true,
                text: `Pace [planned(${plannedMinutes}${paceUnit}) v/s actual(${actualMinutes.toFixed(2)}${paceUnit})]`,
            },
            tooltip: {
                callbacks: {
                    label: function (context) {
                        return `${context.raw.toFixed(2)} ${paceUnit}`;
                    },
                },
            },
        },
        scales: {
            x: {
                barPercentage: 0.8, // Adjust bar width (0.1 to 1.0)
                categoryPercentage: 0.6, // Adjust category spacing (0.1 to 1.0)
            },
            y: {
                beginAtZero: true,
                title: {
                    display: true,
                    text: `Pace (${paceUnit})`,
                },
            },
        },
    };

    return <Bar data={chartData} options={options} />;
};

export default PaceMetric;
