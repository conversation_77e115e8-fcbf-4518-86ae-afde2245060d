import { Box, Typography } from '@mui/material'
import React, { memo } from 'react'
import "./../styles/footerFunctions.css"

function FooterFunctions({ setShowToolBox }) {
    return < >
        <Box className="sticky-footer">
            <Box className="input-box">
                <input type="text" placeholder='Type here...' className='type-msg' >
                </input>
                <img className='emoji-icon' height={17} src="/images/emoji-icon.png" alt="send" />
                <img className='camera-icon' height={17} src="/images/camera.png" alt="send" />
                <img
                    onClick={() => setShowToolBox(pre => !pre)}
                    className='plus-icon' height={15} src="/images/plus-more-icon.png" alt="send" />
            </Box>

            <Box className="send-msg-btn">
                <img height={17} src="/images/send-msg.png" alt="send" />
            </Box>
        </Box>
    </>
}

export default memo(FooterFunctions)