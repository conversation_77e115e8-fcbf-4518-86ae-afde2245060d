import { Button, TextField } from "@mui/material";
import React, { useState } from "react";

const initialValues = {
    phaseName: "",
  };

const CreatePhases = () => {
  const [values, setValues] = useState(initialValues);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    console.log(name, value);
    setValues({
      ...values,
      [name]: value,
    });
  };

  const handleSubmit = async () => {
    console.log(values);
    // try {
    //   const response = await axios.post(
    //     "https://api.example.com/endpoint",
    //     { data: values },
    //     {
    //       headers: {
    //         "Content-Type": "application/json",
    //          Authorization: "Bearer your_access_token_here",
    //       },
    //     }
    //   );
    //   console.log(response.data);
    // } catch (err) {
    //   console.log(err.message);
    // }
  };
  return (
    <div className="container">
      <div className="title">Create Phase</div>
      <div className="form-body">
        <div className="form-group">
          <label className="lable">Create Phase Name</label>
          <div className="activity-inputs">
            <TextField
              className="activity-input"
              id="outlined-basic"
              label="Running"
              variant="outlined"
              name="phaseName"
              value={values.phaseName}
              onChange={handleInputChange}
            />
          </div>
        </div>

        <div className="form-group">
          <Button
            sx={{ background: "black", marginLeft: "-4.5%" }}
            onClick={handleSubmit}
            variant="contained"
          >
            Submit
          </Button>
        </div>
      </div>
    </div>
  );
};

export default CreatePhases;
