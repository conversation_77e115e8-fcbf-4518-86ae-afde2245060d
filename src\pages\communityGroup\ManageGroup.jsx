import Header from "../../components/Header";
import { Modal, Table, Tag, Button } from "antd";
import { Grid, TextField } from "@mui/material";
import React, { useEffect, useMemo, useState } from "react";
import { URL, createCommunityGroup, fetchCommunityGroups, updateCommunityGroup, deleteCommunityGroup, listActivities } from "../../API/api-endpoint";
import Swal from "sweetalert2";
import AssignedChallenge from ".././Challenges/AssignedChallenge";
import ChallengeTrack from ".././Challenges/ChallengeTrack";
import { showInfo } from "../../components/Messages";
import "react-datepicker/dist/react-datepicker.css";
import { EditOutlined, DeleteOutlined } from '@ant-design/icons';
import { htmlToText } from 'html-to-text';
import CreateCommunityGroup from "./components/createCommunityGroup"
import { EditorState, convertToRaw, ContentState } from 'draft-js';
import htmlToDraft from "html-to-draftjs";
import { useRef } from 'react';


const ManageGroup = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [assignedChallengeModal, setShowAssignedChallengeModal] = useState(false);
  const [showTrackChellenges, setShowTrackChellenges] = useState(false);
  const [editData, setEditData] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [name, setName] = useState("");
  const [description, setDescription] = useState("");
  const [location, setLocation] = useState("");
  const [logo, setLogo] = useState(null);
  const [activity, setActivity] = useState([]);
  const [communityGroups, setCommunityGroups] = useState([]);
  const [isImageModalVisible, setIsImageModalVisible] = useState(false);
  const [selectedImage, setSelectedImage] = useState("");
  const [selectedActivities, setSelectedActivities] = useState([]);
  const [editorState, setEditorState] = useState(
    description
      ? EditorState.createWithContent(ContentState.createFromBlockArray(
          htmlToDraft(description).contentBlocks
        ))
      : EditorState.createEmpty()
  );
  const fileInputRef = useRef(null);

  const ITEM_HEIGHT = 48;
  const ITEM_PADDING_TOP = 8;

  // Load groups on initial render
  useEffect(() => {
    fetchCommunityGroups().then(response => setCommunityGroups(response?.data?.data));
  }, []);

  const filteredList = useMemo(() => {
    if (!communityGroups) return [];
    return communityGroups.filter(group =>
      group.communityName.toLowerCase().includes(searchTerm.toLowerCase())
    );
  }, [communityGroups, searchTerm]);

  const handleEdit = (record) => {
    console.log(record.communityLogo,"record");
    setEditData(record);
    setName(record.communityName || "");
    setDescription(record.groupDescription || "");
    setLocation(record.location || "");
    setSelectedActivities(record.activities || []);
    const html = record.groupDescription || "";
    const blocks = htmlToDraft(html);
    const contentState = ContentState.createFromBlockArray(blocks.contentBlocks);
    setEditorState(EditorState.createWithContent(contentState));

    // Leave logo empty so the user can choose a new file if needed
    setLogo(record.communityLogo || null);
    setIsModalOpen(true);
  };

  const handleDeleteConfirmation = async (record) => {
    Swal.fire({
      title: "Are you sure?",
      text: "Do you really want to delete this community group? This action cannot be undone.",
      icon: "warning",
      showCancelButton: true,
      confirmButtonColor: "#3085d6",
      cancelButtonColor: "#d33",
      confirmButtonText: "Yes, delete it!"
    }).then(async (result) => {
      if (result.isConfirmed) {
        const deleteResponse = await deleteCommunityGroup(record);
        if (deleteResponse.data.status === 200) {
          Swal.fire({
            title: "Success",
            text: deleteResponse.data.message,
            icon: "success",
          }).then(() => {
            fetchCommunityGroups().then(response => setCommunityGroups(response?.data?.data));
          });
        } else {
          Swal.fire({
            title: "Error",
            text: deleteResponse.data.message || "Failed to delete community group",
            icon: "error",
          });
        }
      }
    });
  };

  const columns = [
    {
      title: "Logo",
      dataIndex: `communityLogo`,
      key: "communityLogo",
      render: (url) => {
        const imageUrl = `${URL}/views/uploads/athleteCommunityGroups/${url}`;
        return (
          <img
            src={imageUrl}
            alt="Community Logo"
            style={{ width: 50, height: 50, objectFit: "cover" }}
            onClick={() => {
              setSelectedImage(imageUrl);
              setIsImageModalVisible(true);
            }}
          />
        );
      },
    },
    {
      title: "Name",
      dataIndex: "communityName",
      key: "communityName",
    },
    {
      title: "Description",
      dataIndex: "groupDescription",
      key: "groupDescription",
      render: (description) => {
        const plainText = htmlToText(description, { wordwrap: 130 });
        return plainText;
      },
    },
    {
      title: "Location",
      dataIndex: "location",
      key: "location",
    },
    {
      title: "Activities",
      dataIndex: "activities",
      key: "activities",
      render: (activities) => {
        return activities && activities.length > 0 ? (
          activities.map((act) => (
            <Tag key={act.id}>{act.activity_name}</Tag>
          ))
        ) : (
          "No Activity"
        );
      },
    },
    {
      title: "Is Active",
      dataIndex: "isActive",
      key: "isActive",
      render: (value) => (value ? "Yes" : "No"),
    },
    {
      title: "Action",
      key: "action",
      render: (_, record) => (
        <>
          <EditOutlined
            style={{ marginRight: 8, cursor: "pointer", fontSize: "20px" }}
            onClick={() => handleEdit(record)}
          />
          <DeleteOutlined
            style={{ cursor: "pointer", color: "red", fontSize: "20px" }}
            onClick={() => handleDeleteConfirmation(record)}
          />
        </>
      ),
    },
  ];

  const handleFileUpload = (event) => {
    const file = event.target.files[0];
    const acceptedFileTypes = [
      ".jpg", ".JPG", ".jpeg", ".JPEG", ".png", ".PNG", ".gif", ".GIF", ".gfif"
    ];
    const currentFileType = "." + file.type.split("/")[1];
    const maxSizeInBytes = 5 * 1024 * 1024; // 5MB

    if (file.size > maxSizeInBytes) {
      showInfo("File size exceeds the limit of 5MB.");
      event.target.value = null;
    } else if (!acceptedFileTypes.includes(currentFileType)) {
      showInfo("Accepted file types are .jpg, .JPG, .jpeg, .JPEG, .png, .PNG, .gif, .GIF, .gfif");
      event.target.value = null;
    } else {
      setLogo(file);
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    const selectedActivity = selectedActivities.map(act => act.id);
    const desc = editorState.getCurrentContent().getPlainText().trim();
    
    if (!name.trim()) {
      Swal.fire({ title: "Error", text: "Name is required", icon: "error" });
      return;
    }
    if (!logo && !editData) {
      Swal.fire({ title: "Error", text: "Logo is required", icon: "error" });
      return;
    }
    if (selectedActivity.length === 0) {
      Swal.fire({ title: "Error", text: "Activity is required", icon: "error" });
      return;
    }
    if (!desc) {
      Swal.fire({ title: "Error", text: "Description is required", icon: "error" });
      return;
    }

    const payload = {
      name,
      description: desc,
      location,
      activity: selectedActivity,
    };

    const formData = new FormData();
    formData.append("data", JSON.stringify(payload));

    if (logo) {
      formData.append("file", logo);
    } else if (editData) {
      formData.append("file", editData.communityLogo);
    }

    try {
      setIsLoading(true);
      const token = localStorage.getItem("token");
      let response;

      if (editData) {
        const updateResponse = await updateCommunityGroup(editData.id, formData);
        if (updateResponse.status === 200) {
          Swal.fire({ title: "Success", text: "Group updated successfully!", icon: "success" }).then(() => {
            setIsModalOpen(false);
            // Update groups without reloading the page
            fetchCommunityGroups().then(response => setCommunityGroups(response?.data?.data));
          });
        }
      } else {
        const createComGroupResponse = await createCommunityGroup(formData);
        if (createComGroupResponse.status === 201) {
          Swal.fire({ title: "Success", text: "Group created successfully!", icon: "success" }).then(() => {
            handleCancel();
            // Update groups without reloading the page
            fetchCommunityGroups().then(response => setCommunityGroups(response?.data.data));
          });
        }
      }
    } catch (error) {
      Swal.fire({ title: "Error", text: error.response.data.message, icon: "error" });
    } finally {
      setIsLoading(false);
    }
  };

  const handleSearchChange = (event) => {
    setSearchTerm(event.target.value);
  };

  const handleCancel = () => {
    setIsModalOpen(false);
    setEditData(null);
    setName("");
    setDescription("");
    setLocation("");
    setSelectedActivities([]);
    setLogo(null);
    setEditorState(EditorState.createEmpty());
    if (fileInputRef.current) {
    fileInputRef.current.value = null;
  }
  };

  useEffect(() => {
    listActivities().then((response) => { setActivity(response) });
  }, []);

  // const editorContent = document.querySelector(".public-DraftEditor-content");
  // if (editorContent) {
  //   editorContent.innerHTML = description;
  // }

  return (
    <>
      <Header />
      <div className="grid grid-cols-1 xl:grid-cols-5 items-start gap-x-4"></div>
      <div style={{ marginTop: "100px", padding: "20px" }}>
        <div className="add-challenges-modal">
          <Grid container spacing={2}>
            <Grid item xs={12} sm={10} sx={{ marginTop: "30px" }}>
              <Button type="primary" onClick={() => {
                setName("");
                setDescription("");
                setLocation("");
                setSelectedActivities([]);
                setLogo(null);
                setShowTrackChellenges(false);
                setShowAssignedChallengeModal(false);
                setEditData(null);
                setIsModalOpen(true);
              }}>
                Create Community Group
              </Button> &nbsp;
            </Grid>
            {!showTrackChellenges && !assignedChallengeModal && (
              <Grid item xs={12} sm={2} sx={{ textAlign: "start", marginTop: "30px" }}>
                <TextField type="text" size="small" value={searchTerm} onChange={handleSearchChange} placeholder="Search By Community Name.." />
              </Grid>
            )}
          </Grid>
          <CreateCommunityGroup
            isModalOpen={isModalOpen}
            handleCancel={handleCancel}
            handleFileUpload={handleFileUpload}
            activity={activity}
            isLoading={isLoading}
            editData={editData}
            setSelectedActivities={setSelectedActivities}
            selectedActivities={selectedActivities}
            setLocation={setLocation}
            location={location}
            setDescription={setDescription}
            description={description}
            setName={setName}
            name={name}
            handleSubmit={handleSubmit}
            editorState={editorState}
            setEditorState={setEditorState}
            fileInputRef={fileInputRef}
            logo={logo}
          />
          <Modal
            open={isImageModalVisible}
            footer={null}
            onCancel={() => setIsImageModalVisible(false)}
          >
            <img
              src={selectedImage}
              alt="Enlarged Community Logo"
              style={{ width: "100%", height: "100%" }}
            />
          </Modal>
        </div>
        &nbsp;
        {assignedChallengeModal && <AssignedChallenge />}
        {showTrackChellenges && <ChallengeTrack />}
        {!showTrackChellenges && !assignedChallengeModal && (
          <div>
            <Table
              scroll={{ y: 280 }}
              columns={columns}
              dataSource={filteredList}
              pagination={true}
              className="thin-scrollbar"
            />
          </div>
        )}
      </div>
    </>
  );
};

export default ManageGroup;
