// ** React Imports
import React, { useEffect, useState } from "react";

// ** MUI Components
import Box from "@mui/material/Box";
import Divider from "@mui/material/Divider";
import Checkbox from "@mui/material/Checkbox";
import TextField from "@mui/material/TextField";
import InputLabel from "@mui/material/InputLabel";
import Typography from "@mui/material/Typography";
import IconButton from "@mui/material/IconButton";
import CardContent from "@mui/material/CardContent";
import FormControl from "@mui/material/FormControl";
import OutlinedInput from "@mui/material/OutlinedInput";
import { styled, useTheme } from "@mui/material/styles";
import MuiCard from "@mui/material/Card";
import InputAdornment from "@mui/material/InputAdornment";
import MuiFormControlLabel from "@mui/material/FormControlLabel";
import LoadingButton from "@mui/lab/LoadingButton";

// ** Icons Imports
import VisibilityIcon from "@mui/icons-material/Visibility";
import VisibilityOffIcon from "@mui/icons-material/VisibilityOff";

import { useNavigate } from "react-router-dom";
import { SigninWithGoogle, SignupWithGoogle, WriteUserData } from "../../API/firebase.config";
import Swal from "sweetalert2";
import { Login, ResendOtp, VerifyOTP, fetchCurrentProfile, generateUidLogin, getAccesToken, getAlluserSubscription, getPrograms, getzonesupdation, updateProgram } from "../../API/api-endpoint";
import Background from "../../Images/Background.png";
import GoogleImage from "../../Images/GoogleImage.png";
import { useDispatch } from "react-redux";
import {MenuItem } from "@mui/material";
import { Modal } from "antd";
import { showError, showInfo } from "../../components/Messages";
import { useMessageContext } from "../../context/MessageContext"
import { getCurrentUserChatsAction } from "../Chat/redux/action/userChats";
import { getAllUsersAction } from "../Chat/redux/action/usersAction";
import { useSelector } from "react-redux";

// ** Styled Components
const Card = styled(MuiCard)(({ theme }) => ({
  [theme.breakpoints.up("sm")]: { width: "28rem" },
}));

const FormControlLabel = styled(MuiFormControlLabel)(({ theme }) => ({
  "& .MuiFormControlLabel-label": {
    fontSize: "0.875rem",
    color: theme.palette.text.secondary,
  },
}));



const LoginPage = () => {
  const { confirm } = Modal;
  let isVerifyOTP = localStorage.getItem("isOTP")
  const { currentUser } = useSelector((state) => state.auth);
  console.log("currentUser", currentUser);

  const [values, setValues] = useState({
    email: "",
    password: "",
    showPassword: false,
  });

  const [isLoading, setIsLoading] = useState(false);
  const [programId, setProgramId] = useState();
  const [isOTPScreen, setisOTPScreen] = useState(false);
  const [enteredOTP, setenteredOTP] = useState(null);
  const { updateTotalUnseenMessageCount } = useMessageContext();
  const [isGoogleSigninLoading, setIsGoogleSigninLoading] = useState(false);

  useEffect(() => {
    updateTotalUnseenMessageCount(0);
    localStorage.removeItem("userDetails")
    localStorage.removeItem("userSubsription")
  })
  const navigate = useNavigate();
  const dispatch = useDispatch();

  const handleChange = (prop) => (event) => {
    setValues({ ...values, [prop]: event.target.value });
  };

  const handleClickShowPassword = () => {
    setValues({ ...values, showPassword: !values.showPassword });
  };

  const handleGoogleSignin = async () => {
    setIsGoogleSigninLoading(true);
    setIsLoading(false)
    setIsGoogleSigninLoading(false);
    let result = await SigninWithGoogle(dispatch);
    console.log("result", result);
    if (result.success == true) {
      Swal.fire({
        title: "Success",
        text: result.message,
        icon: "success",
      });
      localStorage.setItem("userId", result?.user?.id);
      localStorage.setItem("roleID", result?.user?.role_id)
      const rersponse = await fetchCurrentProfile(result?.user?.id)
      if (result?.user?.strava_id) {
        const getZonesUpdate = await getzonesupdation(result?.user?.id, rersponse?.data?.program_id)
        console.log("ssafgndssbfhsdbnfb", getZonesUpdate);
        if (getZonesUpdate?.shouldshowzonesscreen) {
          showInfo("Time trial have been completed but zones have not been updated, please update zones", "/zone");
        } else {

          showSuccess("Login successful", "/workout");
        }
      } else {
        showSuccess("Login successful", "/strava");
      }
      console.log("result", result);

    } else {
      if (result?.message == "Payment is not done") {
        localStorage.setItem("roleID", result?.user?.role_id)
        Swal.fire({
          title: "Error!!",
          text: result.message,
          icon: "error",
        });
        localStorage.setItem("token", `Bearer ${result?.token}`);
        localStorage.setItem("userId", result.user.id);

        console.log("payment");
        localStorage.setItem("email", result?.user.email);
        localStorage.setItem("fullname", `${result?.user.firstname} ${result?.user.lastname}`);
        navigate("/onboarding-flow");
        setTimeout(() => {
          showError(`${result.message}`);
        }, 100);
      } else {
        if (result?.message == "Firebase: Error (auth/popup-closed-by-user).") {
          setIsLoading(false)
          setIsGoogleSigninLoading(false);
        } else {
          if (result?.message) {
            Swal.fire({
              title: "Error!!",
              text: result.message,
              icon: "error",
            });
          }

          setIsLoading(false)
        }

        setIsGoogleSigninLoading(false);

      }
    }
    setIsGoogleSigninLoading(false);
  };
  console.log("values", values);
  const onSubmit = async () => {
    if (values.email && values.password) {
      setIsLoading(true);
      const result = await Login({
        email: values.email,
        password: values.password,
      }, dispatch);
      const data = result.data;
      
      if (result?.message == "User Email/password is not valid") {
        Swal.fire({
          title: "Error",
          text: result?.message,
          icon: "error",
        });
        setIsLoading(false)

      } else
        if (result?.message == "User is not verified") {
          console.log("testing1");
          localStorage.setItem("email", values.email);
          localStorage.setItem("isOTP", true)
          ResendAPI()
          setIsLoading(false)
          showErrorAndRedirect(`${result.message} Please verify email first`);
          // setisOTPScreen(true)
        }
        else if (result?.message == "Payment is not done") {
          localStorage.setItem("token", `Bearer ${result?.token}`);
          localStorage.setItem("userId", result.user.id);
          if (result?.subs.length == 0) {
            console.log("payment");
            localStorage.setItem("email", values.email);
            localStorage.setItem("fullname", `${result?.user.firstname} ${result?.user.lastname}`);
            navigate("/onboarding-flow");
            setTimeout(() => {
              showError(`${result.message}`);
            }, 100);
          } else {
            console.log("payment2");
            localStorage.setItem("userDetails", JSON.stringify(result?.subs[0]));
            localStorage.setItem("userSubsription", JSON.stringify(result?.subs[0]));

            localStorage.setItem("email", values.email);
            localStorage.setItem("fullname", `${result?.user.firstname} ${result?.user.lastname}`);
            navigate("/onboarding-flow");
            setTimeout(() => {
              showError(`${result.message}`);
            }, 100);
          }

        }
        else
          if (result?.message == "User Email/password is not valid") {
            showErrorAndRedirect(result);
          } else if (result.message == "Invalid credentials") {
            showErrorAndRedirect(result.message);
          } else if (result.message == "Login successful") {
    localStorage.setItem("profileImage", result?.user?.profile);
            
            console.log("results", result);
            await dispatch(getCurrentUserChatsAction(currentUser?.uid));
            await dispatch(getAllUsersAction(result?.user?.role_id));
            updateTotalUnseenMessageCount(0);
            const rersponse = await fetchCurrentProfile(result?.user?.id)
            const getZonesUpdate = await getzonesupdation(result?.user?.id, rersponse?.data?.program_id)
            if (result?.user?.role_id != 5) {
              if (!result?.user?.google_id) {
                const genrate = await generateUidLogin(result?.user.firstname, result?.user.lastname, result.user.email, dispatch, values.password)
                if (genrate.status) {

                  if (getZonesUpdate?.shouldshowzonesscreen) {
                    showInfo("Time trial have been completed but zones have not been updated, please update zones", "/zone");
                  } else {

                    showSuccess("Login successful", "/coach-yoska");
                  }
                }
              } else {
                if (getZonesUpdate?.shouldshowzonesscreen) {
                  showInfo("Time trial have been completed but zones have not been updated, please update zones", "/zone");
                } else {
                  showSuccess("Login successful", "/coach-yoska");
                }

              }

            } else {
              if (!result?.user?.google_id) {
                const genrate = await generateUidLogin(result?.user.firstname, result?.user.lastname, result.user.email, dispatch, values.password)
                if (genrate.status) {
                  localStorage.setItem("user", JSON.stringify(result?.user));
                  
                  const rersponse = await fetchCurrentProfile(result?.user?.id)
                  console.log("rersponse", rersponse);
                  setIsLoading(false);
                  if (rersponse?.data == null) {
                    const response = await getPrograms()
                    console.log("response", response);
                    if (response?.length > 0) {
                      showConfirm(response)
                      Swal.fire({
                        title: "Success",
                        text: "Login successful please select program",
                        icon: "success",
                      });
                    } else {
                      showSuccess("Login successful please select plan first ", "/subscription-flow");
                    }
                  } else {
                    console.log("ssafgndssbfhsdbnfb");
                    if (result?.user?.strava_id) {
                      const getZonesUpdate = await getzonesupdation(result?.user?.id, rersponse?.data?.program_id)
                      console.log("ssafgndssbfhsdbnfb", getZonesUpdate);
                      if (getZonesUpdate?.shouldshowzonesscreen) {
                        showInfo("Time trial have been completed but zones have not been updated, please update zones", "/zone");
                      } else {

                        showSuccess("Login successful", "/workout");
                      }
                    } else {
                      showSuccess("Login successful", "/strava");
                    }
                  }
                }
              } else {
                localStorage.setItem("user", JSON.stringify(result?.user));

                const rersponse = await fetchCurrentProfile(result?.user?.id)
                console.log("rersponse", rersponse);
                setIsLoading(false);
                if (rersponse?.data == null) {
                  const response = await getPrograms()
                  console.log("response", response);
                  if (response?.length > 0) {
                    showConfirm(response)
                    Swal.fire({
                      title: "Success",
                      text: "Login successful please select program",
                      icon: "success",
                    });
                  } else {
                    showSuccess("Login successful please select plan first ", "/subscription-flow");
                  }
                } else {
                  if (result?.user?.strava_id) {
                    const getZonesUpdate = await getzonesupdation(result?.user?.id, rersponse?.data?.program_id)
                    console.log("ssafgndssbfhsdbnfb", getZonesUpdate);
                    if (getZonesUpdate?.shouldshowzonesscreen) {
                      showInfo("Time trial have been completed but zones have not been updated, please update zones", "/zone");
                    } else {

                      showSuccess("Login successful", "/workout");
                    }

                  } else {
                    showSuccess("Login successful", "/strava");
                  }
                }
              }

            }
            // showSuccess("Login successful", "/strava");
          } else {
            showErrorAndRedirect(result.message);
            setIsLoading(false)


          }

    } else {
      Swal.fire({
        title: "Error",
        text: "Please enter all details",
        icon: "error",
      });
    }

  };

  const showErrorAndRedirect = (message, path) => {
    Swal.fire({
      title: "Error",
      text: message,
      icon: "error",
    });
    setIsLoading(false);
    localStorage.setItem("email", values.email);
    navigate(path);
  };

  const showSuccess = (message, path) => {
    Swal.fire({
      title: "Success",
      text: message,
      icon: "success",
    });
    setIsLoading(false);
    navigate(path);
  };
  const updateProfile = async (id) => {
    console.log("programId", programId);
    // if (programId) {

    const response = await updateProgram(programId)
    if (response?.status) {
      showSuccess("Login successful", "/workout");


      Swal.fire({
        title: "Success",
        text: response.message,
        icon: "success",
      });
    }
    // } else{
    //   Swal.fire({
    //     title: "Error",
    //     text: "Please Select any program",
    //     icon: "error",
    //   });
    // }
  }
  const showConfirm = (programList) => {
    Modal.info({
      title:
        <h3 style={{ padding: "10px 0px 0px 10px" }}><b>Select Program:</b></h3>,
      content: <TextField
        fullWidth
        size="small"
        select
        name="challengeActivity"
        value={programId}

        onChange={
          (event) => {
            setProgramId(event.target.value)
          }}
        id="form-layouts-separator-select"
        labelId="form-layouts-separator-select-label"
        input={<OutlinedInput id="select-multiple-language" />}
      >
        <MenuItem value={""} disabled>
          Select Program
        </MenuItem>
        {programList?.map((value, index) => {
          return (
            <MenuItem value={value?.program_id}>
              {value?.prgram_name}
            </MenuItem>
          );
        })}
      </TextField>,
      onOk() {
        updateProfile()
      },

    });
  };
  const ResendAPI = async () => {
    console.log("dsnfbndsfnsdv");
    setenteredOTP("")
    let apicalled = false;
    setisOTPScreen(true);
    let email = localStorage.getItem("email");
    if (apicalled === false) {
      let response = await ResendOtp(email);
      if (response.status) {
        apicalled = true;
        setisOTPScreen(true);

        // Swal.fire({
        //   title: "Success",
        //   text: response?.message,
        //   icon: "success",
        // });
      } else {
        Swal.fire({
          title: "Error",
          text: response?.message,
          icon: "error",
        });
      }
    }
  };

  const verifyOtp = async () => {
    if (enteredOTP.length === 6) {
      setIsLoading(true);
      try {
        let FinalEmail =
          localStorage.getItem("email")
        let result = await VerifyOTP({
          email: FinalEmail,
          otp: enteredOTP,
        });
        if (result.message == "OTP verified successfully") {
          setIsLoading(false);
          Swal.fire({
            title: "Success!!",
            text: "Successfully verified Your Email",
            icon: "success",
          });
          localStorage.removeItem("isOTP")
          setisOTPScreen(false);
          setIsLoading(false);
        } else if (result.message === "Invalid OTP") {
          Swal.fire({
            title: "Error!!",
            text: result.message,
            icon: "error",
          });
          setIsLoading(false);
        }
      } catch (error) {

        Swal.fire({
          title: "Error!!",
          text: "Entered OTP is incorrect",
          icon: "error",
        });
        setIsLoading(false);
      }
    } else {
      Swal.fire({
        title: "Error!!",
        text: "Please enter the 6 digit only",
        icon: "error",
      });
    }
  };
  if (!isOTPScreen) {
    return (
      <Box
        className="content-center"
        style={{
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
          height: "100vh", // 100% of the viewport height
          background: `url(${Background}) center no-repeat`, // Replace 'your-background-image.jpg' with the path to your background image
          backgroundSize: "100% 100%",
        }}
      >
        <Card sx={{ zIndex: 1 ,width:"31rem !important",boxShadow: "-7px 1px 45px 8px rgba(0, 0, 0, 0.07) !important",borderRadius:"25px"
      }}>
          <CardContent>
            <Box sx={{ textAlign: "center", marginBottom: 4, marginTop: 2 }}>
              <Typography variant="h4" sx={{fontWeight:"700"}}>Login<span style={{color:"#FFA654"}}>To Your Account</span></Typography>
            </Box>
            &nbsp;&nbsp;
            <form autoComplete="off" onSubmit={onSubmit}>
              <TextField
                autoSave="false"
                autoFocus={false}
                fullWidth
                id="email"
                label="Email"
                sx={{ marginBottom: 4 }}
                onChange={handleChange("email")}
                type="email"
                required
                InputProps={{ autoComplete: 'off' }}
              />
              <FormControl fullWidth>
                <InputLabel htmlFor="auth-login-password">Password*</InputLabel>
                <OutlinedInput
                  label="Password"
                  value={values.password}
                  id="auth-login-password"
                  onChange={handleChange("password")}
                  required
                  type={values.showPassword ? "text" : "password"}
                  endAdornment={
                    <InputAdornment style={{ marginRight: "15px" }} position="end">
                      <IconButton
                        edge="end"
                        onClick={handleClickShowPassword}
                        aria-label="toggle password visibility"
                      >
                        {values.showPassword ? (
                          <VisibilityIcon />
                        ) : (
                          <VisibilityOffIcon />
                        )}
                      </IconButton>
                    </InputAdornment>
                  }
                />
              </FormControl>
              <Box
                sx={{
                  mb: 4,
                  mt: 2,
                  display: "flex",
                  alignItems: "center",
                  flexWrap: "wrap",
                  justifyContent: "space-between",
                  fontWeight:'600',
                  fontSize:'18px',
                }}
              >
                <a onClick={() => navigate("/forgot-password")} className="cursor-pointer text-[black]">
                  Forgot Password?
                </a>
              </Box>
              <LoadingButton
                fullWidth
                size="large"
                variant="contained"
                sx={{
                  marginBottom: 2,
                  // color: "white",
                  // backgroundColor: "rgb(145, 85, 253)",
                  color: "white",
                  backgroundColor: "#FFA654",
                  '&:hover': {
                    backgroundColor: '#FFA654', // Change to your desired hover color
                  },
                }}
                onClick={() => onSubmit()}
                loading={isLoading}
                loadingPosition="start"
              >
                Continue
              </LoadingButton>
              <Divider sx={{ marginBottom: 2,fontSize:"16px",fontWeight:"500" }}>OR</Divider>

              <LoadingButton
                fullWidth
                size="large"
                variant="contained"
                sx={{
                  marginBottom: 2,
                  // color: "white",
                  // backgroundColor: "rgb(145, 85, 253)",
                  color: "white",
                  backgroundColor: "#FFA654",
                  '&:hover': {
                    backgroundColor: '#FFA654', // Change to your desired hover color
                  },
                }}
                onClick={() => handleGoogleSignin()}
                loading={isGoogleSigninLoading}
                loadingPosition="start"
                startIcon={
                  <span style={{ display: 'flex', alignItems: 'center' ,fontSize:"18px", justifyContent:"center",textAlign:"center"}}>
                    <img src={GoogleImage} alt="Image" style={{ marginRight: '8px' }} />
                    LOG in with Google
                  </span>
                }
              >
              </LoadingButton>
              <Typography variant="h6" component="div" sx={{fontWeight:"600"}}>Don't have an account yet?<span>
              <a onClick={() => navigate("/sign-up")} className="cursor-pointer text-[#E67E22]">Sign up</a>
              </span></Typography>
              <br />
              <a onClick={() => navigate("/admin-login")} className="cursor-pointer text-[#E67E22] text-base ">Login as an admin</a>
            </form>
          </CardContent>
        </Card>

      </Box>
    );
  } else {
    return (
      <div className="app__container">
        <Box
          className="content-center"
          style={{
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            height: "100vh", // 100% of the viewport height
            background: `url(${Background}) no-repeat`, // Replace 'your-background-image.jpg' with the path to your background image
            backgroundSize: "100% 100vh",
          }}
        >
          <Card sx={{ width: "700px" }}>
            <CardContent
              sx={{
                display: "flex",
                alignItems: "center",
                flexDirection: "column",
              }}
            >
              <Typography sx={{ padding: "20px" }} variant="h6" component="div">
                An OTP has been sent to your email, please enter it below to verify your email
              </Typography>
              <TextField
                sx={{ width: "240px" }}
                variant="outlined"
                label="OTP "
                value={enteredOTP}
                onChange={(event) => setenteredOTP(event.target.value)}
              />
              <div>

                {isVerifyOTP &&
                  <LoadingButton
                    variant="contained"
                    sx={{
                      width: "140px",
                      marginTop: "20px",
                      // color: "white",
                      // backgroundColor: "rgb(145, 85, 253)",
                      color: "white",
                      backgroundColor: "#FFA654",
                  '&:hover': {
                    backgroundColor: '#FFA654', // Change to your desired hover color
                  },
                    }}
                    onClick={ResendAPI}
                    loading={isLoading}
                    loadingPosition="start"
                  >
                    Resend OTP
                  </LoadingButton>
                }
                &nbsp;
                <LoadingButton
                  variant="contained"
                  sx={{
                    width: "140px",
                    marginTop: "20px",
                    // color: "white",
                    // backgroundColor: "rgb(145, 85, 253)",
                    color: "white",
                    backgroundColor: "#FFA654",
                  '&:hover': {
                    backgroundColor: '#FFA654', // Change to your desired hover color
                  },
                  }}
                  onClick={verifyOtp}
                  loading={isLoading}
                  loadingPosition="start"
                >
                  Verify Otp
                </LoadingButton>
              </div>
            </CardContent>

          </Card>
        </Box>
      </div>
    );
  }
};

export default LoginPage;
