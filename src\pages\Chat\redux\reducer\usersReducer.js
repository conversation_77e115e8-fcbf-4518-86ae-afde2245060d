import { CLEAR_USERS_TO_CREATE_GROUP_SUCCESS, GET_ALL_USERS_FAIL, GET_ALL_USERS_INIT, GET_ALL_USERS_SUCCESS, SET_SEARCH_USER_DATA, STORE_OPENED_USER_INFO_FAIL, STORE_OPENED_USER_INFO_SUCCESS, STORE_USERS_TO_CREATE_GROUP_SUCCESS } from "../constant/usersConstants"


export const usersReducer = (state = { allUsers: [], openedUser: {}, storedUserToCreateGroup: [], serchedUserData: [] }, { type, payload }) => {
    switch (type) {

        case GET_ALL_USERS_INIT: return { ...state, loading: true }

        case GET_ALL_USERS_SUCCESS: return { ...state, loading: false, allUsers: payload }

        case STORE_OPENED_USER_INFO_SUCCESS: return { ...state, loading: false, openedUser: payload }

        case GET_ALL_USERS_FAIL: return { ...state, loading: false, error: payload }

        case STORE_OPENED_USER_INFO_FAIL: return { ...state, loading: false, error: payload }

        case STORE_USERS_TO_CREATE_GROUP_SUCCESS: return { ...state, loading: false, storedUserToCreateGroup: payload }

        case CLEAR_USERS_TO_CREATE_GROUP_SUCCESS: return { ...state, loading: false, storedUserToCreateGroup: payload }
        
        case SET_SEARCH_USER_DATA: return { ...state, loading: false, serchedUserData: payload }

        default: return state;
    }
}

