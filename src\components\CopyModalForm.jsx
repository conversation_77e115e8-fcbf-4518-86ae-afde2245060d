import React, { useState } from 'react';
import { Modal, Form, Input } from 'antd';
import { FormLabel, TextField } from '@mui/material';

const CustomModal = ({
  title,
  visible,
  onOk,
  onCancel,
  initialTitle,
  onChangeTitle,
}) => {
  const [form] = Form.useForm();

  const handleOk = () => {
    form.validateFields().then((values) => {
      onOk(values.title);
    });
  };

  return (
    <Modal
      title={title}
      centered
      open={visible}
      onOk={handleOk}
      onCancel={onCancel}
    >
    <div style={{borderBottom:"1px solid gray",paddingBottom:"24px"}}>
     <FormLabel style={{textAlign:"center",fontWeight:600}} >Add Title</FormLabel>
     <br/>
       <TextField
       placeholder="Title"
       size="small"
       type="text"
       value={initialTitle?initialTitle:""}
       onChange={(e) => onChangeTitle(e.target.value)}
      
       />
     </div>
     
    </Modal>
  );
};

export default CustomModal;
