import { Grid, TextField } from '@mui/material'
import React, { useMemo } from 'react'
import { fetchSavedAssesmnetGetData, getAllRacesData, getAllSwimingData, URL } from '../../API/api-endpoint'
import { useEffect } from 'react'
import { Button } from 'antd'
import { useState } from 'react'
import Header from '../../components/Header'
import Background from "../../Images/ChallengeTracker.png";
import moment from 'moment';
import { capitalizeFirstLetter } from '../../utils/Resubale';
import StarRating from '../../utils/StarRating'


const AthleteformReview = ({ isEnrolledChallengesOpen }) => {
    const [reviewData, setFormReviewData] = useState()
    const [isLoading, setIsLoading] = useState(false)

    const [searchTerm, setSearchTerm] = useState("");

    useEffect(() => {
        fetchAssesmentData()

    }, [])

   
    const fetchAssesmentData = async () => {
        setIsLoading(true)
        const response = await fetchSavedAssesmnetGetData()
        setIsLoading(false)

        console.log("res", response);
        setFormReviewData(response?.newModified)
    }

    const filteredList = useMemo(() => {
        return reviewData?.filter((row) => {
            // Implement your search logic here
            // For example, if you want to search by a specific property like 'name':
            const activityname = row?.activity?.activity_name?.toLowerCase().includes(searchTerm?.toLowerCase());
            const firstName = row?.user?.firstname?.toLowerCase().includes(searchTerm?.toLowerCase());

            return activityname || firstName ;
        });
    }, [reviewData, searchTerm]);
    return (
        <div>
            <Header />
            <div className="grid grid-cols-1 xl:grid-cols-5 items-start gap-x-4 "></div>
            <div style={isEnrolledChallengesOpen?.assignCoachId ? { padding: "20px", display: "flex", background: "#FFEADC", justifyContent: "center", alignItems: "center" } : { marginTop: "80px", padding: "20px", display: "flex", background: "#FFEADC", justifyContent: "center", alignItems: "center" }}>
                <div style={{ width: "95%", minHeight: "90vh", padding: "20px" }}>
                    <div className='flex justify-between'>
                        <div>
                            <h1 style={{ fontSize: "24px" }}><strong>Form review:</strong></h1>
                        </div>
                        {filteredList?.length>0 &&
                        <div>

                            <TextField
                                type="text"
                                size="small"
                                value={searchTerm}
                                onChange={(e) => setSearchTerm(e.target.value)}
                                placeholder="Search By Activity.."
                            />
                        </div>
                        }

                    </div>
                    {filteredList?.length>0 ?(<>{filteredList?.map((poolData) => {

return (
    <div className='w-[100%] bg-[white] mt-6 p-2'>
        <Grid container>
           
            <Grid container spacing={2} >
                <Grid item xs={12} sm={8}>
                    {Background &&
                        <div style={{
                            // background: `url(${Background}) center no-repeat`,
                            backgroundSize: "100% 100%",
                            "height": "100%",
                            marginLeft: "14px",
                            padding: "12px",
                            minHeight: "70vh",
                            backgroundColor: "white"
                        }}>
                            <Grid sx={{ position: "relative" }} container spacing={2}>
                                <Grid item xs={12} sm={3} >
                                    <Grid item xs={12} sm={12} style={{ fontSize: "25px", margin: "0% 0% 0% 0%", lineHeight: "20px", position: "absolute", zIndex: "5" }}>
                                       Review: {capitalizeFirstLetter(poolData?.review)}
                                    </Grid>
                                    <Grid item xs={12} sm={12} style={{ fontSize: "15px", margin: "15% 0% 0% 2%", lineHeight: "20px" }}>
                                        Rating: <StarRating numStars={parseInt(poolData?.review_rating)} />
                                    </Grid>


                                </Grid>
                                <Grid item xs={12} sm={7} style={{ fontSize: "15px" }}>
                                    {poolData?.image ?
                                        <div style={{ outline: 'none' }}>
                                            <img style={{ outline: 'none', height: "400px", width: "400px", marginTop: "40px" }}
                                                src={`${URL}/static/public/userimages/${poolData?.image}`}
                                                className="p-3 d-block img" alt="Slide 1" />
                                        </div>
                                        : null}


                                </Grid>

                            </Grid>
                        </div>
                    }

                </Grid>
                <Grid item xs={12} sm={3} style={{ "borderLeft": "1px solid #E67E22", "margin": "auto", fontSize: "13px" }}
                >
                    <Grid container spacing={2} >
                        <Grid item xs={12} sm={6}>
                            Activity
                        </Grid>
                        <Grid item xs={12} sm={6} sx={{ textAlign: "end" }}>
                            {poolData?.activity ?.activity_name}
                        </Grid>
                        <Grid item xs={12} sm={6}>
                            Review Comment
                        </Grid>
                        <Grid item xs={12} sm={6} sx={{ textAlign: "end" }}>
                            {poolData?.reviewrating_comments}
                        </Grid>

                       
                      
                        {poolData?.State?.name &&
                            <Grid item xs={12} sm={7}>
                                State
                            </Grid>
                        }
                        {poolData?.State?.name &&

                            <Grid item xs={12} sm={5} sx={{ textAlign: "end" }}>
                                {poolData?.State?.name}
                            </Grid>
                        }
                        <Grid item xs={12} sm={7}>
                            Total Score
                        </Grid>
                        <Grid item xs={12} sm={5} sx={{ textAlign: "end" }}>
                            {poolData?.totalscore}

                        </Grid>
                        <Grid item xs={12} sm={7}>
                            Rate
                        </Grid>
                        <Grid item xs={12} sm={5} sx={{ textAlign: "end" }}>
                            {moment(poolData?.date).format("DD-MM-YYYY")}

                        </Grid>
                        {poolData?.race_website &&
                            <Grid item xs={12} sm={7}>
                                Race Website
                            </Grid>
                        }

                        <Grid item xs={12} sm={5} sx={{ textAlign: "end" }}>
                            <a onClick={() => window.open(poolData?.race_website, '_blank')} style={{ cursor: "pointer", color: "blue", textDecoration: "underline", wordWrap: "break-word" }}>{poolData?.race_website}</a>
                        </Grid>
                        



                    </Grid>
                </Grid>
            </Grid>

        </Grid>
    </div>
)
})}</>):(<div style={{padding: "11px 0px 10px 8px",
    fontSize: "32px",
    fontWeight: 600,
    boxShadow: "rgba(149, 157, 165, 0.2) 0px 8px 24px",
    margin: "10px 0px 0px 0px",
    height: "60vh",
    display: "flex",
    justifyContent: "center",
    alignItems: "center",
    borderRadius: "18px",}}>
    No Data Found
</div>)}
                    


                </div>

            </div>
        </div>
    )
}
export default AthleteformReview
