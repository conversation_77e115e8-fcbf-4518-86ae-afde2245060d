import React from 'react'
import Header from '../components/Header'

const ResetPage = () => {
  return (
    <>
      <Header />
      <div className='flex items-center justify-center p-4 w-full'>
        {/* Create modal */}
        <form className='md:absolute md:top-32 md:right-2/2 px-4 py-6 bg-slate-50 drop-shadow-2xl rounded-md border-2 border-slate-200/75 w-full md:w-8/12 lg:w-6/12 xl:w-4/12'>
          <div className='flex flex-col items-start justify-between mb-3'>
            <span className='text-xl font-medium m-0 pb-2'>
              Change Password
            </span>
            <span className='text-sm'>
              Please Provide the following details before we proceed
            </span>
          </div>
          <hr />
          <br />
          <div className='mb-5'>
            <div>
              <p className='mb-1 text-slate-600 px-1 font-medium'>
                Current password <span className='text-red-500'>*</span>{' '}
              </p>
              <input
                type='password'
                placeholder='Enter your current password'
                className='w-full border-b-2 py-2 rounded-md focus:outline-none text-slate-600 px-1 text-sm'
                required
              />
            </div>
          </div>
          <div className='mb-5'>
            <div>
              <p className='mb-1 text-slate-600 px-1 font-medium'>
                New password <span className='text-red-500'>*</span>{' '}
              </p>
              <input
                type='password'
                placeholder='Enter new pssword'
                className='w-full border-b-2 py-2 rounded-md focus:outline-none text-slate-600 px-1 text-sm'
                required
              />
            </div>
          </div>
          <div className='mb-5'>
            <div>
              <p className='mb-1 text-slate-600 px-1 font-medium'>
                Confirm password <span className='text-red-500'>*</span>{' '}
              </p>
              <input
                type='password'
                placeholder='New password, one more time!'
                className='w-full border-b-2 py-2 rounded-md focus:outline-none text-slate-600 px-1 text-sm'
                required
              />
            </div>
          </div>
          <div className='mb-2'>
            <button
              className='p-2.5 bg-orange-500 text-slate-50 w-full rounded-sm'
              type='submit'
            >
              Change password
            </button>
          </div>
        </form>
      </div>
    </>
  )
}

export default ResetPage
