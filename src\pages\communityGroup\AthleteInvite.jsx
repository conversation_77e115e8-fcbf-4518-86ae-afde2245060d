import React, { useEffect, useState } from "react";
import Header from "../../components/Header";

import {
    Button,
    Grid,
    TextField,
} from "@mui/material";
import { Table } from "antd";
import { deleteInviteForEmailForGroup, getInviteListForGroup } from "../../API/api-endpoint";
import Swal from "sweetalert2";
import SendInvite from "./components/SendInvite";


const AthleteInvite = () => {

    const [userGroup, setUserGroup] = useState(null);
    const [inviteSent, setInviteSent] = useState([]);
    const [isModelAction, setIsModelAction] = useState(false);
    const [searchTerm, setSearchTerm] = useState("");

    useEffect(() => {
        const group = JSON.parse(localStorage.getItem("groupDetail"));
        setUserGroup(group);
        const { id } = group;
        fetchInvite(id);
    }, []);

    const fetchInvite = async (groupId) => {
        try {
            const result = await getInviteListForGroup(groupId);
            setInviteSent(result.data);
        } catch (error) {
            throw error;
        }
    }

    const handleReloadData = async (reload) => {
        if (reload) {
            const { id } = userGroup;
            await fetchInvite(id);
        }
    }

    const handleClose = async () => {
        setIsModelAction(false);
    }

    const handleSearchChange = (event) => {
        setSearchTerm(event.target.value);
    };

    const handleDeleteInvite = async (id) => {
        try {
            const result = await Swal.fire({
                title: "Are you sure?",
                text: "You won't be able to revert this action!",
                icon: "warning",
                showCancelButton: true,
                confirmButtonText: "Yes, delete invite!",
                cancelButtonText: "No, cancel!",
                confirmButtonColor: "#3085d6",
                cancelButtonColor: "#d33",
            });

            if (result.isConfirmed) {
                const res = await deleteInviteForEmailForGroup(id);
                if (res.status) {
                    await fetchInvite(userGroup?.id)
                }
            }
        } catch (error) {
            throw error;
        }
    };


    const columns = [
        {
            title: "User Email",
            dataIndex: "email",
            key: "email",
        },
        {
            title: "Invite Status",
            dataIndex: "status",
            key: "status",
            render: (status) => (

                <span className={`text-sm font-medium ${status.trim().toLowerCase() === "pending" ? "text-orange-500" : "text-green-600"}`}>
                    {status}
                </span>
            ),
        },
        {
            title: "Actions",
            dataIndex: "actions",
            key: "actions",
            render: (_, record) => {
                const disableDelete = record.status.trim().toLowerCase() === "pending" ? false : true;
                return (
                    <span>
                        <Button
                            disabled={disableDelete}
                            variant="contained"
                            className="normal-case text-white cursor-pointer font-semibold"
                            onClick={() => handleDeleteInvite(record.id)}
                        >
                            Delete
                        </Button>
                    </span>
                )
            },
        },
    ];

    return (
        <div>
            <Header />
            <div className="p-5 mt-16">
                <div className="add-challenges-modal">
                    <Grid container spacing={2}>
                        <Grid item xs={12} sm={10}>
                            <Button
                                type="primary"
                                onClick={() => setIsModelAction(true)}
                            >
                                Invite User To Group
                            </Button>
                        </Grid>
                        {(
                            <Grid item xs={12} sm={2}>
                                <TextField
                                    type="text"
                                    size="small"
                                    value={searchTerm}
                                    onChange={handleSearchChange}
                                    placeholder="Search Invite via Email..."
                                />
                            </Grid>
                        )}
                    </Grid>
                    <SendInvite
                        open={isModelAction}
                        handleClose={handleClose}
                        userGroup={userGroup}
                        handleReload={handleReloadData} />
                    {inviteSent.length > 0 ? (<div className="flex justify-center m-5">
                        <Table
                            scroll={{ y: 280 }}
                            columns={columns}
                            dataSource={inviteSent?.filter((row) =>
                                row?.email?.toLowerCase().includes(searchTerm.toLowerCase())
                            )}
                            pagination={true}
                            className="thin-scrollbar"
                        />
                    </div>) : (
                        <div className="flex justify-center">
                            <h2 className="text-2xl font-semibold text-slate-900">Invites is not sent yet.</h2>
                        </div>
                    )}

                </div>
            </div>
        </div >
    )
}

export default AthleteInvite;