Stack trace:
Frame         Function      Args
0007FFFF8B70  00021006118E (00021028DEE8, 000210272B3E, 000000000000, 0007FFFF7A70) msys-2.0.dll+0x2118E
0007FFFF8B70  0002100469BA (000000000000, 000000000000, 000000000000, 000000000004) msys-2.0.dll+0x69BA
0007FFFF8B70  0002100469F2 (00021028DF99, 0007FFFF8A28, 000000000000, 000000000000) msys-2.0.dll+0x69F2
0007FFFF8B70  00021006A41E (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A41E
0007FFFF8B70  00021006A545 (0007FFFF8B80, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A545
0001004F94B7  00021006B9A5 (0007FFFF8B80, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2B9A5
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFC3B1A0000 ntdll.dll
7FFC3A3E0000 KERNEL32.DLL
7FFC384D0000 KERNELBASE.dll
7FFC3AF90000 USER32.dll
7FFC38AD0000 win32u.dll
7FFC3A5B0000 GDI32.dll
7FFC38E00000 gdi32full.dll
7FFC38420000 msvcp_win.dll
7FFC38980000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFC390C0000 advapi32.dll
7FFC399F0000 msvcrt.dll
7FFC3A6D0000 sechost.dll
7FFC39180000 RPCRT4.dll
7FFC378F0000 CRYPTBASE.DLL
7FFC382F0000 bcryptPrimitives.dll
7FFC3A940000 IMM32.DLL
