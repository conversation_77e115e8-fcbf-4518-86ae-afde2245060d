import React, { useEffect, useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON> } from "antd";
import { FormLabel, Grid } from "@mui/material";
import Swal from "sweetalert2";
import { useFormik } from "formik";
import SlickCarousel from "../SlickCarousel";
import Select from "react-select";
import { subsribeAthleteToChallenge, getAllChallenges, getCoachProfile, getPrograms, fetchCommunityGroupEnrolment, fetchCommunityGroupChallenges, creategroupChallengeEnrollment, getAssignedCoach } from "../../API/api-endpoint";


const AssignChallenge = ({  
  isAssignUserModalOpen, 
  isLoading, 
  modalName, 
  setIsAssignUserModalOpen 
}) => {
  const [getList, setGetList] = useState();
  const [challenges, setChallege] = useState([]);
  const [fetchActivePrograms, setFetchActivePrograms] = useState();
  const [programList, setProgramList] = useState();
  const [yraSubscribed, setYraSubscribed] = useState(false);
  const [coachActiveProgram, setActiveCoachProgram] = useState();
  const onboardingState = JSON.parse(localStorage.getItem("user"))?.onboardingState;
  const userId = localStorage.getItem("userId");
  const roleID = localStorage.getItem("roleID");
  const programID = localStorage.getItem("programID");

  const allAtheletes = async () => {
    try {
      const response = await fetchCommunityGroupEnrolment();
      if (response.status === 200) {
        // Process each record from the response.data.data array.
        const uniqueUsers = response.data.data.map((item) => {
          
          return {
            item,
            label: `${item.firstname} ${item.lastname}`, // Display first name + last namme in the dropdown
            id: item.id  // Unique identifier for the dropdown
          };
        });
        setGetList(uniqueUsers);
      } else {
        console.error("API returned unsuccessful status");
      }
    } catch (error) {
      console.error("Error fetching athletes:", error);
    }
  };

  const fetchChallenges = async () => {
    try {
      let groupID = 0;
      const groupDetailString = localStorage.getItem("groupDetail");

      if (groupDetailString) {
        const groupDetail = JSON.parse(groupDetailString);
        groupID = parseInt(groupDetail.id);
      }
      const challenges = await fetchCommunityGroupChallenges(groupID);
      
      setChallege(challenges?.data?.data);
    } catch (error) {
      console.error("Error fetching challenges for community groups:", error);
    }
  };


  // const fetchProgram = async () => {
  //   const response = await getPrograms();
  //   setProgramList(response);
  //   let data = response?.filter((ele) => ele.active == 1)
  //   console.log("yra-subscribed")
  //   console.log(data[0])
  //   const pumaLogo = data.find((program) => program.prgram_name.toLowerCase() === 'yra')
  //   if (pumaLogo) {
  //     setYraSubscribed(true);
  //   }
  //   localStorage.setItem("activeProgram", JSON.stringify(data[0]))

  //   if (roleID == 5 && data?.length > 0) {
  //     localStorage.setItem("programID", data[0]?.program_id)

  //   } else {
  //     const response = await getCoachProfile();
  //     setActiveCoachProgram(response.data)
  //     localStorage.setItem("programID", response?.data?.program_id)
  //     if (setFetchActivePrograms) {
  //       setFetchActivePrograms(response.data)
  //     }

  //   }
  //   console.log("response", response);
  // };

  const fetchYoskaAthletes = async (id) => {
  const response =  await getAssignedCoach(id);
  
  if (response?.allassinged?.length > 0) {
    const uniqueUsers = response.allassinged.map((item) => {
      return {
        item,
        label: `${item?.athlete?.firstname} ${item?.athlete?.lastname}`, // Display first name + last namme in the dropdown
        id: item?.athlete_id // Unique identifier for the dropdown
      };
    });
    setGetList(uniqueUsers);
  } else {
    console.log("No Athletes found");
  }
  }

    const fetchYoskaChallenges = async () => {
      const response = await getAllChallenges()
      if (response?.length > 0) {
        setChallege(response)
      }
    }
      
  useEffect(() => {
    if (onboardingState == "community") {
      allAtheletes();
      fetchChallenges();
    } else if (onboardingState == "yoska_academy") {
      // fetchProgram();
      fetchYoskaAthletes(programID);
      fetchYoskaChallenges();
    }
  }, [programID]);

  const formik = useFormik({
    initialValues: { users: [] },
    onSubmit: (values, { resetForm }) => {
      handleOnSubmitForm(values, resetForm);
    },
  });

  // Updated form submission using axios
  const handleOnSubmitForm = async () => {
    if (formik.values?.users.length > 0 && formik?.values?.challenges) {
      // Retrieve the token from local storage (adjust key as needed)
      try {
        if (onboardingState == "community") {
          // Loop through each selected user to enroll them in the challenge
          await Promise.all(
          formik.values.users.map((userId) =>
            creategroupChallengeEnrollment(formik, userId)
          )
        );
        } else if (onboardingState == "yoska_academy") {
          await Promise.all(
          formik.values.users.map((userId) =>
            subsribeAthleteToChallenge(formik.values.challenges, userId)
          )
        );
        }
        
        setIsAssignUserModalOpen(false);
        Swal.fire({
          title: "Success",
          text: "Challenge enrollment successful",
          icon: "success",
        });
      } catch (error) {
        Swal.fire({
          title: "Error",
          text: error.message,
          icon: "error",
        });
      }
    } else {
      Swal.fire({
        title: "Error",
        text: "Please fill all details",
        icon: "error",
      });
    }
  };

  const handleUserChange = async (event, newValue, name) => {
    formik.setFieldValue(name, newValue);
  };

  return (
    <Modal
      width={1200}
      open={isAssignUserModalOpen}
      onCancel={() => setIsAssignUserModalOpen(false)}
      footer={<div></div>}
    >
      <div className="headingCont">
        <span className="heading">
          {modalName === "simulation" ? "User " : "Assign "}
        </span>{" "}
        <span className="orange heading">
          {modalName === "simulation" ? "Simulation" : "Challenges to athletes"}
        </span>
      </div>
      <div className="parentCont">
        <form className="form1">
          <Grid container spacing={2}>
            <Grid item xs={12} sm={11}>
              <FormLabel>
                Athletes<span className="text-[red]">*</span>
              </FormLabel>
                <Select
                  styles={{
                    menu: (provided) => ({
                      ...provided,
                      scrollbarColor: "#E67E22 transparent",
                      scrollbarWidth: "thin",
                      "&::-webkit-scrollbar": {
                        width: "12px",
                      },
                      "&::-webkit-scrollbar-track": {
                        background: "transparent",
                      },
                      "&::-webkit-scrollbar-thumb": {
                        backgroundColor: "#E67E22",
                        borderRadius: "20px",
                        border: "3px solid transparent",
                        backgroundClip: "content-box",
                      },
                    }),
                  }}
                  isMulti
                  options={getList}
                  getOptionLabel={(option) => option.label}
                  getOptionValue={(option) => option.id}
                  onChange={(selectedOptions) =>
                    handleUserChange(
                      null,
                      selectedOptions.map((option) => option.id),
                      "users"
                    )
                  }
                />
              {/* )} */}
            </Grid>

            <Grid item xs={12} sm={11}>
              <FormLabel>
                Challenges<span className="text-[red]">*</span>
              </FormLabel>
              {onboardingState === "community" && (
                <Select
                  styles={{
                    menu: (provided) => ({
                      ...provided,
                      scrollbarColor: "#E67E22 transparent",
                      scrollbarWidth: "thin",
                      "&::-webkit-scrollbar": {
                        width: "12px",
                      },
                      "&::-webkit-scrollbar-track": {
                        background: "transparent",
                      },
                      "&::-webkit-scrollbar-thumb": {
                        backgroundColor: "#E67E22",
                        borderRadius: "20px",
                        border: "3px solid transparent",
                        backgroundClip: "content-box",
                      },
                    }),
                  }}
                  options={challenges.map((challenge) => ({
                    label: challenge.athletecommunitygroupchallenge.challengeName,
                    value: challenge.athletecommunitygroupchallenge.id,
                  }))}
                  onChange={(selectedOption) =>
                    handleUserChange(null, selectedOption?.value, "challenges")
                  }
                />
              )}
              {onboardingState === "yoska_academy" && (
                <Select
                  styles={{
                    menu: (provided) => ({
                      ...provided,
                      scrollbarColor: "#E67E22 transparent",
                      scrollbarWidth: "thin",
                      "&::-webkit-scrollbar": {
                        width: "12px",
                      },
                      "&::-webkit-scrollbar-track": {
                        background: "transparent",
                      },
                      "&::-webkit-scrollbar-thumb": {
                        backgroundColor: "#E67E22",
                        borderRadius: "20px",
                        border: "3px solid transparent",
                        backgroundClip: "content-box",
                      },
                    }),
                  }}
                  options={challenges.map((challenge) => ({
                    label: challenge.challengeName,
                    value: challenge.id,
                  }))}
                  onChange={(selectedOption) =>
                    handleUserChange(null, selectedOption?.value, "challenges")
                  }
                />
              )}
                
            </Grid>
            <Grid item xs={12} sm={5.5} className="spcl"></Grid>
            <Grid item xs={12} sm={5.5} className="spcl">
              <Button
                className="btn"
                key="submit"
                type="primary"
                loading={isLoading}
                onClick={() => formik.handleSubmit()}
              >
                Submit
              </Button>
            </Grid>
          </Grid>
        </form>
        <div className="slick-container">
          <SlickCarousel />
        </div>
      </div>
    </Modal>
  );
};

export default AssignChallenge;
