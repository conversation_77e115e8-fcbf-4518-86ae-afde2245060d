import { Modal } from "@mantine/core";
import { IconTarget } from "@tabler/icons";
import React, { useState } from "react";
import Header from "../components/Header";

const ZonePage = () => {
	const [openZone, setOpenZone] = useState(false);
	const [openAudit, setOpenAudit] = useState(false);

	return (
		<>
			<Header />
			<div className='p-4'>
				{/* Top header */}
				<div className='w-full mb-6'>
					<h1 className='font-medium text-xl'>Zone Master</h1>
					<p className='text-sm py-1 text-slate-600'>
						Zone Templates help coach in creating zones that can be
						automatically applied to an athlete. These templates are
						automatically assigned to any new athletes that are
						created by the coach.
					</p>
				</div>
				{/* / Top header */}
				{/* Actions */}
				<div className='flex flex-wrap gap-4 items-center'>
					{/* add zone */}
					<Modal
						opened={openZone}
						onClose={() => setOpenZone(false)}
						title='Zones'
					>
						<hr />
						<br />
						<div className='mb-5'>
							<p className='mb-1 text-slate-600 px-1 font-medium'>
								Activity
							</p>
							<select
								name='status'
								id='status'
								placeholder='Group'
								className='w-full border-b-2 py-2 rounded-md focus:outline-none text-slate-600 text-sm'
							>
								<option value='#'>--Select--</option>
								<option value='Subscribed'>Subscribed</option>
							</select>
						</div>
						<div className='mb-5'>
							<p className='mb-1 text-slate-600 px-1 font-medium'>
								Matric
							</p>
							<select
								name='status'
								id='status'
								placeholder='Group'
								className='w-full border-b-2 py-2 rounded-md focus:outline-none text-slate-600 text-sm'
							>
								<option value='#'>--Select--</option>
								<option value='Subscribed'>Subscribed</option>
							</select>
						</div>
						<div className='mb-5'>
							<p className='mb-1 text-slate-600 px-1 font-medium'>
								Unit of measure
							</p>
							<select
								name='status'
								id='status'
								placeholder='Group'
								className='w-full border-b-2 py-2 rounded-md focus:outline-none text-slate-600 text-sm'
							>
								<option value='#'>--Select--</option>
								<option value='Subscribed'>Subscribed</option>
							</select>
						</div>
						<div className='mb-5'>
							<div>
								<p className='mb-1 text-slate-600 px-1 font-medium'>
									Zone number
								</p>
								<input
									type='text'
									placeholder='Enter your city'
									className='w-full border-b-2 py-2 rounded-md focus:outline-none text-slate-600 px-1 text-sm'
								/>
							</div>
						</div>
						<div className='mb-5'>
							<div>
								<p className='mb-1 text-slate-600 px-1 font-medium'>
									Zone name
								</p>
								<input
									type='text'
									placeholder='Enter your city'
									className='w-full border-b-2 py-2 rounded-md focus:outline-none text-slate-600 px-1 text-sm'
								/>
							</div>
						</div>
						<br />
						<div className='mb-2'>
							<button className='p-2.5 bg-orange-500 text-slate-50 w-full rounded-sm'>
								Save
							</button>
						</div>
					</Modal>

					<button
						className='py-2 px-5 bg-blue-500 text-slate-50 rounded text-xs md:text-sm'
						onClick={() => setOpenZone(true)}
					>
						Add zone
					</button>
					{/* / add zone */}
					<button className='py-2 px-5 bg-blue-500 text-slate-50 rounded text-xs md:text-sm'>
						Update zone to athlete
					</button>
					{/* audit */}
					<Modal
						size={"lg"}
						opened={openAudit}
						onClose={() => setOpenAudit(false)}
						title='Audit trail'
					>
						{/* Table */}
						<div className='w-full mb-6'>
							<div className='my-6'>
								<div className='flex flex-col'>
									<div className='overflow-x-auto shadow-md'>
										<div className='inline-block min-w-full align-middle'>
											<div className='overflow-hidden '>
												<table className='min-w-full divide-y divide-gray-200 table-fixed dark:divide-gray-700'>
													<thead className='bg-gray-100 '>
														<tr>
															<th
																scope='col'
																className='py-3 px-6 text-xs font-medium tracking-wider text-left text-gray-700 uppercase dark:text-gray-400'
															>
																Date
															</th>
															<th
																scope='col'
																className='py-3 px-6 text-xs font-medium tracking-wider text-left text-gray-700 uppercase dark:text-gray-400'
															>
																New Value
															</th>
															<th
																scope='col'
																className='py-3 px-6 text-xs font-medium tracking-wider text-left text-gray-700 uppercase dark:text-gray-400'
															>
																Old Value
															</th>
															<th
																scope='col'
																className='py-3 px-6 text-xs font-medium tracking-wider text-left text-gray-700 uppercase dark:text-gray-400'
															>
																Modify by
															</th>
														</tr>
													</thead>
													<tbody className='bg-white divide-y divide-gray-200 '></tbody>
												</table>
											</div>
										</div>
									</div>
								</div>
							</div>
						</div>
					</Modal>
					<button onClick={() => setOpenAudit(true)}>
						<IconTarget size={32} color='#555' />
					</button>
					{/* /audit */}
				</div>
				{/* / Actions */}
			</div>
		</>
	);
};

export default ZonePage;
