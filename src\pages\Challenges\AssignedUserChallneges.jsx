import {
	Card,
	CardContent,
	CircularProgress,
	Grid,
	Typography,
} from "@mui/material";
import React, { useEffect, useState } from "react";
import { Modal } from "@mantine/core";
import {
	URL,
	getProgress,
	getUserChalnnges,
	testSyncChallenge,
} from "../../API/api-endpoint";
import { Button, Table } from "antd";
import Header from "../../components/Header";
import Background from "../../Images/ChallengeTracker.png";
import moment from "moment";
import { showSuccess } from "../../components/Messages";
import PogressBar from "../../components/ProgressBar";
import SlickCarousel from "../SlickCarousel";
import { capitalizeFirstLetter } from "../../utils/Resubale";
import Swal from "sweetalert2";
import Untitled from "../../Images/Running.png";
import Untitled2 from "../../Images/Cycling.png";
import Untitled3 from "../../Images/Swimming.png";
import Description from "../Workouts/Description";
import axios from "axios";

const AssignedUserChallneges = ({ isEnrolledChallengesOpen }) => {
	const [allAssignedChallenges, setAllAssignedList] = useState();
	const [communityChallenges, setCommunityChallenges] = useState([]);
	const [isLoading, setIsloading] = useState(false);
	const [isCheckedProgress, setCheckProgress] = useState(false);
	const [isOpenDescription, setOpenDescription] = useState(false);
	const [assidnedChallengesProress, setassidnedChallengesProress] =
		useState();

	console.log("allAssignedChallenges in assinpage", allAssignedChallenges);

	const handleCheckProgress = async (id) => {
		let data = { id: id };
		console.log("data in assinpage", data);
		const response = await getProgress(data);
		console.log("handleCheckProgress in assinpage", response);
		setassidnedChallengesProress({
			totalProgress: response?.totalprogress,
			progress: response?.progress,
		});
	};

	useEffect(() => {
		if (isEnrolledChallengesOpen?.assignCoachId) {
			fetchAssignedChallenge();
		}
		if (!isEnrolledChallengesOpen?.assignCoachId) {
			fetchAssignedChallenge();
		}
	}, [isEnrolledChallengesOpen?.assignCoachId]);

	useEffect(() => {
		Swal.fire({
			title: "Info",
			text: "Challenge progress would synchronize at start of every hour. Please wait to check if progress have not been updated",
			icon: "info",
		});
	}, []);

	// Fetch for Yoska Academy challenges
	const fetchAssignedChallenge = async () => {
		const response = await getUserChalnnges(
			isEnrolledChallengesOpen?.assignCoachId
		);
		setAllAssignedList(response?.challenges);
		console.log("fetchAssignedChallenge in assignpage", response);
	};

	// Modified handleFetchEnrollChallenge now updates state for community challenges
	const handleFetchEnrollChallenge = async () => {
		const userId = localStorage.getItem("userId");
		if (!userId) {
			console.error("Athlete ID not found");
			return;
		}
		try {
			const token = localStorage.getItem("token");
			const response = await axios.get(
				`${URL}/athlete-community/group-challenge-enrolment/athlete/${userId}`,
				{
					headers: {
						Authorization: token,
					},
				}
			);
			console.log("Enrollment response", response);
			if (response.data && response.data.data) {
				setCommunityChallenges(response.data.data);
			}
		} catch (error) {
			console.error("Error enrolling athlete:", error);
			Swal.fire({
				icon: "error",
				title: "Enrollment Failed",
				text: error.message || "An error occurred during enrollment.",
			});
		}
	};

	// New useEffect to fetch community challenges when onboardingState is "community"
	const storedUser = localStorage.getItem("user");
	let user = null;
	let onboardingState = null;

	if (storedUser) {
		user = JSON.parse(storedUser);
		onboardingState = user.onboardingState;
		console.log("Onboarding State:", onboardingState);
	} else {
		console.log("User not found in localStorage.");
	}

	useEffect(() => {
		if (onboardingState === "community") {
			handleFetchEnrollChallenge();
		}
	}, [onboardingState]);

	const columns = [
		{
			title: <b style={{ color: "#E67E22" }}>Activity Name</b>,
			dataIndex: "activity_name",
			key: "activity_name",
			width: "33%",
		},
		{
			title: <b style={{ color: "#E67E22" }}>Distance</b>,
			dataIndex: "quota",
			key: "quota",
			width: "30%",
		},
		{
			title: <b style={{ color: "#E67E22" }}>Completed Distance</b>,
			dataIndex: `completed_quota`,
			key: "completed_quota",
			width: "33%",
		},
	];

	const HandleTestSync = async () => {
		setIsloading(true);
		const response = await testSyncChallenge(
			isEnrolledChallengesOpen?.assignCoachId
		);
		if (response?.status) {
			setIsloading(false);
			showSuccess();
			fetchAssignedChallenge(isEnrolledChallengesOpen?.assignCoachId);
		}
		setIsloading(false);
		console.log("response", response);
	};

	return (
		<div>
			<Header />
			<div className='grid grid-cols-1 xl:grid-cols-5 items-start gap-x-4 '></div>
			<div
				style={
					isEnrolledChallengesOpen?.assignCoachId
						? {
								padding: "20px",
								display: "flex",
								background: "#FFEADC",
								justifyContent: "center",
								alignItems: "center",
						  }
						: {
								marginTop: "59px",
								padding: "20px",
								display: "flex",
								background: "#FFEADC",
								justifyContent: "center",
								alignItems: "center",
						  }
				}
			>
				<div
					style={{ width: "95%", minHeight: "90vh", padding: "20px" }}
				>
					<div className='flex justify-between'>
						<div>
							<h1 style={{ fontSize: "24px" }}>
								<strong>Challenge Details:</strong>
							</h1>
						</div>
						{!isEnrolledChallengesOpen?.assignCoachId && (
							<div className='flex'>
								<a href='/ongoing-challenge'>
									<Button
										type='primary'
										style={{ margin: "0% 0% 0% 2%" }}
									>
										Ongoing Challenges
									</Button>{" "}
									&nbsp;
								</a>
								<Button type='primary' onClick={HandleTestSync}>
									Sync
								</Button>
							</div>
						)}
					</div>

					{/* Yoska Academy design */}
					{onboardingState === "yoska_academy" &&
						allAssignedChallenges?.map((allAssignedList) => {
							let url =
								allAssignedList?.challenge?.badge?.replace(
									/" /g,
									""
								);
							let ImageURL = url
								? `${URL}/static/public/userimages/${url}`
								: Background;
							console.log("ImageURL", allAssignedList);
							return (
								<div
									className='w-[100%] bg-[white] mt-6 p-2'
									key={allAssignedList?.challenge_id}
								>
									<Grid container>
										<Grid
											item
											xs={12}
											sm={12}
											sx={{ textAlign: "end" }}
										>
											<Button
												disabled
												variant='outlined'
												style={{
													backgroundColor: "white",
													cursor: "default",
													borderColor: "#E67E22",
													color: "#E67E22",
													fontWeight: 700,
												}}
											>
												Challenges ID:
												{allAssignedList?.challenge_id}
											</Button>
										</Grid>
										<Grid container spacing={2}>
											<Grid item xs={12} sm={8}>
												{Background && (
													<div
														style={{
															backgroundSize:
																"100% 100%",
															height: "100%",
															marginLeft: "14px",
															padding: "12px",
															minHeight: "70vh",
															backgroundColor:
																"white",
														}}
													>
														<Grid
															sx={{
																position:
																	"relative",
															}}
															container
															spacing={2}
														>
															<Grid
																item
																xs={12}
																sm={3}
															>
																<Grid
																	item
																	xs={12}
																	sm={12}
																	style={{
																		margin: "0% 0% 10% 2%",
																	}}
																>
																	<Button
																		disabled
																		variant='outlined'
																		style={{
																			backgroundColor:
																				"white",
																			cursor: "default",
																			borderColor:
																				"#E67E22",
																			color: "#E67E22",
																			fontWeight: 700,
																		}}
																	>
																		{`${moment(
																			allAssignedList
																				?.challenge
																				?.challengeStartDate
																		).format(
																			"DD"
																		)} -  ${moment(
																			allAssignedList
																				?.challenge
																				?.challengeEndDate
																		).format(
																			"Do MMMM"
																		)}`}
																	</Button>
																</Grid>
																<Grid
																	item
																	xs={12}
																	sm={12}
																	style={{
																		fontSize:
																			"25px",
																		margin: "0% 0% 5% 0%",
																		lineHeight:
																			"20px",
																		position:
																			"absolute",
																		zIndex: "5",
																	}}
																>
																	{capitalizeFirstLetter(
																		allAssignedList
																			?.challenge
																			?.challengeName
																	)}
																</Grid>
															</Grid>
															<Grid
																item
																xs={12}
																sm={7}
																style={{
																	fontSize:
																		"15px",
																}}
															>
																{allAssignedList
																	?.challenge
																	?.activityGroup
																	?.activity_group ===
																"Cycling" ? (
																	<div
																		style={{
																			outline:
																				"none",
																		}}
																	>
																		<img
																			style={{
																				outline:
																					"none",
																			}}
																			src={
																				Untitled2
																			}
																			className='p-3 d-block img'
																			alt='Cycling'
																		/>
																		<h1 className='text-center mb-3 mr-2 ml-2'>
																			Cycling
																			improves
																			strength,
																			balance
																			and
																			coordination.
																			It
																			may
																			also
																			help
																			to
																			prevent
																			falls
																			and
																			fractures.
																		</h1>
																	</div>
																) : allAssignedList
																		?.challenge
																		?.activityGroup
																		?.activity_group ===
																  "Swimming" ? (
																	<div
																		style={{
																			outline:
																				"none",
																		}}
																	>
																		<img
																			style={{
																				outline:
																					"none",
																			}}
																			src={
																				Untitled3
																			}
																			className='p-3 d-block img'
																			alt='Swimming'
																		/>
																		<h1 className='text-center mb-3 mr-2 ml-2'>
																			Builds
																			endurance,
																			muscle
																			strength
																			and
																			cardiovascular
																			fitness.
																			Tones
																			muscles
																			and
																			builds
																			strength.
																			All
																			muscles
																			are
																			used
																			during
																			swimming.
																		</h1>
																	</div>
																) : allAssignedList
																		?.challenge
																		?.activityGroup
																		?.activity_group ===
																  "Running" ? (
																	<div
																		style={{
																			outline:
																				"none",
																		}}
																	>
																		<img
																			style={{
																				outline:
																					"none",
																			}}
																			src={
																				Untitled
																			}
																			className='p-3 d-block img'
																			alt='Running'
																		/>
																		<h1 className='text-center mb-3 mr-2 ml-2'>
																			Running
																			enhances
																			cardiovascular
																			health,
																			burns
																			calories,
																			and
																			boosts
																			mental
																			well-being
																			through
																			the
																			release
																			of
																			endorphins.
																		</h1>
																	</div>
																) : null}
															</Grid>
															<Grid
																item
																xs={12}
																sm={2}
																sx={{
																	textAlign:
																		"end",
																}}
															>
																<Button
																	type='primary'
																	disabled
																	style={{
																		margin: "0% 0% 0% 2%",
																		color: "white",
																		cursor: "default",
																	}}
																>
																	{
																		allAssignedList
																			?.challenge
																			?.challengeDuration
																	}{" "}
																	Day
																</Button>
															</Grid>
														</Grid>
													</div>
												)}
											</Grid>
											<Grid
												item
												xs={12}
												sm={3}
												style={{
													borderLeft:
														"1px solid #E67E22",
													margin: "auto",
													fontSize: "13px",
												}}
											>
												<Grid container spacing={2}>
													<Grid item xs={12} sm={6}>
														Activity Group
													</Grid>
													<Grid
														item
														xs={12}
														sm={6}
														sx={{
															textAlign: "end",
														}}
													>
														{
															allAssignedList
																?.challenge
																?.activityGroup
																?.activity_group
														}
													</Grid>
													<Grid item xs={12} sm={9}>
														Level
													</Grid>
													<Grid
														item
														xs={12}
														sm={3}
														sx={{
															textAlign: "end",
														}}
													>
														{
															allAssignedList
																?.challenge
																?.activitylevel
																?.level
														}
													</Grid>
													<Grid item xs={12} sm={8}>
														Challenge Points
													</Grid>
													<Grid
														item
														xs={12}
														sm={2}
														sx={{
															textAlign: "end",
														}}
													>
														{
															allAssignedList
																?.challenge
																?.challengePoints
														}
													</Grid>
													<Grid item xs={12} sm={8}>
														Start on{" "}
														{moment(
															allAssignedList
																?.challenge
																?.challengeStartDate
														).format("Do MMMM")}
													</Grid>
													<Grid
														item
														xs={12}
														sm={4}
														sx={{
															textAlign: "end",
														}}
													>
														{
															allAssignedList
																?.challenge
																?.activityTrack
																?.activity_track
														}
													</Grid>
													<Grid
														item
														xs={12}
														sm={12}
														sx={{
															textAlign: "center",
														}}
													>
														<Button
															variant='outlined'
															style={{
																borderColor:
																	"#E67E22",
																color: "#E67E22",
																fontWeight: 700,
															}}
															onClick={() => {
																setCheckProgress(
																	true
																);
																handleCheckProgress(
																	allAssignedList?.id
																);
															}}
														>
															Check Progress
														</Button>
														&nbsp;
														<Button
															variant='outlined'
															style={{
																borderColor:
																	"#E67E22",
																color: "#E67E22",
																fontWeight: 700,
															}}
															onClick={() => {
																setOpenDescription(
																	{
																		isOpen: true,
																		description:
																			capitalizeFirstLetter(
																				allAssignedList
																					?.challenge
																					?.challengeDescription
																			),
																	}
																);
															}}
														>
															View Description
														</Button>
													</Grid>
												</Grid>
											</Grid>
										</Grid>
									</Grid>
								</div>
							);
						})}

					{/* Community challenges design */}
					{onboardingState === "community" &&
						communityChallenges?.map((item) => {
							const challenge =
								item.athletecommunitygroupchallenge;
							// Determine image based on activity
							let imageSrc = Background;
							if (
								challenge.activity.activity_name === "Running"
							) {
								imageSrc = Untitled;
							} else if (
								challenge.activity.activity_name === "Cycling"
							) {
								imageSrc = Untitled2;
							} else if (
								challenge.activity.activity_name === "Swimming"
							) {
								imageSrc = Untitled3;
							}
							return (
								<div
									className='w-[100%] bg-[white] mt-6 p-2'
									key={challenge.id}
								>
									<Grid container>
										<Grid
											item
											xs={12}
											sm={12}
											sx={{ textAlign: "end" }}
										>
											<Button
												disabled
												variant='outlined'
												style={{
													backgroundColor: "white",
													cursor: "default",
													borderColor: "#E67E22",
													color: "#E67E22",
													fontWeight: 700,
												}}
											>
												Challenge ID: {challenge.id}
											</Button>
										</Grid>
										<Grid container spacing={2}>
											<Grid item xs={12} sm={8}>
												<div
													style={{
														backgroundSize:
															"100% 100%",
														height: "100%",
														marginLeft: "14px",
														padding: "12px",
														minHeight: "70vh",
														backgroundColor:
															"white",
													}}
												>
													<Grid container spacing={2}>
														<Grid
															item
															xs={12}
															sm={3}
														>
															<div
																style={{
																	margin: "0% 0% 10% 2%",
																}}
															>
																<Button
																	disabled
																	variant='outlined'
																	style={{
																		backgroundColor:
																			"white",
																		cursor: "default",
																		borderColor:
																			"#E67E22",
																		color: "#E67E22",
																		fontWeight: 700,
																	}}
																>
																	{`${moment(
																		challenge.challengeStartDate
																	).format(
																		"DD"
																	)} -  ${moment(
																		challenge.challengeEndDate
																	).format(
																		"Do MMMM"
																	)}`}
																</Button>
															</div>
															<div
																style={{
																	fontSize:
																		"25px",
																	margin: "0% 0% 5% 0%",
																	lineHeight:
																		"20px",
																}}
															>
																{capitalizeFirstLetter(
																	challenge.challengeName
																)}
															</div>
														</Grid>
														<Grid
															item
															xs={12}
															sm={7}
															style={{
																fontSize:
																	"15px",
															}}
														>
															<div
																style={{
																	outline:
																		"none",
																}}
															>
																<img
																	style={{
																		outline:
																			"none",
																	}}
																	src={
																		imageSrc
																	}
																	className='p-3 d-block img'
																	alt={
																		challenge
																			.activity
																			.activity_name
																	}
																/>
																<h1 className='text-center mb-3 mr-2 ml-2'>
																	Join this
																	community
																	challenge to
																	boost your{" "}
																	{challenge.activity.activity_name.toLowerCase()}{" "}
																	skills!
																</h1>
															</div>
														</Grid>
														<Grid
															item
															xs={12}
															sm={2}
															sx={{
																textAlign:
																	"end",
															}}
														>
															<Button
																type='primary'
																disabled
																style={{
																	margin: "0% 0% 0% 2%",
																	color: "white",
																	cursor: "default",
																}}
															>
																Duration:{" "}
																{challenge.challengeDuration ==
																	1 &&
																challenge.challengeDurationUnit ==
																	"years"
																	? "1 year"
																	: `${challenge.challengeDuration} ${challenge.challengeDurationUnit}`}
															</Button>
														</Grid>
													</Grid>
												</div>
											</Grid>
											<Grid
												item
												xs={12}
												sm={3}
												style={{
													borderLeft:
														"1px solid #E67E22",
													margin: "auto",
													fontSize: "13px",
												}}
											>
												<Grid container spacing={2}>
													<Grid item xs={12} sm={6}>
														Activity
													</Grid>
													<Grid
														item
														xs={12}
														sm={6}
														sx={{
															textAlign: "end",
														}}
													>
														{
															challenge.activity
																.activity_name
														}
													</Grid>
													<Grid item xs={12} sm={9}>
														Level
													</Grid>
													<Grid
														item
														xs={12}
														sm={3}
														sx={{
															textAlign: "end",
														}}
													>
														{challenge.level.name}
													</Grid>
													<Grid item xs={12} sm={8}>
														Target
													</Grid>
													<Grid
														item
														xs={12}
														sm={4}
														sx={{
															textAlign: "end",
														}}
													>
														{challenge.target.name}
													</Grid>
													<Grid
														item
														xs={12}
														sm={12}
														sx={{
															textAlign: "center",
														}}
													>
														<Button
															variant='outlined'
															style={{
																borderColor:
																	"#E67E22",
																color: "#E67E22",
																fontWeight: 700,
															}}
														>
															Check Progress
														</Button>
														&nbsp;
														<Button
															variant='outlined'
															style={{
																borderColor:
																	"#E67E22",
																color: "#E67E22",
																fontWeight: 700,
															}}
															onClick={() => {
																setOpenDescription(
																	{
																		isOpen: true,
																		description:
																			capitalizeFirstLetter(
																				challenge?.challengeDescription
																			),
																	}
																);
															}}
														>
															View Description
														</Button>
													</Grid>
												</Grid>
											</Grid>
										</Grid>
									</Grid>
								</div>
							);
						})}
				</div>
			</div>
			{isCheckedProgress && (
				<Modal
					opened={isCheckedProgress}
					onClose={() => setCheckProgress(false)}
					title='Progress Details'
				>
					<Card>
						<CardContent
							sx={{
								display: "flex",
								flexDirection: "column",
							}}
						>
							<Typography
								sx={{ padding: "10px" }}
								variant='h6'
								component='div'
							>
								Progress Data
							</Typography>
							{assidnedChallengesProress?.progress?.length > 0 ? (
								<>
									<Typography
										sx={{ padding: "10px" }}
										variant='body'
										component='div'
									>
										Activity Name:{" "}
										{
											assidnedChallengesProress
												?.progress[0]?.activity_name
										}
									</Typography>
									<Typography
										sx={{ padding: "10px" }}
										variant='body'
										component='div'
									>
										Distance:{" "}
										{
											assidnedChallengesProress
												?.progress[0]?.quota
										}
										{assidnedChallengesProress?.progress[0]
											?.unit
											? assidnedChallengesProress
													?.progress[0]?.quota
											: "km"}
									</Typography>
									<Typography
										sx={{ padding: "10px" }}
										variant='body'
										component='div'
									>
										Complete Distance:{" "}
										{
											assidnedChallengesProress
												?.progress[0]?.completed_quota
										}
										{assidnedChallengesProress?.progress[0]
											?.unit
											? assidnedChallengesProress
													?.progress[0]?.quota
											: "km"}
									</Typography>
									<Typography
										sx={{ padding: "10px" }}
										variant='body'
										component='div'
									>
										Progress:{" "}
										{assidnedChallengesProress?.totalProgress?.toFixed(
											2
										)}
										%
									</Typography>
									<PogressBar
										completed={
											assidnedChallengesProress?.totalProgress
										}
									/>{" "}
									&nbsp;
								</>
							) : (
								<div style={{ padding: "10px" }}>
									No Data Found
								</div>
							)}

							<Table
								columns={columns}
								dataSource={assidnedChallengesProress?.progress}
								pagination={true}
							/>
						</CardContent>
					</Card>
				</Modal>
			)}
			{isOpenDescription?.isOpen && (
				<Description
					isModalOpen={isOpenDescription}
					handleCancel={setOpenDescription}
				/>
			)}
		</div>
	);
};

export default AssignedUserChallneges;
