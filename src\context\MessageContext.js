// src/contexts/MessageContext.js
import { createContext, useState, useContext,useEffect } from 'react';

const MessageContext = createContext();

export const MessageProvider = ({ children }) => {
  const [pubnubUnseenCount, setPubnubUnseenCount] = useState(0);
  const [firebaseUnseenCount, setFirebaseUnseenCount] = useState(0);
  const [totalUnseenMessageCount, setTotalUnseenMessageCount] = useState(0);

  // This function updates the total unseen count whenever individual counts change
  useEffect(() => {
    setTotalUnseenMessageCount(pubnubUnseenCount + firebaseUnseenCount);
  }, [pubnubUnseenCount, firebaseUnseenCount]);

  useEffect(() => {
    console.log('Total Unseen Messages:', totalUnseenMessageCount);
  }, [totalUnseenMessageCount]);

  return (
    <MessageContext.Provider
      value={{
        totalUnseenMessageCount,
        setPubnubUnseenCount,  // Provide setter functions in context
        setFirebaseUnseenCount,
      }}
    >
      {children}
    </MessageContext.Provider>
  );
};

export const useMessageContext = () => {
  const context = useContext(MessageContext);
  if (!context) {
    throw new Error('useMessageContext must be used within a MessageProvider');
  }
  return context;
};
