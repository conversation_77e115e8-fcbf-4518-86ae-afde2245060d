// ** React Imports
import React, { useState, useEffect } from "react";

// ** MUI Components
import Box from "@mui/material/Box";
import Divider from "@mui/material/Divider";
import Checkbox from "@mui/material/Checkbox";
import TextField from "@mui/material/TextField";
import InputLabel from "@mui/material/InputLabel";
import Typography from "@mui/material/Typography";
import IconButton from "@mui/material/IconButton";
import CardContent from "@mui/material/CardContent";
import FormControl from "@mui/material/FormControl";
import OutlinedInput from "@mui/material/OutlinedInput";
import { styled, useTheme } from "@mui/material/styles";
import MuiCard from "@mui/material/Card";
import InputAdornment from "@mui/material/InputAdornment";
import MuiFormControlLabel from "@mui/material/FormControlLabel";
import LoadingButton from "@mui/lab/LoadingButton";

// ** Icons Imports
import VisibilityIcon from "@mui/icons-material/Visibility";
import VisibilityOffIcon from "@mui/icons-material/VisibilityOff";

import { FcGoogle } from "react-icons/fc";
import { FaApple } from "react-icons/fa6";

import { useNavigate, useParams, useLocation } from "react-router-dom";
import { SignupWithGoogle, WriteUserData, AppleSignup } from "../../API/firebase.config";
import Swal from "sweetalert2";
import {
  EmailSignup,
  Login,
  ResendOtp,
  VerifyOTP,
} from "../../API/api-endpoint";
import Background from "../../Images/Background.png";
import { useDispatch } from "react-redux";
import { useFormik } from "formik";
import { FormHelperText, FormLabel, Grid } from "@mui/material";
import GoogleImage from "../../Images/GoogleImage.png";
import LayoutImage from "./LayoutImage";
import SlickCarousel from "../SlickCarousel";
import { showError, showSuccess } from "../../components/Messages";

// ** Styled Components
const Card = styled(MuiCard)(({ theme }) => ({
  [theme.breakpoints.up("sm")]: { width: "28rem" },
}));

const FormControlLabel = styled(MuiFormControlLabel)(({ theme }) => ({
  "& .MuiFormControlLabel-label": {
    fontSize: "0.875rem",
    color: theme.palette.text.secondary,
  },
}));

const SignupPage = (props) => {

  const location = useLocation();

  let isVerifyOTP = localStorage.getItem("isOTP")
  console.log("isVerifyOTP", isVerifyOTP);
  // ** State
  const [genratedOTP, setgenratedOTP] = useState(null);
  const [enteredOTP, setenteredOTP] = useState("");
  const [isOTPScreen, setisOTPScreen] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [isOTP, setisOTPverify] = useState(false);
  const [groupId, setGroupId] = useState(0);



  const [isLoading, setIsLoading] = useState(false);
  const [isGoogleSignUpLoading, setIsGoogleSignUpLoading] = useState(false);
  const [isAppleSignUpLoading, setIsAppleSignUpLoading] = useState(false);

  useEffect(() => {
    if (isVerifyOTP) {
      setisOTPScreen(true)
    }
  }, [isVerifyOTP]);

  useEffect(() => {
    localStorage.clear();
    const queryParams = new URLSearchParams(location.search);
    const groupId = queryParams.get("groupId");
    if (groupId) {
      setGroupId(parseInt(groupId, 10));
    } else {
      setGroupId(0)
    }
    console.log("----- group id -----");
    console.log(groupId);
  }, [])

  // ** Hook
  const navigate = useNavigate();
  const dispatch = useDispatch();

  const ResendAPI = async () => {
    setenteredOTP("")
    let apicalled = false;
    setisOTPScreen(true);
    let email = localStorage.getItem("email");
    if (apicalled === false) {
      let response = await ResendOtp(email);
      if (response.status) {
        apicalled = true;
        Swal.fire({
          title: "Success",
          text: response?.message,
          icon: "success",
        });
      } else {
        Swal.fire({
          title: "Error",
          text: response?.message,
          icon: "error",
        });
      }
    }
  };

  // ... other code ...

  const formik = useFormik({
    initialValues: {
      email: "",
      password: "",
      firstName: "",
      lastName: "",
    },
    validate: (values) => {
      const errors = {};

      if (!values.email) {
        errors.email = "Email is required";
      } else if (!/^[^\s@]+(\.[^\s@]+)*@[^\s@]+\.[^\s@]{2,}$/.test(values.email)) {
        errors.email = "Invalid email address";
      }

      if (!values.password) {
        errors.password = "Password is required";
      } else if (
        !/^(?=.*[a-zA-Z])(?=.*\d)(?=.*[\W_])\S{8,}$/.test(
          values.password
        )
      ) {
        errors.password =
          "Password must be at least 8 characters long and contain a character, number, and a special character without spaces";
      }

      if (!values.firstName) {
        errors.firstName = "First Name is required";
      } else if (!/^[A-Z][a-z]*$/.test(values.firstName)) {
        errors.firstName = "First Name should start only with an uppercase letter and not contain numbers";
      }
      else if (values.firstName?.length < 3) {
        errors.firstName = "First Name must be at least 3 characters long"
      }
      else if (values.firstName?.length > 20) {
        errors.firstName = "First Name must be less than 20 characters"
      }
      else if (values.firstName?.length > 20) {
        errors.firstName = "First Name must be less than 20 characters"
      }
      if (!values.lastName) {
        errors.lastName = "Last Name is required";
      }
      else if (values.lastName?.length < 1) {
        errors.lastName = "Last Name must be at least 1 characters long"
      }
      else if (!/^[A-Za-z]+$/.test(values.lastName)) {
        errors.lastName = "Last Name should only contain letters";
      }

      return errors;
    },
    onSubmit: async (values) => {
      // Your form submission logic here
      if (formik.isValid) {
        setIsLoading(true);
        let name = `${formik.values.firstName} ${formik.values.lastName}`;
        const previousObj = {
          email: values.email,
          password: values.password,
          firstname: values.firstName,
          lastname: values.lastName,
        };

        const toSentObj = groupId ?
          Object.assign({}, previousObj, {
            athleteGroupId: groupId
          }) :
          previousObj;

        let result = await EmailSignup(toSentObj, dispatch, name);
        console.log("result", result.status);
        if (result.status) {
          Swal.fire({
            title: "Success",
            text: result?.message,
            icon: "success",
          });
          setIsLoading(false);
          setisOTPverify(true)
          localStorage.setItem("isOTP", true)
          setisOTPScreen(true);
          localStorage.setItem(
            "fullname",
            `${formik.values.firstName} ${formik.values.lastName}`
          );
        } else {
          Swal.fire({
            title: "Error!!",
            text: result.message,
            icon: "error",
          });
          setIsLoading(false);
        }
      }
    },
  });

  const handleGoogleSignin = async () => {
    try {
      setIsGoogleSignUpLoading(true);
      const result = await SignupWithGoogle(dispatch);
      if (result.success == true) {
        Swal.fire({
          title: "Success",
          text: result?.message,
          icon: "success",
        });
        navigate("/phone-otp-verification");
        setIsGoogleSignUpLoading(false);
      } else {
        if (result.message) {
          Swal.fire({
            title: "Error!!",
            text: result.message,
            icon: "error",
          });
        }
      }
      setIsGoogleSignUpLoading(false);
    } catch (e) {
      console.error(`Error occured :- ${e}`);
      setIsGoogleSignUpLoading(false);
    }
  };

  const handleAppleSignup = async () => {
    try {
      setIsAppleSignUpLoading(true);
      const result = await AppleSignup(dispatch);
      if (result.success == true) {
        showSuccess(result?.message);
        setIsAppleSignUpLoading(false);
        navigate("/phone-otp-verification");
      } else {
        setIsAppleSignUpLoading(false);
        if (result.message) {
          localStorage.clear();
          showError(result?.message);
        }
      }
    } catch (e) {
      console.error(`Error occured :- ${e}`);
      setIsAppleSignUpLoading(false);
    }
  }

  const verifyOtp = async () => {
    let userToken = localStorage.getItem('userTokentoken')
    if (enteredOTP.length === 6) {
      setIsLoading(true);
      try {
        let FinalEmail =
          formik.values.email.length === 0
            ? localStorage.getItem("email")
            : formik.values.email;
        let result = await VerifyOTP({
          email: FinalEmail,
          otp: enteredOTP,
        });
        if (result.message == "OTP verified successfully") {
          Swal.fire({
            title: "Success!!",
            text: "Successfully verified Your Email",
            icon: "success",
          });
          localStorage.setItem("token", userToken);
          localStorage.removeItem("userTokentoken");

          navigate("/phone-otp-verification");
          localStorage.removeItem("isOTP")

          //navigate("/onboarding-flow");
          setIsLoading(false);
        } else if (result.message === "Invalid OTP") {
          Swal.fire({
            title: "Error!!",
            text: result.message,
            icon: "error",
          });
          setIsLoading(false);
        }
      } catch (error) {
        Swal.fire({
          title: "Error!!",
          text: "Entered OTP is incorrect",
          icon: "error",
        });
        setIsLoading(false);
      }
    } else {
      Swal.fire({
        title: "Error!!",
        text: "Please enter the 6 digit only",
        icon: "error",
      });
    }
  };

  // ... rest of your code ...
  if (!isOTPScreen) {
    return (
      <>
        <Box
          className="content-center"
          style={{
            width: "100vw",
            padding: "40px",
            height: "100vh", // 100% of the viewport height
          }}
        >
          <Grid container>
            <Grid container spacing={2}>
              <Grid item xs={12} sm={12} md={7} style={{
                display: "flex",
                padding: "20px",
                alignItems: "center"
                // justifyContent: "center",

              }}>
                <div style={{ width: "100%" }} >
                  <div >
                    <div className="headingCont  mb-2">
                      <Typography variant="h4" className="heading">Sign<span style={{ color: "orange" }}> Up</span></Typography>{" "}
                    </div>
                    <form autoComplete="off" onSubmit={formik.handleSubmit} className="form1">
                      <Grid container spacing={2}>
                        <Grid item xs={12} sm={12}>
                          <FormLabel sx={{ fontSize: "14px", fontWeight: "600", color: "background: rgba(13, 20, 28, 1)", marginBottom: "10px" }} >Email<span className="text-[red]">*</span></FormLabel>
                          <TextField
                            fullWidth
                            size="small"
                            // id="email"
                            placeholder="Email"
                            onBlur={formik.handleBlur}
                            error={formik.touched.email && formik.errors.email}
                            helperText={formik.touched.email && formik.errors.email}
                            sx={{ marginBottom: 2 }}
                            {...formik.getFieldProps("email")}
                            autoComplete="off"
                          />
                        </Grid>
                        <Grid item xs={12} sm={6}>
                          <FormLabel sx={{ fontSize: "14px", fontWeight: "600", color: "background: rgba(13, 20, 28, 1)", marginBottom: "10px" }} >First Name<span className="text-[red]">*</span></FormLabel>
                          <TextField
                            fullWidth
                            id="first Name"
                            placeholder="First Name"

                            size="small"
                            onBlur={formik.handleBlur}
                            error={formik.touched.firstName && formik.errors.firstName}
                            helperText={
                              formik.touched.firstName && formik.errors.firstName
                            }
                            sx={{ marginBottom: 2 }}
                            {...formik.getFieldProps("firstName")}
                          />
                        </Grid>
                        <Grid item xs={12} sm={6}>
                          <FormLabel sx={{ fontSize: "14px", fontWeight: "600", color: "background: rgba(13, 20, 28, 1)", marginBottom: "10px" }} >Last Name<span className="text-[red]">*</span></FormLabel>
                          <TextField
                            fullWidth
                            id="last Name"
                            size="small"
                            onBlur={formik.handleBlur}
                            placeholder="Last Name"

                            error={formik.touched.lastName && formik.errors.lastName}
                            helperText={formik.touched.lastName && formik.errors.lastName}
                            sx={{ marginBottom: 2 }}
                            {...formik.getFieldProps("lastName")}
                          />
                        </Grid>

                        <Grid item xs={12} sm={12}>
                          <FormControl fullWidth>
                            <FormLabel sx={{ fontSize: "14px", fontWeight: "600", color: "background: rgba(13, 20, 28, 1)" }} >Password<span className="text-[red]">*</span></FormLabel>
                            <OutlinedInput
                              value={formik.values.password}
                              id="auth-login-password"
                              name="password"
                              size="small"
                              onBlur={formik.handleBlur}
                              placeholder="Password"

                              error={formik.touched.password && formik.errors.password}
                              // helperText={formik.touched.password && formik.errors.password}
                              onChange={formik.handleChange}
                              type={showPassword ? "text" : "password"}
                              endAdornment={
                                <InputAdornment
                                  style={{ marginRight: "15px" }}
                                  position="end"
                                >
                                  <IconButton
                                    edge="end"
                                    onClick={() => setShowPassword(!showPassword)}
                                    aria-label="toggle password visibility"
                                  >
                                    {showPassword ? (
                                      <VisibilityIcon />
                                    ) : (
                                      <VisibilityOffIcon />
                                    )}
                                  </IconButton>
                                </InputAdornment>
                              }
                            />
                            <FormHelperText style={{ color: "#d32f2f" }}>
                              {formik.touched.password && formik.errors.password}
                            </FormHelperText>
                          </FormControl>
                        </Grid>
                        {/* Signup buttons */}
                        <div className="w-full flex flex-col my-2 pl-4">
                          <div className="w-full">
                            {/* Signup Button  */}
                            <button
                              type="submit"
                              className="w-full bg-orange-400 py-3 rounded-xl transition duration-200 mb-4 text-white text-xl font-semibold md:text-base sm:text-base"
                              disabled={isLoading ? true : false}
                            >
                              {isLoading ? (
                                <div className="animate-spin bg-center rounded-full h-5 w-5 border-t-2 border-white"></div>
                              ) : (
                                "Sign up"
                              )}
                            </button>
                          </div>
                          {
                            (groupId === 0) && (
                              <div className="w-full flex flex-col gap-4 items-center justify-evenly sm:flex-row">
                                {/* Google Signup Button  */}
                                <button
                                  type="button"
                                  className="w-full sm:w-1/2 py-3 bg-orange-400 rounded-lg flex items-center justify-center transition duration-200 text-white text-xl font-medium md:text-base sm:text-base"
                                  onClick={() => handleGoogleSignin()}
                                  disabled={isGoogleSignUpLoading}
                                >
                                  {isGoogleSignUpLoading ? (
                                    <div className="animate-spin bg-center rounded-full h-5 w-5 border-t-2 border-white"></div>
                                  ) : (
                                    <span className="flex flex-row justify-center">
                                      <span className="mr-2"><FcGoogle size={22} /> </span>
                                      <span>Sign up with Google</span>
                                    </span>
                                  )}
                                </button>

                                {/* Apple Signup Button  */}
                                <button
                                  type="button"
                                  className="w-full sm:w-1/2 py-3 bg-black rounded-lg flex items-center justify-center transition duration-200 text-white text-xl font-medium md:text-base sm:text-base"
                                  onClick={() => handleAppleSignup()}
                                  disabled={isAppleSignUpLoading}
                                >
                                  {isAppleSignUpLoading ? (
                                    <div className="animate-spin bg-center rounded-full h-5 w-5 border-t-2 border-white"></div>
                                  ) : (
                                    <span className="flex flex-row justify-center">
                                      <span className="mr-2">
                                        <FaApple size={22} />
                                      </span>
                                      <span>
                                        Sign up with Apple
                                      </span>
                                    </span>
                                  )}
                                </button>
                              </div>
                            )
                          }

                        </div>

                      </Grid>
                      <Typography variant="h6" component="div" style={{ fontSize: "16px", fontWeight: "500" }}>Already have an account?<span>
                        <a onClick={() => {
                          navigate("/")
                          localStorage.clear();
                        }} className="cursor-pointer text-[#E67E22]"> Sign in</a>
                      </span></Typography>
                    </form>
                  </div>
                </div>
              </Grid>
              <Grid item xs={12} sm={12} md={5}>
                <SlickCarousel />
              </Grid>
            </Grid>
          </Grid>
        </Box>
      </>
    );
  } else {
    return (
      <div className="app__container">
        <Box
          className="content-center"
          style={{
            width: "100vw",
            padding: "40px",
            height: "100vh", // 100% of the viewport height
          }}
        >
          <Grid container>
            <Grid container spacing={2}>
              <Grid item xs={12} sm={7} style={{
                display: "flex",
                // justifyContent: "center",
                // flexDirection: "column",
                padding: "50px",
                alignItems: "center"
              }}>
                <div style={{ width: "100%" }}>
                  <div className="form1">
                    <Box sx={{ marginBottom: 2, marginTop: 2 }}>
                      <Typography variant="h6" component="div">
                        An OTP has been sent to your email, please enter it below to verify your email
                      </Typography>
                    </Box>
                    <Grid container spacing={2}>
                      <Grid item xs={12} sm={12}>
                        <FormLabel sx={{ fontSize: "14px", fontWeight: "600", color: "background: rgba(13, 20, 28, 1)", marginBottom: "10px" }} >OTP</FormLabel><br />
                        <TextField
                          fullWidth
                          variant="outlined"
                          size="small"
                          type="number"
                          value={enteredOTP}
                          onChange={(event) => setenteredOTP(event.target.value)}
                        />
                      </Grid>
                      <Grid item xs={12} sm={6}>
                        {isVerifyOTP &&
                          <LoadingButton
                            fullWidth
                            variant="contained"
                            sx={{
                              marginTop: "20px",
                              marginBottom: "10px",
                              // color: "white",
                              // backgroundColor: "rgb(145, 85, 253)",
                              color: "white",
                              backgroundColor: "#FFA654",
                              '&:hover': {
                                backgroundColor: '#FFA654', // Change to your desired hover color
                              },

                            }}
                            onClick={ResendAPI}
                            loading={isLoading}
                            loadingPosition="start"
                          >
                            Resend OTP
                          </LoadingButton>
                        }

                      </Grid>


                      <Grid item xs={12} sm={6}>
                        <LoadingButton
                          fullWidth
                          variant="contained"
                          sx={{
                            marginBottom: "10px",
                            marginTop: "20px",
                            // color: "white",
                            // backgroundColor: "rgb(145, 85, 253)",
                            color: "white",
                            backgroundColor: "#FFA654",
                            '&:hover': {
                              backgroundColor: '#FFA654', // Change to your desired hover color
                            },

                          }}
                          onClick={verifyOtp}
                          loading={isLoading}
                          loadingPosition="start"
                        >
                          Verify Otp
                        </LoadingButton>
                      </Grid>


                    </Grid>
                    &nbsp;
                    <strong >
                      <a onClick={() => {
                        setisOTPverify(false)

                        setisOTPScreen(false)
                        localStorage.clear()
                      }} className="cursor-pointer text-sm ">
                        Try with new Email
                      </a>
                    </strong>
                  </div>

                </div>
              </Grid>
              <Grid item xs={12} sm={5}>
                <SlickCarousel />
              </Grid>
            </Grid>
          </Grid>
        </Box>
      </div>
    );
  }
};

export default SignupPage;
