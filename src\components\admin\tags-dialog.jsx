import { useState, useEffect } from "react";
import { But<PERSON> } from "../ui/button";
import { Input } from "../ui/input";
import { Label } from "../ui/label";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "../ui/select";
import { <PERSON>alog, DialogContent, DialogHeader, DialogTitle } from "../ui/dialog";
import { creatTagClouddata, updateTagClouddata } from "../../API/api-endpoint";
import Swal from "sweetalert2";

export const TagsDialog = ({ open, onClose, onSuccess, editingItem }) => {
	const [formData, setFormData] = useState({
		tag_cloud_name: "",
		status: true,
	});
	const [isLoading, setIsLoading] = useState(false);

	useEffect(() => {
		if (open) {
			if (editingItem?.["tag-cloud-id"]) {
				const editData = {
					tag_cloud_name: editingItem["tag-cloud-name"] || "",
					status:
						editingItem.status !== undefined
							? editingItem.status
							: true,
				};
				setFormData(editData);
			} else {
				const newData = {
					tag_cloud_name: "",
					status: true,
				};
				setFormData(newData);
			}
		}
	}, [open, editingItem]);

	const handleInputChange = (field, value) => {
		setFormData((prev) => ({
			...prev,
			[field]: value,
		}));
	};

	const handleSubmit = async (e) => {
		e.preventDefault();

		if (!formData.tag_cloud_name.trim()) {
			Swal.fire({
				title: "Error",
				text: "Tag name is required",
				icon: "error",
				timer: 1800,
				showConfirmButton: false,
			});
			return;
		}

		try {
			setIsLoading(true);

			const apiData = {
				tag_cloud_name: formData.tag_cloud_name.trim(),
				status: formData.status,
			};

			let response;
			if (editingItem?.["tag-cloud-id"]) {
				apiData["tag-cloud-id"] = editingItem["tag-cloud-id"];
				response = await updateTagClouddata(apiData);
			} else {
				response = await creatTagClouddata(apiData);
			}

			if (response?.status) {
				Swal.fire({
					title: "Success",
					text:
						response.message ||
						`Tag ${
							editingItem?.["tag-cloud-id"]
								? "updated"
								: "created"
						} successfully`,
					icon: "success",
					timer: 2000,
					showConfirmButton: false,
				});
				onSuccess();
			} else {
				Swal.fire({
					title: "Error",
					text: response?.message || "Failed to save tag",
					icon: "error",
					timer: 3000,
					showConfirmButton: false,
				});
			}
		} catch (error) {
			console.error("Error saving tag:", error);
			Swal.fire({
				title: "Error",
				text: "An error occurred while saving the tag",
				icon: "error",
				timer: 3000,
				showConfirmButton: false,
			});
		} finally {
			setIsLoading(false);
		}
	};

	return (
		<Dialog open={open} onOpenChange={onClose}>
			<DialogContent className='sm:max-w-md bg-white max-h-[90vh] overflow-y-auto'>
				<DialogHeader>
					<DialogTitle className='text-lg font-semibold text-gray-900'>
						{editingItem?.["tag-cloud-id"]
							? "Edit Tag"
							: "Create Tag"}
					</DialogTitle>
				</DialogHeader>

				<form onSubmit={handleSubmit} className='space-y-4'>
					<div className='grid gap-4'>
						<div className='space-y-2'>
							<Label
								htmlFor='tag-cloud-name'
								className='text-sm font-semibold'
							>
								Tag Name <span className='text-red-500'>*</span>
							</Label>
							<Input
								id='tag_cloud_name'
								className='w-full text-sm'
								value={formData.tag_cloud_name}
								onChange={(e) =>
									handleInputChange(
										"tag_cloud_name",
										e.target.value
									)
								}
								placeholder='Enter Tag name'
								disabled={isLoading}
							/>
						</div>

						<div className='space-y-2'>
							<Label
								htmlFor='status'
								className='text-sm font-semibold'
							>
								Status
							</Label>
							<Select
								value={formData.status.toString()}
								onValueChange={(value) =>
									handleInputChange(
										"status",
										value === "true"
									)
								}
								disabled={isLoading}
							>
								<SelectTrigger className='w-full text-sm'>
									<SelectValue placeholder='Select Status' />
								</SelectTrigger>
								<SelectContent className='bg-white'>
									<SelectItem value='true'>Yes</SelectItem>
									<SelectItem value='false'>No</SelectItem>
								</SelectContent>
							</Select>
						</div>
					</div>

					<div className='flex justify-end gap-3 pt-4'>
						<Button
							type='button'
							variant='outline'
							onClick={onClose}
							disabled={isLoading}
						>
							Cancel
						</Button>
						<Button
							type='submit'
							className='bg-orange-600 hover:bg-orange-700 text-white'
							disabled={isLoading}
						>
							{isLoading
								? "Saving..."
								: editingItem?.["tag-cloud-id"]
								? "Update Tag"
								: "Create Tag"}
						</Button>
					</div>
				</form>
			</DialogContent>
		</Dialog>
	);
};
