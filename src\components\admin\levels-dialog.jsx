import React, { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "../../components/ui/button";
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogFooter,
	DialogHeader,
	DialogTitle,
} from "../../components/ui/dialog";
import { Input } from "../../components/ui/input";
import { Label } from "../../components/ui/label";
import { Textarea } from "../../components/ui/textarea";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "../../components/ui/select";

export function LevelsDialog({
	open,
	onOpenChange,
	level,
	onSave,
	activities = [],
}) {
	const [formData, setFormData] = useState({
		activity_id: "",
		level: "",
		description: "",
		order: "",
	});

	useEffect(() => {
		if (level) {
			setFormData({
				activity_id: level.activity_id ? String(level.activity_id) : "",
				level: level.level || "",
				description: level.description || "",
				order: level.order ? String(level.order) : "",
			});
		} else {
			setFormData({
				activity_id: "",
				level: "",
				description: "",
				order: "",
			});
		}
	}, [level, open]);

	const handleSubmit = (e) => {
		e.preventDefault();
		onSave({
			...formData,
			activity_id: Number(formData.activity_id),
			order: formData.order ? Number(formData.order) : undefined,
		});
	};

	return (
		<Dialog open={open} onOpenChange={onOpenChange}>
			<DialogContent className='sm:max-w-[500px] bg-white'>
				<DialogHeader>
					<DialogTitle>
						{level ? "Edit Level" : "Create Level"}
					</DialogTitle>
					<DialogDescription className='text-gray-600 text-sm'>
						{level
							? "Update the level details below."
							: "Create a new skill level for an activity."}
					</DialogDescription>
				</DialogHeader>

				<form onSubmit={handleSubmit} className='space-y-4'>
					<div className='grid grid-cols-2 gap-4'>
						<div className='space-y-2'>
							<Label htmlFor='activity' className='font-semibold'>
								Activity
							</Label>
							<Select
								value={formData.activity_id}
								onValueChange={(value) =>
									setFormData({
										...formData,
										activity_id: value,
									})
								}
							>
								<SelectTrigger>
									<SelectValue placeholder='Select activity' />
								</SelectTrigger>
								<SelectContent className='bg-white'>
									{activities.length > 0 ? (
										activities.map((activity) => (
											<SelectItem
												key={activity.id}
												value={String(activity.id)}
											>
												{activity.activity ||
													activity.activity_name}
											</SelectItem>
										))
									) : (
										<SelectItem
											value='no-activities'
											disabled
										>
											No activities available
										</SelectItem>
									)}
								</SelectContent>
							</Select>
						</div>

						<div className='space-y-2'>
							<Label htmlFor='level' className='font-semibold'>
								Level
							</Label>
							<Select
								value={formData.level}
								onValueChange={(value) =>
									setFormData({ ...formData, level: value })
								}
							>
								<SelectTrigger>
									<SelectValue placeholder='Select level' />
								</SelectTrigger>
								<SelectContent className='bg-white'>
									<SelectItem value='Beginner'>
										Beginner
									</SelectItem>
									<SelectItem value='Novice'>
										Novice
									</SelectItem>
									<SelectItem value='Intermediate'>
										Intermediate
									</SelectItem>
									<SelectItem value='Advanced'>
										Advanced
									</SelectItem>
									<SelectItem value='Elite'>Elite</SelectItem>
								</SelectContent>
							</Select>
						</div>
					</div>

					<div className='space-y-2'>
						<Label htmlFor='order' className='font-semibold'>
							Order
						</Label>
						<Input
							id='order'
							type='number'
							placeholder='e.g., 1'
							value={formData.order}
							onChange={(e) =>
								setFormData({
									...formData,
									order: e.target.value,
								})
							}
							required
							min='1'
							max='5'
							className='w-full text-sm'
						/>
					</div>

					<div className='space-y-2'>
						<Label htmlFor='description' className='font-semibold'>
							Description
						</Label>
						<Textarea
							id='description'
							placeholder='Describe this skill level...'
							value={formData.description}
							onChange={(e) =>
								setFormData({
									...formData,
									description: e.target.value,
								})
							}
							required
							rows={3}
						/>
					</div>

					<DialogFooter>
						<Button
							type='button'
							variant='outline'
							onClick={() => onOpenChange(false)}
						>
							Cancel
						</Button>
						<Button
							type='submit'
							className='bg-orange-500 hover:bg-orange-600 text-white'
						>
							{level ? "Update" : "Create"} Level
						</Button>
					</DialogFooter>
				</form>
			</DialogContent>
		</Dialog>
	);
}
