import { useState, useEffect } from "react";
import { But<PERSON> } from "../ui/button";
import { Input } from "../ui/input";
import { Label } from "../ui/label";
import { <PERSON>alog, DialogContent, DialogHeader, DialogTitle } from "../ui/dialog";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "../ui/select";
import {
	createOptionReadnessdata,
	updateOptionReadnessdata,
	getAllsubSegmentReadnessData,
} from "../../API/api-endpoint";
import Swal from "sweetalert2";

export const OptionReadinessDialog = ({
	open,
	onClose,
	onSuccess,
	editingItem,
}) => {
	const [formData, setFormData] = useState({
		subsegment_id: "",
		points: "",
		option_text: "",
	});
	const [isLoading, setIsLoading] = useState(false);
	const [subSegmentList, setSubSegmentList] = useState([]);

	const fetchSubSegmentData = async () => {
		try {
			const response = await getAllsubSegmentReadnessData();
			setSubSegmentList(response || []);
		} catch (error) {
			console.error("Error fetching sub segment readness data:", error);
			setSubSegmentList([]);
		}
	};

	useEffect(() => {
		if (open) {
			fetchSubSegmentData();
		}
	}, [open]);

	useEffect(() => {
		if (open) {
			if (editingItem?.id) {
				const editData = {
					subsegment_id: editingItem.subsegment_id || "",
					points: editingItem.points || "",
					option_text: editingItem.option_text || "",
				};
				setFormData(editData);
			} else {
				const newData = {
					subsegment_id: "",
					points: "",
					option_text: "",
				};
				setFormData(newData);
			}
		}
	}, [open, editingItem]);

	const handleInputChange = (field, value) => {
		setFormData((prev) => ({
			...prev,
			[field]: value,
		}));
	};

	const handleSubmit = async (e) => {
		e.preventDefault();

		if (!formData.subsegment_id) {
			Swal.fire({
				title: "Error",
				text: "Sub segment is required",
				icon: "error",
				timer: 1800,
				showConfirmButton: false,
			});
			return;
		}

		if (!formData.points.trim()) {
			Swal.fire({
				title: "Error",
				text: "Points is required",
				icon: "error",
				timer: 1800,
				showConfirmButton: false,
			});
			return;
		}

		if (!formData.option_text.trim()) {
			Swal.fire({
				title: "Error",
				text: "Option text is required",
				icon: "error",
				timer: 1800,
				showConfirmButton: false,
			});
			return;
		}

		try {
			setIsLoading(true);

			const apiData = {
				subsegment_id: formData.subsegment_id,
				points: formData.points.trim(),
				option_text: formData.option_text.trim(),
			};

			let response;
			if (editingItem?.id) {
				apiData.id = editingItem.id;
				response = await updateOptionReadnessdata(apiData);
			} else {
				response = await createOptionReadnessdata(apiData);
			}

			if (response?.status) {
				Swal.fire({
					title: "Success",
					text:
						response.message ||
						`Option readiness ${
							editingItem?.id ? "updated" : "created"
						} successfully`,
					icon: "success",
					timer: 2000,
					showConfirmButton: false,
				});
				onSuccess();
			} else {
				Swal.fire({
					title: "Error",
					text: response?.message || "Failed to save option readness",
					icon: "error",
					timer: 3000,
					showConfirmButton: false,
				});
			}
		} catch (error) {
			console.error("Error saving option readness:", error);
			Swal.fire({
				title: "Error",
				text: "An error occurred while saving the option readness",
				icon: "error",
				timer: 3000,
				showConfirmButton: false,
			});
		} finally {
			setIsLoading(false);
		}
	};

	return (
		<Dialog open={open} onOpenChange={onClose}>
			<DialogContent className='sm:max-w-md bg-white'>
				<DialogHeader>
					<DialogTitle className='text-lg font-semibold text-gray-900'>
						{editingItem?.id
							? "Edit Option Readness"
							: "Create Option Readness"}
					</DialogTitle>
				</DialogHeader>

				<form onSubmit={handleSubmit} className='space-y-4'>
					<div className='grid gap-4'>
						<div className='space-y-2'>
							<Label
								htmlFor='subsegment_id'
								className='text-sm font-semibold'
							>
								Sub Segment{" "}
								<span className='text-red-500'>*</span>
							</Label>
							<Select
								value={formData.subsegment_id}
								onValueChange={(value) =>
									handleInputChange("subsegment_id", value)
								}
								disabled={isLoading}
							>
								<SelectTrigger className='w-full text-sm'>
									<SelectValue placeholder='Select sub segment' />
								</SelectTrigger>
								<SelectContent className='bg-white'>
									{subSegmentList.map((subSegment) => (
										<SelectItem
											key={subSegment.id}
											value={subSegment.id.toString()}
										>
											{subSegment.name}
										</SelectItem>
									))}
								</SelectContent>
							</Select>
						</div>

						<div className='space-y-2'>
							<Label
								htmlFor='points'
								className='text-sm font-semibold'
							>
								Points
							</Label>
							<Input
								id='points'
								type='text'
								className='w-full text-sm'
								value={formData.points}
								onChange={(e) =>
									handleInputChange("points", e.target.value)
								}
								placeholder='Enter points'
								disabled={isLoading}
								required
							/>
						</div>

						<div className='space-y-2'>
							<Label
								htmlFor='option_text'
								className='text-sm font-semibold'
							>
								Option Text{" "}
							</Label>
							<Input
								id='option_text'
								type='text'
								className='w-full text-sm'
								value={formData.option_text}
								onChange={(e) =>
									handleInputChange(
										"option_text",
										e.target.value
									)
								}
								placeholder='Enter option text'
								disabled={isLoading}
								required
							/>
						</div>
					</div>

					<div className='flex justify-end gap-3 pt-4'>
						<Button
							type='button'
							variant='outline'
							onClick={onClose}
							disabled={isLoading}
						>
							Cancel
						</Button>
						<Button
							type='submit'
							className='bg-orange-600 hover:bg-orange-700 text-white'
							disabled={isLoading}
						>
							{isLoading
								? "Saving..."
								: editingItem?.id
								? "Update Option Readness"
								: "Create Option Readness"}
						</Button>
					</div>
				</form>
			</DialogContent>
		</Dialog>
	);
};
