import React from 'react'
import RunningImage from "../../Images/Running.png";
import CyclingImage from "../../Images/Cycling.png";
import SwimmingImage from "../../Images/Swimming.png";
import Slider from 'react-slick';
import { Box, Typography } from '@mui/material';
import 'slick-carousel/slick/slick.css';
import 'slick-carousel/slick/slick-theme.css';



const reviewLayoutImage = [{ image: RunningImage, description: "Running Enhances cardiovascular health, burns calories, ANd BoostS MeNtAL WeLl-BeinG THROUgh thE RELEASe of endorPhiNs." },

{ image: CyclingImage, description: "Cycling improves strength, balance and coordination. It may also help to prevent falls and fractures." },
{ image: SwimmingImage, description: "builds endurance, muscle strength and cardiovascular fitness. helps you maintain a healthy weight, healthy heart and lungs." },
]
const settings = {
    color: "black",
    dots: true,
    slidesToShow: 1,  // Adjust this based on the number of cards you want to show
    slidesToScroll: 1,  // Adjust this based on the number of cards you want to scroll
    infinite: true,
    arrows: false,
    autoplay: true,
    autoplaySpeed: 3000,
    // centerMode: true,
    //  centerPadding: '0',
    responsive: [
        {
            breakpoint: 1025,
            settings: {
                slidesToShow: 1,
                slidesToScroll: 1,
            },
        },
        {
            breakpoint: 800,
            settings: {
                slidesToShow: 1,
                slidesToScroll: 1,
            },
        },
        {
            breakpoint: 600,
            settings: {
                slidesToShow: 1,
                slidesToScroll: 1,
            },
        },
        {
            breakpoint: 450,
            settings: {
                slidesToShow: 1,
                slidesToScroll: 1,
            },
        },
    ],

};
const LayoutImage = () => {
    return (
        <div style={{padding:"20px"}}>
        <Slider {...settings}>
            {reviewLayoutImage &&
                reviewLayoutImage.map((data, i) => {
                    return (
                        <Box p={1}>
                            <img src={data?.image} alt="Logo" style={{ width:"100%", marginBottom:"40px", marginTop:"40px"}} />
                            <Typography  variant="h6">{data?.description}</Typography>
                        </Box>
                    )
                })}
        </Slider>
        </div>
    )
}

export default LayoutImage
