import { <PERSON><PERSON>, <PERSON><PERSON> } from "antd";
import {
  FormLabel,
  Grid,
  MenuItem,
  OutlinedInput,
  TextField
} from "@mui/material";
import DatePicker from "react-datepicker";
import moment from "moment";
import RichTextEditor from "../../../../src/components/EditorFile";
import SlickCarousel from "../.././SlickCarousel";
import { Editor } from 'react-draft-wysiwyg';
import draftToHtml from 'draftjs-to-html';
import { convertToRaw } from 'draft-js';

const createChallengeLevel = ({handleCancel, isLoading, isModalOpen, setLevel, initialLevelData, level, handleSubmit, handleInputChange}) => {
    return(
        <Modal
            width={600}
            open={isModalOpen}
            onCancel={() => {
              handleCancel();
              setLevel(initialLevelData);
            }}
            footer={null}
          >
            <div className="headingCont paddingBot">
              <span className="heading">{level?.id ? "Edit " : "Create"}</span>
              <span className="orange heading"> Challenge Level</span>
              <p className="grey">Set up a new level for fitness challenge</p>
            </div>
            <div className="parentCont">
              <form className="form1" onSubmit={handleSubmit}>
                <Grid container spacing={1}>
                  <Grid container spacing={3} className="marbot">
                    <Grid item xs={12} sm={12}>
                      <FormLabel>
                        Name<span className="text-[red]">*</span>
                      </FormLabel>
                      <TextField
                        fullWidth
                        placeholder="Level Name"
                        size="small"
                        type="text"
                        name="name"
                        value={level?.name}
                        onChange={handleInputChange}
                      />
                    </Grid>
                  </Grid>
                  <Grid container spacing={3} className="marbot">
                    <Grid item xs={12} sm={5.5} className="spcl">
                      <Button className="btn" htmlType="submit" loading={isLoading} style={{
                            boxShadow: "0px 4px 10px rgba(0, 0, 0, 0.25)",
                        }}>
                      <span style={{ color: '#fff' }}>Submit</span>
                      </Button>
                    </Grid>
                  </Grid>
                </Grid>
              </form>
              <div className="slick-container">
                <SlickCarousel />
              </div>
            </div>
          </Modal>
    )
}

export default createChallengeLevel;