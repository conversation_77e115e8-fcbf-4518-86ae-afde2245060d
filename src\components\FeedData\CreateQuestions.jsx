import {
    <PERSON><PERSON>abel,
    <PERSON>rid,
    MenuI<PERSON>,
    OutlinedInput,
    TextField,
  } from "@mui/material";
  import { <PERSON><PERSON>, Modal } from "antd";
  import React, { useEffect, useState } from "react";
  import {
    createQuestiondata,
    getAllSubSugmentData,
    updateQuestiondata,
  } from "../../API/api-endpoint";
  import { useFormik } from "formik";
  import Swal from "sweetalert2";
  import SlickCarousel from "../../pages/SlickCarousel";
  const CreateQuestions = ({
    fetchReport,
    setShowAssesmentModal,
    showAssesmentModal,
    editData,
    setEditData,
  }) => {
    const [sugmentList, setSugmentList] = useState([]);
    console.log("editData", sugmentList);
    const formik = useFormik({
      initialValues: {
        question: "",
        subsegment_id: "",
        user_response_type: "",
      },
      validate: (values) => {
        const errors = {};
        if (!values.subsegment_id) {
          errors.subsegment_id = "Sub Segment name is required";
        }
        if (!values.question) {
          errors.question = "Question is required";
        }
        if (!values.user_response_type) {
          errors.user_response_type = "User response type is required";
        }
        return errors;
      },
      // validationSchema: {},
      onSubmit: (values, { resetForm }) => {
        handleSubmitAssesmentForm(values, resetForm);
      },
    });
    console.log("formik", formik?.values, formik.touched, formik.errors);
    const sugmentData = async () => {
      const response = await getAllSubSugmentData();
      console.log("response", response);
      setSugmentList(response);
    };
    useEffect(() => {
      sugmentData();
    }, []);
  
    const handleSubmitAssesmentForm = async (data, resetForm) => {
      let response = "";
      if (editData?.id) {
        response = await updateQuestiondata(data);
      } else {
        response = await createQuestiondata(data);
      }
      if (response?.status) {
        Swal.fire({
          title: "Success",
          text: response.message,
          icon: "success",
        });
        setEditData({});
        setShowAssesmentModal(false);
        fetchReport();
        resetForm();
        formik?.setValues({ level: "", question: "" });
      } else {
        Swal.fire({
          title: "Error",
          text: response.message,
          icon: "error",
        });
      }
      console.log("response", response);
    };
    useEffect(() => {
      if (editData?.id) {
        const { srID, ...data } = editData;
        console.log("data", data);
  
        formik?.setValues(data);
  
        formik?.setFieldValue("comments", data?.comments ? data?.comments : "");
      } else {
        setEditData({});
      }
    }, [editData?.id]);
  
    return (
      <Modal
        width={1200}
        open={showAssesmentModal}
        onCancel={() => {
          setShowAssesmentModal(false);
          setEditData({});
          formik.resetForm();
          formik?.setValues({ level: "", question: "" });
          formik?.setValues({ question: "" });
        }}
        footer={
          <div></div>
          //   loading={isLoading}
        }
      >
        <div className="headingCont">
          <span className="heading">{editData?.id ? "Edit " : "Create"}</span>{" "}
          <span className="orange heading">Questions</span>
        </div>
        {/* <h1>{editData ? editData.challengeId : values.challengeId}</h1> */}
        <div className="parentCont">
          <form className="form1" onSubmit={formik.handleSubmit}>
            <Grid container spacing={2}>
              <Grid item xs={12} sm={11}>
                <FormLabel>Sub Segment Name<span className="text-[red]">*</span></FormLabel>
  
                <TextField
                  fullWidth
                  size="small"
                  select
                  SelectProps={{
                    MenuProps: {
                      PaperProps: {
                        style: {
                           scrollbarColor:"#E67E22 white",
                           scrollbarWidth:"thin"
                        },
                      },
                    },
                  }}
                  name="subsegment_id"
                  value={
                    formik?.values?.subsegment_id
                      ? formik?.values?.subsegment_id
                      : ""
                  }
                  onChange={formik.handleChange}
                  error={
                    formik.touched.subsegment_id && formik.errors.subsegment_id
                  }
                  helperText={
                    formik.touched.subsegment_id && formik.errors.subsegment_id
                  }
                  id="form-layouts-separator-select"
                  labelId="form-layouts-separator-select-label"
                  input={<OutlinedInput id="select-multiple-language" />}
                >
                  <MenuItem value={""} disabled>
                    Select Name
                  </MenuItem>
                  {sugmentList?.map((value, index) => {
                    return <MenuItem value={value?.id}>{value?.name}</MenuItem>;
                  })}
                </TextField>
              </Grid>
              <Grid item xs={12} sm={11}>
                <FormLabel>Question<span className="text-[red]">*</span></FormLabel>
  
                <TextField
                  fullWidth
                  placeholder="question"
                  size="small"
                  type="text"
                  name="question"
                  value={formik?.values?.question}
                  onChange={formik.handleChange}
                  error={formik.touched.question && formik.errors.question}
                  helperText={formik.touched.question && formik.errors.question}
                />
              </Grid>
              <Grid item xs={12} sm={11}>
                <FormLabel>User Response Type<span className="text-[red]">*</span></FormLabel>
  
                <TextField
                  fullWidth
                  size="small"
                  select
                  name="user_response_type"
                  value={
                    formik?.values?.user_response_type
                      ? formik?.values?.user_response_type
                      : ""
                  }
                  onChange={formik.handleChange}
                  error={
                    formik.touched.user_response_type &&
                    formik.errors.user_response_type
                  }
                  helperText={
                    formik.touched.user_response_type &&
                    formik.errors.user_response_type
                  }
                  id="form-layouts-separator-select"
                  labelId="form-layouts-separator-select-label"
                  input={<OutlinedInput id="select-multiple-language" />}
                >
                  <MenuItem value={""} disabled>
                    Select Type
                  </MenuItem>
                  <MenuItem value="radio">Radio</MenuItem>
                  <MenuItem value="input">Input</MenuItem>
                </TextField>
              </Grid>
              <Grid item xs={12} sm={6}>
                <Button
                  className="btn"
                  key="submit"
                  type="primary"
                  onClick={() => formik.handleSubmit()}
                >
                  Submit
                </Button>
              </Grid>
            </Grid>
          </form>
          <div className="slick-container">
            <SlickCarousel />
          </div>
        </div>
      </Modal>
    );
  };
  export default CreateQuestions;
  