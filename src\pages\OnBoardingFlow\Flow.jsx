import React, { useEffect, useState } from "react";
import { useTheme } from "@mui/material/styles";
import Typography from "@mui/material/Typography";
import Box from "@mui/material/Box";
import MobileStepper from "@mui/material/MobileStepper";
import Button from "@mui/material/Button";
import KeyboardArrowLeft from "@mui/icons-material/KeyboardArrowLeft";
import KeyboardArrowRight from "@mui/icons-material/KeyboardArrowRight";
import DirectionsRunIcon from "@mui/icons-material/DirectionsRun";
import Select from 'react-select';

import "./Flow.css";
import Swal from "sweetalert2";
import {
  createUserSubscription,
  getAllCountry,
  getAllGoals,
  getSubscriptionPlanBYProgramID,
  getAllYoskaActivities,
  getAllyoskaProgramByActivityID,
  ValidatePromoCode,
  getAllState,
  getAllCities,
  getFechAlGoals,
  getDayoptionValues,
  createPersonolize,
  URL,
  getPaymentCredData,
  updateUserSubscription,
  updatePrfile,
  getAllyoskaProgramFeateryID,
  getsubspackageData,
  getdiscountcouponData,
  getSystemConfigrationData,
  registerCouponCode,
  generateOrderForPayment,
  getPaymentCredDataForPayment
} from "../../API/api-endpoint";
import { styled } from "@mui/material/styles";
import { useNavigate, useLocation } from "react-router-dom";
import MuiCard from "@mui/material/Card";
import Background from "../../Images/Background.png";
import { Autocomplete, FormControlLabel, Grid, Radio, RadioGroup, TextField } from "@mui/material";
import { showError } from "../../components/Messages";
import { locale } from "moment";
import SlickCarousel from "../SlickCarousel";
import { calculateActualpriceAfterGST, validateDOB } from "../../utils/Resubale";
import { LaptopChromebook } from "@mui/icons-material";

// ** Styled Components
const Card = styled(MuiCard)(({ theme }) => ({
  [theme.breakpoints.up("sm")]: { width: "28rem" },
}));

const Flow = () => {
  const navigate = useNavigate();
  const [plan, setPlan] = useState("100");
  const [subscriptionName, setSubscriptioName] = useState("");
  const [promocodeList, setPromocodeList] = useState("");
  const [dobError, setDobError] = useState(false);
  console.log("dobError", dobError);
  const [amount, setAmount] = useState("100");
  const [duration, setduration] = useState("100");
  const [activity, setActivity] = useState("");
  const [selectActivity, setSelectActivity] = useState("");
  const [selectProgram, setselectProgram] = useState("");
  const [getGSTPer, setGSTper] = useState();
  const [selectProgramFeater, setselectProgramFeature] = useState();
  const [price, setPrice] = useState("");
  const [goalsList, setGoalsList] = useState([]);
  const [yoskaActivitieList, setYoskaActivitieList] = useState([]);
  const [yoskaProgramList, setYoskaProgramList] = useState([]);
  const [SubscriptionPlanList, setSubscriptionPlanList] = useState([]);
  console.log("SubscriptionPlanList", SubscriptionPlanList);
  const [CountryList, setCountryList] = useState([]);
  const [stateList, setStateList] = useState([]);
  const [cityList, setCityList] = useState([]);
  const [fetchGoals, setFechGoals] = useState([]);
  const [paymentcreds, setPaymentCred] = useState('')
  const [discountPrice, setDiscountPrice] = useState(0);
  const [discountAmountAfter, setAfterDicount] = useState(0);

  const [afteraddGSTAmount, setAfterAddGST] = useState(0);

  console.log("discountAmountAfter", afteraddGSTAmount, discountAmountAfter);
  const [getDaysoptionList, setfetchDaysoptionList] = useState([]);
  const [activityBadge, setActivityBadg] = React.useState();
  const [selectedProgram, setSelectedProgram] = useState(null);
  const [couponCode, setCouponCode] = useState(null);

  const [formValue, setFormValue] = useState({
    name: "",
    address: "",
    email: "",
    phoneNumber: "",
    country: undefined,
    state: undefined,
    city: undefined,
    pincode: "",
    company_name: "",
    promocode: "",
  });
  const [formGoalValue, setFormGoalValue] = useState({
    process: "M",
    goal: "",
    duration: 0,
    daysinweek: "",
    daysoption: "",
    program_id: activity?.id,
  });
  const [isFitness, setIsFitness] = useState(null);
  const [plannedDuration, setPlannedDuration] = useState(null);
  const [selectedDuration, setSelectedDuration] = useState(null)

  useEffect(() => {
    if (activity?.id) {
      const { activity_name } = activity;
      const isFitnessActivity = activity_name.toLowerCase() === 'fitness' ? true : false;
      setIsFitness(isFitnessActivity);
      fetchDGoals();
      setFormGoalValue({ ...formGoalValue, program_id: activity?.id });
    }
  }, [activity.id]);

  useEffect(() => {
    setPlannedDuration([30, 45, 60]);
  }, [isFitness]);

  const FullName = localStorage.getItem("fullname");
  const email = localStorage.getItem("email");
  const phoneNumber = localStorage.getItem("phonenumber");

  useEffect(() => {
    if (selectProgram) {

      getSubscriptionPlan();
    }

  }, [selectProgram]);
  useEffect(() => {
    if (subscriptionName) {
      getAllProgramsData(subscriptionName)
    }
  }, [subscriptionName])
  useEffect(() => {
    getdiscountcouponData()
    getGSTPersentage()
  }, []);


  const getGSTPersentage = async () => {
    const response = await getSystemConfigrationData()
    let fileterTax = response?.filter(ele => ele?.type == "tax")
    console.log("fileterTax", fileterTax[0]);
    setGSTper(parseInt(fileterTax[0]?.value))
  }
  const location = useLocation();

  useEffect(() => {
    const handlePopState = (event) => {
      localStorage.clear()
      navigate("/")
    };

    // Push a new state onto the history stack
    window.history.pushState({ page: location.pathname }, '', location.pathname);

    window.addEventListener('popstate', handlePopState);

    return () => {
      window.removeEventListener('popstate', handlePopState);
    };
  }, [location]);

  const normalSubcriptionMsg = `The training plan will be delivered through Yoska's online platform
              (web & mobile app).`;
  const pumaSubscriptionMsg = `The training plan will be delivered through Yoska's online platform
              (web & mobile app). Please proceed through the next steps and at the
              payment screen enter your promocode to avail 100% discount.`;

  const getAllProgramsData = async (subscriptionName) => {
    const response = await getdiscountcouponData()
    setDiscountPrice(0)
    setAfterDicount(amount)
    let gstAmount = calculateActualpriceAfterGST(amount, getGSTPer)
    setAfterAddGST(gstAmount)
    setFormValue({ ...formValue, promocode: "" })
    const filterCoupan = response?.filter((data) => data.subscriptionpackage?.name == subscriptionName)
    if (filterCoupan?.length > 0) {
      let modifiData = filterCoupan?.map((ele) => {
        return {
          label: ele?.coupon,
          value: ele?.coupon
        };
      });
      setPromocodeList(modifiData);
    } else {
      setPromocodeList([]);
    }

  };
  useEffect(() => {
    setFormValue({
      ...formValue,
      name: FullName,
      phoneNumber: formValue.phoneNumber ? phoneNumber : phoneNumber,
      email: email,
    });
    if (formGoalValue?.daysinweek) {
      fetchDaysOptuon();
    }
  }, [formGoalValue?.daysinweek]);
  const fetchDaysOptuon = async () => {
    const response = await getDayoptionValues(
      formGoalValue?.daysinweek,
      activity?.id
    );
    if (response) {
      setfetchDaysoptionList(response);
    }
  };
  const handleCountryChange = async (event, newValue) => {
    const countryId = newValue;
    setFormValue({
      ...formValue,
      country: countryId?.id, // Update the selected country in formValue
    });
    // Fetch states based on the selected country ID
    const response = await getAllState(countryId?.id);
    setStateList(response);
  };
  const handleStateChange = async (event, newValue) => {
    const stateID = newValue;
    setFormValue({
      ...formValue,
      state: stateID?.id, // Update the selected country in formValue
    });
    // Fetch states based on the selected country ID
    const response = await getAllCities(formValue.country, stateID?.id);
    setCityList(response);
  };
  //===========================get data from apis start================
  const fetchDGoals = async () => {
    let response = await getFechAlGoals(activity?.id);
    setFechGoals(response);
  };
  const getGoals = async () => {
    let response = await getAllGoals();
    setGoalsList(response);
  };

  const getYoskaActivities = async () => {
    let response = await getAllYoskaActivities();
    let showResponse = response?.filter((ele) => ele?.is_under_coach_profile == 1)
    console.log("showResponse", response, showResponse);
    setYoskaActivitieList(showResponse);
  };

  const getSubscriptionPlan = async () => {
    setSubscriptionPlanList([])
    let response = await getSubscriptionPlanBYProgramID(
      selectProgram["program_id"]
    ); //we have to send program_id here
    const sortedList = response.sort((a, b) => a.name.localeCompare(b.name));
    setSubscriptionPlanList(sortedList);
  };

  const getAllProgramByActivityID = async (setActiveStep) => {
    setYoskaProgramList([])
    setselectProgram("");
    let response = await getAllyoskaProgramByActivityID(activity["id"]);
    if (response?.length > 0) {
      const result = await getAllyoskaProgramFeateryID(activity["id"])
      if (result.status) {
        setselectProgramFeature(result?.data)

      }
      setActiveStep((prevActiveStep) => prevActiveStep + 1)
      setselectProgram(response[0]);
      setYoskaProgramList(response);
    } else {
      showError(response?.message)
    }

  };

  const getCountry = async () => {
    let response = await getAllCountry();
    setCountryList(response);
  };

  useEffect(() => {
    getGoals();
    getYoskaActivities();
    getCountry();
  }, []);

  //===========================get data from apis end==================
  const fetchReport = async () => {
    const response = await getPaymentCredDataForPayment()
    console.log("----- responsepayment -----", response.data.key);
    setPaymentCred(response.data.key);
  }
  useEffect(() => {
    fetchReport()
  }, [])
  const createUserSubscriptionplan = async (body) => {
    let userId = localStorage.getItem('userId')
    const newbody = JSON.parse(JSON.stringify(body));
    newbody.plan_id = plan;
    newbody.program = selectProgram?.program_id;
    newbody.price = discountAmountAfter == 0 ? discountAmountAfter.toString() : afteraddGSTAmount.toString();
    newbody.user_id = userId

    let result = await createUserSubscription(newbody);

    if (result.status) {
      let token = localStorage.getItem('userTokentoken')
      let token1 = localStorage.getItem('token')

      localStorage.setItem('token', token ? token : token1)
      localStorage.removeItem("userDetails")
      setTimeout(() => {
        localStorage.removeItem("userTokentoken")

      }, 1000);
      localStorage.removeItem("userSubsription")

      navigate("/strava");
    } else {
      Swal.fire({
        title: "Error",
        text: "SomeThing Went Wrong",
        icon: "error",
      });
    }
  };
  const createUserSubscriptionplanWithoutRazorpay = async () => {
    let data = {
      plan_id: plan,
      program: selectProgram?.program_id,
      price: discountAmountAfter == 0 ? discountAmountAfter.toString() : afteraddGSTAmount.toString(),
      razorpay_payment_id: "payment_without_razorpay"
    }
    console.log("without payment", data);
    if ((couponCode && couponCode.trim() !== "") && plan !== "100") {
      const response = await registerCouponCode(couponCode, plan);
      console.log(response);
    }
    let result = await createUserSubscription(data);

    if (result.status) {
      let token = localStorage.getItem('userTokentoken')
      let token1 = localStorage.getItem('token')

      localStorage.setItem('token', token ? token : token1)
      localStorage.removeItem("userDetails")
      localStorage.removeItem("userSubsription")
      setTimeout(() => {
        localStorage.removeItem("userTokentoken")

      }, 1000);
      navigate("/strava");
    } else {
      Swal.fire({
        title: "Error",
        text: "SomeThing Went Wrong",
        icon: "error",
      });
    }
  };
  const createUserSubscriptionplanWithoutPaymnet = async (body) => {
    let userId = localStorage.getItem('userId')

    let data = {
      plan_id: plan,
      program: selectProgram?.program_id,
      price: price,
      user_id: parseInt(userId, 10)
    }
    let result = await createUserSubscription(data, "id");
    if (result.status) {
      setActiveStep((prevActiveStep) => prevActiveStep + 1);
      localStorage.setItem("userSubsription", JSON.stringify(result?.userSubscription));
      setuserSubscription(result?.userSubscription)


    } else {
      Swal.fire({
        title: "Error",
        text: result?.error,
        icon: "error",
      });
    }
  };
  // ====================== razorPay ================================
  const options = {
    key: paymentcreds,
    amount: afteraddGSTAmount * 100, //  = INR 1
    name: "Yoska",
    description: "some description",
    image: "https://cdn.razorpay.com/logos/7K3b6d18wHwKzL_medium.png",
    handler: function (response) {
      createUserSubscriptionplan(response);
    },
    prefill: {
      name: FullName,
      contact: phoneNumber, //"+919876543210", // phoneNumber,
      email: email,
    },
    notes: {
      address: `${formValue.company_name} ${formValue.address} 
                ${formValue.city} ${formValue.pincode} 
                ${formValue.state} ${formValue.country}`,
    },
    theme: {
      color: "orange",
      hide_topbar: false,
    },
  };

  const openPayModal = async (options) => {
    if ((couponCode && couponCode.trim() !== "") && plan !== "100") {
      const response = await registerCouponCode(couponCode, plan);
      console.log(response);
    }
    const dataObj = {
      amount: options.amount,
      currency: 'INR', //default
      receipt: `receipt_${new Date().getTime()}`
    }
    const generateOrderForPurchase = await generateOrderForPayment(dataObj);
    const orderId = generateOrderForPurchase.data.id;
    const currency = generateOrderForPurchase.data.currency;
    const newOptionObj = Object.assign({}, options, {
      order_id: orderId,
      currency
    });
    var rzp1 = new window.Razorpay(newOptionObj);
    rzp1.open();
  };

  useEffect(() => {
    const script = document.createElement("script");
    script.src = "https://checkout.razorpay.com/v1/checkout.js";
    script.async = true;
    document.body.appendChild(script);
  }, []);
  // ====================== razorPay ================================
  const calculateActualPrice = (amount, discountPercentage) => {
    const discountAmount = (amount * discountPercentage) / 100;
    console.log("discountAmount", discountAmount, amount);
    return amount - discountAmount;
  };
  // useEffect(()=>{

  //   const actualPrice = calculateActualPrice(amount, discountPrice);
  //   console.log("actua",actualPrice,amount,discountPrice);
  // },[amount,discountPrice])
  const applyPromoCode = async () => {
    if (formValue.promocode.length == 0) {
      Swal.fire({
        title: "Error",

        text: "Please Enter the Promocode",
        icon: "error",
      });
    } else {
      const subscription_id = plan;
      let response = await ValidatePromoCode(formValue.promocode, subscription_id);
      if (response.status) {
        setCouponCode(formValue.promocode);
        Swal.fire({
          title: "Success",
          text: "Coupon Applied",
          icon: "success",
        });
        setDiscountPrice(parseInt(response?.coupon["discount"]));
        const actualPrice = calculateActualPrice(amount, parseInt(response?.coupon["discount"]));
        setAfterDicount(actualPrice)
        let gstAmount = calculateActualpriceAfterGST(actualPrice, getGSTPer)
        setAfterAddGST(gstAmount)
      } else {
        setCouponCode(null);
        Swal.fire({
          title: "Error",
          text: response,
          icon: "error",
        });
      }
    }
  };

  // ================================== sttper form ==================================

  const renderWelcomeScreen = () => {
    return (
      <div className="text-base md:text-lg lg:text-xl xl:text-2xl p-0 pr-9">
        <p>
          Welcome to the world of fitness, where we embark on a healthier and
          stronger you!
        </p>
        <br />
        <p>
          Whether you are a seasoned athlete or just starting out, I am here to
          guide you every step of the way.
        </p>
      </div>
    );
  };

  const renderLetFindOut = () => {
    return (
      <div className="text-base md:text-lg lg:text-xl xl:text-2xl p-0 pr-9">
        <p>
          A perfect activity that suits your goals, choose from the following?
        </p>
      </div>
    )
  }


  const renderSelectActivityFeature = () => {
    return (

      <div
        style={{
          display: "flex",
          flexDirection: "column",
          gap: "20px",
          width: "100%",
          paddingBottom: "20px",
          background: "#b0c4de",
          color: "white",
          height: "60vh",
          overflow: "scroll"
        }}
      >
        <div className="flex justify-center">
          <img src="/images/feature.png" />
        </div>
        <Grid container spacing={2}>

          {selectProgramFeater?.program_features?.map((item, index) => (
            <Grid item xs={12} sm={3} md={3} lg={3}>
              <Box className="text-center flex flex-col items-center"
                key={index}
                onClick={() => setSelectActivity(item["workout-goal-id"])}
              >
                <img src={`${URL}/static/public/userimages/${item?.image}`} width="100" />
                <p
                  className="text-center w-3/6 text-base sm:text-sm md:text-base lg:text-xl"
                >
                  {item?.text}
                </p>
              </Box>
            </Grid>

          ))}
          <Grid item xs={12} sm={12} md={12} lg={12} style={{ fontSize: "14px", padding: "20px" }}>
            <ul>
              {selectProgramFeater?.OverallPoints?.map((item) => {
                return (
                  <li className="text-justify mb-2 text-base sm:text-sm md:text-base lg:text-xl">
                    {item?.point} &nbsp;
                  </li>
                )
              })}
            </ul>
          </Grid>
        </Grid>
      </div>
    );
  };

  const renderActivity = () => {
    return (
      <div
        style={{
          display: "flex",
          flexDirection: "column",
          gap: "20px",
          width: "100%",
          paddingBottom: "20px",
        }}
      >
        {yoskaActivitieList?.map((item, index) => {
          return (
            <Box
              sx={{
                display: "flex",
                alignItems: "center",
                border:
                  activity["id"] === item["id"]
                    ? "solid 1px orange"
                    : "solid 1px lightgray",
                borderRadius: "10px",
                p: 1,
                cursor: "pointer",
              }}
              key={index}
              onClick={() => {
                setActivity(item)
                setselectProgram("")
                setPrice("")
                setSelectedProgram(item)
              }}
            >
              <img
                src={`${URL}/static/public/assets/${item["badge"]}`}
                alt="..."
                className="w-10 h-10 rounded-full border-2" />
              <p
                style={{
                  margin: "0",
                  paddingLeft: "10px",
                  color: activity["id"] === item["id"] ? "orange" : "",
                }}
                className="text-base sm:text-sm md:text-base lg:text-xl"
              >
                {item["activity_name"]}
              </p>
            </Box>
          )
        })}

      </div>
    );
  };

  const renderRunningLable = () => {
    return (
      <Box
        sx={{
          width: "100%",
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
        }}
      >
        <img
          src={`${URL}/static/public/assets/${activityBadge}`}
          alt="..."
          className="w-10 h-10 rounded-full border-2" />
      </Box>
    );
  };

  const renderRunningDiscription = () => {
    return (
      <div>
        <ul>
          {activity["description"] &&
            <li className="text-base sm:text-sm md:text-base lg:text-xl">{activity["description"]}</li>
          }
          {(
            <li className="text-base sm:text-sm md:text-base lg:text-xl">
              {pumaSubscriptionMsg}
            </li>
          )}
        </ul>
      </div>
    );
  };

  const renderRunningPricing = () => {
    return (
      <div
        style={{
          display: "flex",
          flexDirection: "column",
          gap: "10px",
          width: "100%",
          paddingBottom: "20px",
        }}
      >
        {SubscriptionPlanList.map((item, index) => (
          <Box
            key={index}
            className="cursor-pointer"
            sx={{
              display: "flex",
              alignItems: "center",
              border: plan === item.id ? "solid 1px orange" : "solid 2px gray",
              borderBottom: plan !== item.id && "solid 2px gray",
              flexDirection: "column",
              borderRadius: "10px",
              p: 1,
            }}
            onClick={() => {
              setPrice(item.price);
              setAmount(`${item.price}`);
              setduration(item?.duration);
              setPlan(item.id);
              setSubscriptioName(item.name)
            }}
          >
            <p
              style={{
                margin: "0",
                color: plan === item.id ? "orange" : "",
              }}
              className="text-base sm:text-sm md:text-base lg:text-xl"
            >
              {item.name}
            </p>
            <p
              style={{
                margin: "0",
                color: plan === item.id ? "orange" : "",
              }}
              className="text-base sm:text-sm md:text-base lg:text-xl"
            >
              ₹{item.price}/{item.billing_cycle}
            </p>
          </Box>
        ))}
      </div>
    );
  };
  const renderformTitle = () => {
    return (
      <Box
        sx={{
          width: "100%",
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
        }}
      >
        {activity?.icon}
        <DirectionsRunIcon className="text-4xl rounded-full border-2" />
        <p style={{ margin: "0", paddingLeft: "10px" }}>
          {activity["yoska-activity-name"]}
        </p>
      </Box>
    );
  };

  const renderDescriptionForm = () => {
    return (
      <div style={{ border: "1px solid hsl(0deg 0% 50% / 50%)", borderRadius: "12px", padding: "10px" }}>
        <ul>
          <div
            style={{
              borderRadius: "10px",
              width: "100%",
              display: "flex",
              flexDirection: "column",
              justifyContent: "center",
              alignItems: "center",
              gap: "10px",
            }}
            className="flow-running-form"
          >

            <div className="flow-flex-input">
              <label style={{ width: "20vw" }} className="text-base sm:text-base md:text-lg lg:text-xl">Goals:</label>
              <select
                className="running-step-input text-base sm:text-base md:text-lg lg:text-xl"
                name="Country"
                id="country"
                value={formGoalValue.goal}
                onChange={(e) =>
                  setFormGoalValue({ ...formGoalValue, goal: e.target.value })
                }
              ><option value="" disabled>
                  Select Goals
                </option>
                {fetchGoals?.goals?.map((ele, index) => (
                  <option key={index} value={ele?.goal}>
                    {ele?.goal}
                  </option>
                ))}
              </select>
            </div>



            <div className="flow-flex-input">
              <label style={{ width: "20vw" }} className="text-base sm:text-base md:text-lg lg:text-xl">Days in week:</label>
              <select
                className="running-step-input text-base sm:text-base md:text-lg lg:text-xl"
                name="Country"
                id="country"
                value={formGoalValue.daysinweek}
                onChange={(e) =>
                  setFormGoalValue({
                    ...formGoalValue,
                    daysinweek: e.target.value,
                  })
                }
              >
                <option value="" disabled>
                  Select Days
                </option>
                {fetchGoals?.availibledaysoption?.map((ele, index) => (
                  <option key={index} value={ele?.rundays}>
                    {ele?.rundays}
                  </option>
                ))}
              </select>
            </div>
            {getDaysoptionList?.length > 0 &&
              <div className="flex w-[100%] " style={{ scrollbarColor: "#E67E22 lightGray ", scrollbarWidth: "thin", overflowX: "scroll", height: "50vh" }} >
                {getDaysoptionList?.map((ele) => {
                  return (
                    <div
                      style={{
                        height: "100vh",
                        minWidth: "200px",
                        border: "1px solid gray",
                        margin: "10px",
                        borderRadius: "7px",
                        padding: "10px 0px 10px 10px",
                        boxShadow:
                          "rgba(0, 0, 0, 0.16) 0px 10px 36px 0px, rgba(0, 0, 0, 0.06) 0px 0px 0px 1px",
                        // overflow: "auto"
                      }}
                    >
                      <div>
                        <div className="flex justify-between items-center">
                          <div className="text-base sm:text-base md:text-lg lg:text-xl">Monday </div>
                          <RadioGroup
                            aria-labelledby="demo-radio-buttons-group-label"
                            defaultValue="female"
                            name="radio-buttons-group"
                            value={formGoalValue.daysoption}

                            onChange={(e) =>
                              setFormGoalValue({
                                ...formGoalValue,
                                daysoption: e.target.value,
                              })
                            }
                          >
                            <FormControlLabel
                              value={ele?.options}
                              control={<Radio />}
                            />
                          </RadioGroup>
                        </div>
                        <div className="text-base sm:text-base md:text-lg lg:text-xl">{ele?.mondayworkout}</div>
                      </div>
                      &nbsp;
                      <div className=" mr-4">
                        <div className="text-base sm:text-base md:text-lg lg:text-xl">Tuesday</div>
                        <div className="text-base sm:text-base md:text-lg lg:text-xl">{ele?.tuesdayworkout}</div>
                      </div>
                      &nbsp;
                      <div className=" mr-4">
                        <div className="text-base sm:text-base md:text-lg lg:text-xl">Wednesday</div>
                        <div className="text-base sm:text-base md:text-lg lg:text-xl">{ele?.wednesdayworkout}</div>
                      </div>
                      &nbsp;
                      <div className=" mr-4">
                        <div className="text-base sm:text-base md:text-lg lg:text-xl">Thursday</div>
                        <div className="text-base sm:text-base md:text-lg lg:text-xl">{ele?.thursdayworkout}</div>
                      </div>
                      &nbsp;
                      <div className=" mr-4">
                        <div className="text-base sm:text-base md:text-lg lg:text-xl">Friday</div>
                        <div className="text-base sm:text-base md:text-lg lg:text-xl">{ele?.fridayworkout}</div>
                      </div>
                      &nbsp;
                      <div className=" mr-4">
                        <div className="text-base sm:text-base md:text-lg lg:text-xl">Saturday</div>
                        <div className="text-base sm:text-base md:text-lg lg:text-xl">{ele?.saturdayworkout}</div>
                      </div>
                      &nbsp;
                      <div className=" mr-4">
                        <div className="text-base sm:text-base md:text-lg lg:text-xl">Sunday</div>
                        <div className="text-base sm:text-base md:text-lg lg:text-xl">{ele?.sundayworkout}</div>
                      </div>
                    </div>
                  );
                })}
              </div>
            }

            {(isFitness && formGoalValue.daysoption !== "") && (<div className="flow-flex-input">
              <label style={{ width: "20vw" }} className="text-base sm:text-base md:text-lg lg:text-xl">Duration:</label>
              <select
                className="running-step-input text-base sm:text-base md:text-lg lg:text-xl"
                name="Duration"
                id="duration-fitness"
                value={formGoalValue.duration}
                onChange={(e) => {
                  setSelectedDuration(e.target.value);
                  setFormGoalValue({ ...formGoalValue, duration: e.target.value })
                }}
              ><option value={0} disabled>
                  Select Workout Duration
                </option>
                {plannedDuration?.map((ele, index) => (
                  <option key={index} value={ele}>
                    {ele} minutes
                  </option>
                ))}
              </select>
            </div>)}
          </div>
        </ul>
      </div>
    );
  };

  const today = new Date();
  const twelveYearsAgo = new Date(today.getFullYear() - 14, today.getMonth(), today.getDate());

  const twelveYearsAgoString = twelveYearsAgo.toISOString().split("T")[0];
  useEffect(() => {
    if (formValue?.dob) {
      const dobValidationResult = validateDOB(formValue?.dob);
      if (dobValidationResult === 'Date of birth must be at least 14 years ago.') {
        setDobError(true)
      }
      else {
        setDobError(false)
      }
    }
  }, [formValue?.dob, dobError]);
  const renderForm = () => {
    return (
      <>
        <div className="w-full pb-5">
          <div className="w-11/12 p-5 flex flex-col gap-2.5">
            <div className="border-b border-gray-300 p-2.5 flex justify-between">
              <p className="font-semibold text-base sm:text-sm md:text-base lg:text-xl">Program:</p>
              <p className="font-medium text-base sm:text-sm md:text-base lg:text-xl">{selectProgram["program_name"]}</p>
            </div>
            {/* -------------------------------------------------------------------- */}
            <div className="border-b border-gray-300 p-2.5 flex justify-between">
              <p className="font-semibold text-base sm:text-sm md:text-base lg:text-xl">Monthly Price:</p>
              <p className="font-medium text-base sm:text-sm md:text-base lg:text-xl">₹{(amount / duration).toFixed(2)}</p>
            </div>
            {/* -------------------------------------------------------------------- */}
            <div className="flex p-2.5 justify-between">
              <p className="font-semibold text-base sm:text-sm md:text-base lg:text-xl">Discount</p>
              <p className="font-medium text-base sm:text-sm md:text-base lg:text-xl">{discountPrice}%</p>
            </div>
            {/* -------------------------------------------------------------------- */}
            {/* Sub Total if condition is true */}
            {
              parseInt(discountAmountAfter) !== 0 && (
                <div className="flex p-2.5 justify-between">
                  <p className="font-semibold text-base sm:text-sm md:text-base lg:text-xl">Sub Total</p>
                  <p className="font-medium text-base sm:text-sm md:text-base lg:text-xl">₹{discountAmountAfter}</p>
                </div>
              )
            }
            {/* -------------------------------------------------------------------- */}
            {/* GST if condition is true  */}
            {
              parseInt(discountAmountAfter) != 0 && (
                <div className="flex p-2.5 justify-between">
                  <p className="font-semibold text-base sm:text-sm md:text-base lg:text-xl">GST</p>
                  <p className="font-medium text-base sm:text-sm md:text-base lg:text-xl">{getGSTPer}%</p>
                </div>
              )
            }
            {/* -------------------------------------------------------------------- */}
            <div className="flex p-2.5 justify-between">
              <p className="font-semibold text-base sm:text-sm md:text-base lg:text-xl">Total Price</p>
              <p className="font-medium text-base sm:text-sm md:text-base lg:text-xl">₹{afteraddGSTAmount}</p>
            </div>
            {/* -------------------------------------------------------------------- */}
            {/* Flow form */}
            <div
              className="h-full w-full flex flex-col justify-center items-center gap-2.5 rounded-lg"
            >
              <div className="w-full flex gap-5">
                <input
                  className="running-step-input text-base sm:text-sm md:text-base lg:text-xl"
                  type="text"
                  placeholder="Full Name*"
                  value={formValue?.name == "null" ? "" : formValue.name}
                  onChange={(e) => {
                    setFormValue({ ...formValue, name: e.target.value });
                  }}
                  onInput={(e) => {
                    e.target.value = e.target.value.replace(/[^A-Za-z\s]/g, '');
                  }}
                />
              </div>
              {/* ----------------------------------------------------------------- */}
              <div className="w-full mt-2
                flex flex-col sm:flex-col md:flex-row lg:flex-row
                gap-5 sm:gap-0 md:gap-5 lg:gap-5">
                <div className="
                  w-full sm:w-full md:w-1/2 lg:w-1/2">
                  <TextField
                    fullWidth
                    id="DOB"
                    className="running-step-input text-base sm:text-sm md:text-base lg:text-xl"
                    placeholder="DD-MM-YYYY*"
                    type="date"
                    name="dob"
                    inputProps={{
                      max: twelveYearsAgoString, placeholder: "DD-MM-YYYY*" // Set max attribute to today's date
                    }}
                    value={formValue?.dob == "null" ? "" : formValue.dob}
                    onChange={(e) => {
                      setFormValue({ ...formValue, dob: e.target.value });
                    }}
                  />
                  {(!formValue?.dob || dobError) &&
                    <div className="w-full text-[#ed5252] text-sm">Date of birth must be at least 14 years ago.</div>
                  }
                </div>
                <div className="
                  w-full sm:w-full md:w-1/2 lg:w-1/2">
                  <input
                    className="
                      running-step-input 
                      text-base sm:text-sm md:text-base lg:text-xl"
                    type="text"
                    placeholder="Address*"
                    value={formValue.address}
                    onChange={(e) => {
                      setFormValue({ ...formValue, address: e.target.value });
                    }}
                  />
                </div>
              </div>
              {/* -------------------------------------------------------------------*/}
              <div className="w-full mt-2
                flex flex-col sm:flex-col md:flex-row lg:flex-row
                gap-5 sm:gap-0 md:gap-5 lg:gap-5">
                <div className="
                  sm:w-full md:w-1/2 lg:w-1/2">
                  <input
                    className={`
                      text-base sm:text-sm md:text-base lg:text-xl
                      ${formValue?.email === null
                        ? 'running-step-input'
                        : 'running-step-input disabled-input'}
                    `}
                    type="email"
                    placeholder="Email id*"
                    value={formValue.email}
                    onChange={(e) => {
                      setFormValue({ ...formValue, email: e.target.value });
                    }}
                    disabled={formValue?.email === null ? false : true}
                  />
                </div>
                <div className="
                  w-full sm:w-full md:w-1/2 lg:w-1/2">
                  <input
                    className="running-step-input text-base sm:text-sm md:text-base lg:text-xl"
                    type="number"
                    placeholder="Mobile Number*"
                    value={formValue.phoneNumber}
                    onChange={(e) => {
                      const { value } = e.target;
                      const isValid = /^[0-9]{0,12}$/.test(value);
                      if (isValid) {
                        setFormValue({ ...formValue, phoneNumber: value });
                      }
                    }}
                    onInput={(e) => {
                      e.target.value = e.target.value.replace(/[^0-9]/g, '');
                    }}
                  />
                </div>
              </div>
              {/* ----------------------------------------------------------------- */}
              <div
                className="w-full mt-2
                  flex flex-col sm:flex-col md:flex-row lg:flex-row
                  gap-5 sm:gap-0 md:gap-5 lg:gap-5">
                <div className="w-full sm:w-full md:w-1/2 lg:w-1/2">
                  <Autocomplete
                    disablePortal
                    id="combo-box-demo"
                    options={CountryList}
                    getOptionLabel={(option) => option.name}
                    value={CountryList.find((option) => option.id === formValue.country) || null}
                    onChange={handleCountryChange}
                    className="text-base sm:text-sm md:text-base lg:text-xl"
                    renderInput={(params) => <TextField {...params} label="Country*" />}
                  />
                </div>
                <div className="
                  w-full sm:w-full md:w-1/2 lg:w-1/2">
                  <Autocomplete
                    disablePortal
                    id="combo-box-demo"
                    options={stateList}
                    getOptionLabel={(option) => option.name}
                    value={stateList.find((option) => option.id === formValue.state) || null}
                    onChange={handleStateChange}
                    className="text-base sm:text-sm md:text-base lg:text-xl"
                    renderInput={(params) => <TextField {...params} label="State*" />}
                  />
                </div>
              </div>
              {/* ----------------------------------------------------------------- */}
              <div className="w-full mt-2
                  flex flex-col sm:flex-col md:flex-row lg:flex-row
                  gap-5 sm:gap-0 md:gap-5 lg:gap-5">
                <div className="w-full sm:w-full md:w-1/2 lg:w-1/2">
                  <Autocomplete
                    disablePortal
                    id="combo-box-demo"
                    options={cityList}
                    getOptionLabel={(option) => option.name}
                    value={cityList.find((option) => option.id === formValue.city) || null}
                    onChange={(e, newValue) => {
                      setFormValue({ ...formValue, city: newValue?.id });
                    }}
                    className="running-step-input1 text-base sm:text-sm md:text-base lg:text-xl"
                    renderInput={(params) => <TextField {...params} label="City*" />}
                  />
                </div>
                <div className="w-full sm:w-full md:w-1/2 lg:w-1/2">
                  <input
                    className="running-step-input text-base sm:text-sm md:text-base lg:text-xl"
                    type="number"
                    placeholder="Pin code*"
                    value={formValue.pincode}
                    onChange={(e) => {
                      const { value } = e.target;
                      const isValid = /^[0-9]{0,6}$/.test(value);
                      if (isValid) {
                        setFormValue({ ...formValue, pincode: e.target.value });
                      }
                    }}
                    onInput={(e) => {
                      e.target.value = e.target.value.replace(/[^0-9]/g, '');
                    }}
                  />
                </div>
              </div>
              {/* ----------------------------------------------------------------- */}
              <div className="w-full mt-2
                flex flex-col sm:flex-col md:flex-row lg:flex-row
                gap-5 sm:gap-0 md:gap-5 lg:gap-5">
                <div className="w-full sm:w-full md:w-1/2 lg:w-1/2">
                  <input
                    id="company-name"
                    className="running-step-input text-base sm:text-sm md:text-base lg:text-xl"
                    type="text"
                    placeholder="Organization"
                    value={formValue.company_name}
                    onChange={(e) => {
                      setFormValue({ ...formValue, company_name: e.target.value });
                    }}
                  />
                </div>
                <div className="w-full sm:w-full md:w-1/2 lg:w-1/2">
                  <input
                    id="coupon-code"
                    className="running-step-input text-base sm:text-sm md:text-base lg:text-xl"
                    type="text"
                    placeholder="Coupon Code"
                    value={formValue.promocode}
                    onChange={(e) => {
                      setFormValue({ ...formValue, promocode: e.target.value });
                    }}
                  />
                </div>
              </div>
              {/* ----------------------------------------------------------------- */}
              <div className="justify-self-center self-center mt-2">
                <button
                  className="w-full bg-[#E67E22] px-2 py-1 rounded-[20px] 
                    text-white text-base sm:text-sm md:text-base lg:text-xl"
                  disabled={!formValue.promocode}
                  onClick={applyPromoCode}
                >
                  Apply PromoCode
                </button>
              </div>
              {/* ----------------------------------------------------------------- */}
              <div>
                <input
                  type="checkbox"
                  placeholder="Company Name"
                  name="termsandcondition"
                  id="termsandcondition"
                />
                <lable className="ml-2 
                  text-base sm:text-sm md:text-base lg:text-xl">I agree all terms and conditions</lable>
              </div>
            </div>
          </div>
        </div>
      </>
    );
  };

  const renderPayment = () => {
    return (
      <div
        style={{
          width: "100%",
          display: "flex",
          justifyContent: "center",
          paddingBottom: "20px",
        }}
      >
        <div
          style={{
            width: "100%",
            // padding: "0px 20px",
            display: "flex",
            flexDirection: "column",
            gap: "10px",
            justifyContent: "center",
          }}
        >
          <div style={{
            borderBottom: "solid 1px lightgray", padding: "10px", display: "flex",
            justifyContent: "space-between",
          }}>
            <div>Monthly Program : </div>
            <div>{selectProgram["program_name"]}</div>
          </div>
          <div
            style={{
              borderBottom: "solid 1px lightgray",
              padding: "10px",
              display: "flex",
              justifyContent: "space-between",
            }}
          >
            <div>Monthly Price : </div>
            <div>₹{(amount / duration).toFixed(2)}</div>
          </div>
          <div
            style={{
              borderBottom: "solid 1px lightgray",
              padding: "10px",
              display: "flex",
              justifyContent: "space-between",
            }}
          >
            <div>Discount: </div>
            <div>{discountPrice}%</div>
          </div>
          {parseInt(discountAmountAfter) != 0 &&

            <div
              style={{
                display: "flex",
                padding: "10px 10px 0px 10px",
                justifyContent: "space-between",
              }}
            >
              <div>Sub Total </div>

              <div>₹{discountAmountAfter}</div>
            </div>
          }
          {parseInt(discountAmountAfter) != 0 &&
            <div
              style={{
                display: "flex",
                padding: "0px 10px 0px 10px",
                justifyContent: "space-between",
              }}
            >
              <div >GST </div>

              <div>{getGSTPer}%</div>
            </div>
          }

          <div
            style={{
              display: "flex",
              padding: "10px",
              justifyContent: "space-between",
            }}
          >
            <div style={{ fontWeight: "600" }}>Total Price </div>
            <div>₹{afteraddGSTAmount}</div>
          </div>
          <div style={{ display: "flex", justifyContent: "center" }}>
            <button
              style={{
                width: "50%",
                background: "black",
                padding: "10px 10px",
                borderRadius: "20px",
                color: "white",
              }}
              onClick={() => {
                if (discountAmountAfter === 0) {
                  createUserSubscriptionplanWithoutRazorpay()
                } else {
                  openPayModal(options);
                }
              }}

            >
              Click here to Pay
            </button>
          </div>
        </div>
      </div>
    );
  };

  const renderPrograms = () => {
    return (
      <>
        <p>Select Program For Getting Subscription Plans</p>
        <select
          className="running-step-input"
          name="Program"
          id="program"
          onChange={(e) => {
            setselectProgram(yoskaProgramList[e.target.value]);
          }}
        >
          {yoskaProgramList.map((program, index) => {
            return <option value={index}>{program.program_name}</option>;
          })}
        </select>
      </>
    );
  };

  const steps = [
    {
      label: "Welcome",
      description: renderWelcomeScreen(),
    },
    {
      label:
        "Let's find",
      description: renderLetFindOut(),
    },

    {
      label: "Here are some activities to choose",
      description: renderActivity(),
    },
    {
      label:
        "Program Feature",
      description: renderSelectActivityFeature(),
    },
    {
      label: renderPrograms(),
      description: renderRunningPricing(),
    },
    {
      label: renderRunningLable(),
      description: renderRunningDiscription(),
    },
    {
      label: "",
      description: renderForm(),
    },
    {
      label: renderformTitle(),
      description: renderDescriptionForm(),
    },

    {
      label: "Make a Payment",
      description: renderPayment(),
    },
  ];

  const theme = useTheme();
  const [activeStep, setActiveStep] = React.useState(0);
  const [userDetail, setuserDetail] = React.useState(0);
  const [userSubscription, setuserSubscription] = React.useState(0);

  console.log("activeStep", activeStep == 4);


  const maxSteps = steps.length;
  let data = localStorage.getItem("userDetails")
  let data1 = localStorage.getItem("userSubsription")
  let usersubsriptionData = JSON.parse(data1)
  let userData = JSON.parse(data)
  useEffect(() => {

    setuserDetail(userData)
    setuserSubscription(usersubsriptionData)
    if (userData?.plan_id) {
      setPlan(userData?.plan_id)
      setPrice(userData?.price)
      setAmount(userData?.price)
      setAfterDicount(userData?.price)
      setselectProgram({ ...selectProgram, program_id: userData?.program_id, program_name: userData?.program_name, price: userData?.price })
      setActiveStep(8)
    }

  }, [userData?.plan_id])
  const handleNext = async () => {
    if (activeStep === 2) {
      if (activity.length === 0) {
        Swal.fire({
          title: "Error",
          text: "Please Select Activity",
          icon: "error",
        });
      } else {
        setActivityBadg(activity?.badge)
        getAllProgramByActivityID(setActiveStep);

      }
    } else if (activeStep === 4) {
      if (SubscriptionPlanList?.length > 0) {
        if (!price) {
          Swal.fire({
            title: "Error",
            text: "Please Select Any Plans",
            icon: "error",
          });
        } else {
          setActiveStep((prevActiveStep) => prevActiveStep + 1);
        }
      } else {
        Swal.fire({
          title: "Error",
          text: "Please Choose another programs first",
          icon: "error",
        });
      }

    } else if (activeStep === 6) {

      let response = validateData(); //its valdiate terms and conditions

      if (response) {
        updateUserProfile()
        let checkBoxValue = document.getElementById("termsandcondition");
        if (checkBoxValue.checked) {
          setActiveStep((prevActiveStep) => prevActiveStep + 1);
        } else {
          Swal.fire({
            title: "Error",
            text: "Please accept term and condition ",
            icon: "error",
          });
        }
      } else {
        Swal.fire({
          title: "Error",
          text: "Please fill all details",
          icon: "error",
        });
      }
    } else if (activeStep === 7) {
      if (formGoalValue?.goal && formGoalValue?.daysinweek && formGoalValue?.daysoption) {
        let response = await createPersonolize(formGoalValue); //its valdiate terms and conditions
        if (response.status) {
          createUserSubscriptionplanWithoutPaymnet(formGoalValue); //its valdiate terms and conditions
        } else {
          Swal.fire({
            title: "Error",
            text: response.message,
            icon: "error",
          });
        }
      } else {
        Swal.fire({
          title: "Error",
          text: "Please fill all details",
          icon: "error",
        });
      }
    } else {
      setActiveStep((prevActiveStep) => prevActiveStep + 1);
    }
  };

  const handleBack = () => {
    if (userDetail?.plan_id) {
      localStorage.removeItem("userDetails")
      setActiveStep(0);
      setuserDetail({})
    } else {
      setActiveStep((prevActiveStep) => prevActiveStep - 1);
    }

  };

  const validateData = () => {

    if (
      !formValue?.name ||
      !formValue?.address ||
      !formValue?.country ||
      !formValue?.state ||
      !formValue?.city ||
      !formValue?.pincode ||
      !formValue?.phoneNumber ||
      !formValue?.dob ||
      !formValue?.email || dobError
    ) {
      return false;
    } else {
      return true;
    }

  };
  const userID = localStorage.getItem("userId");
  const updateUserProfile = async () => {

    let nameArray = formValue?.name.split(' ');
    const formData = new FormData();
    formData.append('email', formValue.email);
    formData.append('mobile', formValue.phoneNumber);
    formData.append('city', formValue.city);
    formData.append('country', formValue.country);
    formData.append('state', formValue.state);
    formData.append('address1', formValue.address);
    formData.append('dob', formValue.dob);


    formData.append('firstname', nameArray[0]);
    formData.append('lastname', nameArray[1]);
    formData.append('user_id', userID);
    formData.append('program_id', activity?.id);

    const Result = await updatePrfile(formData);
  }
  useEffect(() => {
    if (amount && getGSTPer) {
      let gstAmount = calculateActualpriceAfterGST(amount, getGSTPer)
      setAfterAddGST(gstAmount)
    }
  }, [amount, getGSTPer])
  return (
    <Box
      className={`w-screen p-10 ${activeStep === 6 ? 'h-auto' : 'h-screen'} 
    sm:p-6 md:p-8 lg:p-10`}>
      <Grid container>
        <Grid container spacing={2}>
          <Grid item xs={12} sm={12} md={7} style={{
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
          }}>
            <div style={{ width: "100%" }}>
              <Typography
                variant="h5"
                sx={{
                  padding: "0",
                  margin: "0",
                  color: activeStep == 3 ? "white" : "orange",
                  paddingBottom: "20px",
                  fontSize: activeStep == 3 ? "40px" : ""
                }}
              >
                <div>
                  <Typography variant="h4" className="heading"><span className="text-orange-500"> {steps[activeStep]?.label}</span></Typography>{" "}
                </div>
              </Typography>

              <Box
                className={activeStep == 4 && "onboarding-flow-description"}
                sx={{ height: "100%", width: "100%" }}>
                {steps[activeStep].description}
              </Box>
              <MobileStepper
                style={activeStep == 3 ? { background: "rgb(176, 196, 222) !important", marginTop: "20px", padding: "0px 47px 0px 0px" } : { background: "white !important", marginTop: "20px", padding: "0px 47px 0px 0px" }}
                variant="text"
                steps={maxSteps}
                position="static"
                activeStep={activeStep}
                nextButton={
                  <Button
                    size="small"
                    className="pure-white-bg-btn bg-white text-base sm:text-sm md:text-base lg:text-xl"
                    onClick={handleNext}
                    disabled={activeStep === maxSteps - 1}
                  >
                    Next
                    {theme.direction === "rtl" ? (
                      <KeyboardArrowLeft />
                    ) : (
                      <KeyboardArrowRight />
                    )}
                  </Button>
                }
                backButton={
                  <Button
                    size="small"
                    className="pure-white-bg-btn bg-white text-base sm:text-sm md:text-base lg:text-xl"
                    onClick={handleBack}
                    disabled={activeStep === 0}
                  >
                    {theme.direction === "rtl" ? (
                      <KeyboardArrowRight />
                    ) : (
                      <KeyboardArrowLeft />
                    )}
                    Back
                  </Button>
                }
              />
            </div>
          </Grid>
          <Grid item xs={10} sm={10} md={5} >
            <SlickCarousel />
          </Grid>
        </Grid>
      </Grid>
    </Box>
  );
};

export default Flow;

//main container
// style={
//   (activeStep == 6) ?
//     {
//       width: "100vw",
//       padding: "40px",
//       height: "unset"
//       // background: `url(${Background}) center no-repeat`, // Replace 'your-background-image.jpg' with the path to your background image
//       // backgroundSize: "100% 100vh",
//     }
//     :
//     {
//       width: "100vw",
//       padding: "40px",
//       height: "100vh", // 100% of the viewport height
//       // background: `url(${Background}) center no-repeat`, // Replace 'your-background-image.jpg' with the path to your background image
//       // backgroundSize: "100% 100vh",
//     }}

// style={{ display: "flex", justifyContent: "center" }}


