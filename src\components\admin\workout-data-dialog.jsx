import { useState, useEffect } from "react";
import {
	<PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON>nt,
	<PERSON><PERSON><PERSON>eader,
	DialogTitle,
} from "../../components/ui/dialog";
import { Button } from "../../components/ui/button";
import { Input } from "../../components/ui/input";
import { Label } from "../../components/ui/label";
import { createworkoutdata, updateworkoutdata } from "../../API/api-endpoint";

export function WorkoutDataDialog({ open, onOpenChange, editData, onSave }) {
	const [formData, setFormData] = useState({
		workout_name: "",
	});

	useEffect(() => {
		if (editData) {
			setFormData({
				workout_name: editData.workout || "",
			});
		} else {
			setFormData({
				workout_name: "",
			});
		}
	}, [editData, open]);

	const handleSubmit = async (e) => {
		e.preventDefault();
		try {
			let response;
			if (editData?.id) {
				response = await updateworkoutdata({
					id: editData.id,
					workout: formData.workout_name,
				});
			} else {
				response = await createworkoutdata({
					workout: formData.workout_name,
				});
			}

			if (response?.status) {
				onSave();
				onOpenChange(false);
			} else {
				console.log(response.message || "Something went wrong");
			}
		} catch (error) {
			console.error("API Error:", error);
		}
	};

	return (
		<Dialog open={open} onOpenChange={onOpenChange}>
			<DialogContent className='max-w-md bg-white'>
				<DialogHeader>
					<DialogTitle className='font-semibold'>
						{editData ? "Edit Workout Data" : "Create Workout Data"}
					</DialogTitle>
				</DialogHeader>

				<form onSubmit={handleSubmit} className='space-y-4'>
					<div className='space-y-2'>
						<Label
							htmlFor='workout_name'
							className='text-base font-semibold'
						>
							Workout Name
						</Label>
						<Input
							className='w-full text-sm'
							id='workout_name'
							value={formData.workout_name}
							onChange={(e) =>
								setFormData((prev) => ({
									...prev,
									workout_name: e.target.value,
								}))
							}
							placeholder='Enter workout name'
							required
						/>
					</div>

					<div className='flex justify-end gap-2 pt-4'>
						<Button
							type='button'
							variant='outline'
							onClick={() => onOpenChange(false)}
						>
							Cancel
						</Button>
						<Button
							type='submit'
							className='bg-orange-700 hover:bg-orange-800 text-white'
						>
							{editData ? "Update" : "Create"}
						</Button>
					</div>
				</form>
			</DialogContent>
		</Dialog>
	);
}
