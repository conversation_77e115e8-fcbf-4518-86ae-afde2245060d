import React from 'react';
import axios from 'axios';
import Button from '@mui/material/Button';

const Test = () => {
    const token = 'b78ed8c9c33b65a4e85ae9c57634254d12a3c26bda45a562e40e6e38cdd9c7c82b3cb3ff009ecd45e730e7ab23f8669890045d53cc2babbf66f333f07fab912f98aa1e913ceb933ea5d153d7c1c836b78f722f781bc6483008713c11e44052820a2e1b5341681d74ac851fb6cf54194f1f4a3d17932877a9fb6403da22e7ea5394b3c73e703e871f3ff54c15c3afa4db973f09f83111491d50c729c2d66dc72506afa19b069158de44c6492f1304dd5ce69eb80568da0d3652980356d80ec768a8262b11f6404448a59a57389898b34ee5d1f0b403f04abdc65a4580114cb616914b05feb5ab956e98d01a92a33ef6d0';

    const config = {
        headers: {
            Authorization: `Bearer ${token}`
        }
    };

    const handleTestFirstAPI = async () => {
        try {
            const payload = {
                totalItems: 10,
                pageNumber: 0,
                type: "0"
            };
            const response = await axios.post(
                '/v5/threads/get_all_threads.php',
                payload,
                config
            );
            console.log("API 1 Response:", response.data);
        } catch (error) {
            console.log("API 1 Error:", error);
        }
    };

    const handleTestSecondAPI = async () => {
        try {
            const payload = {
                postId: "1088"
            };
            const response = await axios.post(
                '/v5/post/get_post_by_id.php',
                payload,
                config
            );
            console.log("API 2 Response:", response.data);
        } catch (error) {
            console.log("API 2 Error:", error);
        }
    };

    return (
        <>
            <Button onClick={handleTestFirstAPI}>API 1</Button>
            <Button onClick={handleTestSecondAPI}>API 2</Button>
        </>
    );
};

export default Test;
