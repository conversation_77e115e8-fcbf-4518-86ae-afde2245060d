import { <PERSON><PERSON>, <PERSON><PERSON> } from "antd";
import { FormLabel, Grid, TextField } from "@mui/material";
import Select from "react-select";
import SlickCarousel from "../.././SlickCarousel";
import { convertToRaw } from 'draft-js';
import { Editor } from 'react-draft-wysiwyg';
import draftToHtml from 'draftjs-to-html';

const CreateCommunityGroup = ({ logo, fileInputRef, editorState, setEditorState, isLoading, activity, handleFileUpload, isModalOpen, handleCancel, handleSubmit, name, setName, description, setDescription, location, setLocation, selectedActivities, setSelectedActivities, editData }) => {
    

    return (
        <Modal
            width={1200}
            open={isModalOpen}
            onCancel={handleCancel}
            footer={<div></div>}
        >
            <div className="headingCont paddingBot">
                <span className="heading">{editData ? "Edit " : "Create"}</span>{"  "}
                <span className="orange heading">Group</span>
                <p className="grey">
                    Create a new community group
                </p>
            </div>

            <div className="parentCont">
                <form className="form1" onSubmit={handleSubmit}>
                    <Grid container spacing={2}>
                        <Grid item xs={12} sm={5.5}>
                            <FormLabel>
                                Name<span className="text-[red]">*</span>
                            </FormLabel>
                            <TextField
                                fullWidth
                                placeholder="Group Name"
                                size="small"
                                type="text"
                                name="Name"
                                value={name}
                                onChange={(e) => setName(e.target.value)}
                            />
                        </Grid>
                        <Grid item xs={12} sm={5.5}>
                            <FormLabel sx={{display: 'flex', alignItems: 'center'}}>Logo:
                                {logo && typeof logo === 'string' && (
                                <p>&nbsp;{logo}</p>
                                )}
                            </FormLabel>
                            <input
                                ref={fileInputRef}
                                type="file"
                                accept="image/*"
                                onChange={handleFileUpload}
                                style={{ width: "100%", border: "1px solid #c2bbbb", borderRadius: "5px", minHeight: "40px", padding: "5px" }}
                            />
                        </Grid>
                        <Grid item xs={12} sm={5}>
                            <FormLabel>
                                Activity<span className="text-[red]">*</span>
                            </FormLabel>
                            <div style={{ width: '100%', display: 'block' }}>
                                {activity.length > 0 ? (
                                    <Select
                                        isMulti
                                        options={activity}
                                        getOptionLabel={(option) => option.activity_name}
                                        getOptionValue={(option) => option.id.toString()}
                                        value={selectedActivities}
                                        onChange={(selectedOptions) => setSelectedActivities(selectedOptions)}
                                        styles={{
                                            menu: (provided) => ({
                                                ...provided,
                                                width: '100%',
                                                display: 'block',
                                                scrollbarColor: '#E67E22 transparent',
                                                scrollbarWidth: 'thin',
                                                '&::-webkit-scrollbar': { width: '12px' },
                                                '&::-webkit-scrollbar-track': { background: 'transparent' },
                                                '&::-webkit-scrollbar-thumb': {
                                                    backgroundColor: '#E67E22',
                                                    borderRadius: '20px',
                                                    border: '3px solid transparent',
                                                    backgroundClip: 'content-box',
                                                },
                                            }),
                                        }}
                                    />
                                ) : (
                                    <p>Loading activities...</p>
                                )}
                            </div>
                        </Grid>
                        <Grid item xs={12} sm={5.5}>
                            <FormLabel>Location:</FormLabel>
                            <input
                                type="text"
                                name="location"
                                className="location"
                                value={location}
                                onChange={(e) => setLocation(e.target.value)}
                                style={{ width: "100%", border: "1px solid #c2bbbb", borderRadius: "5px", minHeight: "40px", padding: "5px" }}
                            />
                        </Grid>
                        <Grid item xs={12}>
                            <FormLabel>Description:</FormLabel>
                            <Editor
                            editorState={editorState}
                            onEditorStateChange={(st) => {
                                setEditorState(st);
                                setDescription(draftToHtml(convertToRaw(st.getCurrentContent())));
                            }}
                            />
                            <input
                                type="hidden"
                                name="description"
                                value={description}
                                required
                            />
                        </Grid>
                        <Grid item xs={12} style={{ textAlign: "right" }}>
                            <Button
                                className="btn"
                                htmlType="submit"
                                variant="contained"
                                color="primary"
                                disabled={isLoading}
                            >
                                <span style={{ color: '#fff' }}>Submit</span>
                            </Button>
                        </Grid>
                    </Grid>
                </form>
                <div className="slick-container">
                    <SlickCarousel />
                </div>
            </div>
        </Modal>
    )
}

export default CreateCommunityGroup;