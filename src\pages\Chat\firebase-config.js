import { initializeApp } from "firebase/app";
import { getAuth, GoogleAuthProvider } from 'firebase/auth'
import { getAnalytics } from "firebase/analytics";
import { getFirestore } from "firebase/firestore";
import { getStorage } from "firebase/storage";
import { get, push, getDatabase, set, update, ref, child, runTransaction, serverTimestamp } from "firebase/database";

const firebaseConfig = {
    apiKey: "AIzaSyAcdgXFsrYIf6BCUgntZPEfoTnXX9dJqEo",
    authDomain: "yo-fit-v1.firebaseapp.com",
    projectId: "yo-fit-v1",
    storageBucket: "yo-fit-v1.appspot.com",
    messagingSenderId: "780771101036",
    appId: "1:780771101036:web:63a8605a3119f12c5e4b49",
    measurementId: "G-J0JS1VDXP5",
  };

// Initialize Firebase
// const app = initializeApp(firebaseConfig);
// const auth = getAuth(app);
// const provider = new GoogleAuthProvider();

// const db = getDatabase(app);
// const storage = getStorage();

// export { auth, provider, db, storage, ref, get, push, set, update, child, runTransaction, serverTimestamp };


// const analytics = getAnalytics(app);