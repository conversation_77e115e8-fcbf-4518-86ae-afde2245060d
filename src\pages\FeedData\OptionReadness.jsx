import { useEffect, useState, useMemo, useCallback } from "react";
import { <PERSON><PERSON> } from "../../components/ui/button";
import { Input } from "../../components/ui/input";
import { Card, CardContent, CardHeader } from "../../components/ui/card";
import {
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableHeader,
	TableRow,
} from "../../components/ui/table";
import { Edit, Trash2, Plus, ArrowLeft } from "lucide-react";
import { OptionReadinessDialog } from "../../components/admin/option-readiness-dialog";
import { DeleteConfirmDialog } from "../../components/admin/delete-confirm-dialog";
import Header from "../../components/Header";
import {
	getAllOptionReadnessData,
	deleteOptionReadnessData,
} from "../../API/api-endpoint";
import Swal from "sweetalert2";

const OptionReadiness = () => {
	const [optionData, setOptionData] = useState([]);
	const [isLoading, setIsLoading] = useState(true);
	const [searchTerm, setSearchTerm] = useState("");
	const [currentPage, setCurrentPage] = useState(1);
	const [showDialog, setShowDialog] = useState(false);
	const [editingItem, setEditingItem] = useState(null);
	const [deleteId, setDeleteId] = useState(null);
	const pageSize = 10;

	const fetchData = useCallback(async () => {
		try {
			setIsLoading(true);
			const response = await getAllOptionReadnessData();
			if (response) {
				setOptionData(response || []);
			} else {
				console.log("Response is empty or invalid");
				setOptionData([]);
			}
		} catch (error) {
			console.error("Error fetching option readiness data:", error);
			Swal.fire({
				title: "Error",
				text: "Failed to fetch option readiness data. Please try again.",
				icon: "error",
				timer: 3000,
				showConfirmButton: false,
			});
			setOptionData([]);
		} finally {
			setIsLoading(false);
		}
	}, []);

	useEffect(() => {
		fetchData();
	}, [fetchData]);

	const filteredData = useMemo(() => {
		if (!searchTerm.trim()) return optionData;

		return optionData.filter((item) => {
			return item?.option_text
				?.toLowerCase()
				.includes(searchTerm.toLowerCase());
		});
	}, [optionData, searchTerm]);

	const paginatedData = useMemo(() => {
		const startIndex = (currentPage - 1) * pageSize;
		const endIndex = startIndex + pageSize;
		return filteredData.slice(startIndex, endIndex);
	}, [filteredData, currentPage, pageSize]);

	const totalPages = Math.ceil(filteredData.length / pageSize);

	const handleSearch = (value) => {
		setSearchTerm(value);
		setCurrentPage(1);
	};

	const handleCreate = () => {
		setEditingItem(null);
		setShowDialog(true);
	};

	const handleEdit = (item) => {
		setEditingItem(item);
		setShowDialog(true);
	};

	const handleDelete = async (id) => {
		try {
			const response = await deleteOptionReadnessData(id);
			console.log("Delete response:", response);

			Swal.fire({
				title: "Success",
				text: "Option readiness deleted successfully",
				icon: "success",
				timer: 2000,
				showConfirmButton: false,
			});

			setCurrentPage(1);
			fetchData();
		} catch (error) {
			console.error("Error deleting option readiness:", error);
			Swal.fire({
				title: "Error",
				text: "Failed to delete option readiness. Please try again.",
				icon: "error",
				timer: 3000,
				showConfirmButton: false,
			});
		}
	};

	const handleDialogSuccess = () => {
		setShowDialog(false);
		setEditingItem(null);
		fetchData();
	};

	return (
		<div>
			<Header />
			<div className='mx-auto p-6 max-w-[1300px] mt-16'>
				<Card>
					<CardHeader className='bg-orange-50 border-b flex flex-col gap-4'>
						<div className='flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4'>
							<div>
								<h1 className='text-2xl font-bold text-orange-900'>
									Option Readness
								</h1>
								<p className='text-orange-700 mt-1 text-sm'>
									Manage readness options with points and text
								</p>
							</div>
						</div>
						<div className='flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4'>
							<div className='flex gap-3'>
								<Button
									onClick={handleCreate}
									className='bg-orange-600 hover:bg-orange-700 text-white'
								>
									<Plus className='h-4 w-4 mr-2' />
									Create Option
								</Button>
							</div>

							<div className='flex gap-3'>
								<Input
									placeholder='Search by option text...'
									value={searchTerm}
									onChange={(e) =>
										handleSearch(e.target.value)
									}
									className='w-full text-sm'
								/>
								<Button
									onClick={() =>
										(window.location.href =
											"/subsegments-readness")
									}
									variant='outline'
									className='border-gray-300 text-gray-700 hover:bg-gray-100'
								>
									<ArrowLeft className='h-4 w-4 mr-2' />
									Back
								</Button>
							</div>
						</div>
					</CardHeader>
					<CardContent className='p-0'>
						<div className='overflow-x-auto'>
							<Table className='min-w-full w-full'>
								<TableHeader>
									<TableRow className='bg-blue-600 hover:bg-blue-600'>
										<TableHead className='text-white font-semibold'>
											Sr No
										</TableHead>
										<TableHead className='text-white font-semibold'>
											Option Text
										</TableHead>
										<TableHead className='text-white font-semibold'>
											Points
										</TableHead>
										<TableHead className='text-white font-semibold'>
											Actions
										</TableHead>
									</TableRow>
								</TableHeader>
								<TableBody>
									{isLoading ? (
										<TableRow>
											<TableCell
												colSpan={4}
												className='text-center py-8'
											>
												Loading...
											</TableCell>
										</TableRow>
									) : paginatedData.length > 0 ? (
										paginatedData.map((item, index) => (
											<TableRow
												key={item.id || index}
												className='hover:bg-gray-50'
											>
												<TableCell className='font-medium'>
													{(currentPage - 1) *
														pageSize +
														index +
														1}
												</TableCell>
												<TableCell>
													{item?.option_text || "N/A"}
												</TableCell>
												<TableCell>
													{item?.points || "N/A"}
												</TableCell>
												<TableCell>
													<div className='flex gap-2'>
														<Button
															variant='ghost'
															size='sm'
															onClick={() =>
																handleEdit(item)
															}
															className='text-blue-600 hover:text-blue-800'
														>
															<Edit className='h-4 w-4' />
														</Button>
														<Button
															variant='ghost'
															size='sm'
															onClick={() =>
																setDeleteId(
																	item.id
																)
															}
															className='text-red-600 hover:text-red-800'
														>
															<Trash2 className='h-4 w-4' />
														</Button>
													</div>
												</TableCell>
											</TableRow>
										))
									) : (
										<TableRow>
											<TableCell
												colSpan={4}
												className='text-center py-8 text-gray-500'
											>
												No options found
											</TableCell>
										</TableRow>
									)}
								</TableBody>
							</Table>
						</div>

						{totalPages > 1 && (
							<div className='flex justify-center gap-2 p-4 border-t'>
								<Button
									variant='outline'
									size='sm'
									onClick={() =>
										setCurrentPage(
											Math.max(1, currentPage - 1)
										)
									}
									disabled={currentPage === 1}
								>
									Previous
								</Button>

								{Array.from({ length: 5 }, (_, i) => {
									const startPage =
										Math.floor((currentPage - 1) / 5) * 5 +
										1;
									const page = startPage + i;
									if (page > totalPages) return null;

									return (
										<Button
											key={page}
											variant={
												currentPage === page
													? "default"
													: "outline"
											}
											size='sm'
											onClick={() => setCurrentPage(page)}
											className={
												currentPage === page
													? "bg-orange-500 hover:bg-orange-600 text-white"
													: ""
											}
										>
											{page}
										</Button>
									);
								})}

								<Button
									variant='outline'
									size='sm'
									onClick={() =>
										setCurrentPage(
											Math.min(
												totalPages,
												currentPage + 1
											)
										)
									}
									disabled={currentPage === totalPages}
								>
									Next
								</Button>
							</div>
						)}
					</CardContent>
				</Card>
			</div>

			<OptionReadinessDialog
				open={showDialog}
				onClose={() => setShowDialog(false)}
				onSuccess={handleDialogSuccess}
				editingItem={editingItem}
			/>

			<DeleteConfirmDialog
				open={!!deleteId}
				onOpenChange={(open) => !open && setDeleteId(null)}
				onConfirm={() => {
					handleDelete(deleteId);
					setDeleteId(null);
				}}
				title='Delete Option Readness'
				description='Are you sure you want to delete this option readness? This action cannot be undone.'
			/>
		</div>
	);
};

export default OptionReadiness;
