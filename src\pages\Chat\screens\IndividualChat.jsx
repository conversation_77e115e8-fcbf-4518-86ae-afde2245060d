import { Avatar, Box, Paper, Toolbar, Typography } from '@mui/material'
import React from 'react'
import Navbar from '../components/Navbar';
import QuestionAnswerOutlinedIcon from '@mui/icons-material/QuestionAnswerOutlined';
import ArrowOutwardSharpIcon from '@mui/icons-material/ArrowOutwardSharp';
import "../styles/chatlanding.css"
import InsertLinkOutlinedIcon from '@mui/icons-material/InsertLinkOutlined';
import NotificationsActiveOutlinedIcon from '@mui/icons-material/NotificationsActiveOutlined';
import NavIndividualChat from '../components/NavIndividualChat';
import PhotoOutlinedIcon from '@mui/icons-material/PhotoOutlined';
import EastOutlinedIcon from '@mui/icons-material/EastOutlined';
import "./../styles/individualChat.css"

export default function IndividualChat() {
    const paper = {
        padding: "0rem",
        minHeight: "100vh",
        maxWidth: '27rem',
        backgroundImage: 'url("https://cdn.wallpapersafari.com/80/83/K7l2qB.jpg")'
    };

    const data = [
        {
            date: 'March 31',
            messages: [
                {
                    senderId: 1,
                    msgType: 'text',
                    msgText: "Hi Aisha! I’m Poornima",
                    timeStamp: '10:30 AM'
                },
                {
                    senderId: 1,
                    msgType: 'text',
                    msgText: "I am travelling to Chennai and wanted to connect with you!",
                    timeStamp: '10:30 AM'
                },
                {
                    senderId: 0,
                    msgType: 'text',
                    msgText: "Hi Poornima!",
                    timeStamp: '10:30 AM'
                }
            ]
        },
        {
            date: 'March 31',
            messages: [
                {
                    senderId: 1,
                    msgType: 'text',
                    msgText: "Hi Aisha! I’m Poornima",
                    timeStamp: '10:30 AM'
                },
                {
                    senderId: 1,
                    msgType: 'text',
                    msgText: "I am travelling to Chennai and wanted to connect with you!",
                    timeStamp: '10:30 AM'
                },
                {
                    senderId: 0,
                    msgType: 'text',
                    msgText: "Hi Poornima!",
                    timeStamp: '10:30 AM'
                }
            ]
        },
        {
            date: 'March 31',
            messages: [
                {
                    senderId: 1,
                    msgType: 'text',
                    msgText: "Hi Aisha! I’m Poornima",
                    timeStamp: '10:30 AM'
                },
                {
                    senderId: 1,
                    msgType: 'text',
                    msgText: "I am travelling to Chennai and wanted to connect with you!",
                    timeStamp: '10:30 AM'
                },
                {
                    senderId: 0,
                    msgType: 'text',
                    msgText: "Hi Poornima!",
                    timeStamp: '10:30 AM'
                }
            ]
        },
        {
            date: "Today",
            messages: [
                {
                    senderId: 1,
                    msgType: 'photo',
                    name: 'Tarun chawla',
                    imageUrl: "https://vietnam.travel/sites/default/files/inline-images/Wallpaper_Ha%20Giang_Vietnam%20Tourism_0.jpg",
                    profileUrl: "https://i.insider.com/5aeb7d1919ee864c008b489d?width=1136&format=jpeg",
                    timeAbout: '12 days ago',
                    touristPlace: 'Rishikesh',
                    timeStamp: '10:30 AM'
                }
            ]
        },
        {
            date: "12:20 PM",
            messages: [
                {
                    senderId: 0,
                    msgType: 'video',
                    videoThumbnail: 'https://douglasgreen.com/wp-content/uploads/2014/03/video-play-btn-featured.png',
                    timeStamp: '10:30 AM'
                }
            ]
        },
    ]

    return <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
        <Paper sx={{ flexGrow: 1 }} elevation={10} style={paper}>
            <NavIndividualChat />
            {
                data?.map((item, index) => <React.Fragment key={index}>
                    <Box className="date-box-parent">
                        <Box className="date-box">
                            <Typography className='ind-chat-date'>{item.date}</Typography>
                        </Box>
                    </Box>
                    {
                        item.messages?.map((msg, index) => <React.Fragment>
                            {msg.msgType === "text" &&
                                <Box key={index} sx={
                                    msg.senderId === 0
                                        ? { paddingLeft: '1rem', paddingRight: "1rem", textAlign: "right", marginBottom: '6px', color: 'white' }
                                        : { paddingLeft: '1rem', paddingRight: "1rem", textAlign: "left", marginBottom: '6px' }
                                } >
                                    <Box className='text-msg-box' sx={msg.senderId === 0 ? { backgroundColor: "#056B6B" } : { backgroundColor: "#FFF388" }}>
                                        {msg.msgText}
                                        <Box className="text-msg-timing-parent">
                                            <Typography className='text-msg-timing'>{msg.timeStamp}</Typography>
                                        </Box>
                                    </Box>
                                </Box>}

                            {msg.msgType === "photo" &&
                                <Box className="photo-msg-parent" s>
                                    <Box className="photo-msg-img-box" style={{ backgroundImage: `url(${msg.imageUrl})` }}>
                                        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-evenly' }}>
                                            <Box style={{ display: "flex", justifyContent: 'center', }}>
                                                <Avatar alt="Remy Sharp" sx={{ height: 25, width: 25 }} src={msg.profileUrl} />
                                                <Box className="photo-img-texts-aligns">
                                                    <Typography className='userName' fontWeight="fontWeightBold" sx={{ color: 'white', fontSize: '12px', paddingLeft: '0.6rem', lineHeight: 1.3 }}>{msg.name}</Typography>
                                                    <Typography className='userName' fontWeight="fontWeightNormal" sx={{ color: 'white', fontSize: '10px', lineHeight: 1.3 }}>{msg.touristPlace}</Typography>
                                                    <Typography className='userName' fontWeight="fontWeightNormal" sx={{ color: 'white', fontSize: '10px', lineHeight: 1.3 }}>{msg.timeAbout}</Typography>
                                                </Box>
                                            </Box>
                                            <Box className="photo-img-icon">
                                                <PhotoOutlinedIcon fontSize='20' />
                                            </Box>
                                        </Box>
                                    </Box>
                                    <Box className="photo-msg-view-post-btn">
                                        <Typography className='view-post-text'>
                                            View Post
                                        </Typography>
                                        <EastOutlinedIcon sx={{ fontSize: '14px', paddingLeft: '0.24rem', color: "#056B6B", marginTop: "-2px" }} />
                                    </Box>
                                    <Box className="text-msg-timing-parent">
                                        <Typography className='text-msg-timing'>{msg.timeStamp}</Typography>
                                    </Box>
                                </Box>}

                            {msg.msgType === "video" &&
                                <Box key={index} sx={
                                    msg.senderId === 0
                                        ? { paddingLeft: '1rem', paddingRight: "1rem", textAlign: "right", marginBottom: '6px', color: 'white' }
                                        : { paddingLeft: '1rem', paddingRight: "1rem", textAlign: "left", marginBottom: '6px' }
                                } >
                                    <Box className='video-msg-box' sx={msg.senderId === 0 ? { backgroundColor: "#056B6B" } : { backgroundColor: "#FFF388" }}>
                                        {msg.msgText}
                                        <Box className="video-box" style={{ backgroundImage: `url(${msg.videoThumbnail})` }} >

                                        </Box>
                                        <Box className="text-msg-timing-parent">
                                            <Typography className='text-msg-timing'>{msg.timeStamp}</Typography>
                                        </Box>
                                    </Box>
                                </Box>}
                        </React.Fragment>)
                    }
                </React.Fragment>)
            }
        </Paper >
    </Box >
}