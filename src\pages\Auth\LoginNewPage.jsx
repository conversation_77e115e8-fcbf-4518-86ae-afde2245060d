// ** React Imports
import React, { useEffect, useState } from "react";

// ** MUI Components
import Box from "@mui/material/Box";
import Divider from "@mui/material/Divider";
import TextField from "@mui/material/TextField";
import InputLabel from "@mui/material/InputLabel";
import Typography from "@mui/material/Typography";
import IconButton from "@mui/material/IconButton";
import CardContent from "@mui/material/CardContent";
import FormControl from "@mui/material/FormControl";
import OutlinedInput from "@mui/material/OutlinedInput";
import { styled } from "@mui/material/styles";
import MuiCard from "@mui/material/Card";
import InputAdornment from "@mui/material/InputAdornment";
import LoadingButton from "@mui/lab/LoadingButton";

// ** Icons Imports
import VisibilityIcon from "@mui/icons-material/Visibility";
import VisibilityOffIcon from "@mui/icons-material/VisibilityOff";

import { FcGoogle } from "react-icons/fc";
import { FaApple } from "react-icons/fa6";

import { useNavigate } from "react-router-dom";
import { SigninWithGoogle, AppleSignin } from "../../API/firebase.config";
import Swal from "sweetalert2";
import { Login, ResendOtp, VerifyOTP, fetchCurrentProfile, getPrograms, updateProgram } from "../../API/api-endpoint";
import Background from "../../Images/Background.png";
import RunningImage from "../../Images/Running.png";

import GoogleImage from "../../Images/GoogleImage.png";
import { useDispatch } from "react-redux";
import { FormLabel, Grid, MenuItem } from "@mui/material";
import { Modal } from "antd";
import { showError } from "../../components/Messages";
import LayoutImage from "./LayoutImage";
import SlickCarousel from "../SlickCarousel";
import withReactContent from 'sweetalert2-react-content';
import { useLocation, useSearchParams } from "react-router-dom";


const MySwal = withReactContent(Swal);

const Card = styled(MuiCard)(({ theme }) => ({
  [theme.breakpoints.up("sm")]: { width: "28rem" },
}));

const LoginNewPage = () => {
  let isVerifyOTP = localStorage.getItem("isOTP")

  const [values, setValues] = useState({
    email: "",
    password: "",
    showPassword: false,
  });

  useEffect(() => {
    localStorage.removeItem("userDetails")
    localStorage.removeItem("userSubsription")
  })

  const [isLoading, setIsLoading] = useState(false);
  const [programId, setProgramId] = useState();
  const [isOTPScreen, setisOTPScreen] = useState(false);
  const [enteredOTP, setenteredOTP] = useState(null);
  const [isGoogleSigninLoading, setIsGoogleSigninLoading] = useState(false);
  const [isAppleSigninLoading, setIsAppleSigninLoading] = useState(false);

  const location = useLocation();
  // const { channelId, channelName } = JSON.parse(localStorage.getItem("invitationParams")) || {};
  const [searchParams] = useSearchParams();
  const channelId = searchParams.get("channelId");
  const channelName = searchParams.get("channelName");

  const navigate = useNavigate();
  const dispatch = useDispatch();

  const showAlert = (data) => {
    MySwal.fire({
      title: 'Info!!',
      text: 'Your account is in restricted mode, You cannot use automation',
      icon: 'info',
      showCancelButton: true,
      confirmButtonText: <div style={{ marginRight: "20px" }} onClick={() => {
        navigate("/renew-flow");
        localStorage.setItem("email", data?.user.email);
        localStorage.setItem("fullname", `${data?.user.firstname} ${data?.user.lastname}`);


      }}>Renew</div>,
      cancelButtonText: 'Cancel',
      customClass: {
        confirmButton: 'swalbtn btn-primary', // Add your custom class for the confirm button
        cancelButton: 'swalbtnCancel btn-secondary', // Add your custom class for the cancel button
      },
      buttonsStyling: false, // Disable SweetAlert2's default styling for buttons
    }).then((result) => {
      if (result.isConfirmed) {
        // Handle OK button click
        navigate("/onboarding-flow");
        localStorage.setItem("email", data?.user.email);
        localStorage.setItem("fullname", `${data?.user.firstname} ${data?.user.lastname}`);
        console.log('OK button clicked');
      } else if (result.isDismissed) {
        // Handle Cancel button click
        console.log('Cancel button clicked');
      }
    });
  };

  const handleChange = (prop) => (event) => {
    setValues({ ...values, [prop]: event.target.value });
  };

  const handleClickShowPassword = () => {
    setValues({ ...values, showPassword: !values.showPassword });
  };

  const handleGoogleSignin = async () => {
    try {
      setIsGoogleSigninLoading(true);
      setIsLoading(false)
      setIsAppleSigninLoading(false);
      const result = await SigninWithGoogle(dispatch);
      const loginObj = result?.data ? result?.data : result;
      await loginOnboardringStateOperationProvider(loginObj, "google");
    } catch (e) {
      setIsLoading(false)
      setIsGoogleSigninLoading(false);
      setIsAppleSigninLoading(false);
      console.log(`Error occured in apple sign-in :- ${e}`);
    }
  };

  const handleAppleSignin = async () => {
    try {
      setIsLoading(false);
      setIsGoogleSigninLoading(false);
      setIsAppleSigninLoading(true);
      const result = await AppleSignin(dispatch);
      const loginObj = result?.data ? result?.data : result;
      await loginOnboardringStateOperationProvider(loginObj, "apple");
    } catch (e) {
      setIsLoading(false)
      setIsGoogleSigninLoading(false);
      setIsAppleSigninLoading(false);
      showError(e.message);
    }
  };

  const onSubmit = async () => {
    try {
      if (values.email && values.password) {
        setIsLoading(true);
        setIsGoogleSigninLoading(false);
        setIsAppleSigninLoading(false);
        const result = await Login({
          email: values.email,
          password: values.password,
        }, dispatch);
        const loginObj = result?.data ? result?.data : result;
        await loginOnboardingStateOperationManual(loginObj);
      } else {
        showError("Please provider email & password to login ")
      }
    } catch (error) {
      setIsLoading(false);
      showError("Oops something went wrong please try after some time.")
    }

  };

  const closeProviderLoader = (provider) => {
    if (provider === "google") {
      setIsGoogleSigninLoading(false)
    } else if (provider === "apple") {
      setIsAppleSigninLoading(false)
    } else {
      setIsLoading(false);
    }
  }

  const loginOnboardingStateOperationManual = async (loginObj) => {
    try {
      const { status, message } = loginObj;
      const msgFromRequest = message.replace(/\s+/g, '').trim().toLowerCase();
      if (!status && msgFromRequest === "invalidcredential.") {
        setIsLoading(false)
        showError(message);
      } else if (!status && message === "userisnotverified") {
        localStorage.setItem("email", values.email);
        localStorage.setItem("isOTP", true);
        ResendAPI();
        setIsLoading(false);
        showErrorAndRedirect(`${message} Please verify email first`);
      } else if (!status) {
        setIsLoading(false);
        showError(message);
      } else {
        const { token, user, subs } = loginObj;
        const { id, email, onboardingState, firstname, lastname, role_id, strava_id } = user;
        const userFullName = `${firstname} ${lastname}`;
        // storing common fields in local-storage 
        localStorage.setItem("email", email);
        localStorage.setItem("fullname", userFullName);
        localStorage.setItem("roleID", role_id);
        localStorage.setItem("userId", id);
        localStorage.setItem("userTokentoken", `Bearer ${token}`);
        localStorage.setItem("user", JSON.stringify(user));
        localStorage.setItem("onboardingState", onboardingState);
        if (onboardingState === "yoska_academy") {
          if (role_id === 5 && msgFromRequest === "paymentisnotdone" && subs.length === 0) {
            setIsLoading(false);
            navigate("/onboarding-flow");
            setTimeout(() => {
              showError(`${message}`);
            }, 100);
          } else if (role_id === 5 && subs.length > 0) {
            const profile = await fetchCurrentProfile(id);
            setIsLoading(false);
            if (!profile) {
              const response = await getPrograms()
              if (response?.length > 0) {
                showConfirm(response)
                Swal.fire({
                  title: "Success",
                  text: "Login successful please select program",
                  icon: "success",
                });
              }
            }
            if (!strava_id) {
              localStorage.setItem("FirstLogin", true);
              showSuccess(message, "/strava");
            } else {
              if (channelId || channelName) {
                navigate(`/workout?channelId=${channelId}&channelName=${channelName}`);
              } else {
                showSuccess(message, "/workout");
              }
            }
          } else if (role_id === 1 || role_id === 2 || role_id === 3 && subs.length === 0) {
            if (channelId || channelName) {
              navigate(`/coach-yoska?channelId=${channelId}&channelName=${channelName}`);
            } else {
              showSuccess(message, "/coach-yoska");
            }
          }

        } else if (onboardingState === "community") {
          const { isMentor, group } = loginObj;
          localStorage.setItem("isMentor", isMentor);
          localStorage.setItem("groupDetail", JSON.stringify(group));
          if (role_id === 5) {
            if (!strava_id) {
              localStorage.setItem("FirstLogin", true);
              showSuccess(message, "/strava");
            } else {
              if (channelId || channelName) {
                navigate(`/workout?channelId=${channelId}&channelName=${channelName}`);
              } else {
                showSuccess(message, "/assignechallenges-user");
              }
            }
          } else if (isMentor) {
            if (channelId || channelName) {
              navigate(`/coach-yoska?channelId=${channelId}&channelName=${channelName}`);
            } else {
              showSuccess(message, "/assignechallenges-user");
            }
          }
        }
      }
    } catch (error) {
      console.log(error?.message);
      setIsLoading(false);
    }
  }

  const loginOnboardringStateOperationProvider = async (loginObj, provider) => {
    try {
      const { status, message } = loginObj;
      const msgFromRequest = message.replace(/\s+/g, '').toLowerCase();
      if (!status && msgFromRequest === "loginfailed") {
        closeProviderLoader(provider);
        showError(`${message}, please signup.`);
      } else if (!status && message === "userisnotverified") {
        localStorage.setItem("email", values.email);
        localStorage.setItem("isOTP", true)
        ResendAPI();
        closeProviderLoader(provider);
        showErrorAndRedirect(`${message} Please verify email first`);
      } else if (!status) {
        closeProviderLoader(provider);
        showError(message);
      } else {
        const { token, user, subs } = loginObj;
        const { id, email, onboardingState, firstname, lastname, role_id, strava_id } = user;
        const userFullName = `${firstname} ${lastname}`;

        // storing common fields in local-storage 
        localStorage.setItem("email", email);
        localStorage.setItem("fullname", userFullName);
        localStorage.setItem("roleID", role_id);
        localStorage.setItem("userId", id);
        localStorage.setItem("userTokentoken", `Bearer ${token}`);
        localStorage.setItem("user", JSON.stringify(user));

        if (onboardingState === "pending" && role_id === 5) {
          closeProviderLoader(provider);
          Swal.fire({
            title: "Success",
            text: `${message}`,
            icon: "success",
          });
        } else if (onboardingState === "yoska_academy") {
          if (role_id === 5 && msgFromRequest === "paymentisnotdone" && subs.length === 0) {
            closeProviderLoader(provider);
            navigate("/onboarding-flow");
            setTimeout(() => {
              showError(`${message}`);
            }, 100);
          } else if (role_id === 5 && subs.length > 0) {
            const profile = await fetchCurrentProfile(id);
            setIsLoading(false);
            if (!profile) {
              const response = await getPrograms()
              if (response?.length > 0) {
                showConfirm(response)
                Swal.fire({
                  title: "Success",
                  text: "Login successful please select program",
                  icon: "success",
                });
              }
            }
            if (!strava_id) {
              localStorage.setItem("FirstLogin", true);
              showSuccess(message, "/strava");
            } else {
              if (channelId || channelName) {
                navigate(`/workout?channelId=${channelId}&channelName=${channelName}`);
              } else {
                showSuccess(message, "/workout");
              }
            }
          } else if (role_id === 1 || role_id === 2 || role_id === 3 && subs.length === 0) {
            if (channelId || channelName) {
              navigate(`/coach-yoska?channelId=${channelId}&channelName=${channelName}`);
            } else {
              showSuccess(message, "/coach-yoska");
            }
          }

        } else if (onboardingState === "community") {
          const { isMentor, group } = loginObj;
          localStorage.setItem("isMentor", isMentor);
          if (role_id === 5) {
            if (group) {
              localStorage.setItem("groupDetail", group);
              closeProviderLoader(provider);
              // navigate("/some-path") here path of page for dashboard will come for group member.
            } else {
              showError("Not selected group to enrolled.");
              closeProviderLoader(provider);
            }
          }
        }
      }
    } catch (error) {
      closeProviderLoader(provider);
      showError("Oops somthing went wrong. Please try after some time")
    }
  }

  const showErrorAndRedirect = (message, path) => {
    Swal.fire({
      title: "Error",
      text: message,
      icon: "error",
    });
    setIsLoading(false);
    localStorage.setItem("email", values.email);
    navigate(path);
  };

  const showSuccess = (message, path) => {
    Swal.fire({
      title: "Success",
      text: message,
      icon: "success",
    });
    setIsLoading(false);
    navigate(path);
  };
  const updateProfile = async (id) => {
    const response = await updateProgram(id)
    if (response?.status) {
      showSuccess("Login successful", "/workout");

      Swal.fire({
        title: "Success",
        text: response.message,
        icon: "success",
      });
    }
  }
  const showConfirm = (programList) => {
    let prgrmid
    Modal.info({
      title:
        <h3 style={{ padding: "10px 0px 0px 10px" }}><b>Select Program:</b></h3>,
      content: <TextField
        fullWidth
        size="small"
        select
        name="challengeActivity"
        value={programId}

        onChange={
          (event) => {
            setProgramId(event.target.value)
            prgrmid = event.target.value
          }}
        id="form-layouts-separator-select"
        labelId="form-layouts-separator-select-label"
        input={<OutlinedInput id="select-multiple-language" />}
      >
        <MenuItem value={""} disabled>
          Select Program
        </MenuItem>
        {programList?.map((value, index) => {
          return (
            <MenuItem value={value?.program_id}>
              {value?.prgram_name}
            </MenuItem>
          );
        })}
      </TextField>,
      onOk() {
        updateProfile(prgrmid)
      },
    });
  };
  const ResendAPI = async () => {
    console.log("dsnfbndsfnsdv");
    setenteredOTP("")
    let apicalled = false;
    setisOTPScreen(true);
    let email = localStorage.getItem("email");
    if (apicalled === false) {
      let response = await ResendOtp(email);
      if (response.status) {
        apicalled = true;
        setisOTPScreen(true);
      } else {
        Swal.fire({
          title: "Error",
          text: response?.message,
          icon: "error",
        });
      }
    }
  };
  const verifyOtp = async () => {
    if (enteredOTP.length === 6) {
      setIsLoading(true);
      try {
        let FinalEmail =
          localStorage.getItem("email")
        let result = await VerifyOTP({
          email: FinalEmail,
          otp: enteredOTP,
        });
        if (result.message == "OTP verified successfully") {
          setIsLoading(false);
          Swal.fire({
            title: "Success!!",
            text: "Successfully verified Your Email",
            icon: "success",
          });
          localStorage.removeItem("isOTP")
          setisOTPScreen(false);
          setIsLoading(false);
        } else if (result.message === "Invalid OTP") {
          Swal.fire({
            title: "Error!!",
            text: result.message,
            icon: "error",
          });
          setIsLoading(false);
        }
      } catch (error) {

        Swal.fire({
          title: "Error!!",
          text: "Entered OTP is incorrect",
          icon: "error",
        });
        setIsLoading(false);
      }
    } else {
      Swal.fire({
        title: "Error!!",
        text: "Please enter the 6 digit only",
        icon: "error",
      });
    }
  };
  if (!isOTPScreen) {
    return (
      <Box
        className="content-center"
        style={{
          width: "100vw",
          padding: "40px",
          height: "100vh",
          background: "white",
        }}
      >
        <Grid container>
          <Grid container spacing={2}>
            <Grid item xs={12} sm={12} md={7} style={{
              display: "flex",
              alignItems: "center",
              padding: "20px"
            }}>
              <div style={{ width: "100%" }}>
                <div>
                  <div className="headingCont">
                    <Typography variant="h4" className="heading">Login<span style={{ color: "orange" }}> To Your Account</span></Typography>{" "}
                  </div>
                  &nbsp;&nbsp;
                  <form autoComplete="off" onSubmit={onSubmit} className="form1">
                    <Grid container spacing={2}>
                      <Grid item xs={12} sm={12}>
                        <FormLabel sx={{ fontSize: "14px", fontWeight: "600", color: "background: rgba(13, 20, 28, 1)", marginBottom: "10px" }} >Email</FormLabel>
                        <TextField
                          autoComplete="off"
                          autoSave="false"
                          size="small"
                          autoFocus={false}
                          fullWidth
                          id="email"
                          sx={{ marginBottom: 2 }}
                          onChange={handleChange("email")}
                          type="email"
                          required
                          InputLabelProps={{ shrink: true }}
                          InputProps={{
                            autoComplete: 'off',
                            style: { backgroundColor: 'white' }
                          }}
                        />
                      </Grid>
                      <Grid item xs={12} sm={12}>
                        <FormControl fullWidth>
                          <FormLabel sx={{ fontSize: "14px", fontWeight: "600", color: "background: rgba(13, 20, 28, 1)" }}>Password</FormLabel>
                          <OutlinedInput
                            size="small"
                            value={values.password}
                            id="auth-login-password"
                            onChange={handleChange("password")}
                            required
                            type={values.showPassword ? "text" : "password"}
                            endAdornment={
                              <InputAdornment style={{ marginRight: "15px" }} position="end">
                                <IconButton
                                  edge="end"
                                  onClick={handleClickShowPassword}
                                  aria-label="toggle password visibility"
                                >
                                  {values.showPassword ? (
                                    <VisibilityIcon />
                                  ) : (
                                    <VisibilityOffIcon />
                                  )}
                                </IconButton>
                              </InputAdornment>
                            }
                          />
                        </FormControl>
                      </Grid>
                    </Grid>


                    <Box
                      sx={{
                        mb: 2,
                        display: "flex",
                        alignItems: "center",
                        flexWrap: "wrap",
                        justifyContent: "space-between",
                        fontWeight: '600',
                        fontSize: '14px',
                      }}
                    >
                      <a onClick={() => navigate("/forgot-password")} className="cursor-pointer text-[black]">
                        Forgot Password?
                      </a>
                    </Box>

                    {/* Login buttons */}
                    <div className="w-full flex flex-col my-2">
                      <div className="w-full">
                        {/* Login Button  */}
                        <button
                          type="button"
                          className="w-full bg-orange-400 py-3 rounded-lg transition duration-200 mb-4 text-white text-xl font-semibold md:text-base sm:text-base"
                          onClick={() => onSubmit()}
                          disabled={isLoading}
                        >
                          {isLoading ? (
                            <div className="animate-spin bg-center rounded-full h-5 w-5 border-t-2 border-white"></div>
                          ) : (
                            "Login"
                          )}
                        </button>
                      </div>

                      <div className="w-full flex flex-col gap-4 items-center justify-evenly sm:flex-row">
                        {/* Google Login Button  */}
                        <button
                          type="button"
                          className="w-full sm:w-1/2 py-3 bg-orange-400 rounded-lg flex items-center justify-center transition duration-200 text-white text-xl font-semibold md:text-base sm:text-base"
                          onClick={() => handleGoogleSignin()}
                          disabled={isGoogleSigninLoading}
                        >
                          {isGoogleSigninLoading ? (
                            <div className="animate-spin bg-center rounded-full h-5 w-5 border-t-2 border-white"></div>
                          ) : (
                            <span className="flex flex-row justify-center">
                              <span className="mr-2"><FcGoogle size={22} /> </span>
                              <span>Sign in with Google</span>
                            </span>
                          )}
                        </button>

                        {/* Apple Sign-in Button  */}
                        <button
                          type="button"
                          className="w-full sm:w-1/2 py-3 bg-black rounded-lg flex items-center justify-center transition duration-200 text-white text-xl font-medium md:text-base sm:text-base"
                          onClick={() => handleAppleSignin()}
                          disabled={isAppleSigninLoading}
                        >
                          {isAppleSigninLoading ? (
                            <div className="animate-spin bg-center rounded-full h-5 w-5 border-t-2 border-white"></div>
                          ) : (
                            <span className="flex flex-row justify-center">
                              <span className="mr-2">
                                <FaApple size={22} />
                              </span>
                              <span>
                                Sign in with Apple
                              </span>
                            </span>
                          )}
                        </button>
                      </div>
                    </div>


                    {/*
                    <Divider sx={{ marginBottom: 2, fontSize: "14px", fontWeight: "500" }}>OR</Divider>
                  */}


                    <Typography variant="h6" sx={{
                      fontWeight: '500',
                      fontSize: '14px',
                    }} component="div" >Don't have an account yet?<span>
                        <a onClick={() => navigate("/sign-up")} className="cursor-pointer text-[#E67E22]"> Sign up</a>
                      </span></Typography>
                    <Typography sx={{
                      mb: 2,
                      fontWeight: '500',
                      fontSize: '14px',
                    }} variant="h6" component="div"><span>
                        <a onClick={() => navigate("/admin-login")} className="cursor-pointer text-[#E67E22]">Login as an admin</a>
                      </span>
                    </Typography>
                  </form>
                </div>
              </div>

            </Grid>
            <Grid item xs={12} sm={12} md={5} >
              <SlickCarousel />
            </Grid>
          </Grid>
        </Grid>
      </Box>
    );
  } else {
    return (
      <Box
        className="content-center"
        style={{
          width: "100vw",
          padding: "40px",
          height: "100vh", // 100% of the viewport height
          background: "white", // Replace 'your-background-image.jpg' with the path to your background image
          // background: `url(${Background}) center no-repeat`, // Replace 'your-background-image.jpg' with the path to your background image
          // backgroundSize: "100% 100%",
        }}
      >
        <Grid container>
          <Grid container spacing={2}>
            <Grid item xs={12} sm={7} style={{
              display: "flex",
              // justifyContent: "center",
              // flexDirection: "column",
              padding: "50px",
              alignItems: "center"
            }}>
              <div style={{ width: "100%" }}>
                <div className="form1">
                  <Box sx={{ marginBottom: 2, marginTop: 2 }}>
                    <Typography variant="h6" component="div">
                      An OTP has been sent to your email, please enter it below to verify your email
                    </Typography>
                  </Box>
                  &nbsp;&nbsp;
                  <Grid container spacing={2}>
                    <Grid item xs={12} sm={12}>
                      <FormLabel sx={{ fontSize: "14px", fontWeight: "600", color: "background: rgba(13, 20, 28, 1)", marginBottom: "10px" }} >OTP</FormLabel><br />
                      <TextField
                        fullWidth
                        size="small"
                        variant="outlined"
                        type="number"
                        value={enteredOTP}
                        onChange={(event) => setenteredOTP(event.target.value)}
                      />
                    </Grid>
                    <Grid item xs={12} sm={6}>
                      {isVerifyOTP &&
                        <LoadingButton
                          variant="contained"
                          fullWidth
                          sx={{
                            marginTop: "14px",
                            // color: "white",
                            // backgroundColor: "rgb(145, 85, 253)",
                            color: "white",
                            backgroundColor: "#FFA654",
                            '&:hover': {
                              backgroundColor: '#FFA654', // Change to your desired hover color
                            },
                          }}
                          onClick={ResendAPI}
                          loading={isLoading}
                          loadingPosition="start"
                        >
                          Resend OTP
                        </LoadingButton>
                      }
                    </Grid>
                    &nbsp;
                    <Grid item xs={12} sm={6}>
                      <LoadingButton
                        variant="contained"
                        fullWidth
                        sx={{
                          marginTop: "14px",
                          // color: "white",
                          // backgroundColor: "rgb(145, 85, 253)",
                          color: "white",
                          backgroundColor: "#FFA654",
                          '&:hover': {
                            backgroundColor: '#FFA654', // Change to your desired hover color
                          },
                        }}
                        onClick={verifyOtp}
                        loading={isLoading}
                        loadingPosition="start"
                      >
                        Verify Otp
                      </LoadingButton>
                    </Grid>

                  </Grid>
                </div>
              </div>
            </Grid>
            <Grid item xs={12} sm={5}>
              <SlickCarousel />
            </Grid>
          </Grid>
        </Grid>
      </Box>
    );
  }
};

export default LoginNewPage;