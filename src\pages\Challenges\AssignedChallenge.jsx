import { Card, CardContent } from '@mui/material'
import React from 'react'
import { getAssignedChallenges } from '../../API/api-endpoint'
import { useEffect } from 'react'
import { Table } from 'antd'
import { useState } from 'react'

const AssignedChallenge = () => {
    const [allAssignedList, setAllAssignedList] = useState()

    useEffect(() => {
        fetchAssignedChallenge()
    }, [])

    const fetchAssignedChallenge = async () => {
        const response = await getAssignedChallenges()
        setAllAssignedList(response)
        console.log("response", response);
    }


    const columns = [
        {
            title: <b>Activity Name</b>,
            dataIndex: "activity",
            key: "activity",
            render: (activity) => activity.activity_name,
        },
        {
            title: <b>Distance</b>,
            dataIndex: "quota",
            key: "quota",

        },
        {
            title: <b>Completed Distance</b>,
            dataIndex: `completed_quota`,
            key: "completed_quota",

        },
    ];
    return (
        <div>
            <h1 style={{ margin: "1% 4% 0% 2%", fontSize: "24px",color:"#E67E22" }}><strong>Challenge Details:</strong></h1>
           
                    {allAssignedList?.map((assigned) => {
                        return (
                            <Card sx={{ minWidth: 275, margin: "1% 4% 0% 2%", boxShadow: "rgba(60, 64, 67, 0.3) 0px 1px 2px 0px, rgba(60, 64, 67, 0.15) 0px 2px 6px 2px" }}>
                            <CardContent>
                            <div className='flex'><div className=' font-bold mb-2'>Challenge ID:</div>   &nbsp;&nbsp; <div>{assigned?.challenge?.id}</div></div>
                            <div className='flex'><div className=' font-bold mb-2'>User Name :</div> &nbsp;&nbsp;<div>{`${assigned?.user?.firstname} ${assigned?.user?.lastname}`}</div> </div>
                            <div className='flex'> <div className=' font-bold mb-2'>Challenge Name: </div> <div>{assigned?.challenge?.challengeName}</div></div>
                            <div><span className='font-bold mb-2'>Challenge Description:  </span>  &nbsp;&nbsp;<span>{assigned?.challenge?.challengeDescription}</span></div>
                            <div className='flex'> <div className=' font-bold mb-2'>Challenge Duration (in days): </div> &nbsp;&nbsp; {assigned?.challenge?.challengeDuration} Days</div>
                            <div className='flex'> <div className=' font-bold mb-2'>Challenge Start Date: </div> &nbsp;&nbsp; {assigned?.challenge?.challengeStartDate}</div>
                            <div className='flex'> <div className=' font-bold mb-2'>Challenge End Date:</div> &nbsp;&nbsp; {assigned?.challenge?.challengeEndDate}</div>
                            {/*
                            <div className='flex'> <div className=' font-bold mb-2'>Training Block:  </div> &nbsp;&nbsp; {assigned?.challenge?.trainingBlock}</div>
                            <div className='flex'> <div className=' font-bold mb-2'>Challenge Level:</div> &nbsp;&nbsp; {assigned?.challenge?.challengeLevel}</div>
                            
                        */}
                            <div className='flex'> <div className=' font-bold mb-2'>Challenge Points: </div>  &nbsp;&nbsp;{assigned?.challenge?.challengePoints}</div>
                            <div className='flex'> <div className=' font-bold mb-2'>Challenge Activity Group: </div>  &nbsp;&nbsp;{assigned?.challenge?.activityGroup?.activity_group}</div>
                            <div className='flex'> <div className=' font-bold mb-2'>Challenge Activity Track: </div>  &nbsp;&nbsp;{assigned?.challenge?.activityTrack?.activity_track}</div>
                            <div className='flex'> <div className=' font-bold mb-2'>Challenge Status:</div> &nbsp;&nbsp;<div>{assigned?.completed?"Completed":"Pending"}</div></div>
&nbsp;
                            <div>
                                <Table
                                    columns={columns}
                                    dataSource={assigned?.userTargets}
                                    pagination={true}
                                />
                            </div>
                            </CardContent></Card>
                        )
                    })}

                
        </div>
    )
}

export default AssignedChallenge
