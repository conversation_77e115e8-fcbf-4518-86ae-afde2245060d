import React from 'react'

export default function SeenUnseenTick({ isSeen }) {
    return <>
        {!isSeen ? <img style={{
            // position: 'absolute',
            // bottom: 0,
            // right: 0,
            // marginRight: '1.7rem',
            marginBottom: "-5px",
            marginLeft: '5px'
        }}
            height={10.8}
            src="/images/msgIcons/tick.png"
            alt="tick" />
            : <img style={{
                // position: 'absolute',
                // bottom: 0,
                // right: 0,
                // marginRight: '1.5rem',
                marginBottom: "-5px",
                marginLeft: '5px'
            }}
                height={15}
                src="/images/msgIcons/double-tick.png"
                alt="tick" />
        }
    </>
}
