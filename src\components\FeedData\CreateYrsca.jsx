import {
    Chip,
    FormControl,
    FormLabel,
    Grid,
    MenuItem,
    OutlinedInput,
    Select,
    TextField,
  } from "@mui/material";
  import { <PERSON><PERSON>, Modal } from "antd";
  import React, { useEffect, useState } from "react";
  import {
    CreatePrograms,
    CreateZonesClasification,
    createGoalNameYTA,
    createGoalsdata,
    getAllActivityData,
    getAllGoalNameForYTA,
    getAllPrograms,
    getAlllevels,
    updateGoalNameYTA,
    updateGoalsdata,
    updatePrograms,
    updateZonesClasification,
    weeklyFeedDataPattern,
    weeklyFeedDataProgram,
  } from "../../API/api-endpoint";
  import { useFormik } from "formik";
  import Swal from "sweetalert2";
  import SlickCarousel from "../../pages/SlickCarousel";
  const scoreData = [1, 2, 3, 4, 5];
  const CreateYrsca = ({
    fetchReport,
    setShowAssesmentModal,
    showAssesmentModal,
    editData,
    setEditData,
    ActivityName,
  }) => {
    const [programList, setProgramList] = useState([]);
    const [goalList, setGoalList] = useState([]);
  
    console.log("editData", editData);
    const formik = useFormik({
      initialValues: {
        activity_id: "",
        goal: "",
        week: "",
        first: "",
        second: "",
        fourth: "",
        third: "",
        fifth: "",
        sixth: "",
        seventh: "",
        total: "",
      },
      validate: (values) => {
        const errors = {};
        if (!values.goal) {
          errors.goal = "Goal name is required";
        }
        if (!values.activity_id) {
          errors.activity_id = "Activity name is required";
        }
        if (!values.week) {
          errors.week = "Week is required";
        }
        if (!values.first) {
          errors.first = "First is required";
        }
        if (!values.second) {
          errors.second = "Second is required";
        }
        if (!values.third) {
          errors.third = "Third is required";
        }
        if (!values.fourth) {
          errors.fourth = "Fourth is required";
        }
        if (!values.fifth) {
          errors.fifth = "Fifth is required";
        }
        if (!values.sixth) {
          errors.sixth = "Sixth is required";
        }
        if (!values.seventh) {
          errors.seventh = "Seventh is required";
        }
        // Repeat the pattern for other fields...
        if (!values.total) {
          errors.total = "Total is required";
        }
        return errors;
      },
      // validationSchema: {},
      onSubmit: (values, { resetForm }) => {
        handleSubmitAssesmentForm(values, resetForm);
      },
    });
    console.log("formik", formik?.values);
    const getAllProgramsData = async () => {
      const response = await getAllActivityData();
      let filterData = response?.rows?.filter(
        (ele) => ele.activity_name == ActivityName
      );
      console.log("response", filterData[0]?.id);
      setProgramList(filterData);
    };
    const getAllGoalsData = async () => {
      const response = await getAllGoalNameForYTA();
      console.log("response", response);
      setGoalList(response);
    };
    useEffect(() => {
      if (ActivityName) {
        getAllProgramsData();
      }
      getAllGoalsData();
    }, [ActivityName]);
  
    const handleSubmitAssesmentForm = async (data, resetForm) => {
      let response = "";
      if (editData?.id) {
        response = await updateGoalsdata(data);
      } else {
        response = await createGoalsdata(data);
        console.log("response", response);
      }
      if (response?.status) {
        Swal.fire({
          title: "Success",
          text: response.message,
          icon: "success",
        });
        setShowAssesmentModal(false);
        fetchReport(ActivityName);
        resetForm();
        setEditData({});
        formik?.setValues({
          week: "",
          first: "",
          second: "",
          fourth: "",
          third: "",
          fifth: "",
          sixth: "",
          seventh: "",
          total: "",
        });
      } else {
        Swal.fire({
          title: "Error",
          text: response.message,
          icon: "error",
        });
      }
      console.log("response", response);
    };
    useEffect(() => {
      if (editData?.id) {
        const { srID, ...data } = editData;
        console.log("data", data);
        formik?.setValues(data);
        formik?.setFieldValue("activity_id", data?.activity?.id);
      }
    }, [editData?.id]);
    return (
      <Modal
        width={1200}
        open={showAssesmentModal}
        onCancel={() => {
          setShowAssesmentModal(false);
          setEditData({});
          formik.resetForm();
          formik?.setValues({
            week: "",
            first: "",
            second: "",
            fourth: "",
            third: "",
            fifth: "",
            sixth: "",
            seventh: "",
            total: "",
          });
        }}
        footer={
          <div></div>
          //   loading={isLoading}
        }
      >
        <div className="headingCont">
          <span className="heading">{editData?.id ? "Edit " : "Create"}</span>{" "}
          <span className="orange heading">{`${ActivityName} Goal`}</span>
        </div>
        {/* <h1>{editData ? editData.challengeId : values.challengeId}</h1> */}
        <div className="parentCont">
          <form className="form1" onSubmit={formik.handleSubmit}>
            <Grid container >
            <Grid  className="mbtm"  item xs={12} sm={10.8}>
               
                <FormLabel>Activity Name<span className="text-[red]">*</span></FormLabel>
  
                <TextField
                  fullWidth
                  size="small"
                  select
                  name="activity_id"
                  value={
                    formik?.values?.activity_id ? formik?.values?.activity_id : ""
                  }
                  onChange={formik.handleChange}
                  error={formik.touched.activity_id && formik.errors.activity_id}
                  helperText={
                    formik.touched.activity_id && formik.errors.activity_id
                  }
                  id="form-layouts-separator-select"
                  labelId="form-layouts-separator-select-label"
                  input={<OutlinedInput id="select-multiple-language" />}
                >
                  <MenuItem value={""} disabled>
                    Select Activity
                  </MenuItem>
                  {programList?.map((value, index) => {
                    return (
                      <MenuItem value={value?.id}>
                        {value?.activity_name}
                      </MenuItem>
                    );
                  })}
                </TextField>
              </Grid>
              <Grid  className="mbtm"  item xs={12} sm={10.8}>
               
                <FormLabel>Goal Name<span className="text-[red]">*</span></FormLabel>
  
                <TextField
                  fullWidth
                  size="small"
                  select
                  SelectProps={{
                    MenuProps: {
                      PaperProps: {
                        style: {
                           scrollbarColor:"#E67E22 white",
                           scrollbarWidth:"thin"
                        },
                      },
                    },
                  }}
                  name="goal"
                  value={formik?.values?.goal}
                  onChange={formik.handleChange}
                  error={formik.touched.goal && formik.errors.goal}
                  helperText={formik.touched.goal && formik.errors.goal}
                  id="form-layouts-separator-select"
                  labelId="form-layouts-separator-select-label"
                  input={<OutlinedInput id="select-multiple-language" />}
                >
                  <MenuItem value={""} disabled>
                    Select Name
                  </MenuItem>
                  {goalList?.map((value, index) => {
                    return (
                      <MenuItem value={value?.id}>{value?.goalname}</MenuItem>
                    );
                  })}
                </TextField>
              </Grid>
              <Grid className="marbot" container spacing={3}>
                <Grid item xs={12} sm={5.5}>
                <FormLabel>Week<span className="text-[red]">*</span></FormLabel>
  
                <TextField
                  fullWidth
                  placeholder="Week"
                  size="small"
                  type="number"
                  name="week"
                  value={formik?.values?.week}
                  onChange={formik.handleChange}
                  error={formik.touched.week && formik.errors.week}
                  helperText={formik.touched.week && formik.errors.week}
                />
              </Grid>
  
              <Grid item xs={12} sm={5.5}>
                <FormLabel>First<span className="text-[red]">*</span></FormLabel>
  
                <TextField
                  fullWidth
                  placeholder="First"
                  size="small"
                  type="number"
                  name="first"
                  value={formik?.values?.first}
                  onChange={formik.handleChange}
                  error={formik.touched.first && formik.errors.first}
                  helperText={formik.touched.first && formik.errors.first}
                />
              </Grid>
              </Grid>
              <Grid className="marbot" container spacing={3}>
                <Grid item xs={12} sm={5.5}>
                <FormLabel>Second<span className="text-[red]">*</span></FormLabel>
  
                <TextField
                  fullWidth
                  placeholder="Second"
                  size="small"
                  type="number"
                  name="second"
                  value={formik?.values?.second}
                  onChange={formik.handleChange}
                  error={formik.touched.second && formik.errors.second}
                  helperText={formik.touched.second && formik.errors.second}
                />
              </Grid>
              <Grid item xs={12} sm={5.5}>
                <FormLabel>Third<span className="text-[red]">*</span></FormLabel>
  
                <TextField
                  fullWidth
                  placeholder="Third"
                  size="small"
                  type="number"
                  name="third"
                  value={formik?.values?.third}
                  onChange={formik.handleChange}
                  error={formik.touched.third && formik.errors.third}
                  helperText={formik.touched.third && formik.errors.third}
                />
              </Grid>
              </Grid>
              <Grid className="marbot" container spacing={3}>
                <Grid item xs={12} sm={5.5}>
                <FormLabel>Fourth<span className="text-[red]">*</span></FormLabel>
  
                <TextField
                  fullWidth
                  placeholder="Fourth"
                  size="small"
                  type="number"
                  name="fourth"
                  value={formik?.values?.fourth}
                  onChange={formik.handleChange}
                  error={formik.touched.fourth && formik.errors.fourth}
                  helperText={formik.touched.fourth && formik.errors.fourth}
                />
              </Grid>
              <Grid item xs={12} sm={5.5}>
                <FormLabel>Fifth<span className="text-[red]">*</span></FormLabel>
  
                <TextField
                  fullWidth
                  placeholder="Fifth"
                  size="small"
                  type="number"
                  name="fifth"
                  value={formik?.values?.fifth}
                  onChange={formik.handleChange}
                  error={formik.touched.fifth && formik.errors.fifth}
                  helperText={formik.touched.fifth && formik.errors.fifth}
                />
              </Grid>
              </Grid>
              <Grid className="marbot" container spacing={3}>
                <Grid item xs={12} sm={5.5}>
                <FormLabel>Sixth<span className="text-[red]">*</span></FormLabel>
  
                <TextField
                  fullWidth
                  placeholder="Sixth"
                  size="small"
                  type="number"
                  name="sixth"
                  value={formik?.values?.sixth}
                  onChange={formik.handleChange}
                  error={formik.touched.sixth && formik.errors.sixth}
                  helperText={formik.touched.sixth && formik.errors.sixth}
                />
              </Grid>
              <Grid item xs={12} sm={5.5}>
                <FormLabel>Seventh<span className="text-[red]">*</span></FormLabel>
  
                <TextField
                  fullWidth
                  placeholder="Seventh"
                  size="small"
                  type="number"
                  name="seventh"
                  value={formik?.values?.seventh}
                  onChange={formik.handleChange}
                  error={formik.touched.seventh && formik.errors.seventh}
                  helperText={formik.touched.seventh && formik.errors.seventh}
                />
              </Grid>
              </Grid>
              <Grid className="marbot" container spacing={3}>
                <Grid item xs={12} sm={5.5}>
                <FormLabel>Total<span className="text-[red]">*</span></FormLabel>
  
                <TextField
                  fullWidth
                  placeholder="Total"
                  size="small"
                  type="number"
                  name="total"
                  value={formik?.values?.total}
                  onChange={formik.handleChange}
                  error={formik.touched.total && formik.errors.total}
                  helperText={formik.touched.total && formik.errors.total}
                />
              </Grid>
              </Grid>
              <Grid item xs={12} sm={5.5}>
                <Button
                  className="btn"
                  key="submit"
                  type="primary"
                  onClick={() => formik.handleSubmit()}
                >
                  Submit
                </Button>
              </Grid>
            </Grid>
            
          </form>
          <div className="slick-container">
            <SlickCarousel />
          </div>
        </div>
      </Modal>
    );
  };
  export default CreateYrsca;
  