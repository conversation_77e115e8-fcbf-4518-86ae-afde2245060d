import { IconPlus } from '@tabler/icons'
import dayjs from 'dayjs'
import React, { useContext, useEffect, useState } from 'react'
import { useParams } from 'react-router-dom'
import GlobalContext from '../context/GlobalContext'

const Day = ({ day, key, rowIdx }) => {
  const {
    setShowEventModal,
    setDaySelected,
    savedEvents,
    setSelectedEvent,
    dispatchCalEvent,
    monthIndex,
    // selectedEvent,
    setShowLibraryModal,
    workoutMode
  } = useContext(GlobalContext)

  const [dayEvents, setDayEvents] = useState([])

  // useEffect(() => {
  //   console.log('dayEvent', dayEvents)
  //   console.log('Day', day)
  //   console.log('Monyj', monthIndex)
  // }, [day, monthIndex])

  const getCurrentDayClass = () => {
    return day.format('DD-MM-YY') === dayjs().format('DD-MM-YY')
      ? 'bg-yellow-500 text-white rounded-full w-7'
      : 'text-black'
  }

  const handleDragStart = (e, plan) => {
    // console.log('Drag started....')
    e.dataTransfer.setData('id', plan.id)
    e.dataTransfer.setData('createdDate', plan.createdDate)
    e.dataTransfer.setData('name', plan.name)
    e.dataTransfer.setData('description', plan.description)
    e.dataTransfer.setData('actualDistance', plan.actualDistance)
    e.dataTransfer.setData('actualDuration', plan.actualDuration)
    e.dataTransfer.setData('plannedDistance', plan.plannedDistance)
    e.dataTransfer.setData('plannedDuration', plan.plannedDuration)
  }

  const handleDragOver = (e) => {
    e.preventDefault()
    console.log('Dragging over')
  }

  const { id } = useParams()

  const handleDropped = async (e, day) => {
    e.preventDefault()

    const planId = e.dataTransfer.getData('id')
    const createdDate = e.dataTransfer.getData('createdDate')
    const name = e.dataTransfer.getData('name')
    const description = e.dataTransfer.getData('description')
    // const actualDistance = e.dataTransfer.getData('actualDistance')
    // const actualDuration = e.dataTransfer.getData('actualDuration')
    const plannedDistance = e.dataTransfer.getData('plannedDistance')
    const plannedDuration = e.dataTransfer.getData('plannedDuration')

    const workoutName = e.dataTransfer.getData('workoutName')
    const workoutDesc = e.dataTransfer.getData('workoutDesc')
    const workoutDistance = e.dataTransfer.getData('workoutDistance')
    const workoutDuration = e.dataTransfer.getData('workoutDuration')

    console.log('createdDate', createdDate.slice(0, 10))

    console.log('day', day)
    console.log('name', e.dataTransfer.getData('workoutName'))

    const bearerToken = 'qmmvofrilpsj59rctkn0ivseuil1jji5'

    const date = dayjs(day).format('YYYY-MM-DD')

    await fetch(
      `https://app.yoska.in/kona-coach/api/athletes/${id}/plan/items?date=${date}`,
      {
        method: 'POST',
        headers: {
          Authorization: `Bearer ${bearerToken}`,
          Accept: 'application/json',
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          action: 'saved',
          activityId: 7,
          actualDistance: 0,
          actualDuration: `0:0:0`,
          createdByUserId: 340,
          createdByUserType: 'coach',
          description: workoutDesc ? `${workoutDesc}` : `${description}`,
          hideWorkout: false,
          isreviewed: false,
          moodMessageTemplateId: 0,
          name: workoutName ? workoutName : name,
          plannedDistance: workoutDistance
            ? `${workoutDistance}`
            : `${plannedDistance}`,
          plannedDuration: workoutDuration
            ? `${workoutDuration}`
            : `${plannedDuration}`,
          plannedUOMId: 8,
          savedBy: 'coach',
          skipWorkout: false,
          unitOfMeasureId: 8
        })
      }
    )
      .then((response) => {
        // if (response.status === 200) {
        //   window.location.reload()
        // } else {
        //   alert('Please fill up all the fields')
        // }
        return response.json()
      })
      .then((data) => {
        console.log('Workout dropped', data)
        // dispatchCalEvent({ type: 'push', payload: addPlan })
        if (workoutMode) {
          window.location.reload()
        } else {
          fetch(
            `https://app.yoska.in/kona-coach/api/athletes/${id}/plan/items/${planId}?date=${createdDate.slice(
              0,
              10
            )}`,
            {
              method: 'DELETE',
              headers: {
                Authorization: `Bearer ${bearerToken}`,
                Accept: 'application/json',
                'Content-Type': 'application/json'
              }
            }
          )
            .then((response) => {
              if (response.status === 204) {
                window.location.reload()
              } else {
                alert('Data not deleted')
              }
              return response.text()
            })
            .then((data) => {
              console.log('Workout deleted', data)
              // dispatchCalEvent({
              //   type: 'delete',
              //   payload: selectedEvent
              // })
            })
            .catch((error) => {
              console.error(error)
            })
        }
      })
      .catch((error) => {
        console.error(error)
      })
  }

  const handleLibrary = () => {
    setDaySelected(day)
    setShowLibraryModal(true)
  }

  useEffect(() => {
    const events = savedEvents.map((item) =>
      item.filter((evt) => evt.day === day.format('DD-MM-YY'))
    )
    // console.log('events', events)
    setDayEvents(events)
  }, [day, savedEvents])

  return (
    <div className='border border-gray-200 flex flex-col'>
      <header
        className={`flex flex-col items-center ${
          day.$M === monthIndex ? 'text-slate-800' : 'text-slate-400'
        }`}
      >
        {rowIdx === 0 && (
          <p className='text-sm mt-1 font-medium text-center'>
            {day.format('ddd').toUpperCase()}
          </p>
        )}
        <p
          className={`text-sm p-1 my-1 text-center ${
            day.$M === monthIndex ? 'text-slate-800' : 'text-slate-400'
          } ${getCurrentDayClass()}`}
        >
          {day.format('DD')}
        </p>
      </header>
      <div className='absolute' onClick={handleLibrary}>
        <IconPlus color='darkgray' className='cursor-pointer' />
      </div>
      <div
        onDragOver={(e) => handleDragOver(e)}
        onDrop={(e) => handleDropped(e, day)}
        className='flex-1 cursor-pointer'
        onClick={() => {
          setDaySelected(day)
          dispatchCalEvent({ type: 'push', payload: [] })
          setShowEventModal(true)
        }}
      >
        {dayEvents.map((evt, idx) => (
          <div key={idx}>
            {evt.map((item, i) => (
              <div key={i}>
                {item.title.map((plan, i) => (
                  <div
                    draggable
                    onDragStart={(e) => handleDragStart(e, plan)}
                    key={i}
                    className={`${
                      plan.completed === true
                        ? 'bg-green-200'
                        : 'bg-indigo-200/80'
                    } ${
                      evt.length === 0 && 'hidden'
                    } p-2 mr-6 font-medium text-black text-sm rounded mb-1`}
                    onClick={() => setSelectedEvent(plan)}
                  >
                    <p>{plan.name}</p>
                    <p>
                      {plan.plannedDuration} | {plan.plannedDistance}
                    </p>
                  </div>
                ))}
              </div>
            ))}
          </div>
        ))}
      </div>
    </div>
  )
}

export default Day
