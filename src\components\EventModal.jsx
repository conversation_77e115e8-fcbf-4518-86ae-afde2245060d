import { DatePicker } from '@mantine/dates'
import dayjs from 'dayjs'
import React, { useContext, useEffect, useState } from 'react'
import { useParams } from 'react-router-dom'
import GlobalContext from '../context/GlobalContext'

export default function EventModal({ userId }) {
  const {
    setShowEventModal,
    daySelected,
    setDaySelected,
    dispatchCalEvent,
    selectedEvent,
    setSelectedEvent
  } = useContext(GlobalContext)

  const [value, setValue] = useState(new Date(daySelected))
  const [title, setTitle] = useState(selectedEvent ? selectedEvent.name : '')
  const [description, setDescription] = useState(
    selectedEvent ? selectedEvent.description : ''
  )
  const [plannedDuration, setPlannedDuration] = useState(
    selectedEvent ? selectedEvent.plannedDuration : ''
  )
  const [plannedDistance, setPlannedDistance] = useState(
    selectedEvent ? selectedEvent.plannedDistance : ''
  )
  const [actualDuration, setActualDuration] = useState(
    selectedEvent ? selectedEvent.actualDuration : ''
  )
  const [actualDistance, setActualDistance] = useState(
    selectedEvent ? selectedEvent.actualDistance : ''
  )

  const { id } = useParams()

  const handleDateChange = (e) => {
    console.log(e)
    setDaySelected(e)
    setValue(e)
  }

  useEffect(() => {
    console.log('daySelected', daySelected)
  }, [daySelected])

  const handleAdd = (e) => {
    e.preventDefault()
    const bearerToken = 'qmmvofrilpsj59rctkn0ivseuil1jji5'

    const date = dayjs(daySelected).format('YYYY-MM-DD')

    const calendarEvent = {
      action: 'saved',
      activityId: 7,
      actualDistance: `${actualDistance ? actualDistance : 0}`,
      actualDuration: `${actualDuration ? actualDuration : '0:0:0'}`,
      createdByUserId: 138,
      createdByUserType: 'coach',
      description: `${description}`,
      hideWorkout: false,
      isreviewed: false,
      moodMessageTemplateId: 0,
      name: title,
      plannedDistance: `${plannedDistance ? plannedDistance : 0}`,
      plannedDuration: `${plannedDuration ? plannedDuration : '0:0:0'}`,
      plannedUOMId: 8,
      savedBy: 'coach',
      skipWorkout: false,
      unitOfMeasureId: 8
    }

    fetch(
      `https://app.yoska.in/kona-coach/api/athletes/${id}/plan/items?date=${date}`,
      {
        method: 'POST',
        headers: {
          Authorization: `Bearer ${bearerToken}`,
          Accept: 'application/json',
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(calendarEvent)
      }
    )
      .then((response) => {
        if (response.status === 200) {
          window.location.reload()
        } else {
          alert('Please fill up all the fields')
        }
        return response.json()
      })
      .then((data) => {
        console.log('Workout Created', data)
        dispatchCalEvent({ type: 'push', payload: calendarEvent })
      })
      .catch((error) => {
        console.error(error)
      })
  }

  const handleUpdate = (e) => {
    e.preventDefault()
    const bearerToken = 'qmmvofrilpsj59rctkn0ivseuil1jji5'

    const date = dayjs(daySelected).format('YYYY-MM-DD')

    console.log('id', selectedEvent.id)

    const updatedEvent = {
      id: selectedEvent.id,
      libraryWorkoutReferenceId: 0,
      activityId: '3',
      subActivityId: null,
      description: `${description}`,
      actualDuration: actualDuration,
      actualTimeOfMeal: '0:0:0',
      plannedAdherance: null,
      actualAdherance: null,
      name: title,
      plannedDistance: plannedDistance,
      actualDistance: actualDistance,
      actualValue: 0,
      plannedValue: 0,
      unitOfMeasureId: 8,
      plannedDuration: plannedDuration,
      plannedTimeOfMeal: '0:0:0',
      planTemplateWorkoutId: selectedEvent.id,
      comments: [],
      moodMessageTemplateId: 0,
      createdByUserType: 'coach',
      createdByUserId: userId,
      plannedUOMId: 8,
      action: 'completed',
      savedBy: 'coach',
      isReviewed: null,
      completed: false,
      skipWorkout: false
    }

    fetch(
      `https://app.yoska.in/kona-coach/api/athletes/${id}/plan/items/${selectedEvent.id}?date=${date}`,
      {
        method: 'PUT',
        headers: {
          Authorization: `Bearer ${bearerToken}`,
          Accept: 'application/json',
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(updatedEvent)
      }
    )
      .then((response) => {
        if (response.status === 200) {
          window.location.reload()
        } else {
          alert('Data not updated')
        }
        return response.json()
      })
      .then((data) => {
        console.log('Workout updated', data)
        dispatchCalEvent({ type: 'update', payload: updatedEvent })
      })
      .catch((error) => {
        console.error(error)
      })
  }

  const handleDelete = () => {
    console.log('Clicked here.')
    const bearerToken = 'qmmvofrilpsj59rctkn0ivseuil1jji5'

    const date = dayjs(daySelected).format('YYYY-MM-DD')

    // https://www.yoska.in/kona-coach/athletes/6683/plan/items/1444621?date=2023-2-25

    fetch(
      `https://app.yoska.in/kona-coach/api/athletes/${id}/plan/items/${selectedEvent.id}?date=${date}`,
      {
        method: 'DELETE',
        headers: {
          Authorization: `Bearer ${bearerToken}`,
          Accept: 'application/json',
          'Content-Type': 'application/json'
        }
      }
    )
      .then((response) => {
        if (response.status === 204) {
          window.location.reload()
        } else {
          alert('Data not deleted')
        }
        return response.json()
      })
      .then((data) => {
        console.log('Workout deleted', data)
        dispatchCalEvent({
          type: 'delete',
          payload: selectedEvent
        })
        setShowEventModal(false)
      })
      .catch((error) => {
        console.error(error)
      })
  }

  return (
    <div className='h-screen w-full fixed left-0 top-0 flex justify-center items-center'>
      <form
        onSubmit={selectedEvent ? handleUpdate : handleAdd}
        className='bg-white rounded-lg shadow-2xl w-1/4'
      >
        <div className='bg-gray-100 px-4 py-3 flex justify-between items-center'>
          <span className='material-icons-outlined text-gray-400'>
            drag_handle
          </span>
          <div className='flex items-center gap-3'>
            <div>
              {selectedEvent && (
                <span
                  onClick={handleDelete}
                  className='material-icons-outlined text-gray-400 cursor-pointer'
                >
                  delete
                </span>
              )}
            </div>
            <button
              onClick={() => {
                setShowEventModal(false)
                setSelectedEvent()
              }}
            >
              <span className='material-icons-outlined text-gray-400'>
                close
              </span>
            </button>
          </div>
        </div>
        <div className='px-6 py-4 flex w-full flex-col items-start gap-4'>
          <div className='w-full flex flex-row myDay-2 items-center gap-4'>
            <p className='material-icons-outlined text-gray-400'>edit</p>
            {/* <p>{daySelected.format('dddd, MMMM DD')}</p> */}
            <DatePicker
              value={value}
              className='w-full border-none'
              onChange={(e) => handleDateChange(e)}
            />
          </div>
          <div className='w-full flex flex-col gap-4'>
            <input
              type='text'
              name='title'
              placeholder='Add Title'
              value={title}
              required={true}
              className='pt-3 border-0 px-0 text-gray-800 pb-2 w-full border-b-2 border-gray-200 focus:outline-none focus:ring-0 focus:border-blue-500;'
              onChange={(e) => setTitle(e.target.value)}
            />
            <textarea
              name='description'
              id=''
              cols='30'
              rows='6'
              placeholder='Add a description'
              value={description}
              required={true}
              autoComplete='off'
              className='pt-3 border-0 px-0 text-gray-800 pb-2 w-full border-b-2 border-gray-200 focus:outline-none focus:ring-0 focus:border-blue-500;'
              onChange={(e) => setDescription(e.target.value)}
            ></textarea>
            <div className='w-full flex flex-col items-start gap-4 mt-2'>
              <h2 className='text-base font-semibold'>Planned</h2>
              <div className='w-full grid grid-cols-1 md:grid-cols-2 items-start gap-4'>
                <input
                  type='text'
                  name='plannedDuration'
                  placeholder='Duration (0:0:0)'
                  value={plannedDuration}
                  autoComplete='off'
                  className='pt-3 border-0 px-0 text-gray-800 pb-2 w-full border-b-2 border-gray-200 focus:outline-none focus:ring-0 focus:border-blue-500;'
                  onChange={(e) => setPlannedDuration(e.target.value)}
                />
                <input
                  type='text'
                  name='plannedDistance'
                  placeholder='Distance (1.5 Km)'
                  value={plannedDistance}
                  autoComplete='off'
                  className='pt-3 border-0 px-0 text-gray-800 pb-2 w-full border-b-2 border-gray-200 focus:outline-none focus:ring-0 focus:border-blue-500;'
                  onChange={(e) => setPlannedDistance(e.target.value)}
                />
              </div>
            </div>
            <div className='w-full flex flex-col items-start gap-4 mt-2'>
              <h2 className='text-base font-semibold'>Actual</h2>
              <div className='w-full grid grid-cols-1 md:grid-cols-2 items-start gap-4'>
                <input
                  type='text'
                  name='plannedDuration'
                  placeholder='Duration (0:0:0)'
                  value={actualDuration}
                  autoComplete='off'
                  className='pt-3 border-0 px-0 text-gray-800 pb-2 w-full border-b-2 border-gray-200 focus:outline-none focus:ring-0 focus:border-blue-500;'
                  onChange={(e) => setActualDuration(e.target.value)}
                />
                <input
                  type='text'
                  name='plannedDistance'
                  placeholder='Distance (3 Km)'
                  value={actualDistance}
                  autoComplete='off'
                  className='pt-3 border-0 px-0 text-gray-800 pb-2 w-full border-b-2 border-gray-200 focus:outline-none focus:ring-0 focus:border-blue-500;'
                  onChange={(e) => setActualDistance(e.target.value)}
                />
              </div>
            </div>
          </div>
        </div>
        <div className='flex justify-end border-t p-3 mt-5'>
          <button
            type='submit'
            className={`${selectedEvent
              ? 'bg-orange-500 hover:bg-orange-600'
              : 'bg-blue-500 hover:bg-blue-600'
              }  px-6 py-2 rounded text-white`}
          >
            {selectedEvent ? 'Update' : 'Save'}
          </button>
        </div>
      </form>
    </div>
  )
}
