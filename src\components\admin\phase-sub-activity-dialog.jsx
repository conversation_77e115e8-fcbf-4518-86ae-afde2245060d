import { useState, useEffect } from "react";
import { Button } from "../../components/ui/button";
import {
	<PERSON><PERSON>,
	DialogContent,
	DialogHeader,
	DialogTitle,
} from "../../components/ui/dialog";
import { Label } from "../../components/ui/label";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "../../components/ui/select";
import {
	getAllActivityData,
	getAlPhaseBlockData,
	getAllworkoutData,
	getAllsubactivityData,
	createPhaseSubdata,
	updatePhaseSubdata,
} from "../../API/api-endpoint";

export function PhaseSubActivityDialog({
	open,
	onOpenChange,
	editingItem,
	onSuccess,
}) {
	console.log("PhaseSubActivityDialog props:", { open, editingItem });
	const [formData, setFormData] = useState({
		activity_id: "",
		phase_id: "",
		workout_id: "",
		subworkout_id: "",
	});
	const [activities, setActivities] = useState([]);
	const [phaseBlocks, setPhaseBlocks] = useState([]);
	const [workouts, setWorkouts] = useState([]);
	const [subworkouts, setSubworkouts] = useState([]);
	const [isLoading, setIsLoading] = useState(false);
	const [isSubmitting, setIsSubmitting] = useState(false);

	useEffect(() => {
		if (open) {
			fetchActivities();
			fetchPhaseBlocks();
			fetchWorkouts();
			fetchSubworkouts();
		}
	}, [open]);

	useEffect(() => {
		if (editingItem) {
			const activityId = editingItem.activity_id?.toString() || "";

			if (activities.length > 0) {
				const activityExists = activities.find(
					(act) => act.id.toString() === activityId
				);
				console.log("Activity exists in list:", activityExists);

				if (!activityExists && activityId) {
					console.warn(
						`Activity with id ${activityId} not found in activities list. Available IDs:`,
						activities.map((a) => a.id)
					);
				}
			}

			setFormData({
				activity_id: activityId,
				phase_id: editingItem.phase_id?.toString() || "",
				workout_id: editingItem.workout_id?.toString() || "",
				subworkout_id: editingItem.subworkout_id?.toString() || "",
			});
		} else {
			setFormData({
				activity_id: "",
				phase_id: "",
				workout_id: "",
				subworkout_id: "",
			});
		}
	}, [editingItem, open, activities, phaseBlocks]);

	const fetchActivities = async () => {
		try {
			setIsLoading(true);
			const response = await getAllActivityData();
			const activitiesData = response?.rows || [];
			setActivities(activitiesData);
		} catch (error) {
			console.error("Error fetching activities:", error);
			setActivities([]);
		} finally {
			setIsLoading(false);
		}
	};

	const fetchPhaseBlocks = async () => {
		try {
			const response = await getAlPhaseBlockData();
			setPhaseBlocks(response || []);
		} catch (error) {
			console.error("Error fetching phase blocks:", error);
			setPhaseBlocks([]);
		}
	};

	const fetchWorkouts = async () => {
		try {
			const response = await getAllworkoutData();
			setWorkouts(response || []);
		} catch (error) {
			console.error("Error fetching workouts:", error);
			setWorkouts([]);
		}
	};

	const fetchSubworkouts = async () => {
		try {
			const response = await getAllsubactivityData();
			setSubworkouts(response || []);
		} catch (error) {
			console.error("Error fetching subworkouts:", error);
			setSubworkouts([]);
		}
	};

	const handleSubmit = async (e) => {
		e.preventDefault();

		if (
			!formData.activity_id ||
			!formData.phase_id ||
			!formData.workout_id ||
			!formData.subworkout_id
		) {
			return;
		}

		try {
			setIsSubmitting(true);
			let response;

			const phaseSubData = {
				activities: parseInt(formData.activity_id),
				phase_id: parseInt(formData.phase_id),
				workout_id: parseInt(formData.workout_id),
				subworkout_id: parseInt(formData.subworkout_id),
			};

			if (editingItem?.id) {
				response = await updatePhaseSubdata({
					...phaseSubData,
					id: editingItem.id,
				});
			} else {
				response = await createPhaseSubdata(phaseSubData);
			}

			if (response?.status) {
				onOpenChange(false);
				if (onSuccess) onSuccess();
			} else {
				console.log(response?.message || "Operation failed");
			}
		} catch (error) {
			console.error("Error submitting form:", error);
		} finally {
			setIsSubmitting(false);
		}
	};

	return (
		<Dialog open={open} onOpenChange={onOpenChange}>
			<DialogContent className='sm:max-w-[425px] bg-white'>
				<DialogHeader>
					<DialogTitle className='text-orange-950'>
						{editingItem ? "Edit" : "Create"} Phase Sub-Activity
					</DialogTitle>
				</DialogHeader>

				<form onSubmit={handleSubmit} className='space-y-4'>
					<div className='space-y-2'>
						<Label htmlFor='activity_id'>Activity</Label>
						<Select
							value={formData.activity_id}
							onValueChange={(value) =>
								setFormData({ ...formData, activity_id: value })
							}
							disabled={isLoading}
						>
							<SelectTrigger className='bg-white border border-gray-300 text-gray-900'>
								<SelectValue
									placeholder={
										isLoading
											? "Loading activities..."
											: formData.activity_id &&
											  !activities.find(
													(act) =>
														act.id.toString() ===
														formData.activity_id
											  )
											? `Activity ID ${formData.activity_id} (Missing)`
											: "Select activity"
									}
									className='text-gray-900'
								/>
							</SelectTrigger>
							<SelectContent className='bg-white border border-gray-200 shadow-lg'>
								{/* Show missing activity option if editing and activity doesn't exist */}
								{formData.activity_id &&
									!activities.find(
										(act) =>
											act.id.toString() ===
											formData.activity_id
									) && (
										<SelectItem
											value={formData.activity_id}
											className='text-red-600 bg-red-50'
										>
											Activity ID {formData.activity_id}{" "}
											(Missing from database) - Click to
											keep
										</SelectItem>
									)}
								{activities.length > 0 ? (
									activities.map((activity) => (
										<SelectItem
											key={activity.id}
											value={String(activity.id)}
											className='text-gray-900 hover:bg-blue-50 hover:text-blue-900 cursor-pointer pr-3 py-2 pl-8 relative'
										>
											{activity.activity ||
												activity.activity_name}
										</SelectItem>
									))
								) : (
									<SelectItem value='no-activities' disabled>
										No activities available
									</SelectItem>
								)}
							</SelectContent>
						</Select>
					</div>

					<div className='space-y-2'>
						<Label htmlFor='phase_id'>Phase Name</Label>
						<Select
							value={formData.phase_id}
							onValueChange={(value) =>
								setFormData({ ...formData, phase_id: value })
							}
						>
							<SelectTrigger className='bg-white border border-gray-300 text-gray-900'>
								<SelectValue
									placeholder='Select phase name'
									className='text-gray-900'
								/>
							</SelectTrigger>
							<SelectContent className='bg-white border border-gray-200 shadow-lg'>
								{phaseBlocks.length > 0 ? (
									phaseBlocks.map((phase) => (
										<SelectItem
											key={phase.id}
											value={String(phase.id)}
											className='text-gray-900 hover:bg-blue-50 hover:text-blue-900 cursor-pointer pr-3 py-2 pl-8 relative'
										>
											{phase.phasename?.phase ||
												phase.phase ||
												"N/A"}
										</SelectItem>
									))
								) : (
									<SelectItem value='no-phases' disabled>
										No phase names available
									</SelectItem>
								)}
							</SelectContent>
						</Select>
					</div>

					<div className='space-y-2'>
						<Label htmlFor='workout_id'>Workout Name</Label>
						<Select
							value={formData.workout_id}
							onValueChange={(value) =>
								setFormData({ ...formData, workout_id: value })
							}
						>
							<SelectTrigger className='bg-white border border-gray-300 text-gray-900'>
								<SelectValue
									placeholder='Select workout name'
									className='text-gray-900'
								/>
							</SelectTrigger>
							<SelectContent className='bg-white border border-gray-200 shadow-lg'>
								{workouts.length > 0 ? (
									workouts.map((workout) => (
										<SelectItem
											key={workout.id}
											value={String(workout.id)}
											className='text-gray-900 hover:bg-blue-50 hover:text-blue-900 cursor-pointer pr-3 py-2 pl-8 relative'
										>
											{workout.workout || "N/A"}
										</SelectItem>
									))
								) : (
									<SelectItem value='no-workouts' disabled>
										No workouts available
									</SelectItem>
								)}
							</SelectContent>
						</Select>
					</div>

					<div className='space-y-2'>
						<Label htmlFor='subworkout_id'>Subworkout Name</Label>
						<Select
							value={formData.subworkout_id}
							onValueChange={(value) =>
								setFormData({
									...formData,
									subworkout_id: value,
								})
							}
						>
							<SelectTrigger className='bg-white border border-gray-300 text-gray-900'>
								<SelectValue
									placeholder='Select subworkout name'
									className='text-gray-900'
								/>
							</SelectTrigger>
							<SelectContent className='bg-white border border-gray-200 shadow-lg'>
								{subworkouts.length > 0 ? (
									subworkouts.map((subworkout) => (
										<SelectItem
											key={subworkout.id}
											value={String(subworkout.id)}
											className='text-gray-900 hover:bg-blue-50 hover:text-blue-900 cursor-pointer pr-3 py-2 pl-8 relative'
										>
											{subworkout.subworkout || "N/A"}
										</SelectItem>
									))
								) : (
									<SelectItem value='no-subworkouts' disabled>
										No subworkouts available
									</SelectItem>
								)}
							</SelectContent>
						</Select>
					</div>

					<div className='flex justify-end gap-2 pt-4'>
						<Button
							type='button'
							variant='outline'
							onClick={() => onOpenChange(false)}
							disabled={isSubmitting}
						>
							Cancel
						</Button>
						{console.log("Button disabled check:", {
							isSubmitting,
							activity_id: formData.activity_id,
							phase_id: formData.phase_id,
							workout_id: formData.workout_id,
							subworkout_id: formData.subworkout_id,
							shouldDisable:
								isSubmitting ||
								!formData.activity_id ||
								!formData.phase_id ||
								!formData.workout_id ||
								!formData.subworkout_id,
						})}
						<Button
							type='submit'
							className='bg-orange-500 hover:bg-orange-600 text-white'
							disabled={
								isSubmitting ||
								!formData.activity_id ||
								!formData.phase_id ||
								!formData.workout_id ||
								!formData.subworkout_id
							}
						>
							{isSubmitting
								? "Saving..."
								: editingItem
								? "Update"
								: "Create"}
						</Button>
					</div>
				</form>
			</DialogContent>
		</Dialog>
	);
}
