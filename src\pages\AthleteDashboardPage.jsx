import { IconEdit, IconTrash } from '@tabler/icons'
import React from 'react'
import Header from '../components/Header'

const AthleteDashboardPage = () => {
  const performanceSummary = [
    {
      id: 1,
      name: 'JAFAR G',
      location: '(Coimbator)',
      SubscriptionStartDate: '18-Jul-2022',
      SubscriptionEndDate: '16-Oct-2022',
      coach: '<PERSON>',
      lastFeedbackDate: '',
      upcomingRules: ''
    },
    {
      id: 2,
      name: '<PERSON><PERSON><PERSON>',
      location: '(North Goa)',
      SubscriptionStartDate: '',
      SubscriptionEndDate: '',
      coach: '<PERSON>',
      lastFeedbackDate: '',
      upcomingRules: ''
    },
    {
      id: 1,
      name: '<PERSON><PERSON>',
      location: '(Bangalore)',
      SubscriptionStartDate: '',
      SubscriptionEndDate: '',
      coach: '<PERSON>',
      lastFeedbackDate: '',
      upcomingRules: ''
    },
    {
      id: 1,
      name: '<PERSON><PERSON><PERSON>',
      location: '(Bangalore)',
      SubscriptionStartDate: '',
      SubscriptionEndDate: '',
      coach: '<PERSON>',
      lastFeedbackDate: '',
      upcomingRules: ''
    },
    {
      id: 1,
      name: '<PERSON><PERSON>',
      location: '(Mumbai)',
      SubscriptionStartDate: '',
      SubscriptionEndDate: '',
      coach: 'Neil Dsilva',
      lastFeedbackDate: '',
      upcomingRules: ''
    },
    {
      id: 1,
      name: 'Rajani Reddy',
      location: '(Hyderabad)',
      SubscriptionStartDate: '28-Feb-2022',
      SubscriptionEndDate: '12-Jun-2022',
      coach: 'Neil Dsilva',
      lastFeedbackDate: '',
      upcomingRules: ''
    }
  ]

  return (
    <>
      <Header />
      {/* Topbar */}
      <div className='p-6'>
        <div className='flex items-end gap-x-2'>
          <h1 className='font-medium text-xl'>Athlete Dashboard</h1>
        </div>
        <br />
        <div className='flex flex-col md:flex-row gap-8 items-start md:items-center mb-6'>
          <div>
            <input
              type='text'
              placeholder='Search'
              className='w-full border-b-2 py-2 rounded-md focus:outline-none text-slate-600 px-1 text-sm'
            />
          </div>
          <div>
            <select
              name='status'
              id='status'
              placeholder='Group'
              className='w-52 border-b-2 py-2 rounded-md focus:outline-none text-slate-600 text-sm'
            >
              <option value='#'>Select coach</option>
              <option value='Subscribed'>Subscribed</option>
            </select>
          </div>
          <div>
            <select
              name='status'
              id='status'
              placeholder='Group'
              className='w-52 border-b-2 py-2 rounded-md focus:outline-none text-slate-600 text-sm'
            >
              <option value='#'>Select a reminder</option>
              <option value='Subscribed'>Subscribed</option>
            </select>
          </div>
          <div>
            <button className='py-1.5 px-5 bg-blue-500 text-slate-50 rounded'>
              Remind
            </button>
          </div>
        </div>
        {/* Table */}
        <div className='w-full mb-6'>
          <div className='my-6'>
            <div className='flex flex-col'>
              <div className='overflow-x-auto shadow-md'>
                <div className='inline-block min-w-full align-middle'>
                  <div className='overflow-hidden '>
                    <table className='min-w-full divide-y divide-gray-200 table-fixed dark:divide-gray-700'>
                      <thead className='bg-gray-100 '>
                        <tr>
                          <th
                            scope='col'
                            className='py-3 px-6 text-xs font-medium tracking-wider text-left text-gray-700 uppercase dark:text-gray-400'
                          >
                            Name
                          </th>
                          <th
                            scope='col'
                            className='py-3 px-6 text-xs font-medium tracking-wider text-left text-gray-700 uppercase dark:text-gray-400'
                          >
                            Subscription Start Date
                          </th>
                          <th
                            scope='col'
                            className='py-3 px-6 text-xs font-medium tracking-wider text-left text-gray-700 uppercase dark:text-gray-400'
                          >
                            Subscription End Date
                          </th>
                          <th
                            scope='col'
                            className='py-3 px-6 text-xs font-medium tracking-wider text-left text-gray-700 uppercase dark:text-gray-400'
                          >
                            Coach/ Assistant Coach
                          </th>
                          <th
                            scope='col'
                            className='py-3 px-6 text-xs font-medium tracking-wider text-left text-gray-700 uppercase dark:text-gray-400'
                          >
                            Last Feedback Date
                          </th>
                          <th
                            scope='col'
                            className='py-3 px-6 text-xs font-medium tracking-wider text-left text-gray-700 uppercase dark:text-gray-400'
                          >
                            Upcoming Races
                          </th>
                        </tr>
                      </thead>
                      <tbody className='bg-white divide-y divide-gray-200 '>
                        {performanceSummary.map((item, index) => (
                          <tr
                            className='hover:bg-gray-100 text-slate-600'
                            key={index}
                          >
                            <td className='py-4 px-6 text-sm font-medium text-gray-900 whitespace-nowrap'>
                              <div className='text-sm font-medium flex flex-col gap-y-2'>
                                <span>{item.name}</span>
                                <span className='text-xs text-slate-600/75'>{item.location}</span>
                              </div>
                            </td>
                            <td className='py-4 px-6 text-sm font-medium text-gray-500 whitespace-nowrap'>
                              {item.SubscriptionStartDate}
                            </td>
                            <td className='py-4 px-6 text-sm font-medium text-gray-500 whitespace-nowrap'>
                              {item.SubscriptionEndDate}
                            </td>
                            <td className='py-4 px-6 text-sm font-medium text-gray-500 whitespace-nowrap'>
                              {item.coach}
                            </td>
                            <td className='py-4 px-6 text-sm font-medium text-green-500 whitespace-nowrap'>
                              {item.lastFeedbackDate}
                            </td>
                            <td className='py-4 px-6 text-sm font-medium text-left whitespace-nowrap flex gap-4 items-center'>
                              <div className='flex gap-x-6'>
                                <IconEdit size={18} color='grey' />
                                <IconTrash size={18} color='grey' />
                              </div>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  )
}

export default AthleteDashboardPage
