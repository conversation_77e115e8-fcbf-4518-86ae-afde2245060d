import { useState, useEffect } from "react";
import { But<PERSON> } from "../ui/button";
import { Input } from "../ui/input";
import { Label } from "../ui/label";
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogFooter,
	DialogHeader,
	DialogTitle,
} from "../ui/dialog";
import { createYTAdata, updateYTAdata } from "../../API/api-endpoint";
import Swal from "sweetalert2";

export function YtaGoalDialog({ open, onClose, onSuccess, editingItem }) {
	const [formData, setFormData] = useState({
		goalname: "",
		goaltable: "",
		phase: "",
	});
	const [isLoading, setIsLoading] = useState(false);

	useEffect(() => {
		if (open) {
			if (editingItem) {
				const editData = {
					goalname: editingItem.goalname || "",
					goaltable: editingItem.goaltable || "",
					phase: editingItem.phase || "",
				};
				setFormData(editData);
			} else {
				const newData = {
					goalname: "",
					goaltable: "",
					phase: "",
				};
				setFormData(newData);
			}
		}
	}, [open, editingItem]);

	const handleInputChange = (field, value) => {
		setFormData((prev) => ({
			...prev,
			[field]: value,
		}));
	};

	const handleSubmit = async (e) => {
		e.preventDefault();
		if (!formData.goalname.trim()) {
			Swal.fire({
				title: "Error",
				text: "Goal name is required",
				icon: "error",
				timer: 1800,
				showConfirmButton: false,
			});
			return;
		}

		if (!formData.goaltable.trim()) {
			Swal.fire({
				title: "Error",
				text: "Goal table is required",
				icon: "error",
				timer: 1800,
				showConfirmButton: false,
			});
			return;
		}

		if (!formData.phase.trim()) {
			Swal.fire({
				title: "Error",
				text: "Phase is required",
				icon: "error",
				timer: 1800,
				showConfirmButton: false,
			});
			return;
		}

		try {
			setIsLoading(true);

			const apiData = {
				goalname: formData.goalname.trim(),
				goaltable: formData.goaltable.trim(),
				phase: formData.phase.trim(),
			};

			if (editingItem) {
				apiData.id = editingItem.id;
			}

			const response = editingItem
				? await updateYTAdata(apiData)
				: await createYTAdata(apiData);

			if (response?.status) {
				Swal.fire({
					title: "Success",
					text:
						response.message ||
						`Goal ${
							editingItem ? "updated" : "created"
						} successfully`,
					icon: "success",
					timer: 2000,
					showConfirmButton: false,
				});
				onSuccess();
			} else {
				throw new Error(response?.message || "Operation failed");
			}
		} catch (error) {
			console.error("Error saving goal:", error);
			Swal.fire({
				title: "Error",
				text: error.message || "Failed to save goal. Please try again.",
				icon: "error",
				timer: 3000,
				showConfirmButton: false,
			});
		} finally {
			setIsLoading(false);
		}
	};

	const handleClose = () => {
		if (!isLoading) {
			onClose();
		}
	};

	return (
		<Dialog open={open} onOpenChange={handleClose}>
			<DialogContent className='sm:max-w-[500px] bg-white'>
				<DialogHeader>
					<DialogTitle>
						{editingItem ? "Edit" : "Create"} YTA Goal
					</DialogTitle>
					<DialogDescription>
						{editingItem
							? "Update the YTA goal information below."
							: "Fill in the details to create a new YTA goal."}
					</DialogDescription>
				</DialogHeader>

				<form onSubmit={handleSubmit} className='space-y-4'>
					<div className='grid gap-4'>
						<div className='space-y-2'>
							<Label
								htmlFor='goalname'
								className='text-sm font-semibold'
							>
								Goal Name
							</Label>
							<Input
								id='goalname'
								type='text'
								className='w-full text-sm'
								value={formData.goalname || ""}
								onChange={(e) =>
									handleInputChange(
										"goalname",
										e.target.value
									)
								}
								placeholder='Enter goal name'
								disabled={isLoading}
								required
								autoFocus={false}
								onFocus={(e) =>
									e.target.setSelectionRange(
										e.target.value.length,
										e.target.value.length
									)
								}
							/>
						</div>

						<div className='space-y-2'>
							<Label
								htmlFor='goaltable'
								className='text-sm font-semibold'
							>
								Goal Table
							</Label>
							<Input
								id='goaltable'
								type='text'
								className='w-full text-sm'
								value={formData.goaltable || ""}
								onChange={(e) =>
									handleInputChange(
										"goaltable",
										e.target.value
									)
								}
								placeholder='Enter goal table'
								disabled={isLoading}
								required
							/>
						</div>

						<div className='space-y-2'>
							<Label
								htmlFor='phase'
								className='text-sm font-semibold'
							>
								Phase
							</Label>
							<Input
								id='phase'
								type='text'
								className='w-full text-sm'
								value={formData.phase || ""}
								onChange={(e) =>
									handleInputChange("phase", e.target.value)
								}
								placeholder='Enter phase name'
								disabled={isLoading}
								required
							/>
						</div>
					</div>

					<DialogFooter className='gap-2'>
						<Button
							type='button'
							variant='outline'
							onClick={handleClose}
							disabled={isLoading}
						>
							Cancel
						</Button>
						<Button
							type='submit'
							disabled={isLoading}
							className='bg-orange-600 hover:bg-orange-700 text-white'
						>
							{isLoading
								? "Saving..."
								: editingItem
								? "Update Goal"
								: "Create Goal"}
						</Button>
					</DialogFooter>
				</form>
			</DialogContent>
		</Dialog>
	);
}
