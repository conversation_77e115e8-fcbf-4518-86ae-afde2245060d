import React, { useContext, useEffect, useState } from "react";
import { Link, useNavigate } from "react-router-dom";

import { Collapse, Drawer, Text } from "@mantine/core";
import {
    IconBell,
    IconCalendar,
    IconReport,
    IconSettings,
    IconUsers,
    IconX,
    IconYoga,
    IconCheck,
    IconMessage,
    IconMenu2,
    IconChevronUp,
    IconChevronDown,
    IconChecklist,
    IconServerCog,
} from "@tabler/icons";
import GlobalContext from "../context/GlobalContext";
import LOGO from "../Images/logo.png";
import { URL, getAllPrograms, getDayoptionValues, getFechAlGoals, getPersonlisation, getPrograms, getUsersProfile } from "../API/api-endpoint";
import axios from "axios";
import { Try } from "@mui/icons-material";
import { removeNullValues } from "../utils/Resubale";

const AtheleteHeader = ({setFechGoals, setIsZoneOpen,formGoalValue,setfetchDaysoptionList,setFormGoalValue,setIsProfileOpen, setCalendarModal, setIsEnrolledChallengesOpen, assignedCocahId, formik, setUserDetails, setIsEditProfile }) => {
    const navigate = useNavigate();
    const roleID = localStorage.getItem("roleID")
    const [isOpenPrfile, setIsOpenProfile] = useState(false);
    const profileRef = React.useRef();
console.log("formik",formik?.values);
    const handleProfile = async () => {
        setIsProfileOpen(true)
        setCalendarModal(false)
        setIsZoneOpen({ isOpen: false })
        setIsEnrolledChallengesOpen({ isOpen: false, assignCoachId: "" })
  let coachProgramID = localStorage.getItem("programID");
    const actProgram = await getPrograms(assignedCocahId);
    let filter = actProgram?.filter((data)=>data.active==1)
    let coachID = filter[0]?.program_id?filter[0]?.program_id:coachProgramID;
    console.log("actProgram",coachID,coachProgramID); 
    if (coachID) {
        const response = await getUsersProfile(coachID,assignedCocahId)
        let data = { ...response?.existingUserContacts, ...response?.personalization_profile, ...response?.user }
        let destructer = removeNullValues(data)
        formik.setFieldValue("height", response?.personalization_profile?.height)
        formik.setFieldValue("weight", response?.personalization_profile?.weight)
        setUserDetails(response)
        setIsEditProfile(false)
        
        formik.setValues(destructer)
        let responseOfactivity = await getPersonlisation(assignedCocahId);
        console.log("destructer",destructer,responseOfactivity);
        formik.setFieldValue("daysoption", responseOfactivity[0]?.daysoption)
        formik.setFieldValue("daysinweek", responseOfactivity[0]?.daysinweek)

        const activityName = await getAllPrograms()
        let filteractivityName = activityName?.rows?.filter((ele) => ele?.program_id == responseOfactivity[0]?.program_id)
        formik.setFieldValue("activity", filteractivityName[0]?.program_name)
        setFormGoalValue({ ...formGoalValue,goal:responseOfactivity[0].goal, daysinweek:responseOfactivity[0]?.daysinweek,activities: responseOfactivity[0]?.program_id ? responseOfactivity[0]?.program_id : 1, id: responseOfactivity[0]?.id});
      
        fetchDGoals(responseOfactivity[0]?.program_id,responseOfactivity[0].goal)
      
        if (responseOfactivity[0]?.daysinweek) {
            fetchDaysOptuon(responseOfactivity[0]?.daysinweek, responseOfactivity[0]?.program_id);
        }
    }
        
    };
    const fetchDGoals = async (id,goal) => {
        let response = await getFechAlGoals(id);
        if (!goal) {
            formik.setFieldValue("goal",response[0]?.goal)
        }
        console.log("checkng conhhsagfhag>>>>>>>>>>>>>>>>>>>",response);
    setFechGoals(response);
        setFormGoalValue({ ...formGoalValue, goal: response.goals[0]?.goal, });
      };
    const fetchDaysOptuon = async (week,activity) => {
        const response = await getDayoptionValues(
          week,
          activity?activity:formGoalValue?.activities
        );
        if (response) {
          setfetchDaysoptionList(response);
        }
      };
    const handleIsOpenEnrolledChallenge = () => {
        setIsZoneOpen({ isOpen: false })
        setIsEnrolledChallengesOpen({ isOpen: true, assignCoachId: assignedCocahId })
        setIsProfileOpen(false)
        setCalendarModal(false)
    }
    const handleIsOpenZones = () => {
        setIsEnrolledChallengesOpen({ isOpen: false})
        setIsZoneOpen({ isOpen: true, assignCoachId: assignedCocahId })
        setIsProfileOpen(false)
        setCalendarModal(false)
    }
    const fetch = async () => {
        try {
            let profileImage = `${URL}/static/public/userimages/${formik?.values?.profile}`
            let response = await axios.get(profileImage, {
                headers: {
                    "Content-Type": `application/json`,
                },
            });
            setIsOpenProfile(true)
        } catch (error) {
            setIsOpenProfile(false)
        }
    }
    const handleBackToCalendar = () => {
        setCalendarModal(true);
        setIsProfileOpen(false);
        setIsZoneOpen({ isOpen: false });
        setIsEnrolledChallengesOpen({ isOpen: false, assignCoachId: "" });
    };
    useEffect(() => {
        if (formik?.values?.profile) {
            fetch()
        } else {
            setIsOpenProfile(false)
        }
    }, [formik?.values?.profile])

    return (
        <header style={{ marginTop: "9px",backgroundColor: "#E67E22" }} className="px-2  py-2  " >
            <div className="flex gap-x-4 xl:gap-x-5 text-sm font-regular items-center">

                <div ref={profileRef}>
                    <div style={{ color: "white" }}>
                        <img
                            src={isOpenPrfile ? `${URL}/static/public/userimages/${formik?.values?.profile}` : "data:image/png;base64,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"}
                            alt="image"
                            className="w-10 h-10 rounded-full border-2 cursor-pointer"
                            onClick={handleProfile}
                        />
                        <b>{formik?.values?.selectedAthleteName || `${formik?.values?.firstname} ${formik?.values?.lastname}`}</b>
                    </div>


                </div>
                <div>
                    <button
                        className="flex flex-cols items-center gap-x-1"
                        onClick={handleIsOpenEnrolledChallenge}
                    >
                        <span>
                            <IconReport size={23} color="white" />
                        </span>
                        <span className="text-white font-bold">Enrolled Challenges</span>
                    </button>
                </div>
                <div>
                <button className="flex flex-cols items-center gap-x-1"
                onClick={handleIsOpenZones}>
              <span>
              
                <IconServerCog size={23} color="white" />
              </span>
              <span className="text-white font-bold">Zones</span>
            </button>
                </div>
                <div style={{ flex: 1 }}></div>
                <div >
                    <button style={{border:"1px solid white",padding:"5px 10px",borderRadius:"5px"}} className="flex flex-cols items-center gap-x-1" onClick={handleBackToCalendar}>
                        <span>
                            <IconCalendar size={18} color="white" />
                        </span>
                        <span className="text-white">Back to Calendar</span>
                    </button>
                </div>
            </div>
        </header>
    );
};

export default AtheleteHeader;

