import { useState, useEffect, useMemo } from "react";
import { getAllSwimingData, URL } from "../../API/api-endpoint";
import Header from "../../components/Header";
import { Search, MapPin, Mail, Phone, User, ExternalLink } from "lucide-react";
import { Input } from "../../components/ui/input";
import { Button } from "../../components/ui/button";
import { Card } from "../../components/ui/card";
import { Badge } from "../../components/ui/badge";

const SwimmingPool = () => {
	const [allSwimmingPoolData, setAllSwimmingData] = useState([]);
	const [searchTerm, setSearchTerm] = useState("");

	useEffect(() => {
		fetchAllSwimmingData();
	}, []);

	const fetchAllSwimmingData = async () => {
		const response = await getAllSwimingData();
		if (response?.length > 0) {
			setAllSwimmingData(response);
		}
	};

	const filteredList = useMemo(() => {
		return allSwimmingPoolData?.filter((pool) => {
			const nameMatches = pool?.swimming_pool_name
				?.toLowerCase()
				.includes(searchTerm?.toLowerCase());
			const cityMatch = pool?.city?.name
				?.toLowerCase()
				.includes(searchTerm?.toLowerCase());

			return nameMatches || cityMatch;
		});
	}, [allSwimmingPoolData, searchTerm]);

	const renderStarRating = (rating) => {
		return (
			<div className='flex'>
				{[...Array(5)].map((_, i) => (
					<svg
						key={i}
						className={`w-4 h-4 ${
							i < rating
								? "text-orange-500 fill-orange-500"
								: "text-gray-300"
						}`}
						xmlns='http://www.w3.org/2000/svg'
						viewBox='0 0 24 24'
					>
						<path d='M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z' />
					</svg>
				))}
			</div>
		);
	};

	return (
		<div className='bg-gradient-to-b from-orange-50/30 to-white min-h-screen'>
			<Header />
			<div className='max-w-7xl mx-auto px-4 py-8 mt-20'>
				<div className='mb-6'>
					<h1 className='text-3xl font-bold tracking-tight text-orange-950 mb-2'>
						Swimming Pools
					</h1>
					<p className='text-gray-600 mb-6 text-base'>
						Find swimming pools for your training sessions
					</p>
				</div>

				<div className='mb-8'>
					<div className='relative max-w-md'>
						<Search className='absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400' />
						<Input
							type='text'
							placeholder='Search pools by name or city...'
							value={searchTerm}
							onChange={(e) => setSearchTerm(e.target.value)}
							className='text-gray-600 text-sm pl-9 border-orange-200 focus-visible:ring-orange-500 w-full md:w-[300px]'
						/>
					</div>
				</div>

				{filteredList?.length > 0 ? (
					<div className='grid grid-cols-1 gap-6'>
						{filteredList.map((pool) => (
							<Card
								key={pool.swimming_pool_id}
								className='border border-orange-200 rounded-lg overflow-hidden '
							>
								<div className='flex flex-col md:flex-row'>
									<div className='md:w-2/3 p-6'>
										<div className='mb-2'>
											<h2 className='text-2xl font-bold text-orange-950 mb-2'>
												{pool.swimming_pool_name}
											</h2>
											<div className='flex items-center gap-1 mt-1'>
												{renderStarRating(
													parseInt(
														pool.swimming_pool_rating
													) || 0
												)}
												<span className='text-sm text-gray-500 ml-1'>
													({pool.swimming_pool_rating}
													/5)
												</span>
											</div>
										</div>

										<div className='mt-4 bg-gray-100 rounded-lg h-64 flex items-center justify-center'>
											{pool.image ? (
												<img
													src={`${URL}/static/public/userimages/${pool.image}`}
													alt={
														pool.swimming_pool_name
													}
													className='w-full h-full object-cover text-sm'
												/>
											) : (
												<div className='text-gray-400 flex flex-col items-center'>
													<svg
														xmlns='http://www.w3.org/2000/svg'
														className='h-12 w-12 mb-2'
														fill='none'
														viewBox='0 0 24 24'
														stroke='currentColor'
													>
														<path
															strokeLinecap='round'
															strokeLinejoin='round'
															strokeWidth={1}
															d='M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z'
														/>
													</svg>
													<span>
														No image available
													</span>
												</div>
											)}
										</div>
									</div>

									<div className='bg-gradient-to-b from-orange-50/30 to-white md:w-1/3 p-6 border-t md:border-t-0 md:border-l border-gray-200'>
										<h3 className='text-lg font-bold text-orange-900 mb-4'>
											Contact Information
										</h3>

										<div className='space-y-4'>
											<div className='flex items-start gap-3'>
												<User className='h-5 w-5 text-orange-500 mt-0.5 flex-shrink-0' />
												<div>
													<p className='text-sm text-gray-500'>
														Contact Person
													</p>
													<p className='font-semibold text-sm '>
														{pool.contact_name ||
															"Not specified"}
													</p>
												</div>
											</div>

											<div className='flex items-start gap-3'>
												<Mail className='h-5 w-5 text-orange-500 mt-0.5 flex-shrink-0' />
												<div>
													<p className='text-sm text-gray-500'>
														Email
													</p>
													<p className='font-semibold text-sm '>
														{pool.email_id ||
															"Not specified"}
													</p>
												</div>
											</div>

											<div className='flex items-start gap-3'>
												<Phone className='h-5 w-5 text-orange-500 mt-0.5 flex-shrink-0' />
												<div>
													<p className='text-sm text-gray-500'>
														Phone
													</p>
													<p className='font-semibold text-sm '>
														{pool.contact_number ||
															"Not specified"}
													</p>
												</div>
											</div>

											<div className='flex items-start gap-3'>
												<MapPin className='h-5 w-5 text-orange-500 mt-0.5 flex-shrink-0' />
												<div>
													<p className='text-sm text-gray-500'>
														Location
													</p>
													<p className='font-semibold text-sm '>
														{pool.city?.name ||
															"Not specified"}
													</p>
												</div>
											</div>
										</div>

										<div className='mt-6 space-y-3'>
											{pool.map_link && (
												<Button
													variant='outline'
													className='w-full justify-center font-bold text-orange-600 border-orange-200 hover:bg-orange-50'
													onClick={(e) => {
														e.stopPropagation();
														window.open(
															pool.map_link,
															"_blank"
														);
													}}
												>
													<MapPin className='mr-2 h-4 w-4' />
													View on Map
												</Button>
											)}

											{pool.website && (
												<Button
													className='w-full justify-center font-bold bg-orange-600 hover:bg-orange-700 text-white'
													onClick={(e) => {
														e.stopPropagation();
														window.open(
															pool.website,
															"_blank"
														);
													}}
												>
													<ExternalLink className='mr-2 h-4 w-4' />
													Visit Website
												</Button>
											)}
										</div>

										{pool.user && (
											<div className='mt-4 pt-4 text-right border-t border-orange-100'>
												<Badge
													variant='outline'
													className='bg-white/80 font-bold text-xs px-2 rounded-full border border-orange-200 text-orange-800 w-fit ml-auto'
												>
													Created by:{" "}
													{pool.user.firstname}
													{pool.user.lastname}
												</Badge>
											</div>
										)}
									</div>
								</div>
							</Card>
						))}
					</div>
				) : (
					<div className='text-center py-12 bg-gray-50 rounded-lg'>
						<div className='inline-flex items-center justify-center w-12 h-12 rounded-full bg-orange-100 mb-4'>
							<svg
								xmlns='http://www.w3.org/2000/svg'
								className='h-6 w-6 text-orange-500'
								fill='none'
								viewBox='0 0 24 24'
								stroke='currentColor'
							>
								<path
									strokeLinecap='round'
									strokeLinejoin='round'
									strokeWidth={2}
									d='M20 12H4'
								/>
							</svg>
						</div>
						<h3 className='text-lg font-medium text-gray-900 mb-2'>
							No swimming pools found
						</h3>
						<p className='text-gray-500 max-w-md mx-auto'>
							{searchTerm
								? "No pools match your search criteria. Try adjusting your search terms."
								: "There are no swimming pools available at the moment."}
						</p>
					</div>
				)}
			</div>
		</div>
	);
};

export default SwimmingPool;
