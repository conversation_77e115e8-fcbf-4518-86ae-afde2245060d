import { Card, CardContent, Grid } from '@mui/material'
import React from 'react'
import { getAllAssignedTrack, getAssignedChallenges } from '../../API/api-endpoint'
import { useEffect } from 'react'
import { Table } from 'antd'
import { useState } from 'react'

const ChallengeTrack = () => {
    const [trackChallenges, setTRackChallenges] = useState()

    useEffect(() => {
        fetchAssignedChallenge()
    }, [])

    const fetchAssignedChallenge = async () => {
        const response = await getAllAssignedTrack()
        setTRackChallenges(response)
        console.log("response", response);
    }

    console.log("trackChallenges", trackChallenges);

    return (
        <div>
            


            <Card sx={{ minWidth: 275, margin: "1% 4% 0% 2%", boxShadow: "rgba(60, 64, 67, 0.3) 0px 1px 2px 0px, rgba(60, 64, 67, 0.15) 0px 2px 6px 2px" }}>
            <h1 style={{ margin: "1% 4% 0% 2%", fontSize: "24px",color:"#E67E22" }}><strong>All created challenges so far</strong></h1>
                <CardContent>
                
                    <Grid container>
                        <Grid container spacing={2}>
                            {trackChallenges?.map((challenges) => {
                                return (
                                    <Grid item xs={12} sm={6}>
                                    <div style={{boxShadow:"rgba(50, 50, 93, 0.25) 0px 2px 5px -1px, rgba(0, 0, 0, 0.3) 0px 1px 3px -1px"}}>
                                        <div className='flex'>
                                            <div style={{ border: "1px solid #d1caca" , borderBottom:"none", borderRight:"none" }} className='p-[6px] box-border rounded-tl-sm w-[50%]  min-h-[5vh]'>
                                                <b>Track Name</b>
                                            </div>
                                            <div style={{ border: "1px solid #d1caca" ,borderBottom:"none"}} className='p-[6px] box-border rounded-tr-sm w-[50%]  min-h-[5vh]'>
                                                <b>challenges In Track</b>
                                            </div>

                                        </div>
                                        <div className='flex'>
                                            <div style={{ border: "1px solid #d1caca" ,borderRight:"none"}} className='p-[6px] box-border rounded-bl-sm w-[50%]  min-h-[5vh]'>
                                            {challenges?.track_name?challenges?.track_name:"NA"}
                                            </div>
                                            <div style={{ border: "1px solid #d1caca" }} className='p-[6px] box-border rounded-br-sm w-[50%]  min-h-[5vh]'>
                                               {challenges?.challanges?.map((challengeName,index)=>{
                                                return(
                                                    <div>
                                                    {index+1}. {challengeName?.challengeName}
                                                    </div>
                                                )
                                               })}
                                            </div>

                                        </div>
                                        </div>
                                    </Grid>
                                )
                            })}

                        </Grid>
                    </Grid>
                </CardContent></Card>



        </div>
    )
}

export default ChallengeTrack
