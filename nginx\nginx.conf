user nginx;
worker_processes auto;
worker_rlimit_nofile 65535;

pid /var/run/nginx.pid;
error_log /var/log/nginx/error.log warn;

events {
  multi_accept on;
  worker_connections 65535;
}

http {
  charset utf-8;
  sendfile on;
  tcp_nopush on;
  tcp_nodelay on;
  log_not_found off;
  types_hash_max_size 2048;
  types_hash_bucket_size 64;
  client_max_body_size 16M;

  # MIME
  include /etc/nginx/mime.types;
  default_type application/octet-stream;

  # Logging
  access_log /var/log/nginx/access.log;

  add_header Permissions-Policy        "interest-cohort=()" always;
  add_header Referrer-Policy           "no-referrer" always;
  add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
  add_header X-Content-Type-Options    "nosniff" always;
  add_header X-XSS-Protection          "1; mode=block" always;

  server {
    listen 80;
    root /usr/share/nginx/html;
    index index.html;
    server_tokens off;
    log_not_found off;
    access_log off;

    location / {
      try_files $uri $uri/ /index.html;
      add_header Cache-Control "no-cache";
    }

    location ~ /\.(?!well-known) {
      deny all;
    }

    location /healthcheck {
      return 200 "100% Healthy!\n";
    }

    # Main - index.html
    location = /index.html {
      add_header Cache-Control "private, no-cache";
      add_header Pragma: no-cache;
    }

    # Fonts
    location ~* \.(?:ttf|ttc|otf|eot|woff|woff2)$ {
      expires 1y;
      add_header Cache-Control "public";
      add_header Access-Control-Allow-Origin "*";
    }
  }

  # GZIP
  gzip on;
  gzip_vary on;
  gzip_proxied any;
  gzip_comp_level 6;
  gzip_types text/plain text/css text/xml application/json application/javascript application/rss+xml application/atom+xml image/svg+xml;
}
