import React, { useEffect, useState } from "react";
import ChatLanding from "./ChatLanding";
import CommunityChatLanding from "./CommunityChatLanding";
import IndividualChat from "./individual/IndividualChat";
import IndividualChatMobile from "./individual/individualChatMobile";
import ChatProfile from "./ChatProfile";
import GroupChatCreator from "./group/chat/GroupChatCreator";
import GroupChatMobile from "./group/chat/GroupChatMobile";
import StartNewChat from "./group/chat/StartNewChat";
import CreateNewGroup from "./group/CreateNewGroup";
import CreateNewGroupStepTwo from "./group/CreateNewGroupStepTwo";
import AddNewGroupMember from "./group/AddNewGroupMember";
import GroupInfo from "./group/GroupInfo";
// import ProfilePage from "../../ProfilePage";
import CameraIndividualChat from "../components/camera/CameraIndividualChat";
import Header from "../../../components/Header";
import "../styles/chatlanding.css";
import { useSelector } from "react-redux";

const ChatLandingNew = () => {
  const [openPage, setOpenPage] = useState("");
  const [openPageOne, setOpenPageOne] = useState("home");
  const [openPageTwo, setOpenPageTwo] = useState("chat");
  const [isUnRead, setIsUnRead] = useState(false);
  useEffect(() => {
    console.error("openPageOne", openPageOne);
    console.error("openPage", openPage);
  }, [openPageOne, openPage]);
  const roleID = localStorage.getItem("roleID");
  const [screenWidth, setScreenWidth] = useState(window.innerWidth);

  useEffect(() => {
    const handleResize = () => setScreenWidth(window.innerWidth);
    
    window.addEventListener("resize", handleResize);
    
    return () => window.removeEventListener("resize", handleResize);
  }, []);
  const userData = JSON.parse(localStorage.getItem("user"));
  
  return (
    <div>
      {openPageTwo === "chat" ? (
        <>
        <Header /> 
        <div className="flex Chat-landing-base">
          <div className="flex chat-box-container" style={{ width: screenWidth <= 430 ? "100%" : (openPage === "group-chat" || openPage === "individual" ? "70%" : "30%"), maxWidth: screenWidth <= 430 ? "100vw" : "calc(100vw - 430px)" }}>
            <div className="section-one" style={{ width: screenWidth <= 430 ? "100%" : (openPage === "group-chat" || openPage === "individual" ? "40%" : "100%"), maxWidth: screenWidth <= 430 ? "100vw" : "calc(100vw - 430px)", display: screenWidth <= 430 && (openPage === "group-chat" || openPage === "individual") ? "none" : "block" }} >
            {openPageOne === "home" ? (
                userData.onboardingState === "community" ? (
                  <CommunityChatLanding
                    openPage={openPage}
                    setOpenPage={setOpenPage}
                    setOpenPageOne={setOpenPageOne}
                    isUnRead={isUnRead}
                    setIsUnRead={setIsUnRead}
                  />
                ) : (
                  <ChatLanding
                    openPage={openPage}
                    setOpenPage={setOpenPage}
                    setOpenPageOne={setOpenPageOne}
                    isUnRead={isUnRead}
                    setIsUnRead={setIsUnRead}
                  />
                )
              ) : null}
              {openPageOne === "create-group" ? (
                <StartNewChat
                  openPageOne={openPageOne}
                  setOpenPageOne={setOpenPageOne}
                />
              ) : null}
              {openPageOne === "stepOne" ? (
                <CreateNewGroup setOpenPageOne={setOpenPageOne}/>
              ) : null}
              {openPageOne === "stepTwo" ? (
                <CreateNewGroupStepTwo setOpenPageOne={setOpenPageOne} />
              ) : null}
              
              {(openPageOne === "home" && (roleID === "3" || roleID === "1" || roleID === "6")) ? (
                <div title="Create Group">
                  <button
                    className={`create-group-button`}
                    onClick={() => setOpenPageOne("stepOne")}
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      viewBox="0 0 20 20"
                      width="24"
                      height="24"
                      fill="currentColor"
                    >
                      <path d="M10 5a1 1 0 0 1 1 1v3h3a1 1 0 1 1 0 2h-3v3a1 1 0 1 1-2 0v-3h-3a1 1 0 1 1 0-2h3v-3a1 1 0 0 1 1-1z" />
                    </svg>
                  </button>
                </div>
              ): null}

              {openPageOne === "addGroupMember" ? <AddNewGroupMember setOpenPageOne={setOpenPageOne} /> : null}
            </div>
            {(openPage === "group-chat" || openPage === "individual") && (
              <>
              <div className="section-two">
                {openPage === "individual" ? <IndividualChat setOpenPageTwo={setOpenPageTwo} setOpenPage={setOpenPage} /> : null}
                {openPage === "group-chat" ? <GroupChatCreator setOpenPage={setOpenPage} /> : null}
              </div>
              <div className="section-two-mobile">
              {openPage === "group-chat" ? <GroupChatMobile setOpenPage={setOpenPage} /> : null}
              {openPage === "individual" ? <IndividualChatMobile setOpenPage={setOpenPage}  setOpenPageTwo={setOpenPageTwo}/> : null}
              </div>
              </>
            )}
          </div>
          <div className="section-three">
            {openPage === "group-chat" ? <GroupInfo  setOpenPageOne={setOpenPageOne} setOpenPage={setOpenPage} /> : null}
            {openPage === "individual" ? <ChatProfile /> : null}
          </div>
        </div>
        </>
      )
        :
        (
          null
        )}

      {openPageTwo === "camera" ? (
        <CameraIndividualChat setOpenPageTwo={setOpenPageTwo} setIsUnRead={setIsUnRead} />
      )
        : (null)}
    </div>
  );
};

export default ChatLandingNew;
