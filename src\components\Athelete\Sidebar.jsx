import React, { useEffect, useState } from 'react'
import { Menu, Switch } from '@mantine/core'
import {
  IconFilter,
  IconPlus,
  IconSearch,
  // IconX,
  IconUser
} from '@tabler/icons'
import { useNavigate } from 'react-router-dom'
// import { Link } from 'react-router-dom'
import EditIcon from '@mui/icons-material/Edit';
import CloseIcon from '@mui/icons-material/Close';
import { Button, MenuItem, OutlinedInput, Select, TextField } from '@mui/material';
import BackupIcon from '@mui/icons-material/Backup';
import { useRef } from 'react';
import { URL } from '../../API/api-endpoint';
import axios from 'axios';
const Sidebar = ({ isEditProfile, setIsEditProfile, assignedCocahId, formik, usersDetails, handleOnSubmitForm,getUserDetails }) => {
  const fileInputRef = useRef(null);
  const tShirtSizes = ["XS", "S", "M", "L", "XL", "XXL"];

  console.log("usersDetails", usersDetails);
  const handleFileUpload = (event) => {
    const file = event.target.files[0];
    formik.setFieldValue("profile_image", file)

    if (file) {
      handleOnSubmitForm(file)
    }
  };
  console.log("profile_image", formik?.values);

  // bg-[#f1f5f9]   for background
  const calculateBMI = (height, weight) => {
    console.log("snbanshe", height, weight);
    if (height && weight, weight) {
      // Convert height to meters (since it's in cm)
      const heightInMeters = height / 100;

      // Calculate BMI
      const bmi = (weight / (heightInMeters * heightInMeters)).toFixed(2); // Calculate BMI rounded to 2 decimal places
      return bmi;
    }

    return 0; // Return null if height or weight is missing
  };
  // const bmi = formik.values?.height&&calculateBMI();
  // console.log('BMI:', bmi); // Output the calculated BMI  
  return (
    <>
      <div className='h-[100%] bg-[#E67E22]  text-[white]'>
        <div className='flex  justify-between border-b-2 p-4 mr-4 ml-4'>
          <div>
            Profile
          </div>
          <div>
            {isEditProfile ? (
              <>
                <Button variant='contained' style={{backgroundColor:"white",border:"1px solid white"}} onClick={() => handleOnSubmitForm()}>Save</Button>
                <CloseIcon className='cursor-pointer' onClick={() =>{ setIsEditProfile(!isEditProfile)

getUserDetails()
                }} />
              </>
            ) : (

              <EditIcon className='cursor-pointer' onClick={() => setIsEditProfile(!isEditProfile)} />
            )}
          </div>
        </div>

        <div className='flex  justify-center  space-x-0 p-2 relative' >
          <img
            src={`${URL}/static/public/userimages/${formik?.values?.profile}`}
            alt="..."
            className="w-44 h-44 rounded-full border-2 profileImage"
          // onClick={handleProfile}
          />
          <div className='relative  right-10 '>
            <label htmlFor="fileInput">
              <BackupIcon className='cursor-pointer' />
            </label>
            <input
              id="fileInput"
              type="file"
              accept="image/*,.jfif"
              ref={fileInputRef}
              style={{ display: 'none' }}
              onChange={handleFileUpload}
            />

          </div>

        </div>
        <div className='flex justify-between p-4 items-center'>
          <p>
            Height (Cms)
          </p>
          <p>
            {isEditProfile ? (
              <TextField

                id="filled-basic" variant="filled"
                placeholder="Height"
                InputProps={{
                  style: { color: '#E67E22',backgroundColor:"#FFEADC",maxWidth:"180px" ,alignSelf:"end"}, // Text color will be white
                }}
                  inputProps={{ maxLength: 4 }}
                type="text"
                name="height"
                value={formik?.values?.height}
                onChange={formik.handleChange}
              />
            ) : (
              <>
                {formik?.values?.height ? formik?.values?.height : "NA"}
              </>
            )}

          </p>
        </div>
        <div className='flex justify-between p-4 items-center'>
          <p>
            Weight (Kgs)
          </p>
          {isEditProfile ? (
            <TextField

              id="filled-basic" variant="filled"
              placeholder="Weight"
              InputProps={{
                style: {color: '#E67E22',backgroundColor:"#FFEADC",maxWidth:"180px",alignSelf:"end" }, // Text color will be white
              }}
               inputProps={{ maxLength: 4 }}
                type="text"
              name="weight"
              value={formik?.values?.weight}
              onChange={formik.handleChange}
            />
          ) : (
            <>
              {formik?.values?.weight ? formik?.values?.weight : "NA"}
            </>
          )}

        </div>

        <div className='flex justify-between p-4 items-center'>
          <p>
            Waist (Inches)
          </p>
          {isEditProfile ? (
            <TextField

              id="filled-basic" variant="filled"
              placeholder="Waist"
              InputProps={{
                style: {color: '#E67E22',backgroundColor:"#FFEADC",maxWidth:"180px",alignSelf:"end" }, // Text color will be white
              }}
              inputProps={{ maxLength: 4 }}
                type="text"
              name="waist"
              value={formik?.values?.waist}
              onChange={formik.handleChange}
            />
          ) : (
            <>
              {formik?.values?.waist ? formik?.values?.waist : "NA"}
            </>
          )}

        </div>
        <div className='flex justify-between p-4  items-center'>
          <p>
            BMI
          </p>
          <>
            {formik?.values?.height ? calculateBMI(formik?.values?.height, formik?.values?.weight) :
              calculateBMI(formik?.values?.height, formik?.values?.weight)}
          </>

        </div>
        <div className='flex justify-between p-4 items-center'>
          <p>
            T-Shirt Size
          </p>
          {/*  */}
          {isEditProfile ? (
             <TextField
             id="filled-basic"
             variant="filled"
             select
             fullWidth
             InputProps={{
               style: {color: '#E67E22',backgroundColor:"#FFEADC",maxWidth:"180px",alignSelf:"end" }, // Text color will be white
             }}
             name="tshirtsize"
             value={formik.values.tshirtsize}
             onChange={formik.handleChange} 
           >
             {tShirtSizes?.map((size) => (
               <MenuItem key={size} value={size}>
                 {size}
               </MenuItem>
             ))}
           </TextField>
          ) : (
            <>
              {formik?.values?.tshirtsize ? formik?.values?.tshirtsize : "NA"}
            </>
          )}

        </div>
        <div className='flex justify-between p-4  items-center'>
          <p>
            Short Size
          </p>
          {isEditProfile ? (
            <TextField

            id="filled-basic"
            variant="filled"
            select
            fullWidth
            InputProps={{
              style: {color: '#E67E22',backgroundColor:"#FFEADC",maxWidth:"180px",alignSelf:"end" }, // Text color will be white
            }}
              name="shortsize"
              value={formik?.values?.shortsize}
              onChange={formik.handleChange}
            >
            {tShirtSizes?.map((size) => (
               <MenuItem key={size} value={size}>
                 {size}
               </MenuItem>
             ))}
             </TextField>
          ) : (
            <>
              {formik?.values?.shortsize ? formik?.values?.shortsize : "NA"}
            </>
          )}

        </div>
        <div className='flex justify-between p-4 items-center'>
          <p>
            GST
          </p>
          {isEditProfile ? (
            <TextField

              id="filled-basic" variant="filled"
              placeholder="GST"
              InputProps={{
                style: { color: '#E67E22',backgroundColor:"#FFEADC",maxWidth:"180px",alignSelf:"end"  }, // Text color will be white
              }}
              type="text"
              name="gst"
              value={formik?.values?.gst}
              inputProps={{ maxLength: 15 }}
              onChange={(event) => {
                const { value } = event.target;
                const isValid = /^[a-zA-Z0-9]*$/.test(value);
                if (isValid) {
                  formik.handleChange(event);
                }
                  
              }}
            />
          ) : (
            <>
              {formik?.values?.gst ? formik?.values?.gst : "NA"}
            </>
          )}

        </div>
        <div className='flex justify-between p-4 items-center'>
          <p>
            Process
          </p>
          {isEditProfile ? (
            <Select
            fullWidth
            style={{color: '#E67E22',backgroundColor:"#FFEADC",maxWidth:"180px",alignSelf:"end" }}
                          placeholder="123"
                          name="process"
                          value={formik.values.process?formik.values.process:""}
                          onChange={formik.handleChange}
                          id="form-layouts-separator-select"
                          labelId="form-layouts-separator-select-label"
                          input={
                            <OutlinedInput id="select-multiple-language" />
                          }
                        >
                          <MenuItem value={"a"}>Automatic</MenuItem>
                          <MenuItem value={"m"}>Manual</MenuItem>

                        </Select>
          ) : (
            <>
            {formik?.values?.process=="m"?"Manual":formik?.values?.process=="a"?"Automatic":"NA"}
            </>
          )}

        </div>
        {/* <div>
      <IconX size={24} color='dodgerblue' />
    </div> */}
      </div>
    </>
  )
}

export default Sidebar
