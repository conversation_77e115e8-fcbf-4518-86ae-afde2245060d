import React, { useContext, useEffect, useState } from "react";
import Header from "../components/Header";
import GlobalContext from "../context/GlobalContext";
import { LeaderBoardUsers, categories } from "../utils/LeaderBoardUsers";
import { Button, Checkbox, Space, Table, Tag } from "antd";

const LeaderBoard = () => {
  const [athletes, setAthletes] = useState([]);
  const [selectedCategories, setSelectedCategories] = useState(categories);
  const [filteredInfo, setFilteredInfo] = useState({});
  const [sortedInfo, setSortedInfo] = useState({});
  const [categoryTableData, setCategoryTableData] = useState({});

  console.log(athletes, "athletes");
  // const [userId, setUserId] = useState(null)
  const { user } = useContext(GlobalContext);

  useEffect(() => {
    console.log("user", user);
    // console.log('Access token', accessToken)

    // eslint-disable-next-line no-unused-expressions
    // user === 'deepak'
    //   ? setUserId(422)
    //   : user === '<EMAIL>'
    //   ? setUserId(340)
    //   : user === '<EMAIL>'
    //   ? setUserId(484)
    //   : setUserId(500)

    const apiUrl = `https://app.yoska.in/kona-coach/api/organizations/60/athletes`;
    const bearerToken = "qmmvofrilpsj59rctkn0ivseuil1jji5";

    fetch(apiUrl, {
      headers: {
        Authorization: `Bearer ${bearerToken}`,
      },
    })
      .then((response) => response.json())
      .then((data) => {
        console.log("athletes", data);
        setAthletes(data);
        // Do something with the data
      })
      .catch((error) => {
        console.error(error);
        // Handle the error
      });
  }, [user]);

  const handleChange = (pagination, filters, sorter) => {
    setFilteredInfo(filters);
    setSortedInfo(sorter);
  };

  const clearFilters = () => {
    setFilteredInfo({});
    setSelectedCategories([]);
  };

  const clearAll = () => {
    setFilteredInfo({});
    setSortedInfo({});
    setSelectedCategories([]);
  };

  const columns = [
    {
      title: "Name",
      dataIndex: "name",
      key: "name",
      filters: LeaderBoardUsers.map((user) => ({
        text: user.name,
        value: user.name,
      })),
      filteredValue: filteredInfo.name || null,
      onFilter: (value, record) => record.name.includes(value),
      sorter: (a, b) => a.name.length - b.name.length,
      sortOrder: sortedInfo.columnKey === "name" ? sortedInfo.order : null,
      ellipsis: true,
    },
    {
      title: "Age",
      dataIndex: "age",
      key: "age",
      filters: [
        { text: "10-20", value: "10-20" },
        { text: "20-30", value: "20-30" },
        { text: "30-40", value: "30-40" },
        { text: "40-50", value: "40-50" },
        { text: "50-60", value: "50-60" },
        { text: "60-70", value: "60-70" },
        { text: "70-80", value: "70-80" },
        { text: "80-90", value: "80-90" },
        { text: "90-100", value: "90-100" },
      ],
      filteredValue: filteredInfo.age || null,
      onFilter: (value, record) => {
        const ageRange = value.split("-").map(Number);
        return record.age >= ageRange[0] && record.age <= ageRange[1];
      },
      sorter: (a, b) => a.age - b.age,
      sortOrder: sortedInfo.columnKey === "age" ? sortedInfo.order : null,
      ellipsis: true,
    },
    {
      title: "Category",
      dataIndex: "category",
      key: "category",
      filters: categories.map((cat) => ({ text: cat, value: cat })),
      filteredValue: filteredInfo.category || null,
      onFilter: (value, record) => record.category === value,
      ellipsis: true,
    },
    {
      title: "Gender",
      dataIndex: "gender",
      key: "gender",
      filters: [
        { text: "Male", value: "male" },
        { text: "Female", value: "female" },
      ],
      filteredValue: filteredInfo.gender || null,
      onFilter: (value, record) => record.gender === value,
      ellipsis: true,
    },
    {
      title: "",
      dataIndex: "isTop",
      key: "isTop",
      filters: [{ text: "Top Users", value: true }],
      filteredValue:
        selectedCategories.length === 0
          ? filteredInfo.isTop || null
          : filteredInfo.isTop || [true],
      onFilter: (value, record) => record.isTop === value,
    //   render: (isTop) => (isTop ? "Yes" : "No"),
      render: (isTop) => {
        const color = isTop && 'green';
        return (
          <Tag color={color}>
            {isTop && "Top Athlete"}
          </Tag>
        );
      },
    }
  ];

  useEffect(() => {
    const initialCategoryData = {};
    categories.forEach((cat) => {
      initialCategoryData[cat] = LeaderBoardUsers.filter(
        (user) => user.category === cat
      );
    });
    setCategoryTableData(initialCategoryData);
  }, []);

  const handleCategoryChange = (category) => {
    setSelectedCategories((prevCategories) => {
      if (prevCategories.includes(category)) {
        const newCategoryData = { ...categoryTableData };
        delete newCategoryData[category];
        setCategoryTableData(newCategoryData);
        return prevCategories.filter((cat) => cat !== category);
      } else {
        const newCategoryData = {
          ...categoryTableData,
          [category]: LeaderBoardUsers.filter(
            (user) => user.category === category
          ),
        };
        setCategoryTableData(newCategoryData);
        return [...prevCategories, category];
      }
    });
  };

  return (
    <>
      <Header />
      <div className="grid grid-cols-1 xl:grid-cols-5 items-start gap-x-4"></div>
      <div style={{ marginTop: "100px", padding: "20px" }}>
        <div style={{ padding: "0px 0px 20px 0px" }}>
          {categories.map((cat) => (
            <Checkbox
              key={cat}
              checked={selectedCategories.includes(cat)}
              defaultChecked={true}
              onChange={() => handleCategoryChange(cat)}
            >
              {cat}
            </Checkbox>
          ))}
        </div>

        <Space
          style={{
            marginBottom: 16,
          }}
        >
          <Button onClick={clearFilters}>Clear filters</Button>
          <Button onClick={clearAll}>Clear filters and sorters</Button>
        </Space>
        {selectedCategories.length === 0 && (
          <Table
            columns={columns}
            dataSource={LeaderBoardUsers}
            onChange={handleChange}
          />
        )}
        {selectedCategories.map((selectedCategory) => (
          <div key={selectedCategory}>
            <h1 className="mb-1 capitalize text-slate-600 px-1 font-larg">
              {selectedCategory} Category
            </h1>
            <Table
              columns={columns}
              dataSource={categoryTableData[selectedCategory]}
              onChange={handleChange}
            />
          </div>
        ))}
      </div>
    </>
  );
};

export default LeaderBoard;
