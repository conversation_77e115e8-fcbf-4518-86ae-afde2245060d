import { But<PERSON>, <PERSON>Field } from "@mui/material";
import React, { useState } from "react";

const initialValues = {
  phaseBlockName: "",
};

const CreatePhaseBlockTable = () => {
  const [values, setValues] = useState(initialValues);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    console.log(name, value);
    setValues({
      ...values,
      [name]: value,
    });
  };

  const handleSubmit = async () => {
    console.log(values);
    // try {
    //   const response = await axios.post(
    //     "https://api.example.com/endpoint",
    //     { data: values },
    //     {
    //       headers: {
    //         "Content-Type": "application/json",
    //          Authorization: "Bearer your_access_token_here",
    //       },
    //     }
    //   );
    //   console.log(response.data);
    // } catch (err) {
    //   console.log(err.message);
    // }
  };

  const elements = [];

  // Loop from 1 to 26 and push each number as an element
  for (let i = 1; i <= 2; i++) {
    elements.push(
      <div className="phase-block-row" key={i}>
        <p>Week {i}</p>
        <p>Phase Table</p>
      </div>
    );
  }
  return (
    <div className="container">
      <div className="title">Create Phase Block</div>
      <div className="form-body">
        <div className="form-group">
          <label className="lable">Create Phase Block</label>
          <div className="activity-inputs">
            <TextField
              className="activity-input"
              id="outlined-basic"
              label="Running"
              variant="outlined"
              name="phaseBlockName"
              value={values.phaseBlockName}
              onChange={handleInputChange}
            />
          </div>
        </div>
        <div className="phase-block-table">
          <div className="phase-block-row">
            <h3>Week</h3>
            <h3>Phases</h3>
          </div>
          <div className="phase-block-row">
            <p>Week 1</p>
            <p>Phase Table</p>
          </div>
          {elements}
          <div className="phase-block-row">
            <p>Week 3...</p>
            <p>Phase Table</p>
          </div>
          <div className="phase-block-row">
            <p>Week 26</p>
            <p>Phase Table</p>
          </div>
        </div>

        <div className="form-group">
          <Button
            sx={{ background: "black", marginLeft: "-4.5%" }}
            onClick={handleSubmit}
            variant="contained"
          >
            Submit
          </Button>
        </div>
      </div>
    </div>
  );
};

export default CreatePhaseBlockTable;
