import React, { useEffect, useState, useRef } from "react";
import { <PERSON>, Button } from "antd";
import Header from "../../components/Header";
import { listChallengeLevel } from "../../API/api-endpoint";
import {Grid,TextField} from "@mui/material";
import Swal from "sweetalert2";
import "react-datepicker/dist/react-datepicker.css";
import { createChallengeLevel, updateChallengeLevel, deleteChallengeLevel } from "../../API/api-endpoint";
import CreateChallengeLevel from "./components/CreateChallengeLevel";

const initialLevelData = {
  id: null,
  name: ""
};

const ChallengeLevel = () => {
  const [level, setLevel] = useState(initialLevelData);
  const [allLevels, setAllLevels] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [initialContentSet, setInitialContentSet] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [challengeForOptions, setChallengeForOptions] = useState([]);
  const [communityGroups, setCommunityGroups] = useState([]);

  const roleID = localStorage.getItem("roleID");

  // Helper to validate required fields
  const isFormValid = (data) => {
    const requiredFields = [
      "name"
    ];
    return requiredFields.every(
      (field) => data[field] !== undefined && data[field] !== ""
    );
  };

  const showModal = () => {
    setLevel(initialLevelData);
    setIsModalOpen(true);
  };

  const handleCancel = () => {
    setIsModalOpen(false);
  };

  // Fetch challenges based on the community groups available
  const fetchLevel = async () => {
    try {
      const response = await listChallengeLevel();
      console.log("allLevels", response);
      if (response.status === 200) {
        console.log("levelsFound", response);
        setAllLevels(response.data.data);
      }
    } catch (error) {
      console.error("Error fetching challenges for community groups:", error);
    }
  };

  useEffect(() => {
    fetchLevel();
  },[])

  const handleSearchChange = (event) => {
    setSearchTerm(event.target.value);
  };

  const handleEditLevel = (challengeLevel) => {    
    setLevel(challengeLevel);
    setIsModalOpen(true);
  };

  if (roleID === "6") {
    let groupID = 0;
    const groupDetailString = localStorage.getItem("groupDetail");

    if (groupDetailString) {
      const groupDetail = JSON.parse(groupDetailString);
      groupID = groupDetail.id;
    }
  }

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!isFormValid(level)) {
      Swal.fire({
        title: "Error",
        text: "Please fill the name",
        icon: "error",
      });
      return;
    }
  
    const payload = {
      name: level.name,
   };
    
    try {
      setIsLoading(true);
      const token = localStorage.getItem("token");
      let response;

      if (level.id) {
        response = await updateChallengeLevel(payload, level.id);
      } else {
        response = await createChallengeLevel(payload);
      }

      if (response.status === 201 || response.status === 200) {
        fetchLevel();
        Swal.fire({
          title: "Success",
          text: response.data.message,
          icon: "success",
        });
        handleCancel();
        // fetchChallenges();
        setLevel(initialLevelData);
      } else {
        Swal.fire({
          title: "Error",
          text: response.data.message,
          icon: "error",
        });
      }
    } catch (error) {
      Swal.fire({
        title: "Error",
        text: error.response?.data?.message || "An error occurred",
        icon: "error",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleDeleteChallengeLevel = (levelId) => {
    Swal.fire({
      title: "Are you sure?",
      text: "Do you really want to delete this level?",
      icon: "warning",
      showCancelButton: true,
      confirmButtonText: "Yes, delete it!",
      cancelButtonText: "Cancel",
    }).then(async (result) => {
      if (result.isConfirmed) {
        try {
          const response = await deleteChallengeLevel(levelId);
          console.log(response,"delete");
          if (response.status === 200) {
            fetchLevel();
            Swal.fire({
              title: "Deleted!",
              text: response.data.message || "Level deleted successfully.",
              icon: "success",
              confirmButtonText: "OK",
            }).then(() => {
              
            });
          }
        } catch (error) {
          Swal.fire({
            title: "Error",
            text:
              error.response?.data?.message ||
              "An error occurred while deleting the challenge level.",
            icon: "error",
          });
        }
      }
    });
  };

  const columns = [
    {
      title: "Sr.ID",
      key: "srid",
      render: (_, __, index) => index + 1, // index starts from 0
    },
    {
      title: "Level Name",
      dataIndex: "name",
      key: "name",
    },
    {
      title: "Actions",
      dataIndex: "actions",
      key: "actions",
      render: (_, record) => {
        return (
          <span>
            <Button
              style={{ color: "#E67E22" }}
              type="link"
              onClick={() => handleEditLevel(record)}
            >
              Edit
            </Button>
            <Button
              type="link"
              danger
              onClick={() => handleDeleteChallengeLevel(record.id)}
            >
              Delete
            </Button>
          </span>
        );
      },
    },
  ];

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setLevel((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  return (
    <div>
      <Header />
      <div style={{ marginTop: "100px", padding: "20px" }}>
        <div className="add-challenges-modal">
          <Grid container spacing={2}>
            <Grid item xs={12} sm={10} sx={{ marginTop: "30px" }}>
              <Button type="primary" onClick={showModal} 
              style={{
                boxShadow: "0px 4px 10px rgba(0, 0, 0, 0.25)",
              }}>
                Create Level
              </Button>
            </Grid>
              <Grid item xs={12} sm={2} sx={{ textAlign: "start", marginTop: "30px" }}>
                <TextField
                  type="text"
                  size="small"
                  value={searchTerm}
                  onChange={handleSearchChange}
                  placeholder="Search By Level Name.."
                />
              </Grid>
          </Grid>
          
          <CreateChallengeLevel
          isModalOpen={isModalOpen}
          setIsModalOpen={setIsModalOpen}
          handleCancel={handleCancel}
          setLevel={setLevel}
          initialLevelData={initialLevelData}
          level={level}
          handleSubmit={handleSubmit}
          handleInputChange={handleInputChange}
          isLoading={isLoading}
          />
        </div>
        &nbsp;
        <div>
          <Table
            columns={columns}
            dataSource={allLevels?.filter((row) =>
              row?.name?.toLowerCase().includes(searchTerm.toLowerCase())
            )}
            pagination={true}
            className="thin-scrollbar"
          />
        </div>
      </div>
    </div>
  );
};

export default ChallengeLevel;
