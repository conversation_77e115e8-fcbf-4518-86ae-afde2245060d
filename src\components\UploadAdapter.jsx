import { useState } from "react";
import axios from "axios";

const UploadAdapter = ({ loader, url }) => {
  const [file, setFile] = useState(null);

  loader.file.then((pic) => setFile(pic));

  const upload = () => {
    const fd = new FormData();
    fd.append("image", file);

    return new Promise((resolve, reject) => {
      axios
        .post(url, fd, {
          onUploadProgress: (e) => {
            console.log(Math.round((e.loaded / e.total) * 100) + " %");
          },
        })
        .then((response) => {
          resolve(response);
        })
        .catch((error) => {
          reject("Server Error");
          console.log("Server Error : ", error);
        });
    });
  };

  return upload();
};

export default UploadAdapter;
