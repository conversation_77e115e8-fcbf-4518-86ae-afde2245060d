import React, { useState, useMemo, useEffect } from "react";
import { Plus, Search, Edit, Trash2, Loader2 } from "lucide-react";

import { <PERSON><PERSON> } from "../../components/ui/button";
import { Input } from "../../components/ui/input";
import { Card, CardContent, CardHeader } from "../../components/ui/card";
import {
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableHeader,
	TableRow,
} from "../../components/ui/table";
import { Badge } from "../../components/ui/badge";
import { SubWorkoutDialog } from "../../components/admin/sub-workout-dialog";
import { DeleteConfirmDialog } from "../../components/admin/delete-confirm-dialog";
import Header from "../../components/Header";
import {
	getAllsubactivityData,
	deletesubactivityData,
	getAllActivityData,
} from "../../API/api-endpoint";
import Swal from "sweetalert2";

const getCategoryColor = (category) => {
	switch (category) {
		case "Running":
			return "bg-green-100 text-green-800";
		case "Swimming":
			return "bg-blue-100 text-blue-800";
		case "Cycling":
			return "bg-yellow-100 text-yellow-800";
		case "Strength":
			return "bg-purple-100 text-purple-800";
		case "General":
			return "bg-gray-100 text-gray-800";
		default:
			return "bg-gray-100 text-gray-800";
	}
};

export default function SubWorkoutsPage() {
	const [searchTerm, setSearchTerm] = useState("");
	const [dialogOpen, setDialogOpen] = useState(false);
	const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
	const [editingItem, setEditingItem] = useState(null);
	const [deletingId, setDeletingId] = useState(null);
	const [currentPage, setCurrentPage] = useState(1);
	const [data, setData] = useState([]);
	const [activities, setActivities] = useState([]);
	const [isLoading, setIsLoading] = useState(true);
	const itemsPerPage = 10;

	useEffect(() => {
		fetchData();
		fetchActivities();
	}, []);

	const fetchData = async () => {
		try {
			setIsLoading(true);
			const response = await getAllsubactivityData();
			setData(response || []);
		} catch (error) {
			console.error("Error fetching sub-workout data:", error);
			setData([]);
		} finally {
			setIsLoading(false);
		}
	};

	const fetchActivities = async () => {
		try {
			const response = await getAllActivityData();
			setActivities(response?.rows || []);
		} catch (error) {
			console.error("Error fetching activities:", error);
			setActivities([]);
		}
	};

	const filteredData = useMemo(() => {
		return data.filter((item) =>
			item.subworkout?.toLowerCase().includes(searchTerm.toLowerCase())
		);
	}, [data, searchTerm]);

	const paginatedData = useMemo(() => {
		const startIndex = (currentPage - 1) * itemsPerPage;
		return filteredData.slice(startIndex, startIndex + itemsPerPage);
	}, [filteredData, currentPage]);

	const handleEdit = (item) => {
		setEditingItem(item);
		setDialogOpen(true);
	};

	const handleDelete = (id) => {
		setDeletingId(id);
		setDeleteDialogOpen(true);
	};

	const handleCreate = () => {
		setEditingItem(null);
		setDialogOpen(true);
	};

	const confirmDelete = async () => {
		try {
			await deletesubactivityData(deletingId);
			setDeleteDialogOpen(false);
			setDeletingId(null);
			fetchData();
		} catch (error) {
			console.error("Error deleting sub-workout:", error);
		}
	};

	const handleDialogSuccess = () => {
		fetchData();
		setDialogOpen(false);
		setEditingItem(null);
		Swal.fire({
			title: "Success",
			text: "Sub-Workout saved successfully!",
			icon: "success",
			timer: 1800,
			showConfirmButton: false,
		});
	};

	const totalPages = Math.ceil(filteredData.length / itemsPerPage);

	return (
		<div>
			<Header />

			<div className='max-w-[1300px] mx-auto py-6 space-y-6 mt-16'>
				<div className='flex items-center justify-between'>
					<h1 className='text-3xl font-bold text-orange-950'>
						Sub-Workouts Management
					</h1>
				</div>

				<Card>
					<CardHeader className='bg-orange-50 border-b'>
						<div className='flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between'>
							<Button
								onClick={handleCreate}
								className='bg-orange-500 hover:bg-orange-600 text-white'
							>
								<Plus className='h-4 w-4 mr-2' /> Create
								Sub-Workout
							</Button>
							<div className='relative'>
								<Search className='absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4' />
								<Input
									placeholder='Search by workout name...'
									value={searchTerm}
									onChange={(e) =>
										setSearchTerm(e.target.value)
									}
									className='pl-10 w-[250px] text-sm'
								/>
							</div>
						</div>
					</CardHeader>

					<CardContent className='p-0'>
						<div className='overflow-x-auto'>
							<Table>
								<TableHeader>
									<TableRow className='bg-orange-500 hover:bg-orange-500'>
										<TableHead className='text-white font-semibold'>
											Sr ID
										</TableHead>
										<TableHead className='text-white font-semibold'>
											Workout Name
										</TableHead>
										<TableHead className='text-white font-semibold'>
											Category
										</TableHead>
										<TableHead className='text-white font-semibold'>
											Description
										</TableHead>
										<TableHead className='text-white font-semibold'>
											Action
										</TableHead>
									</TableRow>
								</TableHeader>
								<TableBody>
									{isLoading ? (
										<TableRow>
											<TableCell
												colSpan={5}
												className='text-center py-8'
											>
												<Loader2 className='h-6 w-6 animate-spin mx-auto' />
												<p className='mt-2 text-gray-500'>
													Loading sub-workouts...
												</p>
											</TableCell>
										</TableRow>
									) : paginatedData.length > 0 ? (
										paginatedData.map((item, index) => (
											<TableRow
												key={item.id}
												className='hover:bg-orange-50/50'
											>
												<TableCell className='font-medium'>
													{(currentPage - 1) *
														itemsPerPage +
														index +
														1}
												</TableCell>
												<TableCell className='font-semibold'>
													{item.subworkout || "N/A"}
												</TableCell>
												<TableCell>
													<Badge
														className={getCategoryColor(
															item.activity
																?.activity ||
																"General"
														)}
													>
														{item.activity
															?.activity ||
															"General"}
													</Badge>
												</TableCell>
												<TableCell className='text-gray-600'>
													{item.description ||
														"No description"}
												</TableCell>
												<TableCell>
													<div className='flex gap-2'>
														<Button
															variant='ghost'
															size='sm'
															onClick={() =>
																handleEdit(item)
															}
															className='text-blue-600 hover:text-blue-800 hover:bg-blue-50'
														>
															<Edit className='h-4 w-4' />
														</Button>
														<Button
															variant='ghost'
															size='sm'
															onClick={() =>
																handleDelete(
																	item.id
																)
															}
															className='text-red-600 hover:text-red-800 hover:bg-red-50'
														>
															<Trash2 className='h-4 w-4' />
														</Button>
													</div>
												</TableCell>
											</TableRow>
										))
									) : (
										<TableRow>
											<TableCell
												colSpan={5}
												className='text-center py-8 text-gray-500'
											>
												No data found
											</TableCell>
										</TableRow>
									)}
								</TableBody>
							</Table>
						</div>

						{totalPages > 1 && (
							<div className='flex justify-center gap-2 p-4 border-t'>
								<Button
									variant='outline'
									size='sm'
									onClick={() =>
										setCurrentPage(
											Math.max(1, currentPage - 1)
										)
									}
									disabled={currentPage === 1}
								>
									Previous
								</Button>

								{Array.from({ length: 5 }, (_, i) => {
									const startPage =
										Math.floor((currentPage - 1) / 5) * 5 +
										1;
									const page = startPage + i;
									if (page > totalPages) return null;

									return (
										<Button
											key={page}
											variant={
												currentPage === page
													? "default"
													: "outline"
											}
											size='sm'
											onClick={() => setCurrentPage(page)}
											className={
												currentPage === page
													? "bg-orange-500 hover:bg-orange-600 text-white"
													: ""
											}
										>
											{page}
										</Button>
									);
								})}

								<Button
									variant='outline'
									size='sm'
									onClick={() =>
										setCurrentPage(
											Math.min(
												totalPages,
												currentPage + 1
											)
										)
									}
									disabled={currentPage === totalPages}
								>
									Next
								</Button>
							</div>
						)}
					</CardContent>
				</Card>

				<SubWorkoutDialog
					open={dialogOpen}
					onOpenChange={setDialogOpen}
					editingItem={editingItem}
					onSuccess={handleDialogSuccess}
				/>
				<DeleteConfirmDialog
					open={deleteDialogOpen}
					onOpenChange={setDeleteDialogOpen}
					onConfirm={confirmDelete}
					title='Are you sure you want to delete this sub-workout?'
					description='This action cannot be undone.'
				/>
			</div>
		</div>
	);
}
