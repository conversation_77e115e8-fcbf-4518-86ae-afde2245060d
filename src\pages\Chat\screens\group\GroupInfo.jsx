import { Box, Paper, Typography } from "@mui/material";
import React, { useEffect, useState } from 'react';
import "../../styles/chatlanding.css";
import "./../../styles/newchat/newChat.css";
import { useSelector } from "react-redux";
import NavGroupInfo from "../../components/navbar/NavGroupInfo";
import GroupMembers from "../../components/group/GroupMembers";
import { db, get, ref, runTransaction } from "../../../../API/firebase.config";
import Swal from "sweetalert2";
import { clearUsersToCreateGroup } from "../../redux/action/usersAction";
import { useDispatch } from "react-redux";
import axios from 'axios';
import { URL } from "../../../../API/api-endpoint";
import KeyboardBackspaceIcon from '@mui/icons-material/KeyboardBackspace';
import {
  getDatabase,
  push,
  serverTimestamp,
  set,
  update,
} from "firebase/database";


export default function GroupInfo({ setOpenPageOne, setOpenPage }) {
  const paper = {
    padding: "0rem",
    maxWidth: "27rem",
    overflowY : "scroll",
    backgroundColor: "rgb(255, 222, 173)",
    boxShadow: "unset"
  };

  const { currentUser } = useSelector((state) => state.auth);
  const { openedGroupInfo } = useSelector((state) => state.group);
  const { openedGroupMembers } = useSelector((state) => state.group);
  console.log("openedGroupInfo", openedGroupInfo);
  console.log("openedGroupMembers", openedGroupMembers);
  const [groupMemberList, setGroupMemberList] = useState([]);

  const handleExitGroup = async (groupId, userId, isAdmin) => {
    const groupMembersRef = ref(db, `groups/${groupId}/groupMembers`);

    try {
      const groupMembersSnapshot = await get(groupMembersRef);
      const groupMembers = groupMembersSnapshot.val();

      if (Array.isArray(groupMembers) && groupMembers.includes(userId)) {
        const updatedGroupMembers = groupMembers.filter(
          (member) => member !== userId
        );

        await runTransaction(groupMembersRef, () => updatedGroupMembers);
        Swal.fire({
          title: "Success",
          text: !isAdmin ? "Group Exited" : "User removed from group",
          icon: "success",
        });
        console.log("User removed from the group successfully");
      } else {
        Swal.fire({
          title: "Error",
          text: !isAdmin
            ? "You are no longer participant!"
            : "This user is no longer participant",
          icon: "error",
        });
        console.log("User is not a member of the group.");
      }
    } catch (error) {
      console.error("Error removing user from group:", error);
      throw error;
    }
  };

// const fetchAllGroups = async () => {
//   try {
//     const authToken = localStorage.getItem("token");
//     if (!authToken) {
//       throw new Error("Authentication token not found.");
//     }

//     const response = await axios.get(`${URL}/group-chat`, {
//       headers: {
//         'Authorization': `${authToken}`,
//       },
//     });

//     return response.data;
//   } catch (error) {
//     console.error("Error fetching groups:", error);
//     throw error;
//   }
// };

  const handleFetchGroupMembers = async (id) => {
    try {
      // let groups = fetchAllGroups();
      // console.log("groups", groups);
      console.log(openedGroupInfo.groupMembers,"openedGroupInfo",id,"id");
      
      const authToken = localStorage.getItem("token");
      const response = await axios.get(`${URL}/subscribe-group/member/${id}`, {
        headers: {
          'Authorization': `${authToken}`
        },
      });
      setGroupMemberList(response.data.data);
      console.log(groupMemberList,"groupMemberList");
      
    } catch (error) {
      console.error('Error fetching group members:', error);
      throw error;
    }
  };

  // useEffect(() => {
  //   handleFetchGroupMembers(openedGroupInfo?.groupId);
  // });

  useEffect(() => {
    if (openedGroupInfo?.groupId) {
      // Find the matching group in openedGroupMembers
      console.log(openedGroupMembers,"openedGroupMembers");
      
      const matchedGroup = openedGroupMembers.find(
        (group) => group.groupId === openedGroupInfo.groupId
      );

      if (matchedGroup) {
        console.log(matchedGroup.groupId,"matchedGroup");
        
        // const extraGroupId = matchedGroup?.externalGroup?.id;
        handleFetchGroupMembers(matchedGroup.groupId);
      } else {
        console.warn("No matching group found in openedGroupMembers.");
      }
    }
  }, [openedGroupInfo, openedGroupMembers]);

  const dispatch = useDispatch();
  const handleGroupInvite = async () => {
    await dispatch(clearUsersToCreateGroup());
    if (window.matchMedia("(max-width: 430px)").matches) {
      const sectionOne = document.querySelector('.section-one');
      const sectionThree = document.querySelector('.section-three');
      if (sectionOne) {
        sectionOne.style.display = 'block';
      }
      if (sectionThree) {
        sectionThree.style.display = 'none';
      }
    }
    setOpenPageOne("addGroupMember")
    setOpenPage("")
  };

  const roleId = localStorage.getItem("roleID");

 const handleShowPrevPage = () => {
    if (window.matchMedia("(max-width: 430px)").matches) {
      const sectionTwoMobile = document.querySelector('.section-two-mobile');
      const sectionThree = document.querySelector('.section-three');
      if (sectionTwoMobile) {
        sectionTwoMobile.style.display = 'block';
      }
      if (sectionThree) {
        sectionThree.style.display = 'none';
      }
    }
 }
  
  return (
    <Box
      sx={{ display: "flex", justifyContent: "center", alignItems: "center" }}
    >
      <Paper
        sx={{ flexGrow: 1 }}
        elevation={10}
        style={{
          ...paper,
          maxWidth: window.matchMedia("(max-width: 430px)").matches ? "23rem" : "",
        }}
        className="groupInfoPaper chatPaper">
      <div className="flex justify-center items-center">
      {window.matchMedia("(max-width: 430px)").matches && (
        <KeyboardBackspaceIcon
          onClick={(e) => {
            handleShowPrevPage();
          }}
          sx={{ cursor: "pointer", color: "orange", fontSize: "23px", marginLeft: "12px" }}
        />
      )}
        <NavGroupInfo
          backButtonPath={"/group-chat/creator"}
          groupName={openedGroupInfo.groupName}
        />
      </div>
        <Box
          sx={{
            padding: "8px",
            display: "flex",
            flexDirection: "row",
            justifyContent: "center",
            alignItems: "center",
          }}
        >
          <Box
            sx={{
              display: "flex",
              flexDirection: "column",
              justifyContent: "center",
              alignItems: "center",
            }}
          >
            <img
              style={{ borderRadius: "100%", width: "250px", height: "250px" }}
              src={openedGroupInfo?.groupProfileURL ? openedGroupInfo?.groupProfileURL : "https://i.ibb.co/5xCF7vx/u-https-spng-pngfind-com-pngs-s-610-6104451-image-placeholder-png-user-profile-placeholder-image-png.jpg"}
              alt=""
            />
          </Box>
          <Box
            sx={{
              display: "flex",
              flexDirection: "column",
              justifyContent: "top",
              alignItems: "top",
              height: "250px",
            }}
          >
            {(roleId === "3" || roleId === "1") && (
              <div title="Add member">
                <button type="button" onClick={handleGroupInvite}>
                  <svg
                    style={{
                      filter: "drop-shadow(0px 4px 4px rgba(0,0,0,0.25))",
                    }}
                    xmlns="http://www.w3.org/2000/svg"
                    width="34"
                    height="34"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    stroke-width="2"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  >
                    <line x1="12" y1="5" x2="12" y2="19"></line>
                    <line x1="5" y1="12" x2="19" y2="12"></line>
                  </svg>
                </button>
              </div>
            )}
          </Box>
        </Box>
        <h5
          style={{ textAlign: "center", fontSize: "16px", marginTop: "11px" }}
        >
          {openedGroupInfo.groupName}
        </h5>

        <Box>
          {/* <Typography  className="select-buddies-text">
            Total Group Members : {openedGroupInfo?.groupMembers?.length}
          </Typography> */}
          <Typography  className="select-buddies-text">
            Total Group Members : {groupMemberList?.length}
          </Typography>
        </Box>

        <Box>
          <Typography mt={-1} className="select-buddies-text">
            Group Created at : {openedGroupInfo?.createdAt ? 
        `${new Date(openedGroupInfo?.createdAt).toLocaleDateString('en-GB', {
          day: '2-digit',
          month: '2-digit',
          year: 'numeric',
        })} ${new Date(openedGroupInfo?.createdAt).toLocaleTimeString('en-US', {
          hour: '2-digit',
          minute: '2-digit',
        })}` 
        : ''}
          </Typography>
        </Box>

        {Array.isArray(groupMemberList) && groupMemberList.map((item, index) => (
          <GroupMembers
            isAdmin={openedGroupInfo.createdById === currentUser.uid}
            key={index}
            item={item}
            handleExitGroup={handleExitGroup}
            openedGroupId={openedGroupInfo.groupId}
            currentUser={currentUser}
            index={index}
          />
        ))}
         {/* {openedGroupInfo?.groupMembers?.map((item, index) => (
          <GroupMembers
            isAdmin={openedGroupInfo.createdById === currentUser.uid}
            key={index}
            item={item}
            handleExitGroup={handleExitGroup}
            openedGroupId={openedGroupInfo.groupId}
            currentUser={currentUser}
            index={index}
          />
        ))} */}
      </Paper>
    </Box>
  );
}