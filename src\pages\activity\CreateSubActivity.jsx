import React, { useState } from "react";
import { TextField, Select, MenuItem, Button } from "@mui/material";
import axios from "axios";
import './activitypages.css'
import Header from "../../components/Header";

const initialValues = {
  subactivityName: "",
  activityName: "",
};

const CreateSubActivity = () => {
  const [values, setValues] = useState(initialValues);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    console.log(name, value);
    setValues({
      ...values,
      [name]: value,
    });
  };

  const handleSubmit = async () => {
    try {
      const response = await axios.post(
        "https://api.example.com/endpoint",
        { data: values },
        {
          headers: {
            "Content-Type": "application/json",
            // Authorization: "Bearer your_access_token_here",
          },
        }
      );
      console.log(response.data);
    } catch (err) {
      console.log(err.message);
    }
  };
  return (
    <>
    <Header/>
    <div className="container">
      <div className="title">Create Sub Activity</div>
      <div className="form-body">
        <div className="form-group">
          <label className="lable"> Sub Activity Name</label>
          <div className="activity-inputs">
            <TextField
              className="activity-input"
              id="outlined-basic"
              label="Select"
              variant="outlined"
              name="subactivityName"
              value={values.subactivityName}
              onChange={handleInputChange}
            />
          </div>
        </div>
        <div className="form-group">
          <label className="lable">Activity </label>
          <div className="activity-inputs">
            <Select
              labelId="demo-simple-select-label"
              id="demo-simple-select"
              className="activity-autocomplete"
              name="activityName"
              value={values.activityName}
              onChange={handleInputChange}
              label="select"
              placeholder="select"
            >
              <MenuItem value={10}>Ten</MenuItem>
              <MenuItem value={20}>Twenty</MenuItem>
              <MenuItem value={30}>Thirty</MenuItem>
            </Select>
          </div>
        </div>
        <div className="form-group">
          <Button
            sx={{ background: "black", marginLeft: "-2.5%" }}
            variant="contained"
            onClick={handleSubmit}
          >
            Create Sub Activity
          </Button>
        </div>
      </div>
    </div>
    </>
  );
};

export default CreateSubActivity;
