import { useState, useEffect } from "react";
import { But<PERSON> } from "../ui/button";
import { Input } from "../ui/input";
import { Label } from "../ui/label";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "../ui/select";
import { <PERSON>alog, DialogContent, DialogHeader, DialogTitle } from "../ui/dialog";
import {
	createSugmentdata,
	updateSugmentdata,
	getAllAssesmentData,
} from "../../API/api-endpoint";
import Swal from "sweetalert2";

export const SegmentDialog = ({ open, onClose, onSuccess, editingItem }) => {
	const [formData, setFormData] = useState({
		name: "",
		assessment_id: "",
		segment_percentage: "",
	});
	const [isLoading, setIsLoading] = useState(false);
	const [assessmentList, setAssessmentList] = useState([]);
	const [isLoadingAssessments, setIsLoadingAssessments] = useState(false);

	// Fetch assessments when dialog opens
	useEffect(() => {
		if (open) {
			fetchAssessments();
		}
	}, [open]);

	// Reset form when dialog opens/closes or when editing different item
	useEffect(() => {
		if (open) {
			if (editingItem?.id) {
				// Editing existing segment
				const editData = {
					name: editingItem.name || "",
					assessment_id: String(editingItem.assessment_id || ""),
					segment_percentage: String(
						editingItem.segment_percentage || ""
					),
				};
				setFormData(editData);
			} else {
				// Creating new segment
				const newData = {
					name: "",
					assessment_id: "",
					segment_percentage: "",
				};
				setFormData(newData);
			}
		}
	}, [open, editingItem]);

	const fetchAssessments = async () => {
		try {
			setIsLoadingAssessments(true);
			const response = await getAllAssesmentData();
			if (response) {
				setAssessmentList(response || []);
			}
		} catch (error) {
			console.error("Error fetching assessments:", error);
			Swal.fire({
				title: "Error",
				text: "Failed to fetch assessments",
				icon: "error",
				timer: 3000,
				showConfirmButton: false,
			});
		} finally {
			setIsLoadingAssessments(false);
		}
	};

	const handleInputChange = (field, value) => {
		setFormData((prev) => ({
			...prev,
			[field]: value,
		}));
	};

	const handleSubmit = async (e) => {
		e.preventDefault();

		// Validation
		if (!formData.assessment_id) {
			Swal.fire({
				title: "Error",
				text: "Assessment name is required",
				icon: "error",
				timer: 1800,
				showConfirmButton: false,
			});
			return;
		}

		if (!formData.name.trim()) {
			Swal.fire({
				title: "Error",
				text: "Segment name is required",
				icon: "error",
				timer: 1800,
				showConfirmButton: false,
			});
			return;
		}

		if (!formData.segment_percentage.trim()) {
			Swal.fire({
				title: "Error",
				text: "Segment percentage is required",
				icon: "error",
				timer: 1800,
				showConfirmButton: false,
			});
			return;
		}

		// Validate percentage is a number between 0 and 100
		const percentage = parseFloat(formData.segment_percentage);
		if (isNaN(percentage) || percentage < 0 || percentage > 100) {
			Swal.fire({
				title: "Error",
				text: "Segment percentage must be a number between 0 and 100",
				icon: "error",
				timer: 1800,
				showConfirmButton: false,
			});
			return;
		}

		try {
			setIsLoading(true);

			// Prepare data for API
			const apiData = {
				name: formData.name.trim(),
				assessment_id: parseInt(formData.assessment_id),
				segment_percentage: parseFloat(formData.segment_percentage),
			};

			let response;
			if (editingItem?.id) {
				apiData.id = editingItem.id;
				response = await updateSugmentdata(apiData);
			} else {
				response = await createSugmentdata(apiData);
			}

			if (response?.status) {
				Swal.fire({
					title: "Success",
					text:
						response.message ||
						`Segment ${
							editingItem?.id ? "updated" : "created"
						} successfully`,
					icon: "success",
					timer: 2000,
					showConfirmButton: false,
				});
				onSuccess();
			} else {
				Swal.fire({
					title: "Error",
					text: response?.message || "Failed to save segment",
					icon: "error",
					timer: 3000,
					showConfirmButton: false,
				});
			}
		} catch (error) {
			console.error("Error saving segment:", error);
			Swal.fire({
				title: "Error",
				text: "An error occurred while saving the segment",
				icon: "error",
				timer: 3000,
				showConfirmButton: false,
			});
		} finally {
			setIsLoading(false);
		}
	};

	return (
		<Dialog open={open} onOpenChange={onClose}>
			<DialogContent className='sm:max-w-md bg-white'>
				<DialogHeader>
					<DialogTitle className='text-lg font-semibold text-gray-900'>
						{editingItem?.id ? "Edit Segment" : "Create Segment"}
					</DialogTitle>
				</DialogHeader>

				<form onSubmit={handleSubmit} className='space-y-4'>
					<div className='grid gap-4'>
						<div className='space-y-2'>
							<Label
								htmlFor='assessment_id'
								className='text-sm font-semibold'
							>
								Assessment Name
							</Label>
							<Select
								value={formData.assessment_id}
								onValueChange={(value) =>
									handleInputChange("assessment_id", value)
								}
								disabled={isLoading || isLoadingAssessments}
							>
								<SelectTrigger>
									<SelectValue placeholder='Select an assessment' />
								</SelectTrigger>
								<SelectContent className='bg-white'>
									{assessmentList.map((assessment) => (
										<SelectItem
											key={assessment.id}
											value={assessment.id.toString()}
										>
											{assessment.name}
										</SelectItem>
									))}
								</SelectContent>
							</Select>
						</div>

						<div className='space-y-2'>
							<Label
								htmlFor='name'
								className='text-sm font-semibold'
							>
								Segment Name
							</Label>
							<Input
								id='name'
								type='text'
								className='w-full text-sm'
								value={formData.name}
								onChange={(e) =>
									handleInputChange("name", e.target.value)
								}
								placeholder='Enter segment name'
								disabled={isLoading}
								required
							/>
						</div>

						<div className='space-y-2'>
							<Label
								htmlFor='segment_percentage'
								className='text-sm font-semibold'
							>
								Segment Percentage
							</Label>
							<Input
								id='segment_percentage'
								type='number'
								min='0'
								max='100'
								step='0.01'
								className='w-full text-sm'
								value={formData.segment_percentage}
								onChange={(e) =>
									handleInputChange(
										"segment_percentage",
										e.target.value
									)
								}
								placeholder='Enter percentage (0-100)'
								disabled={isLoading}
								required
							/>
						</div>
					</div>

					<div className='flex justify-end gap-3 pt-4'>
						<Button
							type='button'
							variant='outline'
							onClick={onClose}
							disabled={isLoading}
						>
							Cancel
						</Button>
						<Button
							type='submit'
							className='bg-orange-600 hover:bg-orange-700 text-white'
							disabled={isLoading}
						>
							{isLoading
								? "Saving..."
								: editingItem?.id
								? "Update Segment"
								: "Create Segment"}
						</Button>
					</div>
				</form>
			</DialogContent>
		</Dialog>
	);
};
