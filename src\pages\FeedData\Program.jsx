import { useEffect, useState, useMemo } from "react";
import { <PERSON><PERSON> } from "../../components/ui/button";
import { Input } from "../../components/ui/input";
import {
	Card,
	CardContent,
	CardHeader,
	CardTitle,
} from "../../components/ui/card";
import {
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableHeader,
	TableRow,
} from "../../components/ui/table";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "../../components/ui/select";
import { Badge } from "../../components/ui/badge";
import { Edit, Trash2, Plus } from "lucide-react";
import { ProgramDialog } from "../../components/admin/program-dialog";
import { DeleteConfirmDialog } from "../../components/admin/delete-confirm-dialog";
import Header from "../../components/Header";
import {
	getAllPrograms,
	deletePrograms,
	getAllActivityData,
} from "../../API/api-endpoint";
import Swal from "sweetalert2";

const Program = () => {
	const [programs, setPrograms] = useState([]);
	const [activities, setActivities] = useState([]);
	const [activityFilterOptions, setActivityFilterOptions] = useState([]);
	const [isLoading, setIsLoading] = useState(true);
	const [showDialog, setShowDialog] = useState(false);
	const [editingProgram, setEditingProgram] = useState(null);
	const [deleteId, setDeleteId] = useState(null);
	const [searchTerm, setSearchTerm] = useState("");
	const [activityFilter, setActivityFilter] = useState("All");
	const [currentPage, setCurrentPage] = useState(1);
	const pageSize = 10;

	// Fetch programs data
	const fetchPrograms = async () => {
		try {
			setIsLoading(true);
			const response = await getAllPrograms();
			if (response?.status) {
				setPrograms(response.rows || []);
			}
		} catch (error) {
			console.error("Error fetching programs:", error);
			Swal.fire({
				title: "Error",
				text: "Failed to fetch programs",
				icon: "error",
			});
		} finally {
			setIsLoading(false);
		}
	};

	// Fetch activities for filter dropdown and dialog
	const fetchActivities = async () => {
		try {
			const response = await getAllActivityData();
			if (response?.status) {
				const activityData = response.rows || [];

				// Store original activities for dialog (without "All")
				setActivities(activityData);

				// Create filter options with "All" for dropdown
				const activitiesWithAll = [
					{ id: "all", activity_name: "All" },
					...activityData,
				];
				setActivityFilterOptions(activitiesWithAll);
			}
		} catch (error) {
			console.error("Error fetching activities:", error);
			setActivities([]);
			setActivityFilterOptions([{ id: "all", activity_name: "All" }]);
		}
	};

	useEffect(() => {
		fetchPrograms();
		fetchActivities();
	}, []);

	// Filter and search logic
	const filteredPrograms = useMemo(() => {
		let filtered = programs;

		// Search filter
		if (searchTerm) {
			filtered = filtered.filter((program) =>
				program.program_name
					?.toLowerCase()
					.includes(searchTerm.toLowerCase())
			);
		}

		// Activity filter
		if (activityFilter !== "All") {
			filtered = filtered.filter((program) => {
				const activity = activities.find(
					(act) => act.id === program.activity_id
				);
				return activity?.activity_name === activityFilter;
			});
		}

		return filtered;
	}, [programs, searchTerm, activityFilter, activities]);

	// Pagination
	const paginatedPrograms = useMemo(() => {
		const startIndex = (currentPage - 1) * pageSize;
		return filteredPrograms.slice(startIndex, startIndex + pageSize);
	}, [filteredPrograms, currentPage, pageSize]);

	const totalPages = Math.ceil(filteredPrograms.length / pageSize);

	// Get activity name by ID (updated for PhaseBlock pattern)
	const getActivityName = (activityId) => {
		// First try to find by ID in the original activities data
		const response = activities.find((act) => act?.id === activityId);
		if (response) {
			return response.activity || response.activity_name || "Unknown";
		}

		// If not found, return Unknown
		return "Unknown";
	};

	// Get activity badge
	const getActivityBadge = (activityId) => {
		const activityName = getActivityName(activityId);
		const colors = {
			Running: "bg-blue-100 text-blue-800",
			Cycling: "bg-green-100 text-green-800",
			Swimming: "bg-cyan-100 text-cyan-800",
			Triathlon: "bg-purple-100 text-purple-800",
		};

		const colorClass = colors[activityName] || "bg-gray-100 text-gray-800";

		return <Badge className={colorClass}>{activityName}</Badge>;
	};

	// Handle edit
	const handleEdit = (program) => {
		setEditingProgram(program);
		setShowDialog(true);
	};

	// Handle delete
	const handleDelete = async (id) => {
		try {
			const response = await deletePrograms(id);
			if (response?.status) {
				Swal.fire({
					title: "Success",
					text: "Program deleted successfully",
					icon: "success",
				});
				fetchPrograms();
			} else {
				Swal.fire({
					title: "Error",
					text: response?.message || "Failed to delete program",
					icon: "error",
				});
			}
		} catch (error) {
			console.error("Error deleting program:", error);
			Swal.fire({
				title: "Error",
				text: "Failed to delete program",
				icon: "error",
			});
		}
		setDeleteId(null);
	};

	// Handle dialog close
	const handleDialogClose = () => {
		setShowDialog(false);
		setEditingProgram(null);
	};

	// Handle successful save
	const handleSaveSuccess = () => {
		fetchPrograms();
		handleDialogClose();
	};

	return (
		<div>
			<Header />
			<div className='mx-auto p-6 max-w-[1300px] mt-16'>
				<Card>
					<CardHeader className='bg-orange-50 border-b'>
						<div className='flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4'>
							<CardTitle className='text-2xl font-bold text-orange-900'>
								Programs
							</CardTitle>
							<Button
								onClick={() => setShowDialog(true)}
								className='bg-orange-600 hover:bg-orange-700 text-white'
							>
								<Plus className='h-4 w-4 mr-2' />
								Create Program
							</Button>
						</div>

						<div className='flex flex-col sm:flex-row gap-4 mt-4'>
							<div className='flex-1'>
								<Input
									placeholder='Search by program name...'
									value={searchTerm}
									onChange={(e) =>
										setSearchTerm(e.target.value)
									}
									className='max-w-sm text-sm'
								/>
							</div>
							<div className='w-full sm:w-48'>
								<Select
									value={activityFilter}
									onValueChange={setActivityFilter}
								>
									<SelectTrigger>
										<SelectValue placeholder='Filter by Activity' />
									</SelectTrigger>
									<SelectContent className='bg-white'>
										{activityFilterOptions.map(
											(activity) => (
												<SelectItem
													key={activity.id}
													value={
														activity.activity_name
													}
												>
													{activity.activity_name}
												</SelectItem>
											)
										)}
									</SelectContent>
								</Select>
							</div>
						</div>
					</CardHeader>

					<CardContent className='p-0'>
						<div className='overflow-x-auto'>
							<Table className='min-w-full w-full'>
								<TableHeader>
									<TableRow className='bg-blue-600 hover:bg-blue-600'>
										<TableHead className='text-white font-semibold'>
											Sr No
										</TableHead>
										<TableHead className='text-white font-semibold'>
											Program Name
										</TableHead>
										<TableHead className='text-white font-semibold'>
											Activity
										</TableHead>
										<TableHead className='text-white font-semibold'>
											Actions
										</TableHead>
									</TableRow>
								</TableHeader>
								<TableBody>
									{isLoading ? (
										<TableRow>
											<TableCell
												colSpan={5}
												className='text-center py-8'
											>
												Loading...
											</TableCell>
										</TableRow>
									) : paginatedPrograms.length > 0 ? (
										paginatedPrograms.map(
											(program, index) => (
												<TableRow
													key={program.program_id}
													className='hover:bg-gray-50'
												>
													<TableCell className='font-medium'>
														{(currentPage - 1) *
															pageSize +
															index +
															1}
													</TableCell>
													<TableCell className='font-medium'>
														{program.program_name}
													</TableCell>
													<TableCell>
														{getActivityBadge(
															program.activity_id
														)}
													</TableCell>

													<TableCell>
														<div className='flex gap-2'>
															<Button
																variant='ghost'
																size='sm'
																onClick={() =>
																	handleEdit(
																		program
																	)
																}
																className='text-blue-600 hover:text-blue-800'
															>
																<Edit className='h-4 w-4' />
															</Button>
															<Button
																variant='ghost'
																size='sm'
																onClick={() =>
																	setDeleteId(
																		program.program_id
																	)
																}
																className='text-red-700 hover:text-red-800'
															>
																<Trash2 className='h-4 w-4' />
															</Button>
														</div>
													</TableCell>
												</TableRow>
											)
										)
									) : (
										<TableRow>
											<TableCell
												colSpan={5}
												className='text-center py-8 text-gray-500'
											>
												No programs found
											</TableCell>
										</TableRow>
									)}
								</TableBody>
							</Table>
						</div>

						{totalPages > 1 && (
							<div className='flex justify-center gap-2 p-4 border-t'>
								<Button
									variant='outline'
									size='sm'
									onClick={() =>
										setCurrentPage(
											Math.max(1, currentPage - 1)
										)
									}
									disabled={currentPage === 1}
								>
									Previous
								</Button>

								{Array.from({ length: 5 }, (_, i) => {
									const startPage =
										Math.floor((currentPage - 1) / 5) * 5 +
										1;
									const page = startPage + i;
									if (page > totalPages) return null;

									return (
										<Button
											key={page}
											variant={
												currentPage === page
													? "default"
													: "outline"
											}
											size='sm'
											onClick={() => setCurrentPage(page)}
											className={
												currentPage === page
													? "bg-orange-500 hover:bg-orange-600 text-white"
													: ""
											}
										>
											{page}
										</Button>
									);
								})}

								<Button
									variant='outline'
									size='sm'
									onClick={() =>
										setCurrentPage(
											Math.min(
												totalPages,
												currentPage + 1
											)
										)
									}
									disabled={currentPage === totalPages}
								>
									Next
								</Button>
							</div>
						)}
					</CardContent>
				</Card>
			</div>

			<ProgramDialog
				open={showDialog}
				onClose={handleDialogClose}
				onSave={handleSaveSuccess}
				editingProgram={editingProgram}
				activities={activities}
			/>

			<DeleteConfirmDialog
				open={!!deleteId}
				onOpenChange={(open) => !open && setDeleteId(null)}
				onConfirm={() => handleDelete(deleteId)}
				title='Delete Program'
				description='Are you sure you want to delete this program? This action cannot be undone.'
			/>
		</div>
	);
};

export default Program;
