import React, { useContext } from 'react'
import { Link } from 'react-router-dom'
import GlobalContext from '../context/GlobalContext'

const LoginPage = ({
  username,
  setUsername,
  password,
  setPassword,
  handleSubmit
}) => {
  const { isRemember, setIsRemember } = useContext(GlobalContext)

  return (
    <div className='container mx-auto'>
      <div className='grid grid-cols-1 md:grid-cols-2 items-center gap-x-4'>
        <div>
          <img
            src='https://app.yoska.in/kona-coach/images/app/backgrounds/new.jpg'
            alt=''
            className='md:h-screen object-cover'
          />
        </div>
        <form
          onSubmit={handleSubmit}
          className='px-8 py-12 border border-slate-300/75 bg-white rounded-md w-full md:w-full lg:w-9/12 xl:w-7/12 mx-auto'
        >
          <h1 className='text-2xl font-semibold text-blue-800 text-center'>
            User Login
          </h1>
          <br />
          <div className='mb-5'>
            <p className='mb-1 px-1 font-medium text-blue-800'>Username</p>
            <input
              type='text'
              placeholder='Enter your username'
              name='username'
              id='username'
              required
              value={username}
              onChange={(e) => setUsername(e.target.value)}
              className='w-full border border-slate-200 placeholder:text-sm p-2 rounded-md focus:outline-none text-slate-600'
            />
          </div>
          <div className='mb-4'>
            <p className='mb-1 px-1 font-medium text-blue-800'>Password</p>
            <input
              type='password'
              placeholder='Enter your password'
              name='password'
              id='password'
              required
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              className='w-full border border-slate-200 placeholder:text-sm p-2 rounded-md focus:outline-none text-slate-600'
            />
          </div>
          <div className='mb-5 flex justify-between items-center'>
            <div className='flex items-center'>
              <input
                type='checkbox'
                name='welcome'
                id='welcome'
                defaultChecked={isRemember}
                onChange={() => setIsRemember(!isRemember)}
              />
              <span className='ml-2 text-sm'>Remember me</span>
            </div>
            <Link to={'#'}>
              <span className='text-sm text-blue-700 font-medium'>
                Forget Password
              </span>
            </Link>
          </div>
          <div className='mb-2'>
            <button
              className='p-2.5 border border-blue-700 text-blue-700 w-full rounded-md duration-150 ease-in-out hover:bg-blue-700 hover:text-slate-50'
              type='submit'
            >
              Log in
            </button>
          </div>
        </form>
      </div>
    </div>
  )
}

export default LoginPage
