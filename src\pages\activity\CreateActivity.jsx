import React, { useState } from "react";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON> } from "@mui/material";
import MenuItem from "@mui/material/MenuItem";
import Select from "@mui/material/Select";
import "./activitypages.css";
import Header from "../../components/Header";
// import axios from "axios";

const initialValues = {
  activityName: "",
  unitOfMeasure1: "",
  unitOfMeasure2: "",
  icon: {},
};

const CreateActivity = () => {
  const [values, setValues] = useState(initialValues);

  const handleFileSelect = (event) => {
    const file = event.target.files[0];
    console.log(file, "file");
    setValues({ ...values, icon: file });
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    console.log(name, value);
    setValues({
      ...values,
      [name]: value,
    });
  };

  const handleSubmit = async () => {
    console.log(values);
    // try {
    //   const response = await axios.post(
    //     "https://api.example.com/endpoint",
    //     { data: values },
    //     {
    //       headers: {
    //         "Content-Type": "application/json",
    //          Authorization: "Bearer your_access_token_here",
    //       },
    //     }
    //   );
    //   console.log(response.data);
    // } catch (err) {
    //   console.log(err.message);
    // }
  };
  return (
    <>
      {" "}
      <Header />
      <div className="container">
        <div className="title">Create Activity</div>
        <div className="form-body">
          <div className="form-group">
            <label className="lable">Activity Name</label>
            <div className="activity-inputs">
              <TextField
                className="activity-input"
                id="outlined-basic"
                label="Running"
                variant="outlined"
                name="activityName"
                value={values.activityName}
                onChange={handleInputChange}
              />
            </div>
          </div>
          <div className="form-group">
            <label className="lable">Unit of measure #1 </label>
            <div className="activity-inputs">
              <Select
                labelId="demo-simple-select-label"
                id="demo-simple-select"
                className="activity-autocomplete"
                name="unitOfMeasure1"
                value={values.unitOfMeasure1}
                onChange={handleInputChange}
              >
                <MenuItem value={10}>Ten</MenuItem>
                <MenuItem value={20}>Twenty</MenuItem>
                <MenuItem value={30}>Thirty</MenuItem>
              </Select>
            </div>
          </div>
          <div className="form-group">
            <label className="lable">Unit of measure #2 </label>
            <div className="activity-inputs">
              <Select
                labelId="demo-simple-select-label"
                id="demo-simple-select"
                className="activity-autocomplete"
                name="unitOfMeasure2"
                value={values.unitOfMeasure2}
                onChange={handleInputChange}
              >
                <MenuItem value={10}>Ten</MenuItem>
                <MenuItem value={20}>Twenty</MenuItem>
                <MenuItem value={30}>Thirty</MenuItem>
              </Select>
            </div>
          </div>
          <div className="form-group">
            <label className="lable">Selecet icon </label>
            <div className="activity-inputs">
              <input type="file" onChange={handleFileSelect} />
            </div>
          </div>
          <div className="form-group">
            <Button
              sx={{ background: "black", marginLeft: "-4.5%" }}
              onClick={handleSubmit}
              variant="contained"
            >
              Create Activity
            </Button>
          </div>
        </div>
      </div>
    </>
  );
};

export default CreateActivity;
