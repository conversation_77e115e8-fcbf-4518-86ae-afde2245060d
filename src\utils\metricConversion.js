// Function to convert "HH:MM:SS" or "HH:MM" to decimal hours (HH.MM)
export const timeToHours = (timeStr) => {
    if (!timeStr) return 0;
    const parts = timeStr.split(":").map(Number);

    if (parts.length === 3) {
        // HH:MM:SS format
        return parts[0] + parts[1] / 60 + parts[2] / 3600;
    } else if (parts.length === 2) {
        // HH:MM format
        return parts[0] + parts[1] / 60;
    }

    return 0; // Default if unexpected format
};

// Function to convert "MM:SS" or "HH:MM:SS" to decimal minutes
export const timeToDecimalMinutes = (timeStr) => {
    if (!timeStr) return 0;
    const parts = timeStr.split(":").map(Number);

    if (parts.length === 3) {
        // HH:MM:SS format
        return parts[0] * 60 + parts[1] + parts[2] / 60;
    } else if (parts.length === 2) {
        // MM:SS format
        return parts[0] + parts[1] / 60;
    }

    return 0; // Default if unexpected format
};

// Function to parse speed values (ensures proper conversion from "00.00" format)
export const parseSpeed = (speedStr) => {
    if (!speedStr || speedStr === "00.00") return 0; // Handle empty or zero values
    return parseFloat(speedStr) || 0; // Convert string to number, fallback to 0
};