import { Box, Typography } from '@mui/material'
import React, { memo } from 'react'

function PlusToolbox() {
    return <Box className="plus-bar-content">
        <Box className="plus-bar-icon-div">
            <img height={32} src="/images/profile-icon.png" alt="emoji" />
            <Typography className='plus-bar-icon-label'>Profile</Typography>
        </Box>
        <Box className="plus-bar-icon-div">
            <img height={32} src="/images/user-icon.png" alt="emoji" />
            <Typography className='plus-bar-icon-label'>Contact</Typography>
        </Box>

        <Box className="line-plus-icon-menu"></Box>

        <Box className="plus-bar-icon-div">
            <img height={32} src="/images/location-icon.png" alt="emoji" />
            <Typography className='plus-bar-icon-label'>Location</Typography>
        </Box>
        <Box className="plus-bar-icon-div">
            <img height={32} src="/images/document-icon.png" alt="emoji" />
            <Typography className='plus-bar-icon-label'>Document</Typography>
        </Box>

        <Box className="line-plus-icon-menu"></Box>

        <Box className="plus-bar-icon-div">
            <img height={32} src="/images/camera-icon.png" alt="emoji" />
            <Typography className='plus-bar-icon-label'>Camera</Typography>
        </Box>
        <Box className="plus-bar-icon-div">
            <img height={32} src="/images/gallery-icon.png" alt="emoji" />
            <Typography className='plus-bar-icon-label'>Gallery</Typography>
        </Box>

    </Box>
}

export default memo(PlusToolbox)