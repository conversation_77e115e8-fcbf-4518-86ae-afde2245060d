import React from 'react'
import Header from '../components/Header'
import { Menu } from '@mantine/core'
import { IconFolder, IconFolderPlus, IconSearch, IconX } from '@tabler/icons'

const WorkoutsPage = () => {
  const athletes = [{ id: 1, name: 'Aerobic Workouts (1)' }]

  const actions = [
    {
      id: 1,
      title: 'Copy workout(s)'
    },
    {
      id: 2,
      title: 'Cut workout(s)'
    },
    {
      id: 3,
      title: 'Paste workout(s)'
    },
    {
      id: 4,
      title: 'Edit workout library'
    },
    {
      id: 5,
      title: 'Delete workout library'
    },
    {
      id: 6,
      title: 'Archive workout library'
    }
  ]

  return (
    <>
      <Header />
      <div className='grid grid-cols-1 xl:grid-cols-5'>
        <div className='hidden lg:block w-80 p-4 bg-slate-100 sticky top-20 h-screen'>
          <div className='flex items-center justify-between'>
            <div className='flex items-center space-x-8'>
              <div>
                <IconFolderPlus size={26} color='dodgerblue' />
              </div>
              <div>
                <IconSearch size={24} color='dodgerblue' />
              </div>
              <div>
                <IconFolder size={22} color='dodgerblue' />
              </div>
            </div>
            <div>
              <IconX size={24} color='dodgerblue' />
            </div>
          </div>
          <br />
          <hr />
          <div className='flex flex-col gap-y-4 items-start my-6'>
            {athletes.map((item) => (
              <div
                className='flex justify-between items-center gap-x-4 w-full'
                key={item.id}
              >
                <div className='flex items-center gap-x-2'>
                  <span className='text-slate-700'>{item.name}</span>
                </div>
                <div>
                  <Menu shadow='md' width={210} position='bottom-end'>
                    <Menu.Target className='cursor-pointer'>
                      <svg
                        xmlns='http://www.w3.org/2000/svg'
                        className='icon icon-tabler icon-tabler-dots-vertical'
                        width='20'
                        height='20'
                        viewBox='0 0 24 24'
                        strokeWidth='1.5'
                        stroke='#2c3e50'
                        fill='none'
                        strokeLinecap='round'
                        strokeLinejoin='round'
                      >
                        <path stroke='none' d='M0 0h24v24H0z' fill='none' />
                        <circle cx='12' cy='12' r='1' />
                        <circle cx='12' cy='19' r='1' />
                        <circle cx='12' cy='5' r='1' />
                      </svg>
                    </Menu.Target>

                    <Menu.Dropdown>
                      {actions.map((item) => (
                        <Menu.Item
                          color={'gray'}
                          icon={item.icon}
                          key={item.id}
                        >
                          {item.title}
                        </Menu.Item>
                      ))}
                    </Menu.Dropdown>
                  </Menu>
                </div>
              </div>
            ))}
          </div>
        </div>
        <div className='flex items-center justify-center p-4 w-full lg:col-span-4'>
          <div className='md:absolute md:top-32 md:right-2/2 px-4 py-6 bg-slate-50 drop-shadow-2xl rounded-md border-2 border-slate-200/75 w-full md:w-8/12 lg:w-6/12 xl:w-4/12'>
            <h1 className='text-xl font-medium m-0 pb-2'>
              Add New Workout Library
            </h1>
            <hr />
            <br />

            <div className='mb-5'>
              <div>
                <p className='mb-1 text-slate-600 px-1 font-medium'>
                  Workout library description{' '}
                  <span className='text-red-500'>*</span>{' '}
                </p>
                <input
                  type='text'
                  placeholder='Description...'
                  className='w-full border-b-2 py-2 rounded-md focus:outline-none text-slate-600 px-1 text-sm'
                  required
                />
              </div>
            </div>
            <div className='mb-2'>
              <button className='p-2.5 bg-orange-500 text-slate-50 w-full rounded-sm'>
                Create
              </button>
            </div>
          </div>
        </div>
      </div>
    </>
  )
}

export default WorkoutsPage
