.timeStamp {
	color: #e67e22;
	font-weight: 600;
	padding-right: 5px;
	font-size: 12px;
}

.badgeIcon {
	display: inline-block;
	height: 22px;
	width: 22px;
	border-radius: 50%;
	background-color: #e67e22;
	color: white;
	text-align: center;
}

.chat-icon {
	/* margin: 5px; */
}

.chat-box-container {
	box-shadow: rgba(60, 64, 67, 0.3) 0px 1px 2px 0px,
		rgba(60, 64, 67, 0.15) 0px 2px 6px 2px;
	border-radius: 8px;
	width: 70%;
	background-color: white;
}

.userName {
	/* color: #111111;
    font-size: 15px !important;
    font-weight: 600 !important;
    line-height: normal !important;
    letter-spacing: 0.7px !important; */

	color: var(--black-2, #111) !important;
	font-size: 14px !important;
	font-style: normal !important;
	font-weight: 600 !important;
	line-height: normal !important;
	letter-spacing: 0.7px !important;
}

.acceptReqPrompt {
	color: #fda623 !important;
	font-size: 10px !important;
	font-family: Open Sauce Sans !important;
	font-style: normal !important;
	font-weight: 700 !important;
	line-height: normal !important;
	letter-spacing: 0.08px !important;
}

.start-new-chat {
	color: var(--black-2, #111) !important;
	font-size: 14px !important;
	font-style: normal !important;
	font-weight: 600 !important;
	line-height: normal !important;
	letter-spacing: 0.14px !important;
}

.chatPaper {
	scrollbar-width: thin; /* For Firefox */
	scrollbar-color: darkgray rgb(255, 222, 173); /* Thumb & Track color */
	overflow-y: auto; /* Ensure vertical scrolling */
	max-height: calc(100vh - 120px) !important;
	height: calc(100vh - 120px) !important;
}

/* Webkit-based browsers (Chrome, Edge, Safari) */
.chatPaper::-webkit-scrollbar {
	width: 8px; /* Visible scrollbar */
}

.chatPaper::-webkit-scrollbar-track {
	background: rgb(255, 222, 173); /* Track color */
	border-radius: 10px;
}

.chatPaper::-webkit-scrollbar-thumb {
	background: darkgray; /* Thumb color */
	border-radius: 10px;
	border: 2px solid rgb(255, 222, 173); /* Creates a gap effect */
}

.chatPaper::-webkit-scrollbar-thumb:hover {
	background: black; /* Darker on hover */
}

.chatLanding {
	position: relative;
	height: 100vh;
	max-height: 100vh;
	overflow: hidden;
}

.chat_box_container {
	position: relative;
}

.create-group-button {
	position: absolute;
	bottom: 20px;
	right: 20px;
	padding: 10px;
	background-color: #e67e22;
	border: none;
	border-radius: 5px;
	cursor: pointer;
	box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.3);
}

.create-group-button.hidden {
	display: none;
}

.Chat-landing-base {
	box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.3);
	border-radius: 8px;
	padding: 2%;
	margin: 5% 1% 1% 1%;
	background-color: rgb(255, 222, 173);
}

.section-one {
	position: relative;
	width: 40%;
	flex-direction: column;
	align-items: center;
}

.section-two {
	border-radius: 8px;
	width: 60%;
}

.section-three {
	width: 30%;
}

@media (max-width: 430px) {
	.section-three {
		width: auto;
	}
}

.mobileChatPaper {
	display: none;
}

.section-two-mobile {
	display: none;
}

@media (max-width: 430px) {
	.chatPaper {
		display: block !important;
		border-radius: 16px 16px 16px 16px !important;
		background-color: rgb(255, 222, 173);
		margin-bottom: 16%;
		overflow-y: auto;
		max-height: calc(100vh - 120px) !important;
		height: calc(100vh - 120px) !important;
		padding-top: 15px !important;
		position: relative !important;
		z-index: 1 !important;
	}
	.create-group-button {
		display: block;
	}
	.section-two {
		display: none;
	}
	.section-three {
		display: none;
	}
	.mobileChatPaper {
		display: none !important;
	}
	.appBar {
		margin-bottom: 10px !important;
	}
	.Chat-landing-base {
		/* box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.3); */
		box-shadow: unset !important;
		/* border-radius: 16px; */
		border-radius: unset !important;
		padding: 2%;
		margin: 16% 0% 0% 0%;
		/* background-color: rgb(255, 222, 173); */
		background-color: unset !important;
		height: calc(100vh - 120px) !important;
		max-height: calc(100vh - 120px) !important;
		display: flex;
		justify-content: center;
		overflow: hidden;
	}
	.chat-box-container {
		box-shadow: unset;
		border: unset;
		background-color: unset;
	}
	.section-three {
		display: none;
	}
	.section-two-mobile {
		display: block;
	}
}

@media (max-width: 768px) {
	.create-group-button {
		/* width: 80%;  */
		/* right: auto;
    left: 50%;
    transform: translateX(-50%); */
		bottom: 10px;
		position: fixed;
		/* left: 10px; */
		font-size: 14px;
		padding: 8px;
	}
}

@media (max-width: 1529px) {
	.chat-box-container {
		max-height: calc(100vh - 120px) !important;
		height: calc(100vh - 120px) !important;
	}
	.groupStepOne {
		max-height: calc(100vh - 120px) !important;
		height: calc(100vh - 120px) !important;
	}
}

@media (min-width: 1463px) and (min-height: 691px) {
	.chat-box-container {
		max-height: calc(100vh - 120px) !important;
		height: calc(100vh - 120px) !important;
	}
	.groupStepOne {
		height: calc(100vh - 120px) !important;
		max-height: calc(100vh - 120px) !important;
	}
	.groupInfoPaper {
		height: calc(100vh - 120px) !important;
		max-height: calc(100vh - 120px) !important;
	}
	.stepOneGroupCreation {
		height: calc(100vh - 120px) !important;
		max-height: calc(100vh - 120px) !important;
	}
	.stepTwoGroupCreation {
		height: calc(100vh - 120px) !important;
		max-height: calc(100vh - 120px) !important;
	}
	.addGroupMember {
		height: calc(100vh - 120px) !important;
		max-height: calc(100vh - 120px) !important;
	}
}

@media (min-width: 1464px) {
	.chat-box-container {
		max-height: calc(100vh - 120px) !important;
		height: calc(100vh - 120px) !important;
	}
	.groupStepOne {
		height: calc(100vh - 120px) !important;
		max-height: calc(100vh - 120px) !important;
	}
	.groupInfoPaper {
		height: calc(100vh - 120px) !important;
		max-height: calc(100vh - 120px) !important;
	}
	.stepOneGroupCreation {
		height: calc(100vh - 120px) !important;
		max-height: calc(100vh - 120px) !important;
	}
	.stepTwoGroupCreation {
		height: calc(100vh - 120px) !important;
		max-height: calc(100vh - 120px) !important;
	}
	.addGroupMember {
		height: calc(100vh - 120px) !important;
		max-height: calc(100vh - 120px) !important;
	}
}

@media (min-width: 1920px) and (min-height: 1080px) {
	.chat-box-container {
		max-height: calc(100vh - 120px) !important;
		height: calc(100vh - 120px) !important;
	}
	.section-three {
		max-height: calc(100vh - 120px) !important;
		height: calc(100vh - 120px) !important;
	}
}

@media (min-width: 1468px) and (min-height: 1534px) {
	.chat-box-container {
		max-height: calc(100vh - 120px) !important;
		height: calc(100vh - 120px) !important;
	}
}

@media (min-width: 1671px) and (min-height: 1050px) {
	.chat-box-container {
		max-height: calc(100vh - 120px) !important;
		height: calc(100vh - 120px) !important;
	}
}
