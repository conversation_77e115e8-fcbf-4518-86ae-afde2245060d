import React, { useEffect, useMemo, useState } from "react";
import Paper from "@mui/material/Paper";
import Table from "@mui/material/Table";
import TableBody from "@mui/material/TableBody";
import TableContainer from "@mui/material/TableContainer";
import TableHead from "@mui/material/TableHead";
import TableRow from "@mui/material/TableRow";
import TableCell, { tableCellClasses } from "@mui/material/TableCell";
import { styled } from "@mui/material/styles";
import { Button, CircularProgress, FormLabel, Pagination, TextField } from "@mui/material";
import Header from "../../components/Header";
import { genrateInvoice, getManageSubscription, putOnHold, putunHold, updateSubscription } from "../../API/api-endpoint";
import { Modal } from "antd";
import { ExclamationCircleFilled } from '@ant-design/icons';
import { showError, showSuccess } from "../../components/Messages";
import moment from "moment";
import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";


const StyledTableCell = styled(TableCell)(({ theme }) => ({
    [`&.${tableCellClasses.head}`]: {
        backgroundColor: "#1e40af",
        color: theme.palette.common.white,
    },
    [`&.${tableCellClasses.body}`]: {
        fontSize: 14,
    },
}));
const StyledTableRow = styled(TableRow)(({ theme }) => ({
    "&:nth-of-type(odd)": {
        backgroundColor: theme.palette.action.hover,
    },
    // hide last border
    "&:last-child td, &:last-child th": {
        border: 0,
    },
}));
const SubscriptionDetails = () => {
    const { confirm } = Modal;
    let PageSize = 15;

    const [formValue, setFormValue] = useState({})
    const [isOpenModal, setIsOpenModal] = useState({ isOpen: false, id: "" })
    const [searchTerm, setSearchTerm] = useState('');
    const [isLoading, setIsLoading] = useState(true)

    const [reportData, setReportData] = useState()
    const [currentPage, setCurrentPage] = useState(1);


    const fetchReport = async () => {
        const response = await getManageSubscription()
        setIsLoading(false)
        setReportData(response)
    }
    useEffect(() => {
        fetchReport()
    }, [])
    const handlUpdateSubscrtion = async (id) => {
        if (formValue?.endDate) {
            let data = {
                sub_id: isOpenModal?.id,
                endDate: formValue?.endDate
            }
            const response = await updateSubscription(data)
            if (response?.status) {
                fetchReport()
                setIsOpenModal({ isOpen: false, id: "" })
            }
        }
        else {
            showError("Please add date")
        }


    }
    const handleGenrateInvoice = async (id, userId) => {
        setIsLoading(true);
        const response = await genrateInvoice(id, userId)
        setIsLoading(false);
        if (response?.status) {
            showSuccess()
            let data = reportData?.map((ele) => {
                return {
                    ...ele, getLink: ele?.subscription_id == id ? response?.url : undefined
                }
            })
            setReportData(data)
        }
        console.log("rseadrsad", response);
    }
    const handleputonHold = async (id) => {
        const response = await putOnHold(id)
        if (response?.status) {
            showSuccess()
            fetchReport()
        }
        console.log("rseadrsad", response);
    }

    const handleputunHold = async (id) => {
        const response = await putunHold(id)
        if (response?.status) {
            showSuccess()
            fetchReport()
        }
        console.log("rseadrsad", response);
    }

    const showConfirm = (id) => {
        confirm({
            title: 'Do you Want to hold this subscription?',
            icon: <ExclamationCircleFilled />,
            onOk() {
                handleputonHold(id)
            },
            onCancel() {
                console.log('Cancel');
            },
        });
    };
    const showConfirmPutOnunhold = (id) => {
        confirm({
            title: 'Do you Want to activate this subscription?',
            icon: <ExclamationCircleFilled />,
            onOk() {
                handleputunHold(id)
            },
            onCancel() {
                console.log('Cancel');
            },
        });
    };
    const filteredList = useMemo(() => {
        return reportData?.filter((row) => {
            // Implement your search logic here
            // For example, if you want to search by a specific property like 'name':
            const firstnameMatches = row?.user?.firstname?.toLowerCase().includes(searchTerm?.toLowerCase());
            const emailMatches = row?.user?.email?.toLowerCase().includes(searchTerm?.toLowerCase());
            return firstnameMatches || emailMatches;
        });
    }, [reportData, searchTerm]);
    const checkLastPage = useMemo(() => {
        let frstPgae = (currentPage - 1) * PageSize;
        let lastPage = frstPgae + PageSize;
        return filteredList?.slice(frstPgae, lastPage)?.map((row, index) => ({
            ...row,
            // Adjusting index on the first page and adding count from the second page onward
            srID: index + 1 + (currentPage > 1 ? frstPgae : 0),
        }));
    }, [currentPage, filteredList]);
    const handleSearchChange = (event) => {
        setSearchTerm(event.target.value);
        setCurrentPage(1); // Reset the current page when the search term changes
    };
    const handlePageChange = (event, page) => {
        setCurrentPage(page);
    };
    const today = new Date();
    const maxDate = new Date();
    maxDate.setDate(today.getDate() + 14);
    return (
        <div style={{ position: "relative", pointerEvents: isLoading ? "none" : "auto" }}>
            {isLoading && (
                <div style={{ position: "absolute", height: "100vh", top: 0, left: 0, right: 0, bottom: 0, backgroundColor: "rgba(255, 255, 255, 0.5)", backdropFilter: "blur(2px)" }}>
                    <CircularProgress style={{ position: "absolute", top: "50%", left: "50%", transform: "translate(-50%, -50%)" }} />
                </div>
            )}
            <Header />
            <div className="grid grid-cols-1 xl:grid-cols-5 items-start gap-x-4"></div>
            <div style={{ marginTop: "100px", padding: "20px" }}>
                <TableContainer component={Paper}>
                    <div style={{ fontSize: "18px", background: "#FFEADC", width: "100%", padding: "10px", display: "flex", justifyContent: "space-between" }}>
                        <div style={{ marginLeft: "10px", marginTop: "15px" }}>Manage Subscription</div>
                        <div style={{ padding: "10px", margin: "0" }}>
                            <TextField type="text" size="small" value={searchTerm} onChange={handleSearchChange} placeholder="Search By User Name.." />
                        </div>
                    </div>
                    <Table sx={{ minWidth: 700, padding: "10px" }} aria-label="customized table">
                        <TableHead>
                            <TableRow>
                                <StyledTableCell align="left">Sr ID</StyledTableCell>
                                <StyledTableCell align="left">Program Name</StyledTableCell>
                                <StyledTableCell align="left">User Name</StyledTableCell>
                                <StyledTableCell align="left">Email</StyledTableCell>
                                <StyledTableCell align="left">Start Date</StyledTableCell>
                                <StyledTableCell align="left">End Date</StyledTableCell>
                                <StyledTableCell align="left">Razor Pay Details</StyledTableCell>
                                <StyledTableCell align="left">Actions</StyledTableCell>
                            </TableRow>
                        </TableHead>
                        <TableBody style={isLoading ? { filter: "blur(4px)", pointerEvents: "none" } : {}}>
                            {checkLastPage?.length > 0 ? (
                                <>
                                    {checkLastPage?.map((row, index) => (
                                        <StyledTableRow key={index} style={isLoading ? { pointerEvents: "none" } : {}}>
                                            <StyledTableCell align="left">
                                                {index + 1}
                                            </StyledTableCell>
                                            <StyledTableCell align="left">
                                                {row?.program?.program_name}
                                            </StyledTableCell>
                                            <StyledTableCell align="left">
                                                {row?.user?.firstname ? `${row?.user?.firstname} ${row?.user?.lastname}` : "NA"}
                                            </StyledTableCell>
                                            <StyledTableCell align="left">
                                                {row?.user?.email ? row?.user?.email : "NA"}
                                            </StyledTableCell>
                                            <StyledTableCell align="left">
                                                {moment(row?.start_date).format("DD-MM-YYYY")}
                                            </StyledTableCell>
                                            <StyledTableCell align="left">
                                                {moment(row?.end_date).format("DD-MM-YYYY")}
                                            </StyledTableCell>
                                            <StyledTableCell align="left">
                                                {row?.razorpay_payment_id ? row?.razorpay_payment_id : "NA"}
                                            </StyledTableCell>
                                            <StyledTableCell align="left">
                                                <Button variant="contained" style={{ marginBottom: "10px", backgroundColor: "#E67E22" }}
                                                    onClick={() => {
                                                        setIsOpenModal({ isOpen: true, id: row?.subscription_id })
                                                        setFormValue({ endDate: row?.end_date })
                                                    }}
                                                    disabled={isLoading}
                                                >Change </Button>
                                                &nbsp;
                                                {row?.getLink ? (
                                                    <a href={row?.getLink} target="_blank" rel="noopener noreferrer">
                                                        <Button variant="contained" style={{ backgroundColor: "#E67E22", marginBottom: "10px" }}
                                                            disabled={isLoading}
                                                        >Show Invoice </Button>
                                                    </a>
                                                ) : (<Button variant="contained" style={{ marginBottom: "10px", backgroundColor: "#E67E22" }}
                                                    onClick={() => handleGenrateInvoice(row?.subscription_id, row?.user?.id)}
                                                    disabled={isLoading || !row?.razorpay_payment_id}
                                                >Get Invoice </Button>)} &nbsp;
                                                <Button variant="contained" style={{ marginBottom: "10px", backgroundColor: "#E67E22" }} sx={row?.onhold ? { background: "red", '&:hover': { backgroundColor: 'red' } } : ""}
                                                    onClick={() => {
                                                        if (row?.onhold) {
                                                            showConfirmPutOnunhold(row?.subscription_id)
                                                        } else {
                                                            showConfirm(row?.subscription_id)
                                                        }
                                                    }}
                                                    disabled={isLoading}
                                                >{row?.onhold ? "Activate" : "Put Onhold"} </Button>
                                            </StyledTableCell>
                                        </StyledTableRow>
                                    ))}
                                </>
                            ) : (
                                <div className="p-4">No data found</div>
                            )}
                        </TableBody>
                    </Table>
                </TableContainer>
                &nbsp;
                <div className="flex justify-end">
                    <Pagination
                        count={Math.ceil(filteredList?.length / PageSize)}
                        color="primary"
                        page={currentPage}
                        onChange={handlePageChange}
                        style={{
                            filter: isLoading ? "blur(2px)" : "none"
                        }}
                    />
                </div>
            </div>
            <Modal
                title="Edit Subscription Dates"
                centered
                open={isOpenModal?.isOpen}
                onOk={() => handlUpdateSubscrtion()}
                onCancel={() => setIsOpenModal({ isOpen: false, id: "" })}
            >
                <div className="dateWrap" style={{ borderBottom: "1px solid gray", paddingBottom: "24px" }}>
                    <FormLabel style={{ textAlign: "center", fontWeight: 600 }}>End Date:</FormLabel>
                    <br />
                    <DatePicker
                        selected={formValue?.endDate ? new Date(formValue?.endDate) : null}
                        onChange={(date) => {
                            setFormValue({ ...formValue, endDate: moment(date).format("YYYY-MM-DD") })
                        }}
                        dateFormat="dd-MM-yyyy"
                        placeholderText="DD-MM-YYYY"
                        minDate={formValue?.endDate ? new Date(formValue?.endDate) : new Date()}
                        maxDate={maxDate}
                        customInput={
                            <TextField
                                placeholder="Added Date and Time"
                                size="small"
                                type="text"
                                name="added_datetime"
                            />
                        }
                    />
                </div>
            </Modal>
        </div>
    )
}
export default SubscriptionDetails
