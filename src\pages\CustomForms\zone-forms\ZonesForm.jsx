import React, { useState, useEffect } from "react";
import Header from "../../../components/Header";
import { InfoCircleOutlined } from "@ant-design/icons";
import {
	Tabs,
	TabsList,
	TabsTrigger,
	TabsContent,
} from "../../../components/ui/tabs";
import { <PERSON>ike, Footprints, Dumbbell, Waves } from "lucide-react";
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from "../../../components/ui/card";
import ZonesTable from "./ZonesTable";
import {
	BarChart,
	Bar,
	LineChart,
	Line,
	XAxis,
	YAxis,
	CartesianGrid,
	Tooltip,
	Legend,
	ResponsiveContainer,
} from "recharts";
import {
	Collapsible,
	CollapsibleContent,
	CollapsibleTrigger,
} from "../../../components/ui/collapsible";
import { ChevronDown, ChevronUp } from "lucide-react";
import { Button } from "../../../components/ui/button";
import { Input } from "../../../components/ui/input";
import { Badge } from "../../../components/ui/badge";
import Swal from "sweetalert2";
import { useSearchParams } from "react-router-dom";
import { handleTimeChangeZones } from "../../../utils/Resubale";
import { showError } from "../../../components/Messages";
import {
	fetchactivitylevel as fetchActivityLevel,
	getAllUserZones,
	getDistanceforactivity as getDistanceForActivity,
	getHeartZoneData,
	getSaveZones as saveZones,
	getFitnessData,
	getSaveCoreZones,
} from "../../../API/api-endpoint";
import {
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableHeader,
	TableRow,
} from "../../../components/ui/table";

const ZonesForm = () => {
	const [searchParams] = useSearchParams();

	const assignedCoach = searchParams.get("userId");

	const [getAllRunning, setGetAllRunning] = useState();
	const [getAllCycling, setGetAllCycling] = useState();
	const [getAllSpeedData, setGetAllSpeedData] = useState();
	const [cyclingForm, setCyclingForm] = useState({
		timeTrail: "",
		timeValue: "",
		level: { level: "" },
		pace: "",
		heartrate: 0,
		power: 0,
		speed: 0,
		speedUnit: "km/h",
	});
	const [cyclingDistance, setCyclingDistance] = useState([]);
	const [runningDistance, setRunningDistance] = useState([]);
	const [swimmingDistance, setSwimmingDistance] = useState([]);
	const [runningForm, setRunningForm] = useState({
		timeTrail: "",
		timeValue: "",
		level: { level: "" },
		pace: "",
		heartrate: 0,
		power: 0,
		speed: 0,
		speedUnit: "km/h",
	});
	const [swimmingForm, setSwimmingForm] = useState({
		timeTrail: "",
		timeValue: "",
		level: { level: "" },
		pace: "",
		heartrate: 0,
		power: 0,
		speed: 0,
		speedUnit: "km/h",
	});
	const [coreData, setCoreData] = useState();
	const [heartZoneData, setheartZoneData] = useState();

	const [coreResults, setCoreResults] = useState([]);
	const [coreTotalPoints, setCoreTotalPoints] = useState(0);
	const [coreForm, setCoreForm] = useState([]);

	// Clean and simplified getCoreZoneData function
	const getCoreZoneData = async () => {
		try {
			const response = await getFitnessData();
			if (response?.status) {
				setCoreData(response);

				// Set total points from API response
				if (response.totalPoints) {
					setCoreTotalPoints(response.totalPoints);
				}

				if (response.zones && response.zones.length > 0) {
					// Format data for the form
					const formattedCoreData = response.zones.map((zone) => ({
						name: zone.subworkout,
						value: "",
						unit: zone.metric === "hh:ss" ? "time" : "reps",
						subworkout_id: zone.subworkout_id,
						uomName: zone.uomname,
						points: zone.points || 0,
						level: zone.fitnesslevel || "Beginner",
					}));

					setCoreForm(formattedCoreData);

					// Create chart data directly from API response
					const apiChartData = response.zones.map((zone) => ({
						name: zone.subworkout,
						points: zone.points || 0,
						level: getLevelValue(zone.fitnesslevel),
						fullLevel: 4,
						value: 0,
					}));

					setCoreChartData(apiChartData);

					// Also set core results for consistency
					const apiResults = response.zones.map((zone) => ({
						workout: zone.subworkout,
						value: "",
						unit: zone.metric === "hh:ss" ? "time" : "reps",
						points: zone.points || 0,
						level: zone.fitnesslevel || "Beginner",
						levelValue: getLevelValue(zone.fitnesslevel),
						subworkout_id: zone.subworkout_id,
					}));

					setCoreResults(apiResults);
				} else {
					// No zones in response
					setCoreForm([]);
					setCoreChartData([]);
				}
			}
		} catch (error) {
			console.error("Error in getCoreZoneData:", error);
		}
	};

	// Helper function to convert level string to numeric value
	const getLevelValue = (levelString) => {
		const levelMap = {
			Beginner: 1,
			Intermediate: 2,
			Advanced: 3,
			Elite: 4,
		};

		return levelMap[levelString] || 1;
	};

	const [formValue, setFormValue] = useState({
		activity_id: 4,
		arrayofvalues: [
			{ subworkout_id: "", uomName: "", metric: "", value: "" },
			{ subworkout_id: "", uomName: "", metric: "", value: "" },
			{ subworkout_id: "", uomName: "", metric: "", value: "" },
			{ subworkout_id: "", uomName: "", metric: "", value: "" },
		],
	});

	// Clean useEffect
	useEffect(() => {
		const fetchData = async () => {
			try {
				setLoading(true);
				await getAllZones();
				await getCoreZoneData();
				setLoading(false);
			} catch (error) {
				console.error("Error in useEffect data fetching:", error);
				setLoading(false);
			}
		};

		fetchData();
	}, []);

	const [coreChartData, setCoreChartData] = useState([]);
	const [showCharts, setShowCharts] = useState(true);
	const [loading, setLoading] = useState(true);

	// to fetch first time all zones
	const getAllZones = async () => {
		// Running (activity_id: 1)
		const runningResponse = await getAllUserZones(1, assignedCoach);
		const runningDistanceResponse = await getDistanceForActivity(1);
		setRunningDistance(runningDistanceResponse?.distancesforactivity || []);
		setRunningForm((prevState) => ({
			...prevState,
			timeTrail: runningDistanceResponse?.distancesforactivity?.[0] || "",
		}));
		setGetAllRunning(runningResponse);

		// Cycling (activity_id: 2)
		const cyclingResponse = await getAllUserZones(2, assignedCoach);
		const cyclingDistanceResponse = await getDistanceForActivity(2);
		setCyclingDistance(cyclingDistanceResponse?.distancesforactivity || []);
		setCyclingForm((prevState) => ({
			...prevState,
			timeTrail: cyclingDistanceResponse?.distancesforactivity?.[0] || "",
		}));
		setGetAllCycling(cyclingResponse);

		// Swimming (activity_id: 3)
		const swimmingResponse = await getAllUserZones(3, assignedCoach);
		const swimmingDistanceResponse = await getDistanceForActivity(3);
		setSwimmingDistance(
			swimmingDistanceResponse?.distancesforactivity || []
		);
		setSwimmingForm((prevState) => ({
			...prevState,
			timeTrail:
				swimmingDistanceResponse?.distancesforactivity?.[0] || "",
		}));
		setGetAllSpeedData(swimmingResponse);
	};

	// onachnge to fetch pace and fitnes

	const HandleChangeTiralTime = async (
		timeValue,
		setData,
		data,
		activityId
	) => {
		if (data?.timeTrail) {
			let value = {
				activity_id: activityId,
				time: timeValue,
				distance: data?.timeTrail,
				unit: "km",
			};

			const response = await fetchActivityLevel(value);

			if (response?.activitylevel?.level) {
				setData((prevState) => ({
					...prevState,
					timeValue: timeValue,
					level: response?.activitylevel?.level,
					pace: response?.pace,
					speed: response?.speed,
					speedUnit: response?.speedunit,
				}));
			} else {
				setData((prevState) => ({
					...prevState,
					timeValue: timeValue,
					level: { level: "" },
					pace: "",
				}));
			}
		} else {
			showError("Please select distance");
		}
	};

	// calculate zones
	const handleSubmit = async (activityId, setData, data, setAllData) => {
		if (data.timeValue && data.timeValue !== "00:00:00") {
			try {
				const response = await saveZones(
					activityId,
					data,
					assignedCoach
				);

				if (response.status) {
					setData((prevState) => ({
						...prevState,
						timeValue: "",
						level: { level: "" },
						pace: "",
						heartrate: 0,
						power: 0,
						value: "",
					}));

					Swal.fire({
						title: "Success",
						text:
							response?.message ||
							"Zones calculated successfully",
						icon: "success",
					});

					getRunningZones(activityId, setData, setAllData);
				} else {
					Swal.fire({
						title: "Error!",
						text: response?.message || "Failed to calculate zones",
						icon: "error",
					});
				}
			} catch (error) {
				console.error("Error calculating zones:", error);
				Swal.fire({
					title: "Error!",
					text: "An unexpected error occurred",
					icon: "error",
				});
			}
		} else {
			Swal.fire({
				title: "Info",
				text: "Please fill correct all details",
				icon: "info",
			});
		}
	};

	// Also update the time conversion function to handle time inputs better

	// convert time with two digits
	function timeStringToNumber(inputTime) {
		const timeComponents = inputTime.split(":");

		// Extract hours, minutes, and seconds
		const hours = parseInt(timeComponents[0]);
		const minutes = parseInt(timeComponents[1]);
		const seconds = Math.round(parseFloat(timeComponents[2]));
		// Format each component to have two digits
		const formattedHours = hours.toString().padStart(2, "0");
		const formattedMinutes = minutes.toString().padStart(2, "0");
		const formattedSeconds = seconds.toString().padStart(2, "0");

		// Construct the final formatted time
		const formattedTime = `${formattedHours}:${formattedMinutes}:${formattedSeconds}`;

		return formattedTime;
	}
	// fetch zone after submit calculation
	const getRunningZones = async (activityId, setData, setAllData) => {
		const response = await getAllUserZones(activityId, assignedCoach);
		const distanceResponse = await getDistanceForActivity(activityId);

		// Update the distance data
		if (activityId === 1) {
			setRunningDistance(distanceResponse?.distancesforactivity || []);
		} else if (activityId === 2) {
			setCyclingDistance(distanceResponse?.distancesforactivity || []);
		} else if (activityId === 3) {
			setSwimmingDistance(distanceResponse?.distancesforactivity || []);
		}

		setAllData(response);
	};

	const fetchHeartZone = async () => {
		try {
			const response = await getHeartZoneData();
			setheartZoneData(response);

			// Reset the form completely with new data
			if (response && response.length > 0) {
				const initialArrayValues = response.map((zone) => ({
					value: "", // Start with empty values for new input
					subworkout_id: zone.subworkout_id,
					metric: zone.metric?.metric_name || "",
					uomName: zone.uom?.uom_name || "",
				}));

				// Create a completely new form value object
				const newFormValue = {
					arrayofvalues: initialArrayValues,
					activity_id: 4, // Core activity ID
				};

				setFormValue(newFormValue);
			}
		} catch (error) {
			console.error("Error fetching heart zone data:", error);
		}
	};

	const handleInputChange = (value, field, index) => {
		const newArrayofvalues = [...formValue.arrayofvalues];
		newArrayofvalues[index][field] = value;
		setFormValue({ ...formValue, arrayofvalues: newArrayofvalues });
	};

	// Update the handleInputPlanksChange function to properly handle time values
	const handleInputPlanksChange = (value, field, index) => {
		const newArrayofvalues = [...formValue.arrayofvalues];

		// Ensure we're not modifying a null or undefined array
		if (!newArrayofvalues[index]) {
			newArrayofvalues[index] = {};
		}

		newArrayofvalues[index][field] = value;

		// Create a new object to ensure React detects the state change
		setFormValue({
			...formValue,
			arrayofvalues: [...newArrayofvalues],
		});
	};

	// Update the updateFormValue function to ensure it creates a new state object
	const updateFormValue = (index, subworkout_id, metric, uomName, value) => {
		const newArrayofvalues = [...formValue.arrayofvalues];

		if (!newArrayofvalues[index]) {
			newArrayofvalues[index] = {};
		}

		// Update the specific index with all required fields
		newArrayofvalues[index] = {
			...newArrayofvalues[index], // Keep existing values
			subworkout_id: subworkout_id,
			metric: metric,
			uomName: uomName,
			value: value,
		};

		// Create a completely new object to ensure React detects the state change
		const newFormValue = {
			...formValue,
			arrayofvalues: [...newArrayofvalues],
			activity_id: 4, // Core activity ID
		};

		setFormValue(newFormValue);
	};

	const calculateCoreZones = async () => {
		// Add activity_id if not already present
		const formData = {
			...formValue,
			activity_id: 4, // Core activity ID
		};

		const isValid = formData.arrayofvalues.every((item) => {
			return (
				item.value !== undefined &&
				item.value !== null &&
				item.value !== "" &&
				item.subworkout_id
			);
		});

		if (!isValid) {
			Swal.fire({
				title: "Info",
				text: "Please Fill All Fields",
				icon: "info",
			});
			return;
		}

		try {
			const response = await getSaveCoreZones(formData, assignedCoach);

			if (response.status) {
				Swal.fire({
					title: "Success",
					text: "Zone Updated Successfully!",
					icon: "success",
				});
				setCoreData(response);

				// Refresh core zone data after successful calculation
				if (typeof getCoreZoneData === "function") {
					await getCoreZoneData();
				}
			} else {
				Swal.fire({
					title: "Error",
					text: response?.message || "Failed to calculate zones",
					icon: "error",
				});
			}
		} catch (error) {
			console.error("Error calculating core zones:", error);
			Swal.fire({
				title: "Error",
				text: "An unexpected error occurred",
				icon: "error",
			});
		}
	};

	useEffect(() => {
		fetchHeartZone();
	}, []);
	// Remove the activeTab state since it will be handled by the Tabs component
	// const [activeTab, setActiveTab] = useState("cycling");

	// Function to convert API data to ZonesTable format
	const formatZonesData = (zones, type) => {
		if (!zones || zones.length === 0) return [];

		return zones.map((zone) => {
			let range = "";

			if (type === "heart") {
				range = `${zone.heartratestart} - ${zone.heartrateend}`;
			} else if (type === "power") {
				range = `${zone.powerstart} - ${zone.powerend}`;
			} else if (type === "speed") {
				range = `${zone.speed_start} - ${zone.speed_end}`;
			} else if (type === "pace") {
				range = `${timeStringToNumber(
					zone.pace_start_range
				)} - ${timeStringToNumber(zone.pace_end_range)}`;
			}

			return {
				key: zone.zone,
				zone: zone.zone,
				name: zone.zone_name,
				range: range,
			};
		});
	};

	// Custom tooltip for charts
	const CustomTooltip = ({ active, payload, label }) => {
		if (active && payload && payload.length) {
			return (
				<div className='bg-white p-3 border border-gray-200 rounded-md shadow-md'>
					<p className='font-medium text-gray-800 mb-2'>
						{payload[0]?.payload?.fullName || label}
					</p>
					{payload.map((entry, index) => (
						<p
							key={`item-${index}`}
							className='text-sm flex items-center gap-2'
						>
							<span
								className='w-3 h-3 inline-block rounded-sm'
								style={{ backgroundColor: entry.color }}
							></span>
							{entry.name}:{" "}
							{entry.name === "Faster Pace" ||
							entry.name === "Slower Pace"
								? convertSecondsToSpeed(entry.value)
								: entry.value}
						</p>
					))}
				</div>
			);
		}
		return null;
	};

	function convertSecondsToSpeed(secondsPerKm) {
		if (!secondsPerKm || secondsPerKm <= 0) return "-";
		const speed = 3600 / secondsPerKm;
		return `${speed.toFixed(2)} km/h`;
	}

	// Updated function to prepare cycling speed data by month
	function getSpeedChartData(changelogs) {
		if (!changelogs || changelogs.length === 0) return [];

		// Sort by date (oldest to newest)
		const sortedLogs = [...changelogs].sort(
			(a, b) => new Date(a.createdAt) - new Date(b.createdAt)
		);

		const recentLogs = sortedLogs.slice(-12);

		return recentLogs.map((entry) => {
			const distance = parseFloat(entry?.distance || 0);
			const time = entry?.time || "00:00:00";
			const [hh, mm, ss] = time.split(":").map(Number);
			const totalSeconds = hh * 3600 + mm * 60 + ss;

			// Calculate speed in km/h
			const speedInKmPerHour =
				distance > 0 && totalSeconds > 0
					? distance / (totalSeconds / 3600)
					: 0;

			const date = new Date(entry.createdAt);
			const formattedDate = date.toLocaleDateString("en-US", {
				day: "numeric",
				month: "short",
				year: "numeric",
			});

			return {
				name: formattedDate,
				speed: parseFloat(speedInKmPerHour.toFixed(1)),
				fullName: `${formattedDate}: ${speedInKmPerHour.toFixed(
					1
				)} km/h`,
				timestamp: date.getTime(),
			};
		});
	}

	// Function to convert swimming data to speed in m/min
	function getSwimmingSpeedChartData(changelogs) {
		if (!changelogs || changelogs.length === 0) return [];

		// Sort by date (oldest to newest)
		const sortedLogs = [...changelogs].sort(
			(a, b) => new Date(a.createdAt) - new Date(b.createdAt)
		);

		// Take up to 12 entries to show a year's worth of data
		const recentLogs = sortedLogs.slice(-12);

		return recentLogs.map((entry) => {
			const distance = parseFloat(entry?.distance || 0) * 1000;
			const time = entry?.time || "00:00:00";
			const [hh, mm, ss] = time.split(":").map(Number);
			const totalMinutes = hh * 60 + mm + ss / 60;

			// Calculate speed in meters per minute
			const speedInMetersPerMin =
				totalMinutes > 0 ? distance / totalMinutes : 0;

			const date = new Date(entry.createdAt);
			const formattedDate = date.toLocaleDateString("en-US", {
				day: "numeric",
				month: "short",
				year: "numeric",
			});

			return {
				name: formattedDate,
				speed: parseFloat(speedInMetersPerMin.toFixed(1)),
				fullName: `${formattedDate}: ${speedInMetersPerMin.toFixed(
					1
				)} m/min`,
				timestamp: date.getTime(),
			};
		});
	}

	return (
		<div className='w-[90%] mx-auto px-4 py-8'>
			<Header />

			<div className='mt-20'>
				<h1 className='text-3xl font-semibold text-orange-950 mb-2'>
					Training Zones Calculator
				</h1>
				<p className='text-gray-600 mb-6 text-base'>
					Calculate your personalized training zones based on your
					performance metrics
				</p>

				<div className='bg-[#fff8f1] border-t border-r border-b border-l border-orange-200 rounded-md p-4 mb-8'>
					<div className='flex items-start'>
						<InfoCircleOutlined className='text-orange-500 text-lg mr-3 mt-1' />
						<div>
							<div className='flex items-center'>
								<h3 className='text-base font-medium text-orange-950'>
									Training Zones
								</h3>
							</div>
							<p className='text-sm text-orange-800'>
								Training zones help you optimize your workouts
								by targeting specific physiological adaptations.
								Enter your metrics below to calculate your
								personalized zones.
							</p>
						</div>
					</div>
				</div>
				<Tabs defaultValue='cycling' className='w-full'>
					<TabsList className='grid grid-cols-4 bg-sky-100 p-1 rounded-lg border border-sky-200 mb-8'>
						<TabsTrigger
							value='cycling'
							className='text-gray-600 font-bold flex gap-2'
							activeClassName='bg-white rounded-full text-black font-semibols'
						>
							<Bike className='h-4 w-4' />
							<span className='hidden sm:inline'>Cycling</span>
						</TabsTrigger>

						<TabsTrigger
							value='running'
							className='text-gray-600 font-bold flex gap-2'
							activeClassName='bg-white rounded-full text-black font-semibold'
						>
							<Footprints className='h-4 w-4' />
							<span className='hidden sm:inline'>Running</span>
						</TabsTrigger>

						<TabsTrigger
							value='swimming'
							className='text-gray-600 font-bold flex gap-2'
							activeClassName='bg-white rounded-full text-black font-semibold'
						>
							<Waves className='h-4 w-4' />
							<span className='hidden sm:inline'>Swimming</span>
						</TabsTrigger>

						<TabsTrigger
							value='core'
							className='text-gray-600 font-bold gap-2 flex'
							activeClassName='bg-white rounded-full text-black font-semibold'
						>
							<Dumbbell className='h-4 w-4' />
							<span className='hidden sm:inline'>Core</span>
						</TabsTrigger>
					</TabsList>
					<Collapsible
						open={showCharts}
						onOpenChange={setShowCharts}
						className='w-full mb-6 border border-gray-200 rounded-md overflow-hidden'
					>
						<CollapsibleTrigger asChild>
							<div className='w-[84vw] sm:[80vw] md:w-[88vw] flex justify-between px-4 py-3 border-b text-orange-800 font-semibold text-sm cursor-pointer hover:bg-orange-50'>
								<span>Zone Visualizations</span>
								{showCharts ? (
									<ChevronUp className='h-4 w-4 text-orange-800' />
								) : (
									<ChevronDown className='h-4 w-4 text-orange-800' />
								)}
							</div>
						</CollapsibleTrigger>

						<CollapsibleContent className='w-full p-4'>
							<div className='p-4'>
								<TabsContent value='cycling'>
									<div className='space-y-6'>
										<div className='grid grid-cols-1 gap-6'>
											<Card className='w-full'>
												<CardHeader>
													<CardTitle className='text-orange-800'>
														Cycling Speed
														Progression
													</CardTitle>
													<CardDescription className='text-gray-600'>
														Your cycling speed over
														time (km/h)
													</CardDescription>
												</CardHeader>
												<CardContent className='w-full p-0 sm:p-2'>
													<div className='h-[400px] w-full'>
														<ResponsiveContainer
															width='100%'
															height='100%'
														>
															<LineChart
																data={getSpeedChartData(
																	getAllCycling?.changelogs1 ||
																		[]
																)}
																margin={{
																	top: 20,
																	right: 30,
																	left: 20,
																	bottom: 70,
																}}
															>
																<CartesianGrid strokeDasharray='3 3' />
																<XAxis
																	dataKey='name'
																	angle={-45}
																	textAnchor='end'
																	height={70}
																	tick={{
																		fontSize: 12,
																	}}
																	interval={0}
																/>
																<YAxis
																	label={{
																		value: "Speed (km/h)",
																		angle: -90,
																		position:
																			"insideLeft",
																		style: {
																			textAnchor:
																				"middle",
																		},
																	}}
																	domain={[
																		0,
																		"dataMax + 5",
																	]}
																/>
																<Tooltip
																	content={
																		<CustomTooltip />
																	}
																/>
																<Legend
																	wrapperStyle={{
																		paddingTop: 10,
																	}}
																/>
																<Line
																	type='monotone'
																	dataKey='speed'
																	name='Cycling Speed'
																	stroke='#f97316'
																	strokeWidth={
																		3
																	}
																	dot={{
																		r: 6,
																		fill: "#3b82f6",
																		stroke: "#3b82f6",
																		strokeWidth: 1,
																	}}
																	activeDot={{
																		r: 8,
																		fill: "#3b82f6",
																		stroke: "#3b82f6",
																		strokeWidth: 1,
																	}}
																/>
															</LineChart>
														</ResponsiveContainer>
													</div>
												</CardContent>
											</Card>
										</div>
									</div>
								</TabsContent>

								<TabsContent value='running'>
									<div className='space-y-6'>
										<div className='grid grid-cols-1 gap-6'>
											<Card>
												<CardHeader>
													<CardTitle className='text-orange-800'>
														Running Pace Progression
													</CardTitle>
													<CardDescription className='text-gray-600'>
														Your running pace over
														time (km/h)
													</CardDescription>
												</CardHeader>
												<CardContent className='w-full p-0 sm:p-2'>
													<div className='h-[400px] w-full'>
														<ResponsiveContainer
															width='100%'
															height='100%'
														>
															<LineChart
																data={getSpeedChartData(
																	getAllRunning?.changelogs1 ||
																		[]
																)}
																margin={{
																	top: 20,
																	right: 30,
																	left: 20,
																	bottom: 70,
																}}
															>
																<CartesianGrid strokeDasharray='3 3' />
																<XAxis
																	dataKey='name'
																	angle={-45}
																	textAnchor='end'
																	height={70}
																	tick={{
																		fontSize: 12,
																	}}
																	interval={0}
																/>
																<YAxis
																	label={{
																		value: "Speed (km/h)",
																		angle: -90,
																		position:
																			"insideLeft",
																		style: {
																			textAnchor:
																				"middle",
																		},
																	}}
																	domain={[
																		0,
																		"dataMax + 5",
																	]}
																/>
																<Tooltip
																	content={
																		<CustomTooltip />
																	}
																/>
																<Legend
																	wrapperStyle={{
																		paddingTop: 10,
																	}}
																/>
																<Line
																	type='monotone'
																	dataKey='speed'
																	name='Running Speed'
																	stroke='#f97316'
																	strokeWidth={
																		3
																	}
																	dot={{
																		r: 6,
																		fill: "#3b82f6",
																		stroke: "#3b82f6",
																		strokeWidth: 1,
																	}}
																	activeDot={{
																		r: 8,
																		fill: "#3b82f6",
																		stroke: "#3b82f6",
																		strokeWidth: 1,
																	}}
																/>
															</LineChart>
														</ResponsiveContainer>
													</div>
												</CardContent>
											</Card>
										</div>
									</div>
								</TabsContent>
								<TabsContent value='swimming'>
									<div className='space-y-6'>
										<div className='grid grid-cols-1 gap-6'>
											<Card>
												<CardHeader>
													<CardTitle className='text-orange-800'>
														Swimming Pace
														Progression
													</CardTitle>
													<CardDescription className='text-gray-600'>
														Your swimming pace over
														time (m/min)
													</CardDescription>
												</CardHeader>
												<CardContent className='w-full p-0 sm:p-2'>
													<div className='h-[400px] w-full'>
														<ResponsiveContainer
															width='100%'
															height='100%'
														>
															<LineChart
																data={getSwimmingSpeedChartData(
																	getAllSpeedData?.changelogs1 ||
																		[]
																)}
																margin={{
																	top: 20,
																	right: 30,
																	left: 20,
																	bottom: 70,
																}}
															>
																<CartesianGrid strokeDasharray='3 3' />
																<XAxis
																	dataKey='name'
																	angle={-45}
																	textAnchor='end'
																	height={70}
																	tick={{
																		fontSize: 12,
																	}}
																	interval={0}
																/>
																<YAxis
																	label={{
																		value: "Speed (m/min)",
																		angle: -90,
																		position:
																			"insideLeft",
																		style: {
																			textAnchor:
																				"middle",
																		},
																	}}
																	domain={[
																		"dataMin - 5",
																		"dataMax + 5",
																	]}
																/>
																<Tooltip
																	content={
																		<CustomTooltip />
																	}
																/>
																<Legend
																	wrapperStyle={{
																		paddingTop: 10,
																	}}
																/>
																<Line
																	type='monotone'
																	dataKey='speed'
																	name='Swimming Speed'
																	stroke='#f97316'
																	strokeWidth={
																		3
																	}
																	dot={{
																		r: 6,
																		fill: "#3b82f6",
																		stroke: "#3b82f6",
																		strokeWidth: 1,
																	}}
																	activeDot={{
																		r: 8,
																		fill: "#3b82f6",
																		stroke: "#3b82f6",
																		strokeWidth: 1,
																	}}
																/>
															</LineChart>
														</ResponsiveContainer>
													</div>
												</CardContent>
											</Card>
										</div>
									</div>
								</TabsContent>

								<TabsContent value='core'>
									<div className='space-y-6'>
										<Card>
											<CardHeader>
												<CardTitle className='text-orange-800'>
													Core Exercise Performance
												</CardTitle>
												<CardDescription>
													{coreTotalPoints > 0
														? `Total Points: ${coreTotalPoints}`
														: "Complete the form to see your results"}
												</CardDescription>
											</CardHeader>
											<CardContent>
												{loading ? (
													<div className='flex justify-center items-center h-[300px]'>
														<div className='animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-orange-500'></div>
													</div>
												) : coreChartData &&
												  coreChartData.length > 0 ? (
													<div className='h-[300px]'>
														<ResponsiveContainer
															width='100%'
															height='100%'
														>
															<BarChart
																data={
																	coreChartData
																}
																margin={{
																	top: 20,
																	right: 30,
																	left: 20,
																	bottom: 70,
																}}
															>
																<CartesianGrid strokeDasharray='3 3' />
																<XAxis dataKey='name' />
																<YAxis
																	label={{
																		value: "Points",
																		angle: -90,
																		position:
																			"insideLeft",
																	}}
																/>
																<Tooltip />
																<Legend />
																<Bar
																	dataKey='level'
																	name='Fitness Level'
																	fill='#f97316'
																/>
																<Bar
																	dataKey='points'
																	name='Points'
																	fill='#0369a1'
																/>
															</BarChart>
														</ResponsiveContainer>
													</div>
												) : (
													<div className='flex flex-col items-center justify-center h-[300px] bg-gray-50 rounded-md'>
														<div className='text-gray-400 text-xl mb-2'>
															No data available
														</div>
														<div className='text-gray-500 text-sm'>
															Complete the form to
															see your results
														</div>
													</div>
												)}
											</CardContent>
										</Card>
									</div>
								</TabsContent>
							</div>
						</CollapsibleContent>
					</Collapsible>
					<TabsContent value='cycling'>
						<div className='space-y-6'>
							{/* Other cycling zone tables */}
							<div className='grid grid-cols-1 lg:grid-cols-2 gap-6'>
								<ZonesTable
									title='Heart Rate Zones'
									unit='BPM'
									data={formatZonesData(
										getAllCycling?.zones,
										"heart"
									)}
								/>
								<ZonesTable
									title='Power Zones'
									unit='Watts'
									data={formatZonesData(
										getAllCycling?.zones,
										"power"
									)}
								/>
								<ZonesTable
									title='Speed Zones'
									unit='km/h'
									data={formatZonesData(
										getAllCycling?.zones,
										"speed"
									)}
								/>
							</div>
						</div>

						<div className='mt-12 bg-white rounded-lg shadow p-6 border border-gray-200'>
							<h2 className='text-2xl font-bold text-gray-950'>
								Calculate Your Cycling Zones
							</h2>
							<p className='text-gray-600 mb-6 text-base'>
								Enter your time trial data to calculate your
								personalized training zones.
							</p>

							<div className='w-[70%] grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-x-6 gap-y-4'>
								<div>
									<label className='block text-sm font-semibold text-gray-700 mb-1'>
										Distance (km)
									</label>
									<select
										className='w-full p-2.5 text-sm border border-gray-300 rounded-md'
										value={cyclingForm.timeTrail || ""}
										onChange={(e) =>
											setCyclingForm({
												...cyclingForm,
												timeTrail: e.target.value,
											})
										}
									>
										<option value=''>
											Select distance
										</option>
										{cyclingDistance &&
										cyclingDistance.length > 0 ? (
											cyclingDistance.map(
												(value, index) => (
													<option
														key={index}
														value={value}
													>
														{value} KM
													</option>
												)
											)
										) : (
											<option disabled>
												No distances available
											</option>
										)}
									</select>
								</div>
								<div>
									<label className='block text-sm font-semibold text-gray-700 mb-1'>
										Time (hh:mm:ss)
									</label>
									<input
										type='text'
										value={cyclingForm.timeValue || ""}
										onChange={(e) => {
											let checkValue =
												handleTimeChangeZones(e);
											setCyclingForm({
												...cyclingForm,
												timeValue: checkValue,
											});
											HandleChangeTiralTime(
												checkValue,
												setCyclingForm,
												cyclingForm,
												2
											);
										}}
										className='w-full p-2.5 text-sm border border-gray-300 rounded-md'
										placeholder='hh:mm:ss'
									/>
								</div>

								<div>
									<label className='block text-sm font-semibold text-gray-700 mb-1'>
										Fitness Level
									</label>
									<input
										type='text'
										value={cyclingForm?.level?.level || ""}
										disabled
										className='w-full p-2.5 text-sm border border-gray-300 rounded-md bg-gray-50'
										placeholder='Auto-calculated'
									/>
								</div>
								<div>
									<label className='block text-sm font-semibold text-gray-700 mb-1'>
										Speed
									</label>
									<input
										type='text'
										value={
											cyclingForm?.speed &&
											cyclingForm?.speedUnit
												? `${cyclingForm?.speed} ${cyclingForm?.speedUnit}`
												: ""
										}
										disabled
										className='w-full p-2.5 text-sm border border-gray-300 rounded-md bg-gray-50'
										placeholder='Auto-calculated'
									/>
								</div>
								<div>
									<label className='block text-sm font-semibold text-gray-700 mb-1'>
										Heart Rate (BPM)
									</label>
									<input
										type='number'
										value={cyclingForm.heartrate || ""}
										onChange={(e) =>
											setCyclingForm({
												...cyclingForm,
												heartrate: e.target.value,
											})
										}
										className='w-full p-2.5 text-sm border border-gray-300 rounded-md'
										placeholder='Enter heart rate'
									/>
								</div>
								<div>
									<label className='block text-sm font-semibold text-gray-700 mb-1'>
										Power (watts)
									</label>
									<input
										type='number'
										value={cyclingForm.power || ""}
										onChange={(e) =>
											setCyclingForm({
												...cyclingForm,
												power: e.target.value,
											})
										}
										className='w-full p-2.5 text-sm border border-gray-300 rounded-md'
										placeholder='Enter power'
									/>
								</div>

								<div className='mt-6 md:col-span-2'>
									<Button
										className='w-full bg-orange-500 hover:bg-orange-600 text-white font-semibold py-3 px-4 rounded text-base'
										onClick={() =>
											handleSubmit(
												2,
												setCyclingForm,
												cyclingForm,
												setGetAllCycling
											)
										}
									>
										Calculate Zones
									</Button>
								</div>
							</div>
						</div>

						{/* Recent Calculations Table */}
						<div className='mt-8 bg-white rounded-lg shadow p-6 border border-gray-200'>
							<h2 className='text-2xl font-bold text-gray-950'>
								Recent Calculations
							</h2>
							<p className='text-gray-600 mb-4 text-base'>
								Your previous zone calculations
							</p>
							<div className='overflow-x-auto'>
								<table className='min-w-full divide-y divide-gray-200'>
									<thead className='bg-gray-50'>
										<tr>
											<th className='px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
												Activity
											</th>
											<th className='px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
												Distance (km)
											</th>
											<th className='px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
												Time
											</th>
											<th className='px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
												Date
											</th>
											<th className='px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
												Heart Rate
											</th>
											<th className='px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
												Power
											</th>
											<th className='px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
												Fitness Level
											</th>
											<th className='px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
												Speed
											</th>
										</tr>
									</thead>
									<tbody className='bg-white divide-y divide-gray-200'>
										{getAllCycling?.changelogs1?.length >
										0 ? (
											getAllCycling?.changelogs1
												?.slice(0, 12)
												?.map((row) => (
													<tr
														key={row?.number}
														className='hover:bg-orange-50'
													>
														<td className='px-4 py-3 whitespace-nowrap text-sm text-gray-700'>
															{
																row?.activity
																	?.activity_name
															}
														</td>
														<td className='px-4 py-3 whitespace-nowrap text-sm text-gray-700'>
															{row?.distance} Km
														</td>
														<td className='px-4 py-3 whitespace-nowrap text-sm text-gray-700'>
															{row?.time}
														</td>
														<td className='px-4 py-3 whitespace-nowrap text-sm text-gray-700'>
															{new Date(
																row?.createdAt
															).toLocaleDateString(
																"en-US",
																{
																	day: "numeric",
																	month: "long",
																	year: "numeric",
																	hour: "numeric",
																	minute: "numeric",
																}
															)}
														</td>
														<td className='px-4 py-3 whitespace-nowrap text-sm text-gray-700'>
															{row?.heartrate}
														</td>
														<td className='px-4 py-3 whitespace-nowrap text-sm text-gray-700'>
															{row?.power}
														</td>
														<td className='px-4 py-3 whitespace-nowrap text-sm text-gray-700'>
															{
																row
																	?.fitnesslevel
																	?.level
															}
														</td>
														<td className='px-4 py-3 whitespace-nowrap text-sm text-gray-700'>
															{row?.speed} km/hr
														</td>
													</tr>
												))
										) : (
											<tr>
												<td
													colSpan='8'
													className='px-4 py-3 text-center text-sm text-gray-500'
												>
													No data found
												</td>
											</tr>
										)}
									</tbody>
								</table>
							</div>
						</div>
					</TabsContent>
					<TabsContent value='running'>
						<div className='mt-8'>
							<h2 className='text-xl font-medium mb-4 text-orange-900'>
								Running Zone Visualizations
							</h2>
							<div className='grid grid-cols-1 lg:grid-cols-2 gap-6 auto-rows-fr'>
								<ZonesTable
									title='Heart Rate Zones'
									unit='BPM'
									data={formatZonesData(
										getAllRunning?.zones,
										"heart"
									)}
								/>
								<ZonesTable
									title='Pace Zones'
									unit='min/km'
									data={formatZonesData(
										getAllRunning?.zones,
										"pace"
									)}
								/>
							</div>
						</div>

						<div className='mt-12 bg-white rounded-lg shadow p-6 border border-gray-200'>
							<h2 className='text-2xl font-bold text-gray-950'>
								Calculate Your Running Zones
							</h2>
							<p className='text-gray-600 mb-6 text-base'>
								Enter your time trial data to calculate your
								personalized training zones.
							</p>

							<div className='w-[70%] grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-x-6 gap-y-4'>
								<div>
									<label className='block text-sm font-semibold text-gray-700 mb-1'>
										Distance (km)
									</label>
									<select
										className='w-full p-2.5 text-sm border border-gray-300 rounded-md'
										value={runningForm.timeTrail || ""}
										onChange={(e) =>
											setRunningForm({
												...runningForm,
												timeTrail: e.target.value,
												timeValue: "",
												level: { level: "" },
												pace: "",
											})
										}
									>
										<option value=''>
											Select Distance
										</option>
										{runningDistance &&
										runningDistance.length > 0 ? (
											runningDistance.map(
												(value, index) => (
													<option
														key={index}
														value={value}
													>
														{value} KM
													</option>
												)
											)
										) : (
											<option disabled>
												No distances available
											</option>
										)}
									</select>
								</div>
								<div>
									<label className='block text-sm font-semibold text-gray-700 mb-1'>
										Time (hh:mm:ss)
									</label>
									<input
										type='text'
										value={runningForm.timeValue || ""}
										onChange={(e) => {
											let checkValue =
												handleTimeChangeZones(e);
											setRunningForm({
												...runningForm,
												timeValue: checkValue,
											});
											HandleChangeTiralTime(
												checkValue,
												setRunningForm,
												runningForm,
												1
											);
										}}
										className='w-full p-2.5 text-sm border border-gray-300 rounded-md'
										placeholder='hh:mm:ss'
									/>
								</div>
								<div>
									<label className='block text-sm font-semibold text-gray-700 mb-1'>
										Heart Rate (BPM)
									</label>
									<input
										type='number'
										value={runningForm.heartrate || ""}
										onChange={(e) =>
											setRunningForm({
												...runningForm,
												heartrate: e.target.value,
											})
										}
										className='w-full p-2.5 text-sm border border-gray-300 rounded-md'
										placeholder='Enter heart rate'
									/>
								</div>

								<div>
									<label className='block text-sm font-semibold text-gray-700 mb-1'>
										Fitness Level
									</label>
									<input
										type='text'
										value={runningForm.level?.level || ""}
										disabled
										className='w-full p-2.5 text-sm border border-gray-300 rounded-md bg-gray-50'
									/>
								</div>
								<div>
									<label className='block text-sm font-semibold text-gray-700 mb-1'>
										Pace
									</label>
									<input
										type='text'
										value={runningForm.pace || ""}
										disabled
										className='w-full p-2.5 text-sm border border-gray-300 rounded-md bg-gray-50'
									/>
								</div>

								<div className='mt-6 md:col-span-3'>
									<Button
										className='w-full bg-orange-500 hover:bg-orange-600 text-white font-semibold py-3 px-4 rounded text-base'
										onClick={() =>
											handleSubmit(
												1,
												setRunningForm,
												runningForm,
												setGetAllRunning
											)
										}
									>
										Calculate Zones
									</Button>
								</div>
							</div>
						</div>

						<div className='mt-8 bg-white rounded-lg shadow p-6 border border-gray-200'>
							<h2 className='text-2xl font-bold  text-gray-950'>
								Recent Calculations
							</h2>
							<p className='text-gray-600 mb-4 text-base'>
								Your previous zone calculations
							</p>

							<div className='overflow-x-auto'>
								<table className='min-w-full divide-y divide-gray-200'>
									<thead className='bg-gray-50'>
										<tr>
											<th className='px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
												Activity
											</th>
											<th className='px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
												Distance (km)
											</th>
											<th className='px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
												Time
											</th>
											<th className='px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
												Date
											</th>
											<th className='px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
												Heart Rate
											</th>
											<th className='px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
												Pace
											</th>
											<th className='px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
												Fitness Level
											</th>
										</tr>
									</thead>
									<tbody className='bg-white divide-y divide-gray-200'>
										{getAllRunning?.changelogs1?.length >
										0 ? (
											getAllRunning?.changelogs1
												?.slice(0, 12)
												?.map((row) => (
													<tr
														key={row?.number}
														className='hover:bg-orange-50'
													>
														<td className='px-4 py-3 whitespace-nowrap text-sm text-gray-700'>
															{
																row?.activity
																	?.activity_name
															}
														</td>
														<td className='px-4 py-3 whitespace-nowrap text-sm text-gray-700'>
															{row?.distance} Km
														</td>
														<td className='px-4 py-3 whitespace-nowrap text-sm text-gray-700'>
															{row?.time}
														</td>
														<td className='px-4 py-3 whitespace-nowrap text-sm text-gray-700'>
															{new Date(
																row?.createdAt
															).toLocaleDateString(
																"en-US",
																{
																	day: "numeric",
																	month: "long",
																	year: "numeric",
																	hour: "numeric",
																	minute: "numeric",
																}
															)}
														</td>
														<td className='px-4 py-3 whitespace-nowrap text-sm text-gray-700'>
															{row?.heartrate}
														</td>
														<td className='px-4 py-3 whitespace-nowrap text-sm text-gray-700'>
															{row?.pace}
														</td>
														<td className='px-4 py-3 whitespace-nowrap text-sm text-gray-700'>
															{
																row
																	?.fitnesslevel
																	?.level
															}
														</td>
													</tr>
												))
										) : (
											<tr>
												<td
													colSpan='7'
													className='px-4 py-3 text-center text-sm text-gray-500'
												>
													No data found
												</td>
											</tr>
										)}
									</tbody>
								</table>
							</div>
						</div>
					</TabsContent>
					<TabsContent value='swimming'>
						<div className='mt-8'>
							<h2 className='text-xl font-medium mb-4 text-orange-900'>
								Swimming Zone Visualizations
							</h2>
							<div className='grid grid-cols-1 lg:grid-cols-2 gap-6 auto-rows-fr'>
								<ZonesTable
									title='Heart Rate Zones'
									unit='BPM'
									data={formatZonesData(
										getAllSpeedData?.zones,
										"heart"
									)}
								/>
								<ZonesTable
									title='Pace Zones'
									unit='min/100m'
									data={formatZonesData(
										getAllSpeedData?.zones,
										"pace"
									)}
								/>
							</div>
						</div>

						<div className='mt-12 bg-white rounded-lg shadow p-6 border border-gray-200'>
							<h2 className='text-2xl font-bold text-gray-950'>
								Calculate Your Swimming Zones
							</h2>
							<p className='text-gray-600 mb-6 text-base'>
								Enter your time trial data to calculate your
								personalized training zones.
							</p>

							<div className='w-[70%] grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-x-6 gap-y-4'>
								<div>
									<label className='block text-sm font-semibold text-gray-700 mb-1'>
										Distance (m)
									</label>
									<select
										className='w-full p-2.5 text-sm border border-gray-300 rounded-md'
										value={swimmingForm.timeTrail || ""}
										onChange={(e) =>
											setSwimmingForm({
												...swimmingForm,
												timeTrail: e.target.value,
											})
										}
									>
										<option value=''>
											Select distance
										</option>
										{swimmingDistance &&
										swimmingDistance.length > 0 ? (
											swimmingDistance.map(
												(value, index) => (
													<option
														key={index}
														value={value}
													>
														{value} M
													</option>
												)
											)
										) : (
											<option disabled>
												No distances available
											</option>
										)}
									</select>
								</div>
								<div>
									<label className='block text-sm font-semibold text-gray-700 mb-1'>
										Time (hh:mm:ss)
									</label>
									<input
										type='text'
										value={swimmingForm.timeValue || ""}
										onChange={(e) => {
											let checkValue =
												handleTimeChangeZones(e);
											setSwimmingForm({
												...swimmingForm,
												timeValue: checkValue,
											});
											HandleChangeTiralTime(
												checkValue,
												setSwimmingForm,
												swimmingForm,
												3
											);
										}}
										className='w-full p-2.5 text-sm border border-gray-300 rounded-md'
										placeholder='hh:mm:ss'
									/>
								</div>
								<div>
									<label className='block text-sm font-semibold text-gray-700 mb-1'>
										Heart Rate (BPM)
									</label>
									<input
										type='number'
										value={swimmingForm.heartrate || ""}
										onChange={(e) =>
											setSwimmingForm({
												...swimmingForm,
												heartrate: e.target.value,
											})
										}
										className='w-full p-2.5 text-sm border border-gray-300 rounded-md'
										placeholder='Enter heart rate'
									/>
								</div>
								<div>
									<label className='block text-sm font-semibold text-gray-700 mb-1'>
										Fitness Level
									</label>
									<input
										type='text'
										value={swimmingForm?.level?.level || ""}
										disabled
										className='w-full p-2.5 text-sm border border-gray-300 rounded-md bg-gray-50'
										placeholder='Auto-calculated'
									/>
								</div>
								<div>
									<label className='block text-sm font-semibold text-gray-700 mb-1'>
										Pace
									</label>
									<input
										type='text'
										value={swimmingForm?.pace || ""}
										disabled
										className='w-full p-2.5 text-sm border border-gray-300 rounded-md bg-gray-50'
										placeholder='Auto-calculated'
									/>
								</div>

								<div className='mt-6 md:col-span-3'>
									<Button
										className='w-full bg-orange-500 hover:bg-orange-600 text-white font-semibold py-3 px-4 rounded text-base'
										onClick={() =>
											handleSubmit(
												3,
												setSwimmingForm,
												swimmingForm,
												getAllSpeedData
											)
										}
									>
										Calculate Zones
									</Button>
								</div>
							</div>
						</div>

						<div className='mt-8 bg-white rounded-lg shadow p-6 border border-gray-200'>
							<h2 className='text-2xl font-bold  text-gray-950'>
								Recent Calculations
							</h2>
							<p className='text-gray-600 mb-4 text-base'>
								Your previous zone calculations
							</p>

							<div className='overflow-x-auto'>
								<table className='min-w-full divide-y divide-gray-200'>
									<thead className='bg-gray-50'>
										<tr>
											<th className='px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
												Activity
											</th>
											<th className='px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
												Distance (km)
											</th>
											<th className='px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
												Time
											</th>
											<th className='px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
												Date
											</th>
											<th className='px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
												Heart Rate
											</th>
											<th className='px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
												Level
											</th>
											<th className='px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
												Speed
											</th>
										</tr>
									</thead>
									<tbody className='bg-white divide-y divide-gray-200'>
										{getAllSpeedData?.changelogs1?.length >
										0 ? (
											getAllSpeedData?.changelogs1?.map(
												(row) => (
													<tr
														key={row?.id}
														className='hover:bg-gray-50'
													>
														<td className='px-4 py-3 whitespace-nowrap text-sm font-medium text-gray-700'>
															{
																row?.activity
																	?.activity_name
															}
														</td>
														<td className='px-4 py-3 whitespace-nowrap text-sm font-medium text-gray-700'>
															{row?.distance} Km
														</td>
														<td className='px-4 py-3 whitespace-nowrap text-sm font-medium text-gray-700'>
															{row?.time}
														</td>
														<td className='px-4 py-3 whitespace-nowrap text-sm font-medium text-gray-700'>
															{new Date(
																row?.createdAt
															).toLocaleDateString(
																"en-US",
																{
																	day: "numeric",
																	month: "long",
																	year: "numeric",
																	hour: "numeric",
																	minute: "numeric",
																}
															)}
														</td>
														<td className='px-4 py-3 whitespace-nowrap text-sm font-medium text-gray-700'>
															{row?.heartrate}
														</td>
														<td className='px-4 py-3 whitespace-nowrap text-sm'>
															<span className='px-3 py-1 inline-flex text-xs font-medium rounded-full bg-orange-50 text-orange-800 border border-orange-200'>
																{
																	row
																		?.fitnesslevel
																		?.level
																}
															</span>
														</td>
														<td className='px-4 py-3 whitespace-nowrap text-sm font-medium text-gray-700'>
															{row?.pace}
														</td>
													</tr>
												)
											)
										) : (
											<tr>
												<td
													colSpan='8'
													className='px-4 py-3 text-center text-sm text-gray-500'
												>
													No recent calculations found
												</td>
											</tr>
										)}
									</tbody>
								</table>
							</div>
						</div>
					</TabsContent>
					<TabsContent value='core'>
						<div className='space-y-6'>
							<Card>
								<CardHeader>
									<CardTitle className='text-orange-800'>
										Core Exercise Performance
									</CardTitle>
									<CardDescription>
										{coreTotalPoints > 0
											? `Total Points: ${coreTotalPoints}`
											: "Complete the form to see your results"}
									</CardDescription>
								</CardHeader>
								<CardContent>
									{heartZoneData &&
									heartZoneData.length > 0 ? (
										<div className='space-y-4'>
											{heartZoneData.map(
												(zones, index) => (
													<div
														key={index}
														className='grid grid-cols-1 sm:grid-cols-2 gap-4 my-4 items-center'
													>
														<div className='font-medium text-base text-gray-600'>
															{
																zones
																	?.subworkout
																	?.subworkout
															}
															<span className='text-red-500 ml-1'>
																*
															</span>
														</div>
														<div className='my-2'>
															{zones?.subworkout
																?.subworkout ===
															"Planks" ? (
																<Input
																	className='text-base'
																	placeholder='hh:mm:ss'
																	value={
																		formValue
																			?.arrayofvalues[
																			index
																		]
																			?.value ||
																		""
																	}
																	onChange={(
																		e
																	) => {
																		let checkValue =
																			handleTimeChangeZones(
																				e
																			);
																		handleInputPlanksChange(
																			checkValue,
																			"value",
																			index
																		);
																		updateFormValue(
																			index,
																			zones?.subworkout_id,
																			zones
																				?.metric
																				?.metric_name,
																			zones
																				?.uom
																				?.uom_name,
																			checkValue
																		);
																	}}
																/>
															) : (
																<Input
																	className='text-base'
																	type='number'
																	placeholder='Enter value'
																	value={
																		formValue
																			?.arrayofvalues[
																			index
																		]
																			?.value ||
																		""
																	}
																	onChange={(
																		e
																	) => {
																		// Only allow numbers
																		const value =
																			e.target.value.replace(
																				/[^0-9]/g,
																				""
																			);
																		handleInputChange(
																			value,
																			"value",
																			index
																		);
																		updateFormValue(
																			index,
																			zones?.subworkout_id,
																			zones
																				?.metric
																				?.metric_name,
																			zones
																				?.uom
																				?.uom_name,
																			value
																		);
																	}}
																/>
															)}
														</div>
													</div>
												)
											)}

											<div className='pt-4'>
												<Button
													className='w-full bg-orange-500 hover:bg-orange-600 text-white'
													onClick={calculateCoreZones}
												>
													Calculate Zones
												</Button>
											</div>
										</div>
									) : (
										<div className='py-8 text-center'>
											<div className='animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-orange-500 mx-auto mb-4'></div>
											<p className='text-gray-500'>
												Loading core exercises...
											</p>
										</div>
									)}
								</CardContent>
							</Card>

							{/* Core Results Table */}
							{coreData &&
								coreData.zones &&
								coreData.zones.length > 0 && (
									<Card>
										<CardHeader>
											<CardTitle>
												Core & Strengthening Results
											</CardTitle>
											<CardDescription>
												Your fitness level for core
												exercises
												{coreData.totalPoints && (
													<span className='font-medium ml-2'>
														Total Points:{" "}
														{coreData.totalPoints}
													</span>
												)}
											</CardDescription>
										</CardHeader>
										<CardContent className='p-0'>
											<Table>
												<TableHeader>
													<TableRow>
														<TableHead>
															Workout
														</TableHead>
														<TableHead>
															Fitness Level
														</TableHead>
														<TableHead className='text-right'>
															Points
														</TableHead>
													</TableRow>
												</TableHeader>
												<TableBody>
													{coreData.zones.map(
														(row, index) => (
															<TableRow
																key={index}
															>
																<TableCell>
																	{
																		row.subworkout
																	}
																</TableCell>
																<TableCell>
																	<Badge
																		variant='outline'
																		className=''
																	>
																		{
																			row.fitnesslevel
																		}
																	</Badge>
																</TableCell>
																<TableCell className='text-right'>
																	{row.points}
																</TableCell>
															</TableRow>
														)
													)}
													<TableRow>
														<TableCell
															colSpan={2}
															className='font-bold'
														>
															Total Points
														</TableCell>
														<TableCell className='text-right font-bold'>
															{
																coreData.totalPoints
															}
														</TableCell>
													</TableRow>
												</TableBody>
											</Table>
										</CardContent>
									</Card>
								)}
						</div>
					</TabsContent>
				</Tabs>
			</div>
		</div>
	);
};

export default ZonesForm;
