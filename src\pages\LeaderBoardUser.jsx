/* eslint-disable no-undef */
import React, { useEffect, useState } from "react";
import "../components/AssignCoach/Assign.css";
import {
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableHeader,
	TableRow,
} from "../components/ui/table";

import { Card, CardContent } from "../components/ui/card";
import { Button } from "../components/ui/button";
import { Button as AntButton } from "antd";
import { Avatar, AvatarFallback } from "../components/ui/avatar";

import { useNavigate } from "react-router";
import Header from "../components/Header";
import moment from "moment";
import {
	createPostLeaderBoard,
	getAllUserChallenges,
	URL,
	getStravaURL,
	getAccesToken,
	fetchleaderBoard,
	checkStrava,
	syncChallengeLeaderboard,
	generateLeaderboardData,
	deleteAthleteChallengeEnrollment,
} from "../API/api-endpoint";
import ReactSelect from "react-select";
import axios from "axios";
import Swal from "sweetalert2";
import { Trophy, Medal, Calendar, Star, Info } from "lucide-react";
import { useFormik } from "formik";
import { validateDOB, validateMobileNumber } from "../utils/Resubale";
import { Skeleton } from "../components/ui/skeleton";
import CircularProgress from "@mui/material/CircularProgress";
import { Trash2 } from "lucide-react";

const LeaderBoardUser = ({ assignedCocahId }) => {
	const user = JSON.parse(localStorage.getItem("user"));
	const navigate = useNavigate();
	const [activityName, setActivityName] = useState("");
	const [roleID, setRoleId] = useState(null);
	const [downloadLoading, setDownloadLoading] = useState(false);

	useEffect(() => {
		setRoleId(localStorage.getItem("roleID"));
	}, []);

	const formik = useFormik({
		initialValues: {
			firstname: "",
			mobile: "",
			dateofbirth: "",
			strava_id: "",
		},
		validate: (values) => {
			let mobileValidationResult = "";
			let econtactValidationResult = "";
			// Validate the mobile number using the validateMobileNumber function
			const dobValidationResult = validateDOB(values.dateofbirth);
			if (values?.mobile) {
				mobileValidationResult = validateMobileNumber(
					values?.mobile?.toString()
				);
			}
			if (values?.econtact) {
				econtactValidationResult = validateMobileNumber(
					values?.econtact?.toString()
				);
			}
			const firstNameValidationResult = validateName(
				values?.firstname,
				"First name"
			);
			const lastNameValidationResult = validateName(
				values?.lastname,
				"Last name"
			);
			// Update the formik errors based on the mobile and DOB validation
			return {
				dateofbirth: dobValidationResult,
				mobile: mobileValidationResult.error,
				econtact: econtactValidationResult.error,
				firstname: firstNameValidationResult,
				lastname: lastNameValidationResult,
			};
		},
	});

	// Determine the user's state for conditional rendering.
	const isCommunity = user?.onboardingState === "community";
	const isYoskaAcademy = user?.onboardingState === "yoska_academy";
	const [getList, setGetList] = useState([]);
	const [selectChallenge, setSelectChallenge] = useState();
	const [fetchLeaderBoard, setFetchLeaderBoard] = useState([]);
	const [stravaURL, setstravaURL] = useState();
	const [usersDetails, setUserDetails] = useState();
	const [loading, setLoading] = useState(false);
	const userId = localStorage.getItem("userId");
	const onboardingState = user?.onboardingState;
	const [isConnected, setIsConnected] = useState(false);
	const [isLoader, setIsLoader] = useState(false);
	const [loaderButton, setLoaderButton] = useState(false);

	useEffect(() => {
		if (isCommunity) {
			fetchCommunityChallenges();
		} else {
			allAtheletes();
		}

		if (userId && onboardingState === "community" && roleID == 5) {
			getURLByLibraryId();
		}
	}, [roleID]);

	// Fetch challenges for community users using the group ID and token from localStorage.
	const fetchCommunityChallenges = async () => {
		try {
			setLoading(true);
			let athleteCommunityGroupId = 0;
			const groupDetailString = localStorage.getItem("groupDetail");

			if (groupDetailString) {
				const groupDetail = JSON.parse(groupDetailString);
				athleteCommunityGroupId = groupDetail.id;
			}

			const token = localStorage.getItem("token");
			const response = await axios.get(
				`${URL}/athlete-community/group-challenge/group/${athleteCommunityGroupId}`,
				{ headers: { Authorization: token } }
			);
			if (response.data.status) {
				setGetList(response.data.data);

				// Auto-select first challenge if available
				if (response.data.data.length > 0) {
					setSelectChallenge(
						response.data.data[0].athletecommunitygroupchallenge.id
					);
				}
			}
			setLoading(false);
		} catch (error) {
			console.error("Error fetching community challenges", error);
			setLoading(false);
		}
	};

	const allAtheletes = async () => {
		try {
			setLoading(true);
			const response = await getAllUserChallenges();
			setGetList(response);

			// Auto-select first challenge if available
			if (response.length > 0) {
				setSelectChallenge(response[0].id);
			}
			setLoading(false);
		} catch (error) {
			console.error("Error fetching challenges", error);
			setLoading(false);
		}
	};

	// For non-community users, call the existing postLeaderBoard endpoint.
	const postLeaderBoard = async () => {
		try {
			setLoading(true);
			let data = {
				challengeid: selectChallenge,
			};
			const response = await createPostLeaderBoard(data);

			setFetchLeaderBoard(response?.sorteduserchallengeswithpoints || []);
			setLoading(false);
		} catch (error) {
			console.error("Error fetching leaderboard data:", error);
			setFetchLeaderBoard([]);
			setLoading(false);
		}
	};

	// For community users, fetch leaderboard from the new endpoint.
	const fetchCommunityLeaderboard = async () => {
		try {
			setLoading(true);
			const response = await fetchleaderBoard(selectChallenge);

			if (response?.status) {
				setFetchLeaderBoard(response?.data || []);
			} else {
				setFetchLeaderBoard([]);
			}
			setLoading(false);
		} catch (error) {
			console.error("Error fetching community leaderboard", error);
			setFetchLeaderBoard([]);
			setLoading(false);
		}
	};

	// When a challenge is selected, call the appropriate leaderboard endpoint.
	useEffect(() => {
		if (selectChallenge) {
			if (isCommunity) {
				fetchCommunityLeaderboard();
			} else {
				postLeaderBoard();
			}
		}
		// eslint-disable-next-line react-hooks/exhaustive-deps
	}, [selectChallenge]);

	// Map options depending on the onboarding state:
	// For community, use the nested athletecommunitygroupchallenge data.
	const options = (getList || []).map((value) =>
		isCommunity
			? {
					label: value.athletecommunitygroupchallenge?.challengeName,
					value: value.athletecommunitygroupchallenge?.id,
			  }
			: {
					label: value.challengeName,
					value: value.id,
			  }
	);

	// Get the selected challenge data
	const selectedChallengeData = isCommunity
		? getList.find(
				(c) => c.athletecommunitygroupchallenge.id === selectChallenge
		  )?.athletecommunitygroupchallenge
		: getList.find((c) => c.id === selectChallenge);

	const getURLByLibraryId = async () => {
		if (userId) {
			let result = await getStravaURL(userId);
			setstravaURL(result);
			checkStravaConnection();
		}
	};

	const withTimeout = (promise, ms = 10000) =>
		Promise.race([
			promise,
			new Promise((_, reject) =>
				setTimeout(() => reject(new Error("Timeout")), ms)
			),
		]);

	const checkStravaConnection = async () => {
		setIsLoader(true);
		try {
			let apiTokenObj = await withTimeout(getAccesToken(), 10000); // <-- timeout after 10s
			let token = apiTokenObj?.access_token;

			if (!token) {
				const user = JSON.parse(localStorage.getItem("user"));
				token = user?.access_token;
			}

			if (!token) {
				setIsConnected(false);
				return;
			}
			const response = await withTimeout(checkStrava(token), 10000); // <-- also timeout
			if (response?.status === 200) {
				setIsConnected(true);
			} else {
				setIsConnected(false);
			}
		} catch (error) {
			console.error("🔥 Error in checkStravaConnection:", error.message);
			setIsConnected(false);

			// Optional: alert or show error
			Swal.fire({
				title: "Connection Error",
				text: "Strava not connected.",
				icon: "error",
			});
		} finally {
			setIsLoader(false);
		}
	};

	// this API is for testing now later will be shifted to api-endpoint
	// 	useEffect(() => {
	//     	checkStravaConnection();
	//   }, []);

	// Helper function to get badge for top 3 positions
	const getTopThreeBadge = (index) => {
		switch (index) {
			case 0:
				return <Info style={{ color: "#f59e0b" }} />;
			case 1:
				return <Info style={{ color: "#94a3b8" }} />;
			case 2:
				return <Info style={{ color: "#d97706" }} />;
			default:
				return null;
		}
	};

	// Helper function to get class for top 3 positions
	const getTopThreeClass = (index) => {
		switch (index) {
			case 0:
				return "top-1";
			case 1:
				return "top-2";
			case 2:
				return "top-3";
			default:
				return "";
		}
	};

	const handleSync = async () => {
		try {
			setLoaderButton(true);
			const response = await syncChallengeLeaderboard(selectChallenge);

			// If the sync is successful and contains data
			if (response?.data?.status && Array.isArray(response?.data?.data)) {
				setFetchLeaderBoard(response?.data?.data);
				if (response?.data?.challenge?.activity?.activity_name) {
					setActivityName(
						response.data.challenge.activity.activity_name
					);
				}

				Swal.fire({
					title: "Leaderboard synced successfully.",
					icon: "success",
					showConfirmButton: true,
				});
			} else {
				Swal.fire({
					title: "Failed to sync leaderboard.",
					icon: "error",
					showConfirmButton: true,
				});
			}
		} catch (error) {
			console.error("Error during sync:", error);
			alert("An error occurred during sync.");
		} finally {
			setLoaderButton(false); // stop loader and enable button
		}
	};

	useEffect(() => {
		if (selectedChallengeData?.activity?.activity_name) {
			setActivityName(selectedChallengeData.activity.activity_name);
		}
	}, [selectedChallengeData]);

	// const handleDownloadReport = async () => {
	// 	try {
	// 		const response = await generateLeaderboardData(selectChallenge);
	// 		alert(response);
	// 	} catch (error) {}
	// }
	const handleDownloadReport = async () => {
		try {
			setDownloadLoading(true);
			const response = await generateLeaderboardData(selectChallenge);
			const downloadUrl = response?.data?.url;

			if (response?.data?.status && downloadUrl) {
				const link = document.createElement("a");
				link.href = downloadUrl;
				link.setAttribute("download", "leaderboard.xlsx"); // Optional: force filename
				document.body.appendChild(link);
				link.click();
				document.body.removeChild(link);
			} else {
				Swal.fire({
					title: "Download Failed",
					text: "No file URL returned from server.",
					icon: "error",
				});
			}
		} catch (error) {
			console.error("Download error:", error);
			Swal.fire({
				title: "Error",
				text: "An error occurred while downloading the report.",
				icon: "error",
			});
		} finally {
			setDownloadLoading(false);
		}
	};

	const handleDeleteEnrollment = async (athleteId, challengeId) => {
		const response = await deleteAthleteChallengeEnrollment(
			challengeId,
			athleteId
		);
		if (response?.data?.status) {
			Swal.fire({
				title: response?.data?.message,
				icon: "success",
				showConfirmButton: true,
			});

			if (isCommunity) {
				await fetchCommunityLeaderboard();
			} else {
				await postLeaderBoard();
			}
		}
	};

	return (
		<div className='max-w-[80%] mx-auto px-4 py-8 '>
			<Header />

			{isLoader && (
				<div
					style={{
						position: "absolute",
						top: "50%",
						left: "50%",
						transform: "translate(-50%, -50%)",
					}}
				>
					<CircularProgress />
				</div>
			)}

			{onboardingState == "community" &&
				!isConnected &&
				!isLoader &&
				roleID == 5 && (
					<div
						className='w-[15%] m-auto bg-[#fffdfd] rounded-xl shadow-lg shadow-#fffdfd-500 p-4 mt-4 flex items-center justify-center'
						style={{
							position: "absolute",
							top: "50%",
							left: "50%",
							transform: "translate(-50%, -50%)",
						}}
					>
						<div>
							<div className='flex items-center justify-center'>
								<b>Connect</b>
								&nbsp;
								<Info />
							</div>
							<p className='text-center'>Strava&nbsp;</p>
							<Button
								variant='contained'
								style={{
									fontSize: "12px",
									backgroundColor: "#E67E22",
								}}
							>
								<a
									href={stravaURL?.url}
									target='_blank'
									rel='noreferrer'
								>
									Connect with strava
								</a>
							</Button>
							<br />
						</div>
					</div>
				)}

			{((onboardingState == "community" &&
				isConnected &&
				!isLoader &&
				roleID == 5) ||
				(onboardingState == "community" &&
					!isLoader &&
					roleID == 6)) && (
				<>
					<div className='mb-8 mt-20'>
						<h1 className='text-4xl font-bold tracking-tight text-orange-950'>
							Challenge Leaderboard
						</h1>
						<p className='mt-2 text-base text-slate-500'>
							Track your progress and see how you rank against
							other athletes
						</p>
					</div>
					{onboardingState === "yoska_academy" && (
						<div className='grid grid-cols-1 md:grid-cols-4 gap-6 mb-8'>
							<div
								className='col-span-1 md:col-span-3 rounded-lg shadow-sm bg-white overflow-hidden'
								style={{ border: "2px solid #fff4eb" }}
							>
								<div
									className='flex flex-col p-4'
									style={{
										borderBottom: "2px solid #fff4eb",
									}}
								>
									<p className='text-lg text-orange-800 font-bold leading-none tracking-tight'>
										Challenge Selection
									</p>
								</div>
								<div className='p-6 relative'>
									<ReactSelect
										options={options}
										value={options.find(
											(option) =>
												option.value === selectChallenge
										)}
										onChange={(selectedOption) =>
											setSelectChallenge(
												selectedOption.value
											)
										}
										placeholder='Select a challenge'
										isLoading={loading}
										className='w-full'
										styles={{
											menu: (provided) => ({
												...provided,
												zIndex: 9999,
											}),
											menuPortal: (provided) => ({
												...provided,
												zIndex: 9999,
											}),
											control: (provided) => ({
												...provided,
												borderColor: "#fff4eb",
												borderWidth: "1px",
												boxShadow: "none",
												"&:hover": {
													borderColor: "#ffedd5",
												},
												fontSize: "16px",
												minHeight: "45px",
											}),
											option: (provided, state) => ({
												...provided,
												backgroundColor:
													state.isSelected
														? "#f97316"
														: state.isFocused
														? "#fff4eb"
														: "white",
												color: state.isSelected
													? "white"
													: "#333",
												fontSize: "16px",
												padding: "10px 12px",
											}),
											singleValue: (provided) => ({
												...provided,
												fontSize: "16px",
												color: "#333",
											}),
											placeholder: (provided) => ({
												...provided,
												fontSize: "16px",
											}),
											dropdownIndicator: (provided) => ({
												...provided,
												padding: "8px",
											}),
										}}
										menuPortalTarget={document.body}
										menuPosition='fixed'
									/>
								</div>
							</div>

							{selectedChallengeData && (
								<div
									className='rounded-lg shadow-sm bg-white overflow-hidden'
									style={{ border: "2px solid #fff4eb" }}
								>
									<div
										className='flex flex-col p-4'
										style={{
											borderBottom: "2px solid #fff4eb",
										}}
									>
										<p className='text-lg text-orange-800 font-bold leading-none tracking-tight'>
											Challenge Info
										</p>
									</div>
									<div className='p-6'>
										<div className='flex items-center gap-2 mb-2'>
											<Calendar
												style={{
													color: "#f97316",
													fontSize: "1rem",
												}}
											/>
											<span className='text-sm'>
												{moment(
													selectedChallengeData.challengeStartDate
												).format("DD-MM-YYYY")}{" "}
												-{" "}
												{moment(
													selectedChallengeData.challengeEndDate
												).format("DD-MM-YYYY")}
											</span>
										</div>
										<div className='flex items-center gap-2'>
											<Trophy
												style={{
													color: "#f97316",
													fontSize: "1rem",
												}}
											/>
											<span className='text-sm'>
												{fetchLeaderBoard.length}{" "}
												participants
											</span>
										</div>
									</div>
								</div>
							)}
						</div>
					)}

					{onboardingState === "community" && (
						<div className='grid grid-cols-1 md:grid-cols-4 gap-6 mb-8'>
							<div
								className='col-span-1 md:col-span-3 rounded-lg shadow-sm bg-white overflow-hidden'
								style={{ border: "2px solid #fff4eb" }}
							>
								<div
									className='flex flex-col p-4'
									style={{
										borderBottom: "2px solid #fff4eb",
									}}
								>
									<p className='text-lg text-orange-800 font-bold leading-none tracking-tight'>
										Challenge Selection
									</p>
								</div>
								<div className='grid grid-cols-5 gap-6'>
									<div className='p-6 relative col-span-4'>
										<ReactSelect
											options={options}
											value={options.find(
												(option) =>
													option.value ===
													selectChallenge
											)}
											onChange={(selectedOption) =>
												setSelectChallenge(
													selectedOption.value
												)
											}
											placeholder='Select a challenge'
											isLoading={loading}
											className='w-full'
											styles={{
												menu: (provided) => ({
													...provided,
													zIndex: 9999,
												}),
												menuPortal: (provided) => ({
													...provided,
													zIndex: 9999,
												}),
												control: (provided) => ({
													...provided,
													borderColor: "#fff4eb",
													borderWidth: "1px",
													boxShadow: "none",
													"&:hover": {
														borderColor: "#ffedd5",
													},
													fontSize: "16px",
													minHeight: "45px",
												}),
												option: (provided, state) => ({
													...provided,
													backgroundColor:
														state.isSelected
															? "#f97316"
															: state.isFocused
															? "#fff4eb"
															: "white",
													color: state.isSelected
														? "white"
														: "#333",
													fontSize: "16px",
													padding: "10px 12px",
												}),
												singleValue: (provided) => ({
													...provided,
													fontSize: "16px",
													color: "#333",
												}),
												placeholder: (provided) => ({
													...provided,
													fontSize: "16px",
												}),
												dropdownIndicator: (
													provided
												) => ({
													...provided,
													padding: "8px",
												}),
											}}
											menuPortalTarget={document.body}
											menuPosition='fixed'
										/>
									</div>
									{roleID != 6 && (
										<div className='p-6 col-span-1 flex items-center'>
											<AntButton
												type='primary'
												className='w-full'
												style={{
													color: loaderButton
														? "white"
														: undefined,
												}}
												disabled={loaderButton}
												loading={loaderButton}
												onClick={handleSync}
											>
												Sync
											</AntButton>
										</div>
									)}
									{roleID == 6 && (
										<div className='col-span-1 flex items-center'>
											<div className='pr-3 pl-3'>
												<AntButton
													type='primary'
													className='mb-1'
													disabled={loaderButton}
													loading={loaderButton}
													onClick={handleSync}
													style={{
														color: loaderButton
															? "white"
															: undefined,
													}}
												>
													Sync
												</AntButton>
												<AntButton
													type='primary'
													className='w-full mt-1'
													loading={downloadLoading}
													disabled={downloadLoading}
													onClick={
														handleDownloadReport
													}
													style={{
														color: downloadLoading
															? "white"
															: undefined,
													}}
												>
													Download Report
												</AntButton>
											</div>
										</div>
									)}
								</div>
							</div>

							{selectedChallengeData && (
								<div
									className='rounded-lg shadow-sm bg-white overflow-hidden'
									style={{ border: "2px solid #fff4eb" }}
								>
									<div
										className='flex flex-col p-4'
										style={{
											borderBottom: "2px solid #fff4eb",
										}}
									>
										<p className='text-lg text-orange-800 font-bold leading-none tracking-tight'>
											Challenge Info
										</p>
									</div>
									<div className='p-6'>
										<div className='flex items-center gap-2 mb-2'>
											<Calendar
												style={{
													color: "#f97316",
													fontSize: "1rem",
												}}
											/>
											<span className='text-sm'>
												{moment(
													selectedChallengeData.challengeStartDate
												).format("DD-MM-YYYY")}{" "}
												-{" "}
												{moment(
													selectedChallengeData.challengeEndDate
												).format("DD-MM-YYYY")}
											</span>
										</div>
										<div className='flex items-center gap-2'>
											<Trophy
												style={{
													color: "#f97316",
													fontSize: "1rem",
												}}
											/>
											<span className='text-sm'>
												{fetchLeaderBoard.length}{" "}
												participants
											</span>
										</div>
									</div>
								</div>
							)}
						</div>
					)}
					<Card>
						<div className='p-4'>
							<div className='flex items-center gap-2'>
								<Trophy
									style={{
										color: "#f97316",
										fontSize: "24px",
									}}
								/>
								<p className='text-lg text-orange-800 font-bold leading-none tracking-tight'>
									Leaderboard Rankings
								</p>
							</div>

							{selectChallenge &&
								options.find(
									(option) => option.value === selectChallenge
								) && (
									<p className='text-orange-600 mt-1 text-base ml-8'>
										{
											options.find(
												(option) =>
													option.value ===
													selectChallenge
											).label
										}
									</p>
								)}
						</div>
						<CardContent>
							{loading ? (
								<div className='w-full p-8 flex flex-col gap-2'>
									<Skeleton className='w-full h-[60px]' />
									<Skeleton className='w-full h-[400px]' />
								</div>
							) : fetchLeaderBoard &&
							  fetchLeaderBoard.length > 0 ? (
								<div className='rounded-md border'>
									<Table>
										<TableHeader>
											<TableRow className='bg-orange-50'>
												<TableHead className='w-[80px] text-orange-800 font-semibold'>
													Rank
												</TableHead>
												<TableHead className='text-orange-800 font-semibold'>
													Athlete
												</TableHead>
												<TableHead className='text-right text-orange-800 font-semibold'>
													Total Distance (km)
												</TableHead>
												<TableHead className='text-right text-orange-800 font-semibold'>
													Total Duration (hr)
												</TableHead>
												<TableHead className='text-right text-orange-800 font-semibold'>
													{["cycling"].includes(
														activityName?.toLowerCase()
													)
														? "Speed (km/min)"
														: "Pace (km/min)"}
												</TableHead>
												{!(
													roleID == 5 &&
													onboardingState ===
														"community"
												) && (
													<TableHead className='text-center text-orange-800 font-semibold'>
														Action
													</TableHead>
												)}

												{/* <TableHead className='text-right text-orange-800 font-semibold'>
													Score
												</TableHead>
												<TableHead className='text-orange-800 font-semibold'>
													Badge
												</TableHead> */}
											</TableRow>
										</TableHeader>
										<TableBody>
											{fetchLeaderBoard.map(
												(row, index) => (
													<TableRow
														key={`${
															row.user?.id ||
															index
														}-${
															row.challenge?.id ||
															index
														}`}
														className={`${
															index === 0
																? "bg-[#fff8f1] border-l-4 border-l-orange-500"
																: index === 1
																? "bg-[#f1f5f9] border-l-4 border-l-slate-500"
																: index === 2
																? "bg-[#fffbf8] border-l-4 border-l-orange-400"
																: ""
														}`}
													>
														<TableCell>
															<div className='flex items-center'>
																{index === 0 ? (
																	<Trophy className='h-5 w-5 text-orange-400' />
																) : index ===
																  1 ? (
																	<Medal className='h-5 w-5 text-slate-400' />
																) : index ===
																  2 ? (
																	<Medal className='h-5 w-5 text-orange-300' />
																) : (
																	<span>
																		{index +
																			1}
																	</span>
																)}
															</div>
														</TableCell>
														<TableCell>
															<div className='flex items-center gap-3'>
																<Avatar className='h-8 w-8 bg-orange-200 text-orange-800 border border-orange-300'>
																	<img
																		src={
																			row
																				?.user
																				?.profile ||
																			row?.profile
																				? `${URL}/static/public/userimages/${
																						row
																							?.user
																							?.profile ||
																						row?.profile
																				  }`
																				: "https://i.ibb.co/5xCF7vx/u-https-spng-pngfind-com-pngs-s-610-6104451-image-placeholder-png-user-profile-placeholder-image-png.jpg"
																		}
																		alt='User avatar'
																		className='h-full w-full object-cover'
																		onError={(
																			e
																		) => {
																			e.target.onerror =
																				null;
																			e.target.src =
																				"https://i.ibb.co/5xCF7vx/u-https-spng-pngfind-com-pngs-s-610-6104451-image-placeholder-png-user-profile-placeholder-image-png.jpg";
																		}}
																	/>
																</Avatar>

																<div>
																	<h1 className='font-semibold'>
																		{
																			row.name
																		}
																	</h1>
																	<div>
																		<div className='font-medium'>
																			{row
																				.user
																				?.firstname ||
																				row.firstname ||
																				""}{" "}
																			{row
																				.user
																				?.lastname ||
																				row.lastname ||
																				""}
																		</div>
																		{index <
																			3 && (
																			<div
																				className={`inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-medium ${
																					index ===
																					0
																						? "bg-white text-orange-600 border-orange-600"
																						: index ===
																						  1
																						? "bg-white text-gray-500 border-gray-500"
																						: "bg-white text-orange-800 border-orange-800"
																				}`}
																			>
																				{index ===
																				0
																					? "Gold"
																					: index ===
																					  1
																					? "Silver"
																					: "Bronze"}
																			</div>
																		)}
																	</div>
																</div>
															</div>
														</TableCell>
														<TableCell className='text-right'>
															{row.totalDistance ||
																"0"}
														</TableCell>
														<TableCell className='text-right'>
															{row.totalDuration ||
																"00"}
														</TableCell>{" "}
														<TableCell className='text-right'>
															{[
																"cycling",
															].includes(
																activityName?.toLowerCase()
															)
																? row.avgSpeed ||
																  "0"
																: row.avgPace ||
																  "0"}
														</TableCell>
														{!(
															roleID == 5 &&
															onboardingState ===
																"community"
														) && (
															<TableCell className='text-center'>
																{(() => {
																	const challengeData =
																		getList.find(
																			(
																				item
																			) =>
																				item
																					?.athletecommunitygroupchallenge
																					?.id ===
																				(row
																					?.challenge
																					?.id ||
																					selectChallenge)
																		)?.athletecommunitygroupchallenge;

																	const challengeEndDate =
																		challengeData?.challengeEndDate;
																	const isPastChallenge =
																		challengeEndDate &&
																		new Date(
																			challengeEndDate
																		) <
																			new Date();

																	const shouldDisable =
																		roleID ==
																			6 &&
																		onboardingState ===
																			"community" &&
																		isPastChallenge;

																	return (
																		<button
																			onClick={() =>
																				!shouldDisable &&
																				handleDeleteEnrollment(
																					row?.userId,
																					row
																						?.challenge
																						?.id ||
																						selectChallenge
																				)
																			}
																			className={`${
																				shouldDisable
																					? "text-gray-400 cursor-not-allowed"
																					: "text-red-600 hover:text-red-800"
																			}`}
																			title={
																				shouldDisable
																					? "Cannot delete. Challenge has ended."
																					: "Delete Enrollment"
																			}
																			disabled={
																				shouldDisable
																			}
																		>
																			<Trash2 className='w-4 h-4' />
																		</button>
																	);
																})()}
															</TableCell>
														)}
														{/* <TableCell className='text-right'>
														<span className='font-bold text-lg text-orange-900'>
															{row.userchallangepoint
																?.points ||
																row.points ||
																row.userchallenge
																	?.points ||
																row.challengePoints ||
																0}
														</span>
													</TableCell>
													<TableCell>
														{row.completed && (
															<div className='w-8 h-8 rounded-full bg-orange-100 flex items-center justify-center border border-orange-300'>
																<Star className='h-4 w-4 text-orange-500' />
															</div>
														)}
													</TableCell> */}
													</TableRow>
												)
											)}
										</TableBody>
									</Table>
								</div>
							) : (
								<div className='text-center p-8 text-gray-500'>
									No leaderboard data available
								</div>
							)}
						</CardContent>
					</Card>
				</>
			)}

			{onboardingState == "yoska_academy" && !isLoader && (
				<>
					<div className='mb-8 mt-20'>
						<h1 className='text-4xl font-bold tracking-tight text-orange-950'>
							Challenge Leaderboard
						</h1>
						<p className='mt-2 text-base text-slate-500'>
							Track your progress and see how you rank against
							other athletes
						</p>
					</div>

					<div className='grid grid-cols-1 md:grid-cols-4 gap-6 mb-8'>
						<div
							className='col-span-1 md:col-span-3 rounded-lg shadow-sm bg-white overflow-hidden'
							style={{ border: "2px solid #fff4eb" }}
						>
							<div
								className='flex flex-col p-4'
								style={{ borderBottom: "2px solid #fff4eb" }}
							>
								<p className='text-lg text-orange-800 font-bold leading-none tracking-tight'>
									Challenge Selection
								</p>
							</div>
							<div className='p-6 relative'>
								<ReactSelect
									options={options}
									value={options.find(
										(option) =>
											option.value === selectChallenge
									)}
									onChange={(selectedOption) =>
										setSelectChallenge(selectedOption.value)
									}
									placeholder='Select a challenge'
									isLoading={loading}
									className='w-full'
									styles={{
										menu: (provided) => ({
											...provided,
											zIndex: 9999,
										}),
										menuPortal: (provided) => ({
											...provided,
											zIndex: 9999,
										}),
										control: (provided) => ({
											...provided,
											borderColor: "#fff4eb",
											borderWidth: "1px",
											boxShadow: "none",
											"&:hover": {
												borderColor: "#ffedd5",
											},
											fontSize: "16px",
											minHeight: "45px",
										}),
										option: (provided, state) => ({
											...provided,
											backgroundColor: state.isSelected
												? "#f97316"
												: state.isFocused
												? "#fff4eb"
												: "white",
											color: state.isSelected
												? "white"
												: "#333",
											fontSize: "16px",
											padding: "10px 12px",
										}),
										singleValue: (provided) => ({
											...provided,
											fontSize: "16px",
											color: "#333",
										}),
										placeholder: (provided) => ({
											...provided,
											fontSize: "16px",
										}),
										dropdownIndicator: (provided) => ({
											...provided,
											padding: "8px",
										}),
									}}
									menuPortalTarget={document.body}
									menuPosition='fixed'
								/>
							</div>
						</div>

						{selectedChallengeData && (
							<div
								className='rounded-lg shadow-sm bg-white overflow-hidden'
								style={{ border: "2px solid #fff4eb" }}
							>
								<div
									className='flex flex-col p-4'
									style={{
										borderBottom: "2px solid #fff4eb",
									}}
								>
									<p className='text-lg text-orange-800 font-bold leading-none tracking-tight'>
										Challenge Info
									</p>
								</div>
								<div className='p-6'>
									<div className='flex items-center gap-2 mb-2'>
										<Calendar
											style={{
												color: "#f97316",
												fontSize: "1rem",
											}}
										/>
										<span className='text-sm'>
											{moment(
												selectedChallengeData.challengeStartDate
											).format("DD-MM-YYYY")}{" "}
											-{" "}
											{moment(
												selectedChallengeData.challengeEndDate
											).format("DD-MM-YYYY")}
										</span>
									</div>
									<div className='flex items-center gap-2'>
										<Trophy
											style={{
												color: "#f97316",
												fontSize: "1rem",
											}}
										/>
										<span className='text-sm'>
											{fetchLeaderBoard.length}{" "}
											participants
										</span>
									</div>
								</div>
							</div>
						)}
					</div>

					<Card>
						<div className='p-4'>
							<div className='flex items-center gap-2'>
								<Trophy
									style={{
										color: "#f97316",
										fontSize: "24px",
									}}
								/>
								<p className='text-lg text-orange-800 font-bold leading-none tracking-tight'>
									Leaderboard Rankings
								</p>
							</div>

							{selectChallenge &&
								options.find(
									(option) => option.value === selectChallenge
								) && (
									<p className='text-orange-600 mt-1 text-base ml-8'>
										{
											options.find(
												(option) =>
													option.value ===
													selectChallenge
											).label
										}
									</p>
								)}
						</div>
						<CardContent>
							{loading ? (
								<div className='w-full p-8 flex flex-col gap-2'>
									<Skeleton className='w-full h-[60px]' />
									<Skeleton className='w-full h-[400px]' />
								</div>
							) : fetchLeaderBoard &&
							  fetchLeaderBoard.length > 0 ? (
								<div className='rounded-md border'>
									<Table>
										<TableHeader>
											<TableRow className='bg-orange-50'>
												<TableHead className='w-[80px] text-orange-800 font-semibold'>
													Rank
												</TableHead>
												<TableHead className='text-orange-800 font-semibold'>
													Athlete
												</TableHead>
												{/* <TableHead className='text-right text-orange-800 font-semibold'>
													Score
												</TableHead>
												<TableHead className='text-orange-800 font-semibold'>
													Badge
												</TableHead> */}
											</TableRow>
										</TableHeader>
										<TableBody>
											{fetchLeaderBoard.map(
												(row, index) => (
													<TableRow
														key={`${
															row.user?.id ||
															index
														}-${
															row.challenge?.id ||
															index
														}`}
														className={`${
															index === 0
																? "bg-[#fff8f1] border-l-4 border-l-orange-500"
																: index === 1
																? "bg-[#f1f5f9] border-l-4 border-l-slate-500"
																: index === 2
																? "bg-[#fffbf8] border-l-4 border-l-orange-400"
																: ""
														}`}
													>
														<TableCell>
															<div className='flex items-center'>
																{index === 0 ? (
																	<Trophy className='h-5 w-5 text-orange-400' />
																) : index ===
																  1 ? (
																	<Medal className='h-5 w-5 text-slate-400' />
																) : index ===
																  2 ? (
																	<Medal className='h-5 w-5 text-orange-300' />
																) : (
																	<span>
																		{index +
																			1}
																	</span>
																)}
															</div>
														</TableCell>
														<TableCell>
															<div className='flex items-center gap-3'>
																<Avatar className='h-8 w-8 bg-orange-200 text-orange-800 border border-orange-300'>
																	<AvatarFallback>
																		{row
																			.user
																			?.firstname?.[0] ||
																			row
																				.firstname?.[0] ||
																			""}
																		{row
																			.user
																			?.lastname?.[0] ||
																			row
																				.lastname?.[0] ||
																			""}
																	</AvatarFallback>
																</Avatar>
																<div>
																	<div className='font-medium'>
																		{row
																			.user
																			?.firstname ||
																			row.firstname ||
																			""}{" "}
																		{row
																			.user
																			?.lastname ||
																			row.lastname ||
																			""}
																	</div>
																	{index <
																		3 && (
																		<div
																			className={`inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-medium ${
																				index ===
																				0
																					? "bg-white text-orange-600 border-orange-600"
																					: index ===
																					  1
																					? "bg-white text-gray-500 border-gray-500"
																					: "bg-white text-orange-800 border-orange-800"
																			}`}
																		>
																			{index ===
																			0
																				? "Gold"
																				: index ===
																				  1
																				? "Silver"
																				: "Bronze"}
																		</div>
																	)}
																</div>
															</div>
														</TableCell>
													</TableRow>
												)
											)}
										</TableBody>
									</Table>
								</div>
							) : (
								<div className='text-center p-8 text-gray-500'>
									No leaderboard data available
								</div>
							)}
						</CardContent>
					</Card>
				</>
			)}
		</div>
	);
};

export default LeaderBoardUser;
