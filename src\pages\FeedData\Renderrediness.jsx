import { useEffect, useState, use<PERSON>emo, useCallback } from "react";
import { <PERSON><PERSON> } from "../../components/ui/button";
import { Input } from "../../components/ui/input";
import { Card, CardContent, CardHeader } from "../../components/ui/card";
import {
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableHeader,
	TableRow,
} from "../../components/ui/table";
import { ArrowRight } from "lucide-react";
import Header from "../../components/Header";
import { getAllraceReadnessdata } from "../../API/api-endpoint";
import Swal from "sweetalert2";

const RenderReadiness = () => {
	const [readinessData, setReadinessData] = useState([]);
	const [isLoading, setIsLoading] = useState(true);
	const [searchTerm, setSearchTerm] = useState("");
	const [currentPage, setCurrentPage] = useState(1);
	const pageSize = 10;

	// Fetch readiness data
	const fetchData = useCallback(async () => {
		try {
			setIsLoading(true);
			const response = await getAllraceReadnessdata();
			console.log("Readiness data response:", response);

			if (response) {
				setReadinessData(response || []);
			} else {
				console.log("Response is empty or invalid");
				setReadinessData([]);
			}
		} catch (error) {
			console.error("Error fetching readiness data:", error);
			Swal.fire({
				title: "Error",
				text: "Failed to fetch readiness data. Please try again.",
				icon: "error",
				timer: 3000,
				showConfirmButton: false,
			});
			setReadinessData([]);
		} finally {
			setIsLoading(false);
		}
	}, []);

	// Initial data fetch
	useEffect(() => {
		fetchData();
	}, [fetchData]);

	// Filter data based on search term
	const filteredData = useMemo(() => {
		if (!searchTerm.trim()) return readinessData;

		return readinessData.filter((item) => {
			return item?.name?.toLowerCase().includes(searchTerm.toLowerCase());
		});
	}, [readinessData, searchTerm]);

	// Paginate filtered data
	const paginatedData = useMemo(() => {
		const startIndex = (currentPage - 1) * pageSize;
		const endIndex = startIndex + pageSize;
		return filteredData.slice(startIndex, endIndex);
	}, [filteredData, currentPage, pageSize]);

	// Calculate total pages
	const totalPages = Math.ceil(filteredData.length / pageSize);

	// Handle page change
	const handlePageChange = (page) => {
		setCurrentPage(page);
	};

	// Handle search
	const handleSearch = (value) => {
		setSearchTerm(value);
		setCurrentPage(1);
	};

	return (
		<div>
			<Header />
			<div className='mx-auto p-6 max-w-[1300px] mt-16'>
				<Card>
					<CardHeader className='bg-orange-50 border-b flex flex-col gap-4'>
						<div className='flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4'>
							<div>
								<h1 className='text-2xl font-bold text-orange-900'>
									Race Readiness
								</h1>
								<p className='text-orange-700 mt-1 text-sm'>
									Manage race readiness assessments and data
								</p>
							</div>
							<div className='flex flex-wrap gap-3'>
								<Button
									onClick={() =>
										(window.location.href =
											"/score-readness")
									}
									className='bg-blue-600 hover:bg-blue-700 text-white'
								>
									Get Score
									<ArrowRight className='h-4 w-4 ml-2' />
								</Button>
								<Button
									onClick={() =>
										(window.location.href =
											"/segments-readness")
									}
									className='bg-green-600 hover:bg-green-700 text-white'
								>
									Get Segments
									<ArrowRight className='h-4 w-4 ml-2' />
								</Button>
								<Button
									onClick={() =>
										(window.location.href =
											"/subsegments-readness")
									}
									className='bg-purple-600 hover:bg-purple-700 text-white'
								>
									Get Sub Segments
									<ArrowRight className='h-4 w-4 ml-2' />
								</Button>
								<Button
									onClick={() =>
										(window.location.href =
											"/option-readness")
									}
									className='bg-indigo-600 hover:bg-indigo-700 text-white'
								>
									Get Option
									<ArrowRight className='h-4 w-4 ml-2' />
								</Button>
							</div>
						</div>

						{/* Search Controls */}
						<div className='flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4'>
							<div className='flex gap-3'>
								<Input
									placeholder='Search by name...'
									value={searchTerm}
									onChange={(e) =>
										handleSearch(e.target.value)
									}
									className='max-w-sm text-sm'
								/>
							</div>
							<div className='text-sm text-orange-700 flex items-center'>
								Total: {filteredData.length} readiness items
							</div>
						</div>
					</CardHeader>
					<CardContent className='p-0'>
						<div className='overflow-x-auto'>
							<Table className='min-w-full w-full'>
								<TableHeader>
									<TableRow className='bg-blue-600 hover:bg-blue-600'>
										<TableHead className='text-white font-semibold'>
											Sr No
										</TableHead>
										<TableHead className='text-white font-semibold'>
											Name
										</TableHead>
									</TableRow>
								</TableHeader>
								<TableBody>
									{isLoading ? (
										<TableRow>
											<TableCell
												colSpan={2}
												className='text-center py-8'
											>
												Loading...
											</TableCell>
										</TableRow>
									) : paginatedData.length > 0 ? (
										paginatedData.map((item, index) => (
											<TableRow
												key={item.srID || index}
												className='hover:bg-gray-50'
											>
												<TableCell className='font-medium'>
													{item?.srID ||
														(currentPage - 1) *
															pageSize +
															index +
															1}
												</TableCell>
												<TableCell>
													{item?.name || "N/A"}
												</TableCell>
											</TableRow>
										))
									) : (
										<TableRow>
											<TableCell
												colSpan={2}
												className='text-center py-8 text-gray-500'
											>
												No readiness data found
											</TableCell>
										</TableRow>
									)}
								</TableBody>
							</Table>
						</div>

						{/* Pagination */}
						{totalPages > 1 && (
							<div className='flex items-center justify-between px-6 py-4 border-t'>
								<div className='text-sm text-gray-700'>
									Showing {(currentPage - 1) * pageSize + 1}{" "}
									to{" "}
									{Math.min(
										currentPage * pageSize,
										filteredData.length
									)}{" "}
									of {filteredData.length} results
								</div>
								<div className='flex items-center gap-2'>
									<Button
										variant='outline'
										size='sm'
										onClick={() =>
											handlePageChange(
												Math.max(1, currentPage - 1)
											)
										}
										disabled={currentPage === 1}
									>
										Previous
									</Button>

									{/* Page numbers */}
									{Array.from(
										{ length: Math.min(5, totalPages) },
										(_, i) => {
											const pageNum =
												Math.max(
													1,
													Math.min(
														totalPages - 4,
														currentPage - 2
													)
												) + i;
											return (
												<Button
													key={pageNum}
													variant={
														currentPage === pageNum
															? "default"
															: "outline"
													}
													size='sm'
													onClick={() =>
														handlePageChange(
															pageNum
														)
													}
													className={
														currentPage === pageNum
															? "bg-orange-600 hover:bg-orange-700"
															: ""
													}
												>
													{pageNum}
												</Button>
											);
										}
									)}

									<Button
										variant='outline'
										size='sm'
										onClick={() =>
											handlePageChange(
												Math.min(
													totalPages,
													currentPage + 1
												)
											)
										}
										disabled={currentPage === totalPages}
									>
										Next
									</Button>
								</div>
							</div>
						)}
					</CardContent>
				</Card>
			</div>
		</div>
	);
};

export default RenderReadiness;
