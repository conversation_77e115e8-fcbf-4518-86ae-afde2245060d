import * as React from "react";

const CollapsibleContext = React.createContext();

const Collapsible = React.forwardRef(
	({ className, open, onOpenChange, children, ...props }, ref) => {
		const [isOpen, setIsOpen] = React.useState(open ?? false);

		React.useEffect(() => {
			if (open !== undefined) {
				setIsOpen(open);
			}
		}, [open]);

		const handleToggle = () => {
			const newState = !isOpen;
			setIsOpen(newState);
			onOpenChange?.(newState);
		};

		return (
			<CollapsibleContext.Provider value={{ isOpen, handleToggle }}>
				<div
					ref={ref}
					data-state={isOpen ? "open" : "closed"}
					className={className}
					{...props}
				>
					{children}
				</div>
			</CollapsibleContext.Provider>
		);
	}
);
Collapsible.displayName = "Collapsible";

const CollapsibleTrigger = React.forwardRef(({ className, ...props }, ref) => {
	const context = React.useContext(CollapsibleContext);

	if (!context)
		throw new Error("CollapsibleTrigger must be used inside Collapsible");

	const { handleToggle } = context;

	return (
		<button
			ref={ref}
			type='button'
			className={className}
			onClick={(e) => {
				handleToggle();
				props.onClick?.(e);
			}}
			{...props}
		/>
	);
});
CollapsibleTrigger.displayName = "CollapsibleTrigger";

const CollapsibleContent = React.forwardRef(({ className, ...props }, ref) => {
	const context = React.useContext(CollapsibleContext);

	if (!context)
		throw new Error("CollapsibleContent must be used inside Collapsible");

	const { isOpen } = context;

	return (
		<div
			ref={ref}
			data-state={isOpen ? "open" : "closed"}
			className={`transition-all duration-300 ${
				isOpen ? "block" : "hidden"
			} ${className}`}
			{...props}
		/>
	);
});
CollapsibleContent.displayName = "CollapsibleContent";

export { Collapsible, CollapsibleTrigger, CollapsibleContent };
