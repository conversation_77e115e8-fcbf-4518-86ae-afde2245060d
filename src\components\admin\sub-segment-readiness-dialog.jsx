import { useState, useEffect } from "react";
import { But<PERSON> } from "../ui/button";
import { Input } from "../ui/input";
import { Label } from "../ui/label";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "../ui/dialog";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "../ui/select";
import {
	createsuSegmentReadnessdata,
	updatesuSegmentReadnessdata,
	getAllSegmentReadnessData,
} from "../../API/api-endpoint";
import Swal from "sweetalert2";

export const SubSegmentReadinessDialog = ({
	open,
	onClose,
	onSuccess,
	editingItem,
}) => {
	const [formData, setFormData] = useState({
		segment_id: "",
		name: "",
		measuring_factor: "",
		metric: "",
	});
	const [isLoading, setIsLoading] = useState(false);
	const [segmentList, setSegmentList] = useState([]);

	const fetchSegmentData = async () => {
		try {
			const response = await getAllSegmentReadnessData();
			setSegmentList(response || []);
		} catch (error) {
			console.error("Error fetching segment readness data:", error);
			setSegmentList([]);
		}
	};

	useEffect(() => {
		if (open) {
			fetchSegmentData();
		}
	}, [open]);

	useEffect(() => {
		if (open) {
			if (editingItem?.id) {
				const editData = {
					segment_id: editingItem.segment_id || "",
					name: editingItem.name || "",
					measuring_factor: editingItem.measuring_factor || "",
					metric: editingItem.metric || "",
				};
				setFormData(editData);
			} else {
				const newData = {
					segment_id: "",
					name: "",
					measuring_factor: "",
					metric: "",
				};
				setFormData(newData);
			}
		}
	}, [open, editingItem]);

	const handleInputChange = (field, value) => {
		setFormData((prev) => ({
			...prev,
			[field]: value,
		}));
	};

	const handleSubmit = async (e) => {
		e.preventDefault();

		if (!formData.segment_id) {
			Swal.fire({
				title: "Error",
				text: "Sub segment name is required",
				icon: "error",
				timer: 1800,
				showConfirmButton: false,
			});
			return;
		}

		if (!formData.name.trim()) {
			Swal.fire({
				title: "Error",
				text: "Name is required",
				icon: "error",
				timer: 1800,
				showConfirmButton: false,
			});
			return;
		}

		if (!formData.measuring_factor.trim()) {
			Swal.fire({
				title: "Error",
				text: "Measuring factor is required",
				icon: "error",
				timer: 1800,
				showConfirmButton: false,
			});
			return;
		}

		try {
			setIsLoading(true);

			const apiData = {
				segment_id: formData.segment_id,
				name: formData.name.trim(),
				measuring_factor: formData.measuring_factor.trim(),
				metric: formData.metric.trim(),
			};

			let response;
			if (editingItem?.id) {
				apiData.id = editingItem.id;
				response = await updatesuSegmentReadnessdata(apiData);
			} else {
				response = await createsuSegmentReadnessdata(apiData);
			}

			if (response?.status) {
				Swal.fire({
					title: "Success",
					text:
						response.message ||
						`Sub segment readiness ${
							editingItem?.id ? "updated" : "created"
						} successfully`,
					icon: "success",
					timer: 2000,
					showConfirmButton: false,
				});
				onSuccess();
			} else {
				Swal.fire({
					title: "Error",
					text:
						response?.message ||
						"Failed to save sub segment readiness",
					icon: "error",
					timer: 3000,
					showConfirmButton: false,
				});
			}
		} catch (error) {
			console.error("Error saving sub segment readiness:", error);
			Swal.fire({
				title: "Error",
				text: "An error occurred while saving the sub segment readiness",
				icon: "error",
				timer: 3000,
				showConfirmButton: false,
			});
		} finally {
			setIsLoading(false);
		}
	};

	return (
		<Dialog open={open} onOpenChange={onClose}>
			<DialogContent className='sm:max-w-md bg-white'>
				<DialogHeader>
					<DialogTitle className='text-lg font-semibold text-gray-900'>
						{editingItem?.id
							? "Edit Sub Segment Readness"
							: "Create Sub Segment Readness"}
					</DialogTitle>
				</DialogHeader>

				<form onSubmit={handleSubmit} className='space-y-4'>
					<div className='grid gap-4'>
						<div className='space-y-2'>
							<Label
								htmlFor='segment_id'
								className='text-sm font-semibold'
							>
								Sub Segment Name{" "}
							</Label>
							<Select
								value={formData.segment_id}
								onValueChange={(value) =>
									handleInputChange("segment_id", value)
								}
								disabled={isLoading}
							>
								<SelectTrigger className='w-full text-sm'>
									<SelectValue placeholder='Select segment name' />
								</SelectTrigger>
								<SelectContent className='bg-white'>
									{segmentList.map((segment) => (
										<SelectItem
											key={segment.id}
											value={segment.id.toString()}
										>
											{segment.name}
										</SelectItem>
									))}
								</SelectContent>
							</Select>
						</div>

						<div className='space-y-2'>
							<Label
								htmlFor='name'
								className='text-sm font-semibold'
							>
								Name
							</Label>
							<Input
								id='name'
								type='text'
								className='w-full text-sm'
								value={formData.name}
								onChange={(e) =>
									handleInputChange("name", e.target.value)
								}
								placeholder='Enter name'
								disabled={isLoading}
								required
							/>
						</div>

						<div className='space-y-2'>
							<Label
								htmlFor='measuring_factor'
								className='text-sm font-semibold'
							>
								Measuring Factor{" "}
							</Label>
							<Input
								id='measuring_factor'
								type='text'
								className='w-full text-sm'
								value={formData.measuring_factor}
								onChange={(e) =>
									handleInputChange(
										"measuring_factor",
										e.target.value
									)
								}
								placeholder='Enter measuring factor'
								disabled={isLoading}
								required
							/>
						</div>

						<div className='space-y-2'>
							<Label
								htmlFor='metric'
								className='text-sm font-semibold'
							>
								Metric
							</Label>
							<Input
								id='metric'
								type='text'
								className='w-full text-sm'
								value={formData.metric}
								onChange={(e) =>
									handleInputChange("metric", e.target.value)
								}
								placeholder='Enter metric (optional)'
								disabled={isLoading}
							/>
						</div>
					</div>

					<div className='flex justify-end gap-3 pt-4'>
						<Button
							type='button'
							variant='outline'
							onClick={onClose}
							disabled={isLoading}
						>
							Cancel
						</Button>
						<Button
							type='submit'
							className='bg-orange-600 hover:bg-orange-700 text-white'
							disabled={isLoading}
						>
							{isLoading
								? "Saving..."
								: editingItem?.id
								? "Update Sub Segment Readness"
								: "Create Sub Segment Readness"}
						</Button>
					</div>
				</form>
			</DialogContent>
		</Dialog>
	);
};
