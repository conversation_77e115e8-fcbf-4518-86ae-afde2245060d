import { GET_ALL_USERS_CHATS_FAIL, GET_ALL_USERS_CHATS_INIT, GET_ALL_USERS_CHATS_SUCCESS } from "../constant/userChats";

export const getCurrentUserChatsReducer = (state = { currentUserChats: [] }, { type, payload }) => {
    switch (type) {

        case GET_ALL_USERS_CHATS_INIT: return { ...state, loading: true }

        case GET_ALL_USERS_CHATS_SUCCESS: return { ...state, loading: false, currentUserChats: payload }

        case GET_ALL_USERS_CHATS_FAIL: return { ...state, loading: false, error: payload }

        default: return state;
    }
}
