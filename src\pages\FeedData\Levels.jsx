import { useState, use<PERSON>emo, useEffect } from "react";
import { Plus, Search, Edit, Trash2, Trophy, Star } from "lucide-react";

import { <PERSON><PERSON> } from "../../components/ui/button";
import { Input } from "../../components/ui/input";
import {
	Card,
	CardContent,
	CardHeader,
	CardTitle,
} from "../../components/ui/card";
import { Badge } from "../../components/ui/badge";
import {
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableHeader,
	TableRow,
} from "../../components/ui/table";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "../../components/ui/select";
import { LevelsDialog } from "../../components/admin/levels-dialog";
import { DeleteConfirmDialog } from "../../components/admin/delete-confirm-dialog";
import Header from "../../components/Header";
import {
	getAllActivityData,
	getAlllevels,
	CreateLevels,
	updateLevel,
	deleteLevel,
} from "../../API/api-endpoint";
import Swal from "sweetalert2";

const getLevelColor = (level) => {
	switch ((level || "").toLowerCase()) {
		case "beginner":
			return "bg-gray-100 text-gray-800 border-gray-200";
		case "novice":
			return "bg-blue-100 text-blue-800 border-blue-200";
		case "intermediate":
			return "bg-yellow-100 text-yellow-800 border-yellow-200";
		case "advanced":
			return "bg-orange-100 text-orange-800 border-orange-200";
		case "elite":
			return "bg-red-100 text-red-800 border-red-200";
		default:
			return "bg-gray-100 text-gray-800 border-gray-200";
	}
};

const getActivityColor = (activity) => {
	switch ((activity || "").toLowerCase()) {
		case "running":
			return "bg-green-100 text-green-800 border-green-200";
		case "cycling":
			return "bg-blue-100 text-blue-800 border-blue-200";
		case "swimming":
			return "bg-cyan-100 text-cyan-800 border-cyan-200";
		case "triathlon":
			return "bg-purple-100 text-purple-800 border-purple-200";
		case "fitness":
			return "bg-orange-100 text-orange-800 border-orange-200";
		default:
			return "bg-gray-100 text-gray-800 border-gray-200";
	}
};

const getLevelIcon = (level) => {
	switch (level.toLowerCase()) {
		case "elite":
			return <Trophy className='h-4 w-4' />;
		case "advanced":
			return <Star className='h-4 w-4' />;
		default:
			return null;
	}
};

export default function LevelsPage() {
	const [searchTerm, setSearchTerm] = useState("");
	const [activityFilter, setActivityFilter] = useState("all");
	const [currentPage, setCurrentPage] = useState(1);
	const [isDialogOpen, setIsDialogOpen] = useState(false);
	const [editingLevel, setEditingLevel] = useState(null);
	const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
	const [levelToDelete, setLevelToDelete] = useState(null);
	const [levelsData, setLevelsData] = useState([]);
	const [activities, setActivities] = useState([]);

	const itemsPerPage = 10;

	useEffect(() => {
		fetchMeta();
	}, []);

	const fetchMeta = async () => {
		const acts = await getAllActivityData();
		setActivities(acts?.rows || []);
		const lvls = await getAlllevels();
		setLevelsData(lvls?.rows || []);
	};

	const filteredLevels = useMemo(() => {
		return levelsData.filter((level) => {
			const matchesSearch =
				(level.activity_name || "")
					.toLowerCase()
					.includes(searchTerm.toLowerCase()) ||
				(level.level || "")
					.toLowerCase()
					.includes(searchTerm.toLowerCase()) ||
				(level.description || "")
					.toLowerCase()
					.includes(searchTerm.toLowerCase());
			const matchesActivity =
				activityFilter === "all" ||
				(level.activity_name || "").toLowerCase() === activityFilter;
			return matchesSearch && matchesActivity;
		});
	}, [levelsData, searchTerm, activityFilter]);

	const paginatedLevels = useMemo(() => {
		const startIndex = (currentPage - 1) * itemsPerPage;
		return filteredLevels.slice(startIndex, startIndex + itemsPerPage);
	}, [filteredLevels, currentPage]);

	const totalPages = Math.ceil(filteredLevels.length / itemsPerPage);

	const handleEdit = (level) => {
		setEditingLevel(level);
		setIsDialogOpen(true);
	};

	const handleDelete = (level) => {
		setLevelToDelete(level);
		setDeleteDialogOpen(true);
	};

	const confirmDelete = async () => {
		if (levelToDelete) {
			await deleteLevel(levelToDelete.id);
			setDeleteDialogOpen(false);
			setLevelToDelete(null);
			fetchMeta();
		}
	};

	const handleSave = async (levelData) => {
		let response;
		if (editingLevel) {
			response = await updateLevel({ id: editingLevel.id, ...levelData });
		} else {
			response = await CreateLevels(levelData);
		}
		if (response?.status) {
			Swal.fire({
				title: "Success",
				text: response.message,
				icon: "success",
			});
			setIsDialogOpen(false);
			setEditingLevel(null);
			fetchMeta();
		} else {
			Swal.fire({
				title: "Error",
				text: response.message,
				icon: "error",
			});
		}
	};

	return (
		<div>
			<Header />
			<div className='mx-auto px-4 py-8 max-w-[1300px] mt-16'>
				<div className='mb-8'>
					<div className='flex items-center gap-3 mb-2'>
						<Trophy className='h-8 w-8 text-orange-500' />
						<h1 className='text-3xl font-bold text-gray-900'>
							Levels Management
						</h1>
					</div>
					<p className='text-gray-600 text-base'>
						Manage skill levels for different activities: Beginner,
						Novice, Intermediate, Advanced, and Elite
					</p>
				</div>

				{/* Card */}
				<Card>
					<CardHeader className='bg-orange-50 border-b border-orange-100'>
						<div className='flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4'>
							<CardTitle className='text-xl font-semibold text-orange-900'>
								Activity Levels
							</CardTitle>
							<Button
								onClick={() => {
									setEditingLevel(null);
									setIsDialogOpen(true);
								}}
								className='bg-orange-500 hover:bg-orange-600 text-white'
							>
								<Plus className='h-4 w-4 mr-2' />
								Create Level
							</Button>
						</div>
					</CardHeader>

					<CardContent className='p-6 my-6'>
						{/* Filters */}
						<div className='flex flex-col sm:flex-row gap-4 mb-6'>
							<div className='flex-1 relative'>
								<Search className='absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4' />
								<Input
									placeholder='Search by activity, level, or description...'
									value={searchTerm}
									onChange={(e) =>
										setSearchTerm(e.target.value)
									}
									className='pl-10 text-sm'
								/>
							</div>
							<div className='w-full sm:w-48'>
								<Select
									value={activityFilter}
									onValueChange={setActivityFilter}
								>
									<SelectTrigger>
										<SelectValue placeholder='Filter by Activity' />
									</SelectTrigger>
									<SelectContent className='bg-white'>
										<SelectItem key='all' value='all'>
											All
										</SelectItem>
										{activities.map((activity) => (
											<SelectItem
												key={activity.id}
												value={activity.activity_name}
											>
												{activity.activity_name}
											</SelectItem>
										))}
									</SelectContent>
								</Select>
							</div>
						</div>

						{/* Table */}
						<div className='border rounded-lg overflow-hidden'>
							<Table>
								<TableHeader className='bg-orange-500'>
									<TableRow>
										<TableHead className='text-white font-semibold'>
											Sr ID
										</TableHead>
										<TableHead className='text-white font-semibold'>
											Activity
										</TableHead>
										<TableHead className='text-white font-semibold'>
											Level
										</TableHead>
										<TableHead className='text-white font-semibold'>
											Order
										</TableHead>
										<TableHead className='text-white font-semibold'>
											Description
										</TableHead>
										<TableHead className='text-white font-semibold'>
											Actions
										</TableHead>
									</TableRow>
								</TableHeader>
								<TableBody>
									{paginatedLevels.length > 0 ? (
										paginatedLevels.map((level, index) => {
											// Map activity_id to activity name
											const activityObj = activities.find(
												(a) =>
													a.id === level.activity_id
											);
											const activityName =
												activityObj?.activity ||
												activityObj?.activity_name ||
												"—";
											return (
												<TableRow
													key={level.id}
													className='hover:bg-gray-50'
												>
													<TableCell>
														{(currentPage - 1) *
															itemsPerPage +
															index +
															1}
													</TableCell>
													<TableCell>
														<Badge
															variant='outline'
															className={getActivityColor(
																activityName
															)}
														>
															{activityName}
														</Badge>
													</TableCell>
													<TableCell>
														<div className='flex items-center gap-2'>
															<Badge
																variant='outline'
																className={getLevelColor(
																	level.level
																)}
															>
																<div className='flex items-center gap-1'>
																	{getLevelIcon(
																		level.level
																	)}
																	{
																		level.level
																	}
																</div>
															</Badge>
														</div>
													</TableCell>
													<TableCell>
														<Badge
															variant='outline'
															className='bg-gray-50 text-gray-700'
														>
															{level.order ?? "—"}
														</Badge>
													</TableCell>
													<TableCell className='max-w-xs'>
														<p
															className='text-sm text-gray-600 truncate'
															title={
																level.description
															}
														>
															{level.description ||
																"—"}
														</p>
													</TableCell>
													<TableCell>
														<div className='flex items-center gap-2'>
															<Button
																variant='ghost'
																size='sm'
																onClick={() =>
																	handleEdit(
																		level
																	)
																}
																className='text-blue-600 hover:text-blue-800 hover:bg-blue-50'
															>
																<Edit className='h-4 w-4' />
															</Button>
															<Button
																variant='ghost'
																size='sm'
																onClick={() =>
																	handleDelete(
																		level
																	)
																}
																className='text-red-600 hover:text-red-800 hover:bg-red-50'
															>
																<Trash2 className='h-4 w-4' />
															</Button>
														</div>
													</TableCell>
												</TableRow>
											);
										})
									) : (
										<TableRow>
											<TableCell
												colSpan={6}
												className='text-center py-8 text-gray-500'
											>
												No levels found matching your
												criteria
											</TableCell>
										</TableRow>
									)}
								</TableBody>
							</Table>
						</div>

						{/* Pagination */}
						{totalPages > 1 && (
							<div className='flex justify-center mt-6'>
								<div className='flex items-center gap-2'>
									<Button
										variant='outline'
										size='sm'
										onClick={() =>
											setCurrentPage(
												Math.max(1, currentPage - 1)
											)
										}
										disabled={currentPage === 1}
									>
										Previous
									</Button>
									{Array.from(
										{ length: totalPages },
										(_, i) => i + 1
									).map((page) => (
										<Button
											key={page}
											variant={
												currentPage === page
													? "default"
													: "outline"
											}
											size='sm'
											onClick={() => setCurrentPage(page)}
											className={
												currentPage === page
													? "bg-orange-500 hover:bg-orange-600 text-white"
													: ""
											}
										>
											{page}
										</Button>
									))}
									<Button
										variant='outline'
										size='sm'
										onClick={() =>
											setCurrentPage(
												Math.min(
													totalPages,
													currentPage + 1
												)
											)
										}
										disabled={currentPage === totalPages}
									>
										Next
									</Button>
								</div>
							</div>
						)}
					</CardContent>
				</Card>

				<LevelsDialog
					open={isDialogOpen}
					onOpenChange={setIsDialogOpen}
					level={editingLevel}
					onSave={handleSave}
					activities={activities}
				/>

				<DeleteConfirmDialog
					open={deleteDialogOpen}
					onOpenChange={setDeleteDialogOpen}
					title='Delete Level'
					description={`Are you sure you want to delete the ${levelToDelete?.level} level for ${levelToDelete?.activity_name}? This action cannot be undone.`}
					onConfirm={confirmDelete}
				/>
			</div>
		</div>
	);
}
