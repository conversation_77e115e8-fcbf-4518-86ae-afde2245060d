import { useState, useEffect } from "react";
import { But<PERSON> } from "../ui/button";
import { Input } from "../ui/input";
import { Label } from "../ui/label";
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from "../ui/dialog";
import {
	createAssesmentdata,
	updateAssesmentdata,
} from "../../API/api-endpoint";
import Swal from "sweetalert2";

export const AssessmentDialog = ({ open, onClose, onSuccess, editingItem }) => {
	const [formData, setFormData] = useState({
		name: "",
	});
	const [isLoading, setIsLoading] = useState(false);

	// Reset form when dialog opens/closes or when editing different item
	useEffect(() => {
		if (open) {
			if (editingItem?.id) {
				// Editing existing assessment
				const editData = {
					name: editingItem.name || "",
				};
				setFormData(editData);
			} else {
				// Creating new assessment
				const newData = {
					name: "",
				};
				setFormData(newData);
			}
		}
	}, [open, editingItem]);

	const handleInputChange = (field, value) => {
		setFormData((prev) => ({
			...prev,
			[field]: value,
		}));
	};

	const handleSubmit = async (e) => {
		e.preventDefault();

		// Validation
		if (!formData.name.trim()) {
			Swal.fire({
				title: "Error",
				text: "Assessment name is required",
				icon: "error",
				timer: 1800,
				showConfirmButton: false,
			});
			return;
		}

		try {
			setIsLoading(true);

			// Prepare data for API
			const apiData = {
				name: formData.name.trim(),
			};

			let response;
			if (editingItem?.id) {
				// Add ID for update
				apiData.id = editingItem.id;
				response = await updateAssesmentdata(apiData);
			} else {
				response = await createAssesmentdata(apiData);
			}

			console.log("API response:", response);

			if (response?.status) {
				Swal.fire({
					title: "Success",
					text:
						response.message ||
						`Assessment ${
							editingItem?.id ? "updated" : "created"
						} successfully`,
					icon: "success",
					timer: 2000,
					showConfirmButton: false,
				});
				onSuccess();
			} else {
				Swal.fire({
					title: "Error",
					text: response?.message || "Failed to save assessment",
					icon: "error",
					timer: 3000,
					showConfirmButton: false,
				});
			}
		} catch (error) {
			console.error("Error saving assessment:", error);
			Swal.fire({
				title: "Error",
				text: "An error occurred while saving the assessment",
				icon: "error",
				timer: 3000,
				showConfirmButton: false,
			});
		} finally {
			setIsLoading(false);
		}
	};

	return (
		<Dialog open={open} onOpenChange={onClose}>
			<DialogContent className='sm:max-w-md bg-white'>
				<DialogHeader>
					<DialogTitle className='text-lg font-semibold text-gray-900'>
						{editingItem?.id
							? "Edit Assessment"
							: "Create Assessment"}
					</DialogTitle>
				</DialogHeader>

				<form onSubmit={handleSubmit} className='space-y-4'>
					<div className='grid gap-4'>
						<div className='space-y-2'>
							<Label
								htmlFor='name'
								className='text-sm font-semibold'
							>
								Assessment Name
							</Label>
							<Input
								id='name'
								type='text'
								className='w-full text-sm'
								value={formData.name}
								onChange={(e) =>
									handleInputChange("name", e.target.value)
								}
								placeholder='Enter assessment name'
								disabled={isLoading}
								required
							/>
						</div>
					</div>

					<div className='flex justify-end gap-3 pt-4'>
						<Button
							type='button'
							variant='outline'
							onClick={onClose}
							disabled={isLoading}
						>
							Cancel
						</Button>
						<Button
							type='submit'
							className='bg-orange-600 hover:bg-orange-700 text-white'
							disabled={isLoading}
						>
							{isLoading
								? "Saving..."
								: editingItem?.id
								? "Update Assessment"
								: "Create Assessment"}
						</Button>
					</div>
				</form>
			</DialogContent>
		</Dialog>
	);
};
