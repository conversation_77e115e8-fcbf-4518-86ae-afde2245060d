import React from 'react'
import { Select } from '@mantine/core'
import Header from '../components/Header'

const AutomationSummaryPage = () => {
  return (
    <>
      <Header />
      {/* Topbar */}
      <div className='p-6'>
        <div className='flex items-start gap-x-2'>
          <h1 className='font-medium text-xl'>Automation Steps</h1>
        </div>
        <br />
        <div className='flex flex-wrap gap-4 items-end'>
          <Select
            label='Select an organization'
            data={[
              { value: 'YRA', label: 'YRA' },
              { value: 'YCA', label: 'YCA' },
              { value: 'YTA', label: 'YTA' },
              { value: 'YFA', label: 'YFA' }
            ]}
          />
          <Select
            label='Duration'
            data={[
              { value: '1 Week', label: '1 Week' },
              { value: '4 Weeks', label: '4 Weeks' },
              { value: '8 Weeks', label: '8 Weeks' },
              { value: '12 Weeks', label: '12 Weeks' }
            ]}
          />
          <button className='py-2 px-5 bg-blue-600 text-slate-50 rounded text-sm'>
            Automation Steps
          </button>
          <button className='py-2 px-5 bg-blue-600 text-slate-50 rounded text-sm'>
            Automation Summary
          </button>
        </div>
        <br />
        <div className='flex flex-wrap gap-4 items-end'>
          <button className='py-2 px-5 bg-green-600 text-slate-50 rounded text-sm'>
            Run Automation
          </button>
          <button className='py-2 px-5 bg-green-600 text-slate-50 rounded text-sm'>
            Perform Roll Back
          </button>
        </div>
        {/* Table */}
        <div className='w-full mb-6'>
          <div className='my-6'>
            <div className='flex flex-col'>
              <div className='overflow-x-auto shadow-md'>
                <div className='inline-block min-w-full align-middle'>
                  <div className='overflow-hidden '>
                    <table className='min-w-full divide-y divide-gray-200 table-fixed dark:divide-gray-700'>
                      <thead className='bg-gray-100 '>
                        <tr>
                          <th
                            scope='col'
                            className='py-3 px-6 text-xs font-medium tracking-wider text-left text-gray-700 uppercase dark:text-gray-400'
                          >
                            Name
                          </th>
                          <th
                            scope='col'
                            className='py-3 px-6 text-xs font-medium tracking-wider text-left text-gray-700 uppercase dark:text-gray-400'
                          >
                            Last Job Run On
                          </th>
                          <th
                            scope='col'
                            className='py-3 px-6 text-xs font-medium tracking-wider text-left text-gray-700 uppercase dark:text-gray-400'
                          >
                            Success
                          </th>
                        </tr>
                      </thead>
                      <tbody className='bg-white divide-y divide-gray-200 '></tbody>
                    </table>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  )
}

export default AutomationSummaryPage
