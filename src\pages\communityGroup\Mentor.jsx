import React, { useEffect, useMemo, useState } from "react";
import Header from "../../components/Header";
import { Table, Button } from "antd";
import { Grid, TextField } from "@mui/material";
import Swal from 'sweetalert2';
import { listGroupMentors, deleteMentor } from "../../API/api-endpoint";
import MentorRegister from "./MentorRegister";

import "./Mentor.css";

const Mentor = () => {
  const [groupMentors, setGroupMentors] = useState([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [pagination, setPagination] = useState({ current: 1, pageSize: 10 });

  const fetchMentors = () => {
    listGroupMentors()
      .then(response => {
        if (response.data.status) {
          setGroupMentors(response.data.data);
        } else {
          console.error("Failed to fetch group mentors:", response.data);
        }
      })
      .catch(error => {
        console.error("Error fetching group mentors:", error);
      });
  };

  // initial load
  useEffect(fetchMentors, []);

  // Handle search input change
  const handleSearchChange = event => {
    setSearchTerm(event.target.value);
  };

  // Filter mentors based on search term
  const filteredMentors = useMemo(() => {
    const term = searchTerm.toLowerCase();
    return groupMentors.filter(mentor => {
      const communityName = mentor.athletecommunitygroup?.communityName?.toLowerCase() || "";
      const fullName = `${mentor.user?.firstname || ""} ${mentor.user?.lastname || ""}`.toLowerCase();
      const email = mentor.user?.email?.toLowerCase() || "";
      return (
        communityName.includes(term) ||
        fullName.includes(term) ||
        email.includes(term)
      );
    });
  }, [groupMentors, searchTerm]);

  // Handle pagination changes
  const handleTableChange = paginationInfo => {
    setPagination({
      current: paginationInfo.current,
      pageSize: paginationInfo.pageSize,
    });
  };

  // Close the mentor registration modal
  const handleCancel = () => {
    setIsModalOpen(false);
  };

  // Handle "Make Athlete" action
  const handleMakeAthlete = record => {
    Swal.fire({
      title: 'Are you sure?',
      text: "This will convert the mentor to an athlete.",
      icon: 'warning',
      showCancelButton: true,
      confirmButtonText: 'Yes, make athlete!',
      cancelButtonText: 'Cancel'
    }).then(result => {
      if (result.isConfirmed) {
        deleteMentor(parseInt(record?.athletecommunitygroup?.id))
          .then(() => {
            Swal.fire('Converted!', 'Mentor has been made an athlete.', 'success');
            fetchMentors();
          })
          .catch(error => {
            console.error('Error making athlete:', error);
            Swal.fire('Error', 'There was an error processing your request.', 'error');
          });
      }
    });
  };

  // Table column definitions with Action column
  const groupMentorColumns = [
    {
      title: "No.",
      key: "serial",
      render: (text, record, index) =>
        (pagination.current - 1) * pagination.pageSize + index + 1,
    },
    {
      title: "Community Name",
      dataIndex: ["athletecommunitygroup", "communityName"],
      key: "communityName",
    },
    {
      title: "Mentor",
      key: "username",
      render: (_, record) => `${record.user.firstname} ${record.user.lastname}`,
    },
    {
      title: "Email",
      dataIndex: ["user", "email"],
      key: "email",
    },
    {
      title: "Action",
      key: "action",

      render: (_, record) => (
        <Button danger onClick={() => handleMakeAthlete(record)}>
          Make Athlete
        </Button>
      ),
    },
  ];

  return (
    <>
      <Header />
      <div style={{ marginTop: "100px", padding: "20px" }}>
        <Grid container spacing={2} alignItems="center" justifyContent="space-between" sx={{ mb: 2 }}>
          <Grid item xs={12} sm={8} sx={{ marginTop: "30px" }}>
            <Button type="primary" onClick={() => setIsModalOpen(true)}>
              Create Mentor
            </Button>
          </Grid>
          <Grid item xs={12} sm={4}>
            <TextField
              type="text"
              size="small"
              value={searchTerm}
              onChange={handleSearchChange}
              placeholder="Search by community or user..."
              fullWidth
            />
          </Grid>
        </Grid>

        <Table
          columns={groupMentorColumns}
          dataSource={filteredMentors}
          pagination={{
            current: pagination.current,
            pageSize: pagination.pageSize,
            total: filteredMentors.length,
          }}
          rowKey="id"
          onChange={handleTableChange}
        />

        <MentorRegister
          isModalOpen={isModalOpen}
          handleCancel={handleCancel}
          onRefresh={fetchMentors}
        />
      </div>
    </>
  );
};

export default Mentor;
