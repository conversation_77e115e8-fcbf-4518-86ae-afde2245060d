import React, { useEffect, useState, useRef } from "react";
import { <PERSON>, Button } from "antd";
import Header from "../../components/Header";
import { fetchCommunityGroups, fetchCommunityGroupChallengesTarget,listChallengeLevel, getAllActivities, getAllTrainingBlock, fetchCommunityGroupChallenges } from "../../API/api-endpoint";
import {Grid,TextField} from "@mui/material";
import Swal from "sweetalert2";
import "react-datepicker/dist/react-datepicker.css";
import AssignChallenge from "../Challenges/AssignChallenge";
import CreateChallengeTrack from ".././Challenges/CreateChallengeTrack";
import AssignedChallenge from ".././Challenges/AssignedChallenge";
import ChallengeTrack from ".././Challenges/ChallengeTrack";
import { showInfo } from "../../components/Messages";
import AssignChallengeGroup from ".././Challenges/AssignChallengeGroup";
import axios from "axios";
import { URL } from "../../API/api-endpoint";
import CommunityChallenge from "./components/createCommunityChallenge";
import { EditorState, convertToRaw, ContentState } from 'draft-js';
import htmlToDraft from "html-to-draftjs";


const initialChallengeData = {
  challengeName: "",
  challengeDescription: "",
  challengeActivity: "",
  challengeDuration: "",
  challengePoints: "",
  durationunit: "",
  challengeStartDate: "",
  challengeEndDate: "",
  challengeLevel: "",
  challengeFor: "",
  targetDistance: "",
  targetDuration: "",
  isActive: true,
  challengeTarget: [],
  athleteCommunityGroupId: "",
};

const Challenges = () => {
  const [challengeData, setChallengeData] = useState(initialChallengeData);
  const [allChanges, setAllChallenges] = useState([]);
  const [allChallengesData, setAllChallengesData] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isAssignUserModalOpen, setIsAssignUserModalOpen] = useState(false);
  const [isAssignedGroupModal, setIsAssignedGroupModal] = useState(false);
  const [showCreateTrackModal, setShowCreateTrackModal] = useState(false);
  const [assignedChallengeModal, setShowAssignedChallengeModal] = useState(false);
  const [showTrackChallenges, setShowTrackChellenges] = useState(false);
  const [initialContentSet, setInitialContentSet] = useState(false);
  const [modalName, setModalName] = useState();
  const [fetchAllActivities, setFetchActivities] = useState([]);
  const [fetchChallengeTarget, setFetchChallengeTarget] = useState([]);
  const [allTrainingBlocks, setAllTrainingBlock] = useState(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [challengeForOptions, setChallengeForOptions] = useState([]);
  const [communityGroups, setCommunityGroups] = useState([]);
  const [editorState, setEditorState] = useState(
    challengeData.challengeDescription
      ? EditorState.createWithContent(ContentState.createFromBlockArray(
          htmlToDraft(challengeData.challengeDescription).contentBlocks
        ))
      : EditorState.createEmpty()
  );

  const roleID = localStorage.getItem("roleID");

  // Helper to validate required fields
  const isFormValid = (data) => {
    const requiredFields = [
      "challengeName",
      "challengeDescription",
      "challengeActivity",
      "challengeDuration",
      "durationunit",
      "challengeStartDate",
      "challengeEndDate",
      "challengeLevel",
      "challengePoints",
      "athleteCommunityGroupId"
    ];
    return requiredFields.every(
      (field) => data[field] !== undefined && data[field] !== ""
    );
  };

  const showModal = () => {
    setChallengeData(initialChallengeData);
    setShowTrackChellenges(false);
    setShowAssignedChallengeModal(false);
    setIsModalOpen(true);
  };

  const handleCancel = () => {
    setIsModalOpen(false);
    setEditorState(EditorState.createEmpty());
  };

  const showAssignUserModal = (name) => {
    setIsAssignUserModalOpen(true);
    setShowAssignedChallengeModal(false);
    setShowTrackChellenges(false);
    setModalName(name);
  };

  const showAssignGroupModal = (name) => {
    setIsAssignUserModalOpen(false);
    setShowAssignedChallengeModal(false);
    setShowTrackChellenges(false);
    setIsAssignedGroupModal(true);
    setModalName(name);
  };

  // Fetch challenges based on the community groups available
  const fetchChallenges = async () => {
    try {
      if (communityGroups.length > 0) {
        const challengesPromises = communityGroups.map((group) =>
          fetchCommunityGroupChallenges(group.id)
        );
        const responses = await Promise.all(challengesPromises);
        const allChallenges = responses.reduce((acc, response) => {
          if (response.status === 200 && response.data.data) {
            const flattened = response.data.data.map((item) => ({
              ...item.athletecommunitygroupchallenge
            }));
            return acc.concat(flattened);
          }
          return acc;
        }, []);
        setAllChallenges(allChallenges);
      }
    } catch (error) {
      console.error("Error fetching challenges for community groups:", error);
    }
  };

  const fetchAllChallengesData = async () => {
    const response = await listChallengeLevel();
    if (response.status === 200) {
      const levels = response.data.data.map((item) => ({
        id: item.id,
        level: item.name,
      }));
      setAllChallengesData({ levels });
    } else {
      Swal.fire({
        icon: "error",
        title: "Error",
        text: "Failed to fetch challenge levels",
      });
    }
  };

  const fetchChallengeForOptions = async () => {
    try {
      const response =  await fetchCommunityGroupChallengesTarget();
      if (response.status === 200) {
        setChallengeForOptions(response?.data?.data);
      }
    } catch (error) {
      console.error("Error fetching challenge targets:", error);
    }
  };

  const fetchTrainingBlock = async () => {
    const response = await getAllTrainingBlock();
    setAllTrainingBlock(response);
  };

  const getAllActivitiesDetails = async () => {
    let result = await getAllActivities();
    setFetchActivities(result);
  };

  const handleFetchCommunityGroups = async () => {
    try {
      let response = await fetchCommunityGroups();
      response = response?.data?.data
      if (response.length > 0) {
        setCommunityGroups(response);
      }
    } catch (error) {
      console.error("Error fetching community groups:", error);
    }
  };

  useEffect(() => {
    if (communityGroups.length > 0) {
      fetchChallenges();
    }
  }, [communityGroups]);

  useEffect(() => {
    getAllActivitiesDetails();
    fetchTrainingBlock();
    fetchAllChallengesData();
    handleFetchCommunityGroups();
    fetchChallengeForOptions();
  }, []);

  useEffect(() => {
    const updateFetchChallengeTarget = (activityId) => {
      let updatedChallengeTarget = [{ activity_id: activityId, quota: "" }];
      if (activityId === 6) {
        const running = fetchAllActivities?.find((a) => a.id === 1);
        const cycling = fetchAllActivities?.find((a) => a.id === 2);
        const swimming = fetchAllActivities?.find((a) => a.id === 3);
        setFetchChallengeTarget([running, cycling, swimming].filter(Boolean));
        const triathlonActivities = [1, 2, 3];
        updatedChallengeTarget = [
          ...triathlonActivities
            .filter((id) => !updatedChallengeTarget.some((item) => item.activity_id === id))
            .map((id) => ({ activity_id: id, quota: "" })),
        ];
      } else if (activityId === 5) {
        const running = fetchAllActivities?.find((a) => a.id === 1);
        const cycling = fetchAllActivities?.find((a) => a.id === 2);
        setFetchChallengeTarget([running, cycling].filter(Boolean));
        const duoActivities = [1, 2];
        updatedChallengeTarget = [
          ...duoActivities
            .filter((id) => !updatedChallengeTarget.some((item) => item.activity_id === id))
            .map((id) => ({ activity_id: id, quota: "" })),
        ];
      } else {
        const found = fetchAllActivities?.find((a) => a.id === activityId);
        setFetchChallengeTarget(found ? [found] : []);
      }
      setChallengeData((prev) => ({
        ...prev,
        challengeTarget: updatedChallengeTarget,
      }));
    };

    if (challengeData.challengeActivity) {
      updateFetchChallengeTarget(challengeData.challengeActivity);
    }
  }, [challengeData.challengeActivity]);

  useEffect(() => {
    if (
      challengeData.challengeStartDate &&
      challengeData.challengeDuration &&
      challengeData.durationunit
    ) {
      const startDate = new Date(challengeData.challengeStartDate);
      const duration = parseInt(challengeData.challengeDuration);
      const unit = challengeData.durationunit;
      if (unit === "days") {
        startDate.setDate(startDate.getDate() + duration);
      } else if (unit === "weeks") {
        startDate.setDate(startDate.getDate() + duration * 7);
      } else if (unit === "months") {
        startDate.setMonth(startDate.getMonth() + duration);
      } else if (unit === "years") {
        startDate.setFullYear(startDate.getFullYear() + duration);
      }
      const endDate = startDate.toISOString().split("T")[0];
      setChallengeData((prev) => ({
        ...prev,
        challengeEndDate: endDate,
      }));
    }
  }, [challengeData.durationunit, challengeData.challengeDuration, challengeData.challengeStartDate]);

  const handleFileUpload = (event) => {
    const file = event.target.files[0];
    const acceptedFileTypes = [".jpg", ".JPG", ".jpeg", ".JPEG", ".png", ".PNG", ".gif", ".GIF", ".gfif"];
    const currentFileType = "." + file.type.split("/")[1];
    const maxSizeInBytes = 5 * 1024 * 1024;
    if (file.size > maxSizeInBytes) {
      showInfo("File size exceeds the limit of 5MB.");
      event.target.value = null;
    } else if (!acceptedFileTypes.includes(currentFileType)) {
      showInfo("Accepted file types are .jpg, .jpeg, .png, .gif, .gfif");
      event.target.value = null;
    } else {
      setChallengeData((prev) => ({
        ...prev,
        badge: file,
      }));
    }
  };

  const handleSearchChange = (event) => {
    setSearchTerm(event.target.value);
  };

  const handleEditChallenge = (challenge) => {
    setChallengeData({
      ...initialChallengeData,
      ...challenge,
      originalChallengeStartDate: challenge.challengeStartDate,
      durationunit: challenge.challengeDurationUnit || "",
      challengePoints: challenge.challengePoints || "",
      challengeTarget: challenge.challengeTarget || [],
      challengeDescription: challenge.challengeDescription || "Not Found",
      challengeActivity: (challenge.activity && challenge.activity.id) || "",
      challengeLevel: (challenge.level && challenge.level.id) || "",
      challengeFor: (challenge.target && challenge.target.id) || "",
    });
    const html = challenge?.challengeDescription || "";
    const blocks = htmlToDraft(html);
    const contentState = ContentState.createFromBlockArray(blocks.contentBlocks);
    setEditorState(EditorState.createWithContent(contentState));
    setIsModalOpen(true);
  };

  if (roleID === "6") {
    let groupID = 0;
    const groupDetailString = localStorage.getItem("groupDetail");

    if (groupDetailString) {
      const groupDetail = JSON.parse(groupDetailString);
      groupID = groupDetail.id;
    }
    challengeData.athleteCommunityGroupId = groupID;
  }

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!isFormValid(challengeData)) {
      Swal.fire({
        title: "Error",
        text: "Please fill all required fields",
        icon: "error",
      });
      return;
    }
    const desc = editorState.getCurrentContent().getPlainText().trim();
    const payload = {
      challengeName: challengeData.challengeName,
      challengeDescription: desc,
      challengeActivity: challengeData.challengeActivity,
      challengeDuration: challengeData.challengeDuration,
      challengeDurationUnit: challengeData.durationunit,
      challengeStartDate: challengeData.challengeStartDate,
      challengeEndDate: challengeData.challengeEndDate,
      challengeLevel: challengeData.challengeLevel,
      challengeFor: challengeData.challengeFor === "" ? null : parseInt(challengeData.challengeFor, 10),
      ...(challengeData.id
        ? { challengePoints: challengeData.challengePoints }
        : { challengePoint: challengeData.challengePoints }),
      athleteCommunityGroupId: challengeData.athleteCommunityGroupId,
    };
    console.log("payload", payload);
    
    try {
      setIsLoading(true);
      const token = localStorage.getItem("token");
      let response;

      if (challengeData.id) {
        response = await axios.put(
          `${URL}/athlete-community/group-challenge/${challengeData.id}`,
          payload,
          {
            headers: {
              "Content-Type": "application/json",
              Authorization: token,
            },
          }
        );
      } else {
        response = await axios.post(
          `${URL}/athlete-community/group-challenge`,
          payload,
          {
            headers: {
              "Content-Type": "application/json",
              Authorization: token,
            },
          }
        );
      }

      if (response.status === 201 || response.status === 200) {
        Swal.fire({
          title: "Success",
          text: response.data.message,
          icon: "success",
        });
        handleCancel();
        fetchChallenges();
        setChallengeData(initialChallengeData);
      } else {
        Swal.fire({
          title: "Error",
          text: response.data.message,
          icon: "error",
        });
      }
    } catch (error) {
      Swal.fire({
        title: "Error",
        text: error.response?.data?.message || "An error occurred",
        icon: "error",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleDeleteChallenge = (challengeId) => {
    Swal.fire({
      title: "Are you sure?",
      text: "Do you really want to delete this challenge?",
      icon: "warning",
      showCancelButton: true,
      confirmButtonText: "Yes, delete it!",
      cancelButtonText: "Cancel",
    }).then(async (result) => {
      if (result.isConfirmed) {
        const token = localStorage.getItem("token");
        try {
          const response = await axios.delete(
            `${URL}/athlete-community/group-challenge/${challengeId}`,
            {
              headers: { Authorization: token },
            }
          );
          if (response.status === 200) {
            Swal.fire({
              title: "Deleted!",
              text: response.data.message || "Challenge deleted successfully.",
              icon: "success",
              confirmButtonText: "OK",
            }).then(() => {
              // Refresh the challenges list instead of reloading the page
              fetchChallenges();
            });
          }
        } catch (error) {
          Swal.fire({
            title: "Error",
            text:
              error.response?.data?.message ||
              "An error occurred while deleting the challenge.",
            icon: "error",
          });
        }
      }
    });
  };

  const columns = [
    {
      title: "Challenge Name",
      dataIndex: "challengeName",
      key: "challengeName",
    },
    {
      title: "Duration",
      dataIndex: "challengeDuration",
      key: "challengeDuration",
      render: (challengeDuration, record) => (
        <span>{`${challengeDuration} ${record.challengeDurationUnit}`}</span>
      ),
    },
    {
      title: "Activity",
      dataIndex: "activity",
      key: "activity",
      render: (activity) => activity.activity_name,
    },
    {
      title: "Challenge Target",
      dataIndex: "target",
      key: "target",
      render: (target) => <span>{target ? target.name : "NA"}</span>,
    },
    {
      title: "Challenges Point",
      dataIndex: "challengePoints",
      key: "challengePoints",
    },
    {
      title: "Actions",
      dataIndex: "actions",
      key: "actions",
      render: (_, record) => {
        const recordStart = record.challengeStartDate
          ? new Date(record.challengeStartDate).setHours(0, 0, 0, 0)
          : null;
        const today = new Date().setHours(0, 0, 0, 0);
        const recordIsPast = recordStart && recordStart < today;
        return (
          <span>
            <Button
              style={{ color: "#E67E22" }}
              type="link"
              onClick={() => handleEditChallenge(record)}
            >
              Edit
            </Button>
            <Button
              disabled={recordIsPast}
              type="link"
              danger
              onClick={() => handleDeleteChallenge(record.id)}
            >
              Delete
            </Button>
          </span>
        );
      },
    },
  ];

  const options = allTrainingBlocks?.map((block) => ({
    label: block["training-block-name"],
    value: block["training-block-id"],
  })) || [];

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setChallengeData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const isPastChallenge =
    challengeData.id &&
    challengeData.challengeStartDate &&
    new Date(challengeData.challengeStartDate).setHours(0, 0, 0, 0) <
      new Date().setHours(0, 0, 0, 0);

  const isSavedStartDatePast =
    challengeData.id &&
    challengeData.originalChallengeStartDate &&
    new Date(challengeData.originalChallengeStartDate).setHours(0, 0, 0, 0) <
      new Date().setHours(0, 0, 0, 0);

// const editorContent = document.querySelector(".public-DraftEditor-content");
const placeholder = document.querySelector(".public-DraftEditorPlaceholder-inner");

// if (editorContent) {
//   if (challengeData.challengeDescription && challengeData.challengeDescription.trim() !== "") {
//     // If there is a description, display it and hide the placeholder
//     editorContent.innerHTML = challengeData.challengeDescription;
//     if (placeholder) {
//       placeholder.style.display = "none";
//     }
//   } else {
//     // If there is no description, clear the content and show the placeholder
//     editorContent.innerHTML = "";
//     if (placeholder) {
//       placeholder.style.display = "block";
//     }
//   }
// }


  return (
    <div>
      <Header />
      <div style={{ marginTop: "100px", padding: "20px" }}>
        <div className="add-challenges-modal">
          <Grid container spacing={2}>
            <Grid item xs={12} sm={10} sx={{ marginTop: "30px" }}>
              <Button type="primary" onClick={showModal}>
                Create Challenges
              </Button>
              &nbsp;
              {roleID === "6" && (
                <Button type="primary" onClick={() => showAssignUserModal("User")}>
                  Assign challenge to athlete
                </Button>
              )}
            </Grid>
            {!showTrackChallenges && !assignedChallengeModal && (
              <Grid item xs={12} sm={2} sx={{ textAlign: "start", marginTop: "30px" }}>
                <TextField
                  type="text"
                  size="small"
                  value={searchTerm}
                  onChange={handleSearchChange}
                  placeholder="Search By Challenge Name.."
                />
              </Grid>
            )}
          </Grid>
          
          <CommunityChallenge
          isModalOpen={isModalOpen}
          setIsModalOpen={setIsModalOpen}
          handleCancel={handleCancel}
          setChallengeData={setChallengeData}
          initialChallengeData={initialChallengeData}
          challengeData={challengeData}
          handleSubmit={handleSubmit}
          handleInputChange={handleInputChange}
          fetchAllActivities={fetchAllActivities}
          isSavedStartDatePast={isSavedStartDatePast}
          allChallengesData={allChallengesData}
          roleID={roleID}
          communityGroups={communityGroups}
          challengeForOptions={challengeForOptions}
          setInitialContentSet={setInitialContentSet}
          initialContentSet={initialContentSet}
          isLoading={isLoading}
          editorState={editorState}
          setEditorState={setEditorState}
          />

          {isAssignUserModalOpen && (
            <AssignChallenge
              modalName={modalName}
              isAssignUserModalOpen={isAssignUserModalOpen}
              setIsAssignUserModalOpen={setIsAssignUserModalOpen}
              isLoading={isLoading}
              fetchAllActivities={fetchAllActivities}
              handleSubmit={handleSubmit}
            />
          )}
          {isAssignedGroupModal && (
            <AssignChallengeGroup
            allChallengesData={allChallengesData}
              modalName={modalName}
              isAssignUserModalOpen={isAssignedGroupModal}
              setIsAssignUserModalOpen={setIsAssignedGroupModal}
              isLoading={isLoading}
              fetchAllActivities={fetchAllActivities}
              handleSubmit={handleSubmit}
            />
          )}
          {showCreateTrackModal && (
            <CreateChallengeTrack
            allChallengesData={allChallengesData}
              modalName={modalName}
              isAssignUserModalOpen={showCreateTrackModal}
              setIsAssignUserModalOpen={setShowCreateTrackModal}
              isLoading={isLoading}
              fetchAllActivities={fetchAllActivities}
              handleSubmit={handleSubmit}
            />
          )}
        </div>
        &nbsp;
        {assignedChallengeModal && <AssignedChallenge />}
        {showTrackChallenges && <ChallengeTrack />}
        {!showTrackChallenges && !assignedChallengeModal && (
          <div>
            <Table
              columns={columns}
              dataSource={allChanges?.filter((row) =>
                row?.challengeName?.toLowerCase().includes(searchTerm.toLowerCase())
              )}
              pagination={true}
              className="thin-scrollbar"
            />
          </div>
        )}
      </div>
    </div>
  );
};

export default Challenges;
