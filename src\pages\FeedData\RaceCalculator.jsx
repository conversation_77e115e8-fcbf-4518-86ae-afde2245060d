import { useEffect, useState, useMemo } from "react";
import { <PERSON><PERSON> } from "../../components/ui/button";
import { Input } from "../../components/ui/input";
import {
	Card,
	CardContent,
	CardHeader,
	CardTitle,
} from "../../components/ui/card";
import {
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableHeader,
	TableRow,
} from "../../components/ui/table";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "../../components/ui/select";
import { Badge } from "../../components/ui/badge";
import { Edit, Trash2, Plus, ChevronLeft, ChevronRight } from "lucide-react";
import { RaceCalculatorDialog } from "../../components/admin/race-calculator-dialog";
import { DeleteConfirmDialog } from "../../components/admin/delete-confirm-dialog";
import Header from "../../components/Header";
import {
	getAllRaceCalculation,
	deleteRaceCalculation,
	getAllActivityData,
} from "../../API/api-endpoint";
import Swal from "sweetalert2";
import { Link } from "react-router-dom";

const RaceCalculator = () => {
	const [raceCalculations, setRaceCalculations] = useState([]);
	const [isLoading, setIsLoading] = useState(true);
	const [showDialog, setShowDialog] = useState(false);
	const [editingRaceCalculation, setEditingRaceCalculation] = useState(null);
	const [deleteId, setDeleteId] = useState(null);
	const [searchTerm, setSearchTerm] = useState("");
	const [activities, setActivities] = useState([]);
	const [activityFilter, setActivityFilter] = useState("All");
	const [currentPage, setCurrentPage] = useState(1);
	const pageSize = 10;

	// Fetch activities for filter dropdown (following PhaseBlock pattern)
	const fetchActivities = async () => {
		try {
			const response = await getAllActivityData();
			const activityNames =
				response?.rows?.map(
					(activity) => activity.activity || activity.activity_name
				) || [];
			setActivities(["All", ...activityNames]);
		} catch (error) {
			console.error("Error fetching activities:", error);
			setActivities(["All"]);
		}
	};

	// Fetch race calculations data
	const fetchRaceCalculations = async () => {
		try {
			setIsLoading(true);
			const response = await getAllRaceCalculation();
			if (response?.status) {
				setRaceCalculations(response.goals || []);
			}
		} catch (error) {
			console.error("Error fetching race calculations:", error);
			Swal.fire({
				title: "Error",
				text: "Failed to fetch race calculations",
				icon: "error",
			});
		} finally {
			setIsLoading(false);
		}
	};

	useEffect(() => {
		fetchRaceCalculations();
		fetchActivities();
	}, []);

	// Filter and search logic
	const filteredRaceCalculations = useMemo(() => {
		let filtered = raceCalculations;

		// Search filter
		if (searchTerm) {
			filtered = filtered.filter((calc) =>
				calc.goal_name?.toLowerCase().includes(searchTerm.toLowerCase())
			);
		}
		// Activity filter (following PhaseBlock pattern)
		if (activityFilter !== "All") {
			filtered = filtered.filter((calc) => {
				return (
					calc.type?.toLowerCase().trim() ===
					activityFilter.toLowerCase().trim()
				);
			});
		}

		return filtered;
	}, [raceCalculations, searchTerm, activityFilter]);

	// Pagination (matching Program page)
	const paginatedRaceCalculations = useMemo(() => {
		const startIndex = (currentPage - 1) * pageSize;
		return filteredRaceCalculations.slice(
			startIndex,
			startIndex + pageSize
		);
	}, [filteredRaceCalculations, currentPage, pageSize]);

	const totalPages = Math.ceil(filteredRaceCalculations.length / pageSize);

	// Get activity badge with colors matching Program page
	const getActivityBadge = (type) => {
		const normalizedType = (type || "").trim().toLowerCase();

		const colorMap = {
			running: "bg-blue-100 text-blue-800",
			cycling: "bg-green-100 text-green-800",
			swimming: "bg-cyan-100 text-cyan-800",
			triathlon: "bg-purple-100 text-purple-800",
			fitness: "bg-orange-100 text-orange-800",
		};

		const colorClass =
			colorMap[normalizedType] || "bg-gray-100 text-gray-800";
		return <Badge className={colorClass}>{type}</Badge>;
	};

	// Handle edit
	const handleEdit = (raceCalculation) => {
		console.log("Editing race calculation:", raceCalculation);
		setEditingRaceCalculation(raceCalculation);
		// Use setTimeout to ensure state is set before opening dialog
		setTimeout(() => {
			setShowDialog(true);
		}, 0);
	};

	// Handle delete
	const handleDelete = async (id) => {
		try {
			const response = await deleteRaceCalculation(id);
			if (response?.status) {
				Swal.fire({
					title: "Success",
					text: "Race calculation deleted successfully",
					icon: "success",
					timer: 1800,
					showConfirmButton: false,
				});
				fetchRaceCalculations();
				setCurrentPage(1);
			} else {
				Swal.fire({
					title: "Error",
					text:
						response?.message ||
						"Failed to delete race calculation",
					icon: "error",
				});
			}
		} catch (error) {
			console.error("Error deleting race calculation:", error);
			Swal.fire({
				title: "Error",
				text: "Failed to delete race calculation",
				icon: "error",
			});
		}
		setDeleteId(null);
	};

	// Handle dialog close
	const handleDialogClose = () => {
		setShowDialog(false);
		setEditingRaceCalculation(null);
	};

	// Handle successful save
	const handleSaveSuccess = () => {
		fetchRaceCalculations();
		handleDialogClose();
	};

	return (
		<div>
			<Header />
			<div className='mx-auto p-6 max-w-[1300px] mt-16'>
				<Card>
					<CardHeader className='bg-orange-50 border-b'>
						<div className='flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4'>
							<CardTitle className='text-2xl font-bold text-orange-900'>
								Race Calculator
							</CardTitle>
							<div className='flex gap-2'>
								<Button
									onClick={() => setShowDialog(true)}
									className='bg-orange-600 hover:bg-orange-700 text-white'
								>
									<Plus className='h-4 w-4 mr-2' />
									Create Race Calculation
								</Button>
								<Link to='/multiple-racecalculation'>
									<Button className='bg-blue-600 hover:bg-blue-700 text-white'>
										<Plus className='h-4 w-4 mr-2' />
										Multiple Race Calculation
									</Button>
								</Link>
							</div>
						</div>

						<div className='flex flex-col sm:flex-row gap-4 mt-4'>
							<div className='flex-1'>
								<Input
									placeholder='Search by goal name...'
									value={searchTerm}
									onChange={(e) => {
										setSearchTerm(e.target.value);
										setCurrentPage(1);
									}}
									className='max-w-sm text-sm'
								/>
							</div>
							<div className='w-full sm:w-48'>
								<Select
									value={activityFilter}
									onValueChange={(value) => {
										setActivityFilter(value);
										setCurrentPage(1);
									}}
								>
									<SelectTrigger>
										<SelectValue placeholder='Filter by Activity' />
									</SelectTrigger>
									<SelectContent className='bg-white'>
										{activities.map((activity) => (
											<SelectItem
												key={activity}
												value={activity}
											>
												{activity}
											</SelectItem>
										))}
									</SelectContent>
								</Select>
							</div>
						</div>
					</CardHeader>

					<CardContent className='p-0'>
						<div className='overflow-x-auto'>
							<Table className='min-w-full w-full'>
								<TableHeader>
									<TableRow className='bg-blue-600 hover:bg-blue-600'>
										<TableHead className='text-white font-semibold'>
											Sr No
										</TableHead>
										<TableHead className='text-white font-semibold'>
											Goal Name
										</TableHead>
										<TableHead className='text-white font-semibold'>
											Distance/Quota
										</TableHead>
										<TableHead className='text-white font-semibold'>
											Type
										</TableHead>
										<TableHead className='text-white font-semibold'>
											Actions
										</TableHead>
									</TableRow>
								</TableHeader>
								<TableBody>
									{isLoading ? (
										<TableRow>
											<TableCell
												colSpan={5}
												className='text-center py-8'
											>
												Loading...
											</TableCell>
										</TableRow>
									) : paginatedRaceCalculations.length > 0 ? (
										paginatedRaceCalculations.map(
											(calc, index) => (
												<TableRow
													key={calc.id}
													className='hover:bg-gray-50'
												>
													<TableCell className='font-medium'>
														{(currentPage - 1) *
															pageSize +
															index +
															1}
													</TableCell>
													<TableCell className='font-medium'>
														{calc.goal_name}
													</TableCell>
													<TableCell>
														{calc.quota}
													</TableCell>
													<TableCell>
														{getActivityBadge(
															calc.type
														)}
													</TableCell>
													<TableCell>
														<div className='flex gap-2'>
															<Button
																variant='ghost'
																size='sm'
																onClick={() =>
																	handleEdit(
																		calc
																	)
																}
																className='text-blue-600 hover:text-blue-800'
															>
																<Edit className='h-4 w-4' />
															</Button>
															<Button
																variant='ghost'
																size='sm'
																onClick={() =>
																	setDeleteId(
																		calc.id
																	)
																}
																className='text-red-700 hover:text-red-800'
															>
																<Trash2 className='h-4 w-4' />
															</Button>
														</div>
													</TableCell>
												</TableRow>
											)
										)
									) : (
										<TableRow>
											<TableCell
												colSpan={5}
												className='text-center py-8 text-gray-500'
											>
												No race calculations found
											</TableCell>
										</TableRow>
									)}
								</TableBody>
							</Table>
						</div>
					</CardContent>
				</Card>

				{/* Pagination - matching Program page */}
				{totalPages > 1 && (
					<div className='flex items-center justify-center gap-2 mt-6'>
						<Button
							variant='outline'
							size='sm'
							onClick={() =>
								setCurrentPage(Math.max(1, currentPage - 1))
							}
							disabled={currentPage === 1}
						>
							<ChevronLeft className='h-4 w-4 mr-1' />
							Previous
						</Button>

						{Array.from({ length: 5 }, (_, i) => {
							const startPage =
								Math.floor((currentPage - 1) / 5) * 5 + 1;
							const page = startPage + i;
							if (page > totalPages) return null;

							return (
								<Button
									key={page}
									variant={
										currentPage === page
											? "default"
											: "outline"
									}
									size='sm'
									onClick={() => setCurrentPage(page)}
									className={
										currentPage === page
											? "bg-orange-500 hover:bg-orange-600 text-white"
											: ""
									}
								>
									{page}
								</Button>
							);
						})}

						<Button
							variant='outline'
							size='sm'
							onClick={() =>
								setCurrentPage(
									Math.min(totalPages, currentPage + 1)
								)
							}
							disabled={currentPage === totalPages}
						>
							Next
							<ChevronRight className='h-4 w-4 ml-1' />
						</Button>
					</div>
				)}
			</div>

			{/* Race Calculator Dialog */}
			<RaceCalculatorDialog
				open={showDialog}
				onClose={handleDialogClose}
				onSave={handleSaveSuccess}
				editingRaceCalculation={editingRaceCalculation}
				activities={activities}
			/>

			{/* Delete Confirmation Dialog */}
			<DeleteConfirmDialog
				open={!!deleteId}
				onOpenChange={(open) => !open && setDeleteId(null)}
				onConfirm={() => handleDelete(deleteId)}
				title='Delete Race Calculation'
				description='Are you sure you want to delete this race calculation? This action cannot be undone.'
			/>
		</div>
	);
};

export default RaceCalculator;
