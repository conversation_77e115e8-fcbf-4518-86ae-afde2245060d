import React, { useEffect, useState } from "react";
import Paper from "@mui/material/Paper";
import TableContainer from "@mui/material/TableContainer";
import TableRow from "@mui/material/TableRow";
import Grid from "@mui/material/Grid";
import TableCell, { tableCellClasses } from "@mui/material/TableCell";
import { styled } from "@mui/material/styles";
import { FormLabel, MenuItem, OutlinedInput, TextField } from "@mui/material";
import { useNavigate } from "react-router";
import { getAllCoachesList, getAllCoachesSheduelList, updatecoachbooking } from "../../API/api-endpoint";
import Header from "../Header";
import FullCalendar from "@fullcalendar/react";
import dayGridPlugin from "@fullcalendar/daygrid";
import timeGridPlugin from "@fullcalendar/timegrid";
import interactionPlugin, { Draggable } from "@fullcalendar/interaction";
import listPlugin from "@fullcalendar/list";
import { <PERSON><PERSON>, Modal } from "antd";
import { showError, showSuccess } from "../Messages";



const CoachBooking = () => {
    const navigate = useNavigate();

    const [getList, setGetList] = useState()
    const [selectChallenge, setSelectChallenge] = useState()
    const [isOpenModal, setIsOpenModal] = useState({ isOpen: false, id: "", date: null, startTime: "", endTime: "" })

    const [getAllShedueldList, setGetAllShedueldList] = useState()

    useEffect(() => {
        getAllCoachLists()
    }, [])
    const getAllCoachLists = async () => {
        const response = await getAllCoachesList()
        setGetList(response)
    }
    const sheduelData = async () => {
        setGetAllShedueldList([])
        const response = await getAllCoachesSheduelList(selectChallenge)
        setGetAllShedueldList(response)
    }
    useEffect(() => {
        if (selectChallenge) {
            sheduelData()
        }
    }, [selectChallenge])
    const eventContent = (eventInfo) => {
        return (
            <div style={{ color: "black" }} onClick={() => setIsOpenModal({ isOpen: true, id: eventInfo.event.id, startTime: eventInfo.event.extendedProps.start_time, endTime: eventInfo.event.extendedProps.end_time, date: eventInfo.event.date })}>
                <div>
                    Start Time: {eventInfo.event.extendedProps.start_time}
                </div>
                <div>
                    End Time:  {eventInfo.event.extendedProps.end_time}
                </div>
            </div>)
    }
    const BookSlots = async (id) => {
        const response = await updatecoachbooking(id)
        if (response?.status) {
            showSuccess(response?.message)
            setIsOpenModal({ isOpen: false, id: "", date: null, startTime: "", endTime: "" })
        } else {
            showError(response?.message)
        }
    }
    return (
        <div className="zones-form-container absolute top-20 w-[100vw]">
            <Header />
            <div
                className="zones-form-main"
                style={{ fontSize: "2rem", textAlign: "center" }}
            >
                Coach Booking
            </div>

            <Grid xs={12} md={12}>
                <div className="table-section">
                    <div className="zone-table-title">

                    </div>
                    <div className="zone-table">
                        <TableContainer component={Paper}>
                            <div style={{ fontSize: "18px", background: "#FFEADC", width: "100%" }}>
                                <Grid container spacing={2} className="pl-3">
                                    <Grid item xs={12} md={10} sm={10}>
                                        <h3 style={{ padding: "10px 0px 0px 0px" }}><b>Select Users:</b></h3>
                                    </Grid>
                                    <Grid item xs={12} md={2} sm={2} style={{ padding: "10px 10px 0px 10px" }} >
                                        <div style={{ padding: "20px 0px 0px 10px",float:"right" }}>
                                            <Button type="primary" onClick={() => navigate(`/all-coach-booking`)}><b>All Booking </b></Button>
                                        </div>
                                    </Grid>
                                    <Grid item xs={12} md={4} sm={4} >
                                        <TextField
                                            fullWidth
                                            size="small"
                                            select
                                            name="challengeActivity"
                                            value={selectChallenge}
                                            SelectProps={{
                                                MenuProps: {
                                                  PaperProps: {
                                                    style: {
                                                       scrollbarColor:"#E67E22 white",
                                                       scrollbarWidth:"thin"
                                                    },
                                                  },
                                                },
                                              }}
                                            onChange={
                                                (event) => {
                                                    setSelectChallenge(event.target.value)
                                                }}
                                            id="form-layouts-separator-select"
                                            labelId="form-layouts-separator-select-label"
                                            input={<OutlinedInput id="select-multiple-language" />}
                                        >
                                            <MenuItem value={""} disabled>
                                                Select User
                                            </MenuItem>
                                            {getList?.map((value, index) => {
                                                return (
                                                    <MenuItem value={value?.id}>
                                                        {value?.firstname} {value?.lastname}
                                                    </MenuItem>
                                                );
                                            })}
                                        </TextField>
                                    </Grid>
                                </Grid>
                                &nbsp;
                            </div>
                        </TableContainer>
                        <FullCalendar
                            plugins={[
                                dayGridPlugin,
                                timeGridPlugin,
                                interactionPlugin,
                                listPlugin,
                            ]}
                            className="custom-calendar"
                            eventContent={eventContent}
                            headerToolbar={{
                                left: 'prev,next,title',
                                center: '',
                                right: 'dayGridMonth,timeGridWeek,timeGridDay',
                            }}
                            initialView="dayGridMonth"
                            // weekNumbers="true"
                            editable={true}
                            // drop={handleDrop}
                            selectable={true}
                            firstDay={1}
                            selectMirror={true}
                            dayMaxEvents={false}
                            weekends={true}
                            // initialEvents={events}
                            // droppable={true}
                            events={getAllShedueldList}
                        />
                    </div>
                </div>
            </Grid>

            <Modal
                title="Booking Coach"
                centered
                open={isOpenModal?.isOpen}
                onOk={() => setIsOpenModal({ isOpen: false, id: "" })}
                onCancel={() => setIsOpenModal({ isOpen: false, id: "" })}
                footer={[
                    <Button key="cancel" onClick={() => setIsOpenModal({ isOpen: false, id: "" })}>
                        Close
                    </Button>,
                    <Button key="book" type="primary" onClick={() => BookSlots(isOpenModal?.id)}>
                        Book
                    </Button>,
                ]}
            >
                <div style={{ borderBottom: "1px solid gray", paddingBottom: "24px" }}>
                    <div style={{ display: "flex" }}>
                        <h3 style={{ fontWeight: 600 }}>Start Date :</h3>
                        <h3>{isOpenModal?.date}</h3>
                    </div>
                    <div style={{ display: "flex" }}>
                        <h3 style={{ fontWeight: 600 }}>Start Time :</h3>
                        <h3>{isOpenModal?.startTime}</h3>
                    </div>
                    <div style={{ display: "flex" }}>
                        <h3 style={{ fontWeight: 600 }}>End Time :</h3>
                        <h3>{isOpenModal?.endTime}</h3>
                    </div>
                </div>
            </Modal>
        </div>
    );
};
export default CoachBooking
