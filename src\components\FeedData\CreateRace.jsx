import {
    <PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON>,
    <PERSON><PERSON>ield,
  } from "@mui/material";
  import { <PERSON><PERSON>, Modal } from "antd";
  import React, { useEffect } from "react";
  import {
    CreateRaces,
    updateRaces,
  } from "../../API/api-endpoint";
  import { useFormik } from "formik";
  import <PERSON>wal from "sweetalert2";
  import SlickCarousel from "../../pages/SlickCarousel";
  const CreateRace = ({
    fetchReport,
    setShowAssesmentModal,
    showAssesmentModal,
    editData,
    setEditData,
  }) => {
    const formik = useFormik({
      initialValues: {
        race_type_name: "",
      },
      validate: (values) => {
        const errors = {};
        if (!values.race_type_name) {
          errors.race_type_name = "Name is required";
        }
        return errors;
      },
      // validationSchema: {},
      onSubmit: (values, { resetForm }) => {
        handleSubmitAssesmentForm(values, resetForm);
      },
    });
  
    const handleSubmitAssesmentForm = async (data, resetForm) => {
      let response = "";
      if (editData?.race_type_id) {
        response = await updateRaces(data);
      } else {
        response = await CreateRaces(data);
      }
      if (response?.status) {
        Swal.fire({
          title: "Success",
          text: response.message,
          icon: "success",
        });
        setEditData({});
        setShowAssesmentModal(false);
        fetchReport();
        resetForm();
        formik?.setValues({ race_type_name: "" });
      } else {
        Swal.fire({
          title: "Error",
          text: response.message,
          icon: "error",
        });
      }
      console.log("response", response);
    };
    useEffect(() => {
      if (editData?.race_type_id) {
        const { srID,race_type_status, ...data } = editData;
        console.log("data", data);
        formik?.setValues(data);
      } else {
        setEditData({});
      }
    }, [editData?.race_type_id]);
    return (
      <Modal
        width={1200}
        open={showAssesmentModal}
        onCancel={() => {
          setShowAssesmentModal(false);
          setEditData({});
          formik.resetForm();
          formik?.setValues({ race_type_name: "" });
        }}
        footer={<div></div>}
      >
        <div className="headingCont ">
          <span className="heading">{editData?.race_type_id ? "Edit " : "Create"}</span>{" "}
          <span className="orange heading">Race</span>
         
        </div>
        <div className="parentCont">
          <form className="form1" onSubmit={formik.handleSubmit}>
            <Grid container spacing={1}>
              <Grid className="marbot" item xs={12} sm={11}>
                <FormLabel>Race Name<span className="text-[red]">*</span></FormLabel>
                <TextField
                  fullWidth
                  placeholder="Race name"
                  size="small"
                  type="text"
                  name="race_type_name"
                  value={formik?.values?.race_type_name}
                  onChange={formik.handleChange}
                  error={formik.touched.race_type_name && formik.errors.race_type_name}
                  helperText={formik.touched.race_type_name && formik.errors.race_type_name}
                />
              </Grid>
  
              <Grid item xs={12} sm={10}>
                  <Button 
                  className="btn"
                  key="submit"
                  type="primary"
                  
                  onClick={() => formik.handleSubmit()}
                >
                  Submit
                </Button>
                </Grid>
            </Grid>
          </form>
  
          <div className="slick-container">
            <SlickCarousel />
          </div>
        </div>
      </Modal>
    );
  };
export default CreateRace
