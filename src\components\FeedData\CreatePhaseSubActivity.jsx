import { Chip, FormControl, FormLabel, Grid, MenuItem, OutlinedInput, Select, TextField } from '@mui/material';
import { But<PERSON>, Modal } from 'antd'
import React, { useEffect, useState } from 'react'
import { CreatePrograms, CreateZonesClasification ,createPhaseSubdata,getAlPhaseBlockData,getAllActivityData,getAllPrograms, getAlllevels, getAllsubactivityData, getAllworkoutData, updatePhaseSubdata, updatePrograms, updateZonesClasification, weeklyFeedDataPattern, weeklyFeedDataProgram } from '../../API/api-endpoint';
import { useFormik } from 'formik';
import Swal from 'sweetalert2';
import SlickCarousel from '../../pages/SlickCarousel';
const scoreData = [1, 2, 3, 4, 5]
const CreatePhaseSubActivity = ({ fetchReport, setShowAssesmentModal, showAssesmentModal,editData ,setEditData}) => {
    const [programList, setProgramList] = useState([]);
    const [phaseList, setPhaseList] = useState([]);

    const [workoutList, setworkoutList] = useState([]);
    const [subworkoutList, setsubworkoutList] = useState([]);


console.log("editData",editData);
    const formik = useFormik({
        initialValues: {
             activities: "", 
             workout_id:"",
             subworkout_id:"",
             phase_id:""
        }, validate: (values) => {
            const errors = {};
            if (!values.activities) {
                errors.activities = "Activity name is required";
            }
            if (!values.workout_id) {
                errors.workout_id = "Workout name is required";
            }
            if (!values.subworkout_id) {
                errors.subworkout_id = "Sub workout name is required";
            }
            if (!values.phase_id) {
                errors.phase_id = "Phase name is required";
            }
            return errors;
        },
        // validationSchema: {},
        onSubmit: (values, { resetForm }) => {
            handleSubmitAssesmentForm(values, resetForm)

        },
    });
    console.log("formik", formik?.values);
    const getAllProgramsData = async () => {
        const response = await getAllActivityData()
        console.log("response", response);
        setProgramList(response?.rows)
    }
    const getAllPhaseData = async () => {
        const response = await getAlPhaseBlockData()
        console.log("response", response);
        setPhaseList(response)
    }
    const getAllWorkoutData = async () => {
        const response = await getAllworkoutData()
        console.log("response", response);
        setworkoutList(response)
    }
    const getAllSubworkoutData = async () => {
        const response = await getAllsubactivityData()
        console.log("response", response);
        setsubworkoutList(response)
    }
    useEffect(() => {
        getAllProgramsData()
        getAllWorkoutData()
        getAllPhaseData()
        getAllSubworkoutData()
    }, [])
  
    const handleSubmitAssesmentForm = async (data,resetForm) => {
        console.log("dfvdsbvf bdvbv");
        let response =""
        if (editData?.id) {

         response = await updatePhaseSubdata(data)
        }else{
         response = await createPhaseSubdata(data)

        }
        if (response?.status) {
            Swal.fire({
                title: "Success",
                text: response.message,
                icon: "success",
            });
            setShowAssesmentModal(false)
            fetchReport()
            setEditData({})
            resetForm()
        } else {
            Swal.fire({
                title: "Error",
                text: response.message,
                icon: "error",
            });
        }
        console.log("response", response);
    }
    useEffect(()=>{
        if (editData?.id) {
            const {srID,...data}=editData
            console.log("data",data);
            formik?.setValues(data)
        }
    },[editData?.id])
    return (
        <Modal
            width={1200}
           open={showAssesmentModal}
            onCancel={() => {setShowAssesmentModal(false)
                setEditData({})
        formik.resetForm()
            }}
            footer={
                <div >
                    </div>
                //   loading={isLoading}
            }
        >

<div className="headingCont">
        <span className="heading">{editData?.id ? "Edit " : "Create"}</span>{" "}
        <span className="orange heading">Phase Sub-Activity</span>
      </div>
      {/* <h1>{editData ? editData.challengeId : values.challengeId}</h1> */}
      <div className="parentCont">
        <form className="form1" onSubmit={formik.handleSubmit}>
          
                <Grid container spacing={2}>

                    <Grid item xs={12}    sm={11}>
                        <FormLabel >Activity Name<span className="text-[red]">*</span></FormLabel>
                    
                        <TextField
                            fullWidth
                            size="small"
                            select
                            name="activities"
                            SelectProps={{
                                MenuProps: {
                                  PaperProps: {
                                    style: {
                                       scrollbarColor:"#E67E22 white",
                                       scrollbarWidth:"thin"
                                    },
                                  },
                                },
                              }}
                            value={formik?.values?.activities}
                            onChange={formik.handleChange}
                            error={formik.touched.activities && formik.errors.activities}
                            helperText={
                                formik.touched.activities && formik.errors.activities
                            }

                            id="form-layouts-separator-select"
                            labelId="form-layouts-separator-select-label"
                            input={<OutlinedInput id="select-multiple-language" />}
                        >
                            <MenuItem value={""} disabled>
                                Select Activity
                            </MenuItem>
                            {programList?.map((value, index) => {
                                return (
                                    <MenuItem value={value?.id}>
                                    {value?.activity_name}
                                    </MenuItem>
                                );
                            })}
                        </TextField>
                    </Grid>
                    <Grid item xs={12}  sm={11}>
                        <FormLabel >Phase Name<span className="text-[red]">*</span></FormLabel>
                   
                        <TextField
                            fullWidth
                            size="small"
                            select
                            name="phase_id"
                            SelectProps={{
                                MenuProps: {
                                  PaperProps: {
                                    style: {
                                       scrollbarColor:"#E67E22 white",
                                       scrollbarWidth:"thin"
                                    },
                                  },
                                },
                              }}
                            value={formik?.values?.phase_id?formik?.values?.phase_id:""}
                            onChange={formik.handleChange}
                            error={formik.touched.phase_id && formik.errors.phase_id}
                            helperText={
                                formik.touched.phase_id && formik.errors.phase_id
                            }

                            id="form-layouts-separator-select"
                            labelId="form-layouts-separator-select-label"
                            input={<OutlinedInput id="select-multiple-language" />}
                        >
                            <MenuItem value={""} disabled>
                                Select Name
                            </MenuItem>
                            {phaseList?.map((value, index) => {
                                return (
                                    <MenuItem value={value?.phasename?.id}>
                                    {value?.phasename?.phase}
                                    </MenuItem>
                                );
                            })}
                        </TextField>
                    </Grid>


                    <Grid item xs={12}  sm={11}>
                    <FormLabel >Workout Name<span className="text-[red]">*</span></FormLabel>
                
                    <TextField
                        fullWidth
                        size="small"
                        select
                        name="workout_id"
                        SelectProps={{
                            MenuProps: {
                              PaperProps: {
                                style: {
                                   scrollbarColor:"#E67E22 white",
                                   scrollbarWidth:"thin"
                                },
                              },
                            },
                          }}
                        value={formik?.values?.workout_id?formik?.values?.workout_id:""}
                        onChange={formik.handleChange}
                        error={formik.touched.workout_id && formik.errors.workout_id}
                        helperText={
                            formik.touched.workout_id && formik.errors.workout_id
                        }

                        id="form-layouts-separator-select"
                        labelId="form-layouts-separator-select-label"
                        input={<OutlinedInput id="select-multiple-language" />}
                    >
                        <MenuItem value={""} disabled>
                            Select Activity
                        </MenuItem>
                        {workoutList?.map((value, index) => {
                            return (
                                <MenuItem value={value?.id}>
                                {value?.workout}
                                </MenuItem>
                            );
                        })}
                    </TextField>
                </Grid>


                <Grid item xs={12}  sm={11}>
                        <FormLabel >Sub Workout Name<span className="text-[red]">*</span></FormLabel>
                   
                        <TextField
                            fullWidth
                            size="small"
                            select
                            name="subworkout_id"
                            SelectProps={{
                                MenuProps: {
                                  PaperProps: {
                                    style: {
                                       scrollbarColor:"#E67E22 white",
                                       scrollbarWidth:"thin"
                                    },
                                  },
                                },
                              }}
                            value={formik?.values?.subworkout_id?formik?.values?.subworkout_id:""}
                            onChange={formik.handleChange}
                            error={formik.touched.subworkout_id && formik.errors.subworkout_id}
                            helperText={
                                formik.touched.subworkout_id && formik.errors.subworkout_id
                            }

                            id="form-layouts-separator-select"
                            labelId="form-layouts-separator-select-label"
                            input={<OutlinedInput id="select-multiple-language" />}
                        >
                            <MenuItem value={""} disabled>
                                Select Activity
                            </MenuItem>
                            {subworkoutList?.map((value, index) => {
                                return (
                                    <MenuItem value={value?.id}>
                                    {value?.subworkout}
                                    </MenuItem>
                                );
                            })}
                        </TextField>
                    </Grid>

                    <Grid item xs={12} sm={6}>
              <Button
                className="btn"
                key="submit"
                type="primary"
                onClick={() => formik.handleSubmit()}
              >
                Submit
              </Button>
            </Grid>

                </Grid>
            </form>

            <div className="slick-container">
          <SlickCarousel />
        </div>
            </div>
        </Modal>
    )
}

export default CreatePhaseSubActivity
