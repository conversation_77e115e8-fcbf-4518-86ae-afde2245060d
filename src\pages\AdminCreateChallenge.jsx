import { useEffect, useState, useMemo, useCallback } from "react";
import { But<PERSON> } from "../components/ui/button";
import { Input } from "../components/ui/input";
import { Card, CardContent, CardHeader } from "../components/ui/card";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "../components/ui/select";
import {
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableHeader,
	TableRow,
} from "../components/ui/table";
import { Edit, Trash2, Plus } from "lucide-react";
import { ChallengeDialog } from "../components/admin/challenge-dialog";
import { DeleteConfirmDialog } from "../components/admin/delete-confirm-dialog";
import Header from "../components/Header";
import {
	getAllChallenges,
	deleteCraetedCahllenges,
	URL,
} from "../API/api-endpoint";
import Swal from "sweetalert2";

const AdminCreateChallenge = () => {
	const [challengeData, setChallengeData] = useState([]);
	const [isLoading, setIsLoading] = useState(true);
	const [searchTerm, setSearchTerm] = useState("");
	const [selectedActivityFilter, setSelectedActivityFilter] = useState("All");
	const [currentPage, setCurrentPage] = useState(1);
	const [showDialog, setShowDialog] = useState(false);
	const [editingItem, setEditingItem] = useState(null);
	const [deleteId, setDeleteId] = useState(null);
	const pageSize = 10;

	const activityOptions = [
		"All",
		"Running",
		"Cycling",
		"Swimming",
		"Triathlon",
		"Duathlon",
		"Walking",
	];

	const fetchData = useCallback(async () => {
		try {
			setIsLoading(true);
			const response = await getAllChallenges();

			if (response) {
				const dataWithSrID = response.map((item, index) => ({
					...item,
					srID: index + 1,
				}));
				setChallengeData(dataWithSrID || []);
			} else {
				setChallengeData([]);
			}
		} catch (error) {
			console.error("Error fetching challenge data:", error);
			Swal.fire({
				title: "Error",
				text: "Failed to fetch challenge data. Please try again.",
				icon: "error",
				timer: 3000,
				showConfirmButton: false,
			});
			setChallengeData([]);
		} finally {
			setIsLoading(false);
		}
	}, []);

	useEffect(() => {
		fetchData();
	}, [fetchData]);

	const filteredData = useMemo(() => {
		return challengeData.filter((item) => {
			const matchesSearch =
				!searchTerm.trim() ||
				item?.challengeName
					?.toLowerCase()
					.includes(searchTerm.toLowerCase());

			const matchesActivity =
				selectedActivityFilter === "All" ||
				item?.activity?.activity_name === selectedActivityFilter;

			return matchesSearch && matchesActivity;
		});
	}, [challengeData, searchTerm, selectedActivityFilter]);

	const paginatedData = useMemo(() => {
		const startIndex = (currentPage - 1) * pageSize;
		const endIndex = startIndex + pageSize;
		return filteredData.slice(startIndex, endIndex);
	}, [filteredData, currentPage, pageSize]);

	const totalPages = Math.ceil(filteredData.length / pageSize);

	const handlePageChange = (page) => {
		setCurrentPage(page);
	};

	const handleSearch = (value) => {
		setSearchTerm(value);
		setCurrentPage(1);
	};

	const handleActivityFilterChange = (value) => {
		setSelectedActivityFilter(value);
		setCurrentPage(1);
	};

	const handleCreate = () => {
		setEditingItem(null);
		setShowDialog(true);
	};

	const handleEdit = (item) => {
		setEditingItem(item);
		setShowDialog(true);
	};

	const handleDelete = async (id) => {
		try {
			await deleteCraetedCahllenges(id);

			Swal.fire({
				title: "Success",
				text: "Challenge deleted successfully",
				icon: "success",
				timer: 2000,
				showConfirmButton: false,
			});

			setCurrentPage(1);
			fetchData();
		} catch (error) {
			console.error("Error deleting challenge:", error);
			Swal.fire({
				title: "Error",
				text: "Failed to delete challenge. Please try again.",
				icon: "error",
				timer: 3000,
				showConfirmButton: false,
			});
		}
	};

	const handleDialogSuccess = () => {
		setShowDialog(false);
		setEditingItem(null);
		fetchData();
	};

	const formatDuration = (duration, unit) => {
		return `${duration} ${unit || "days"}`;
	};

	const formatChallengeTarget = (targets) => {
		if (!targets || targets.length === 0) return "N/A";
		return targets
			.map((target) => (target.quota ? `${target.quota} KM` : "N/A"))
			.join(", ");
	};

	return (
		<div>
			<Header />
			<div className='mx-auto p-6 max-w-[1400px] mt-16'>
				<Card>
					<CardHeader className='bg-orange-50 border-b flex flex-col gap-4'>
						<div className='flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4'>
							<div>
								<h1 className='text-2xl font-bold text-orange-900'>
									Challenge Management
								</h1>
								<p className='text-orange-700 mt-1 text-sm'>
									Create and manage fitness challenges for
									users
								</p>
							</div>
						</div>
						<div className='flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4'>
							<div className='flex gap-3'>
								<Button
									onClick={handleCreate}
									className='bg-orange-600 hover:bg-orange-700 text-white'
								>
									<Plus className='h-4 w-4 mr-2' />
									Create Challenge
								</Button>
							</div>

							<div className='flex gap-3'>
								<Input
									placeholder='Search by challenge ...'
									value={searchTerm}
									onChange={(e) =>
										handleSearch(e.target.value)
									}
									className='w-full text-sm'
								/>
							</div>
						</div>
					</CardHeader>
					<CardContent className='p-0'>
						<div className='overflow-x-auto'>
							<Table className='min-w-full w-full'>
								<TableHeader>
									<TableRow className='bg-blue-600 hover:bg-blue-600'>
										<TableHead className='text-white font-semibold'>
											Badge
										</TableHead>
										<TableHead className='text-white font-semibold'>
											Challenge Name
										</TableHead>
										<TableHead className='text-white font-semibold'>
											Duration
										</TableHead>
										<TableHead className='text-white font-semibold'>
											Activity
										</TableHead>
										<TableHead className='text-white font-semibold'>
											Target
										</TableHead>
										<TableHead className='text-white font-semibold'>
											Points
										</TableHead>
										<TableHead className='text-white font-semibold'>
											Actions
										</TableHead>
									</TableRow>
								</TableHeader>
								<TableBody>
									{isLoading ? (
										<TableRow>
											<TableCell
												colSpan={7}
												className='text-center py-8'
											>
												Loading...
											</TableCell>
										</TableRow>
									) : paginatedData.length > 0 ? (
										paginatedData.map((item, index) => (
											<TableRow
												key={item.id || index}
												className='hover:bg-gray-50'
											>
												<TableCell>
													<img
														src={
															item.badge
																? `${URL}/static/public/userimages/${item.badge}`
																: "https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcTbQIfgvrUcDEY90ZrIVSMPYX4hvjFe963pHSxaVK3fuw&s"
														}
														alt='Challenge Badge'
														className='w-10 h-10 rounded-full border-2 object-cover'
													/>
												</TableCell>
												<TableCell className='font-medium'>
													{item?.challengeName ||
														"N/A"}
												</TableCell>
												<TableCell>
													{formatDuration(
														item?.challengeDuration,
														item?.durationunit
													)}
												</TableCell>
												<TableCell>
													{item?.activity
														?.activity_name ||
														"N/A"}
												</TableCell>
												<TableCell>
													{formatChallengeTarget(
														item?.challangeTarget
													)}
												</TableCell>
												<TableCell>
													{item?.challengePoints ||
														"0"}
												</TableCell>
												<TableCell>
													<div className='flex gap-2'>
														<Button
															variant='ghost'
															size='sm'
															onClick={() =>
																handleEdit(item)
															}
															className='text-blue-600 hover:text-blue-800'
														>
															<Edit className='h-4 w-4' />
														</Button>
														<Button
															variant='ghost'
															size='sm'
															onClick={() =>
																setDeleteId(
																	item.id
																)
															}
															className='text-red-600 hover:text-red-800'
														>
															<Trash2 className='h-4 w-4' />
														</Button>
													</div>
												</TableCell>
											</TableRow>
										))
									) : (
										<TableRow>
											<TableCell
												colSpan={7}
												className='text-center py-8 text-gray-500'
											>
												No challenges found
											</TableCell>
										</TableRow>
									)}
								</TableBody>
							</Table>
						</div>

						{totalPages > 1 && (
							<div className='flex justify-center gap-2 p-4 border-t'>
								<Button
									variant='outline'
									size='sm'
									onClick={() =>
										setCurrentPage(
											Math.max(1, currentPage - 1)
										)
									}
									disabled={currentPage === 1}
								>
									Previous
								</Button>

								{Array.from({ length: 5 }, (_, i) => {
									const startPage =
										Math.floor((currentPage - 1) / 5) * 5 +
										1;
									const page = startPage + i;
									if (page > totalPages) return null;

									return (
										<Button
											key={page}
											variant={
												currentPage === page
													? "default"
													: "outline"
											}
											size='sm'
											onClick={() => setCurrentPage(page)}
											className={
												currentPage === page
													? "bg-orange-500 hover:bg-orange-600 text-white"
													: ""
											}
										>
											{page}
										</Button>
									);
								})}

								<Button
									variant='outline'
									size='sm'
									onClick={() =>
										setCurrentPage(
											Math.min(
												totalPages,
												currentPage + 1
											)
										)
									}
									disabled={currentPage === totalPages}
								>
									Next
								</Button>
							</div>
						)}
					</CardContent>
				</Card>
			</div>

			<ChallengeDialog
				open={showDialog}
				onClose={() => setShowDialog(false)}
				onSuccess={handleDialogSuccess}
				editingItem={editingItem}
			/>

			<DeleteConfirmDialog
				open={!!deleteId}
				onOpenChange={(open) => !open && setDeleteId(null)}
				onConfirm={() => {
					handleDelete(deleteId);
					setDeleteId(null);
				}}
				title='Delete Challenge'
				description='Are you sure you want to delete this challenge? This action cannot be undone.'
			/>
		</div>
	);
};

export default AdminCreateChallenge;
