import { useEffect, useState, useMemo, useCallback } from "react";
import { <PERSON><PERSON> } from "../../components/ui/button";
import { Input } from "../../components/ui/input";
import { Card, CardContent, CardHeader } from "../../components/ui/card";
import {
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableHeader,
	TableRow,
} from "../../components/ui/table";
import { Edit, Trash2, Plus, ArrowLeft } from "lucide-react";
import { ScorDataDialog } from "../../components/admin/scor-data-dialog";
import { DeleteConfirmDialog } from "../../components/admin/delete-confirm-dialog";
import Header from "../../components/Header";
import {
	getAllScoreReadnessData,
	deleteScoreReadnessData,
} from "../../API/api-endpoint";
import Swal from "sweetalert2";

const ScorData = () => {
	const [scorData, setScorData] = useState([]);
	const [isLoading, setIsLoading] = useState(true);
	const [searchTerm, setSearchTerm] = useState("");
	const [currentPage, setCurrentPage] = useState(1);
	const [showDialog, setShowDialog] = useState(false);
	const [editingItem, setEditingItem] = useState(null);
	const [deleteId, setDeleteId] = useState(null);
	const pageSize = 10;

	const fetchData = useCallback(async () => {
		try {
			setIsLoading(true);
			const response = await getAllScoreReadnessData();
			if (response) {
				setScorData(response || []);
			} else {
				console.log("Response is empty or invalid");
				setScorData([]);
			}
		} catch (error) {
			console.error("Error fetching score data:", error);
			Swal.fire({
				title: "Error",
				text: "Failed to fetch score data. Please try again.",
				icon: "error",
				timer: 3000,
				showConfirmButton: false,
			});
			setScorData([]);
		} finally {
			setIsLoading(false);
		}
	}, []);

	useEffect(() => {
		fetchData();
	}, [fetchData]);

	const filteredData = useMemo(() => {
		if (!searchTerm.trim()) return scorData;

		return scorData.filter((item) => {
			return (
				item?.ironman_description
					?.toLowerCase()
					.includes(searchTerm.toLowerCase()) ||
				item?.olympic_description
					?.toLowerCase()
					.includes(searchTerm.toLowerCase()) ||
				item?.sprint_description
					?.toLowerCase()
					.includes(searchTerm.toLowerCase()) ||
				item?.seventy_point_three_description
					?.toLowerCase()
					.includes(searchTerm.toLowerCase())
			);
		});
	}, [scorData, searchTerm]);

	const paginatedData = useMemo(() => {
		const startIndex = (currentPage - 1) * pageSize;
		const endIndex = startIndex + pageSize;
		return filteredData.slice(startIndex, endIndex);
	}, [filteredData, currentPage, pageSize]);

	const totalPages = Math.ceil(filteredData.length / pageSize);

	const handleSearch = (value) => {
		setSearchTerm(value);
		setCurrentPage(1);
	};

	const handleCreate = () => {
		setEditingItem(null);
		setShowDialog(true);
	};

	const handleEdit = (item) => {
		setEditingItem(item);
		setShowDialog(true);
	};

	const handleDelete = async (id) => {
		try {
			const response = await deleteScoreReadnessData(id);
			Swal.fire({
				title: "Success",
				text: "Score data deleted successfully",
				icon: "success",
				timer: 2000,
				showConfirmButton: false,
			});

			setCurrentPage(1);
			fetchData();
		} catch (error) {
			console.error("Error deleting score data:", error);
			Swal.fire({
				title: "Error",
				text: "Failed to delete score data. Please try again.",
				icon: "error",
				timer: 3000,
				showConfirmButton: false,
			});
		}
	};

	const handleDialogSuccess = () => {
		setShowDialog(false);
		setEditingItem(null);
		fetchData();
	};

	return (
		<div>
			<Header />
			<div className='mx-auto p-6 max-w-[1300px] mt-16'>
				<Card>
					<CardHeader className='bg-orange-50 border-b flex flex-col gap-4'>
						<div className='flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4'>
							<div>
								<h1 className='text-2xl font-bold text-orange-900'>
									Score Data
								</h1>
								<p className='text-orange-700 mt-1 text-sm'>
									Manage race readiness score data and point
									ranges
								</p>
							</div>
						</div>
						<div className='flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4'>
							<div className='flex gap-3'>
								<Button
									onClick={handleCreate}
									className='bg-orange-600 hover:bg-orange-700 text-white'
								>
									<Plus className='h-4 w-4 mr-2' />
									Create Score Data
								</Button>
							</div>

							<div className='flex gap-3'>
								<Input
									placeholder='Search by description...'
									value={searchTerm}
									onChange={(e) =>
										handleSearch(e.target.value)
									}
									className='max-w-sm text-sm'
								/>
								<Button
									onClick={() =>
										(window.location.href = "/readness")
									}
									variant='outline'
									className='border-gray-300 text-gray-700 hover:bg-gray-100'
								>
									<ArrowLeft className='h-4 w-4 mr-2' />
									Back
								</Button>
							</div>
						</div>
					</CardHeader>
					<CardContent className='p-0'>
						<div className='overflow-x-auto'>
							<Table className='min-w-full w-full'>
								<TableHeader>
									<TableRow className='bg-blue-600 hover:bg-blue-600'>
										<TableHead className='text-white font-semibold'>
											Sr No
										</TableHead>
										<TableHead className='text-white font-semibold'>
											Ironman Description
										</TableHead>
										<TableHead className='text-white font-semibold'>
											Olympic Description
										</TableHead>
										<TableHead className='text-white font-semibold'>
											Sprint Description
										</TableHead>
										<TableHead className='text-white font-semibold'>
											70.3 Description
										</TableHead>
										<TableHead className='text-white font-semibold'>
											Points Range
										</TableHead>
										<TableHead className='text-white font-semibold'>
											Actions
										</TableHead>
									</TableRow>
								</TableHeader>
								<TableBody>
									{isLoading ? (
										<TableRow>
											<TableCell
												colSpan={7}
												className='text-center py-8'
											>
												Loading...
											</TableCell>
										</TableRow>
									) : paginatedData.length > 0 ? (
										paginatedData.map((item, index) => (
											<TableRow
												key={item.id || index}
												className='hover:bg-gray-50'
											>
												<TableCell className='font-medium'>
													{(currentPage - 1) *
														pageSize +
														index +
														1}
												</TableCell>
												<TableCell className='max-w-[150px] truncate'>
													{item?.ironman_description ||
														"N/A"}
												</TableCell>
												<TableCell className='max-w-[150px] truncate'>
													{item?.olympic_description ||
														"N/A"}
												</TableCell>
												<TableCell className='max-w-[150px] truncate'>
													{item?.sprint_description ||
														"N/A"}
												</TableCell>
												<TableCell className='max-w-[150px] truncate'>
													{item?.seventy_point_three_description ||
														"N/A"}
												</TableCell>
												<TableCell>
													{item?.points_range_from &&
													item?.points_range_to
														? `${item.points_range_from} - ${item.points_range_to}`
														: "N/A"}
												</TableCell>
												<TableCell>
													<div className='flex gap-2'>
														<Button
															variant='ghost'
															size='sm'
															onClick={() =>
																handleEdit(item)
															}
															className='text-blue-600 hover:text-blue-800'
														>
															<Edit className='h-4 w-4' />
														</Button>
														<Button
															variant='ghost'
															size='sm'
															onClick={() =>
																setDeleteId(
																	item.id
																)
															}
															className='text-red-600 hover:text-red-800'
														>
															<Trash2 className='h-4 w-4' />
														</Button>
													</div>
												</TableCell>
											</TableRow>
										))
									) : (
										<TableRow>
											<TableCell
												colSpan={7}
												className='text-center py-8 text-gray-500'
											>
												No score data found
											</TableCell>
										</TableRow>
									)}
								</TableBody>
							</Table>
						</div>

						{totalPages > 1 && (
							<div className='flex justify-center gap-2 p-4 border-t'>
								<Button
									variant='outline'
									size='sm'
									onClick={() =>
										setCurrentPage(
											Math.max(1, currentPage - 1)
										)
									}
									disabled={currentPage === 1}
								>
									Previous
								</Button>

								{Array.from({ length: 5 }, (_, i) => {
									const startPage =
										Math.floor((currentPage - 1) / 5) * 5 +
										1;
									const page = startPage + i;
									if (page > totalPages) return null;

									return (
										<Button
											key={page}
											variant={
												currentPage === page
													? "default"
													: "outline"
											}
											size='sm'
											onClick={() => setCurrentPage(page)}
											className={
												currentPage === page
													? "bg-orange-500 hover:bg-orange-600 text-white"
													: ""
											}
										>
											{page}
										</Button>
									);
								})}

								<Button
									variant='outline'
									size='sm'
									onClick={() =>
										setCurrentPage(
											Math.min(
												totalPages,
												currentPage + 1
											)
										)
									}
									disabled={currentPage === totalPages}
								>
									Next
								</Button>
							</div>
						)}
					</CardContent>
				</Card>
			</div>

			<ScorDataDialog
				open={showDialog}
				onClose={() => setShowDialog(false)}
				onSuccess={handleDialogSuccess}
				editingItem={editingItem}
			/>

			<DeleteConfirmDialog
				open={!!deleteId}
				onOpenChange={(open) => !open && setDeleteId(null)}
				onConfirm={() => {
					handleDelete(deleteId);
					setDeleteId(null);
				}}
				title='Delete Score Data'
				description='Are you sure you want to delete this score data? This action cannot be undone.'
			/>
		</div>
	);
};

export default ScorData;
