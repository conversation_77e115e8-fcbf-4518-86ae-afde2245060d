import React, { useState } from 'react'
import Header from '../../components/Header'
import { getWellnessReult } from '../../API/api-endpoint'
import { useEffect } from 'react'
import { LinearProgress, linearProgressClasses } from '@mui/material';
import { styled } from '@mui/material/styles';
import Box from '@mui/material/Box';
import Card from '@mui/material/Card';
import CardActions from '@mui/material/CardActions';
import CardContent from '@mui/material/CardContent';
import Button from '@mui/material/Button';
import Typography from '@mui/material/Typography';
const BorderLinearProgress = styled(LinearProgress)(({ theme }) => ({
    height: 10,
    borderRadius: 5,
    [`&.${linearProgressClasses.colorPrimary}`]: {
      backgroundColor: theme.palette.grey[theme.palette.mode === 'light' ? 200 : 800],
    },
    [`& .${linearProgressClasses.bar}`]: {
      borderRadius: 5,
      backgroundColor: theme.palette.mode === 'light' ? '#13b613' : '#308fe8',
    },
  }));
const WellResult = () => {
  const [wellNessScore,setwellNessScore]=useState()
    useEffect(()=>{
        getWeelnessResult()
    },[])
    const getWeelnessResult =async()=>{
        const response = await getWellnessReult()
        console.log("response",JSON.parse(response));
        setwellNessScore(JSON.parse(response))
    }
  return (
    <div>
      <Header />
      <div style={{ marginTop: '90px', padding: '20px' }}>
      {wellNessScore?.map((wellness)=>{
        return(
          <Card sx={{ minWidth: 275 ,margin:"2% 6% 2% 6%", boxShadow:"rgba(60, 64, 67, 0.3) 0px 1px 2px 0px, rgba(60, 64, 67, 0.15) 0px 2px 6px 2px"}}>
          <CardContent>
          <h2><strong className='text-xl'>{wellness?.segment?.name}</strong></h2>
          <div className='flex'>
          <div>
          Total Score:{wellness?.segment?.totalScore}
          </div> |
          <div>
          Gained Score:{wellness?.segment?.scored}
          </div> |
          <div>
          Percentage Gained:{wellness?.segment?.percentageGained}%
          </div>
          </div>
          {wellness?.segment?.subsegment?.map((ele)=>{
            return(
              <div className='p-4 bg-[#f5f5f59c] rounded-xl mb-2'>
              <div className='flex justify-between' >
              <div>{ele?.name}</div>
              <div className='flex'>
              <div>
              Total Score:{ele?.totalScore}
              </div> |
              <div>
              Gained Score:{ele?.gainedScore}
              </div> |
              <div>
              Percentage Gained:{ele?.percentageGained}%
              </div>
              </div>

              </div>
              <BorderLinearProgress variant="determinate" value={ele?.percentageGained} />
              </div>
            )
          })}
           
          </CardContent>
        
        </Card>
        )
      })}
      
      </div>
    </div>
  )
}

export default WellResult
