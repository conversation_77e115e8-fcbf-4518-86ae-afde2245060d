import React, { useEffect, useState } from 'react'
import { Menu, Switch } from '@mantine/core'
import {
  IconFilter,
  IconPlus,
  IconSearch,
  // IconX,
  IconUser
} from '@tabler/icons'
import { Link } from 'react-router-dom'

const SidePanel = ({ actions, handleModalReveal, setOpened, athletes }) => {
  const [username, setUsername] = useState('')

  useEffect(() => {
    setUsername(localStorage.getItem('username'))
  }, [username])

  return (
    <>
      <div className='flex items-center justify-between'>
        <div className='flex items-center space-x-8'>
          <div>
            <IconPlus
              size={24}
              color='dodgerblue'
              onClick={handleModalReveal}
              className='cursor-pointer'
            />
          </div>
          <div>
            <IconSearch size={24} color='dodgerblue' />
          </div>
          <div>
            <IconFilter size={24} color='dodgerblue' />
          </div>
        </div>
        {/* <div>
          <IconX size={24} color='dodgerblue' />
        </div> */}
      </div>
      <div className='my-4'>
        <Switch label='Multiple Athletes' />
      </div>
      <div className='mb-6'>
        <Switch label='Lite Program Athletes' />
      </div>
      <div className='flex flex-col gap-y-2 text-sm mb-4'>
        <div className='text-slate-700'>
          Showing Athletes for{' '}
          {username === 'deepak' ? 'Deepak' : 'Toby Somerville'}
        </div>
        <div className='text-slate-700'>
          <span className='font-medium'>Total Athletes</span> :{' '}
          {athletes.length}
        </div>
      </div>
      <hr />
      <div className='flex flex-col gap-y-4 items-start my-6 h-full'>
        {athletes.map((item) => (
          <Link
            className='flex justify-between items-center gap-x-4 w-full'
            key={item.id}
            to={`/athlete/${item.id}`}
            onClick={() => setOpened(false)}
          >
            <div className='flex items-center gap-x-2 cursor-pointer'>
              <span>
                <IconUser size={20} color='#334155' />
              </span>
              <span className='text-slate-700'>
                {item.firstName} {item.lastName}
              </span>
            </div>
            <div>
              <Menu shadow='md' width={150} position='bottom-end'>
                <Menu.Target className='cursor-pointer'>
                  <svg
                    xmlns='http://www.w3.org/2000/svg'
                    className='icon icon-tabler icon-tabler-dots-vertical'
                    width='20'
                    height='20'
                    viewBox='0 0 24 24'
                    strokeWidth='1.5'
                    stroke='#2c3e50'
                    fill='none'
                    strokeLinecap='round'
                    strokeLinejoin='round'
                  >
                    <path stroke='none' d='M0 0h24v24H0z' fill='none' />
                    <circle cx='12' cy='12' r='1' />
                    <circle cx='12' cy='19' r='1' />
                    <circle cx='12' cy='5' r='1' />
                  </svg>
                </Menu.Target>

                <Menu.Dropdown>
                  {actions.map((item) => (
                    <Menu.Item color={'gray'} icon={item.icon} key={item.id}>
                      {item.title}
                    </Menu.Item>
                  ))}
                </Menu.Dropdown>
              </Menu>
            </div>
          </Link>
        ))}
      </div>
    </>
  )
}

export default SidePanel
