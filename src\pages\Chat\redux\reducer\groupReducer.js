import { SET_OPENED_GROUP_MEMBERS, SET_OPENED_GROUP_INFO, GET_GROUP_INFO_FAIL, GET_GROUP_INFO_INIT, GET_GROUP_INFO_SUCCESS, GET_GROUP_MESSAGES_FAIL, GET_GROUP_MESSAGES_INIT, GET_GROUP_MESSAGES_SUCCESS } from "../constant/groupConstants";

export const groupReducer = (state = { openedGroupMembers: [], openedGroupData: {}, openedGroupMessages: [] }, { type, payload, action }) => {
    switch (type) {

        case GET_GROUP_INFO_INIT: return { ...state, loading: true }

        case GET_GROUP_INFO_SUCCESS: return { ...state, loading: false, openedGroupInfo: payload }

        case GET_GROUP_INFO_FAIL: return { ...state, loading: false, error: payload }

        case GET_GROUP_MESSAGES_INIT: return { ...state, loading: true }

        case GET_GROUP_MESSAGES_SUCCESS: return { ...state, loading: false, openedGroupMessages: payload }

        case GET_GROUP_MESSAGES_FAIL: return { ...state, loading: false, error: payload }

        case SET_OPENED_GROUP_INFO: return { ...state, loading: false, openedGroupData: payload };

        case SET_OPENED_GROUP_MEMBERS: return { ...state, loading: false, openedGroupMembers: [...state.openedGroupMembers, ...payload], };
        
        default: return state;
    }
}