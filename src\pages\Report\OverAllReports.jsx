import React, { useEffect, useState } from 'react'
import Header from '../../components/Header'
import { UserGrowthReportData, getAllPrograms, userOverallReports } from '../../API/api-endpoint'
import { Button, FormLabel, Grid, LinearProgress, MenuItem, OutlinedInput, TextField } from '@mui/material'
import { Bar, Line, Pie } from "react-chartjs-2";
import PieChar from '../../components/Chart/PieChar';
import BarChart from '../../components/Chart/BarChart';
import LineBar from '../../components/Chart/LineBar';

const OverAllReports = () => {
    const [chartData, setChartData] = useState({});
    const [pieChartData, setPieChartdata] = useState({});
    const [cityChart, setCityChart] = useState({});
    const [countryChart, setCountryChart] = useState({});
    const [lineChartByYear, setline<PERSON><PERSON>By<PERSON>ear] = useState({});
    const [lineChartByMonth, setlineChartByMonth] = useState({});
    const [isLoading, setIsLoading] = useState(false)
    useEffect(() => {
        fetchReport()
    }, [])
    const fetchReport = async () => {
        setIsLoading(true)
        const response = await userOverallReports()
        setIsLoading(false)
        // generate bar and pie for results 
        console.log("response", response);
        if (response?.data) {
            let data = response?.data
            const pieChartData = [
                data.newsingups,
                data.onhold,
                data.activeusers,
                data.inactiveusers,
                data.paiduser,
                data.freeusers,
                // ... (other variables for the pie chart)
            ];

            // Labels for the pie chart
            const pieChartLabels = [
                'New Signups',
                'On Hold',
                'Active Users',
                "Inactive Users",
                'Paid Users',
                "Free Users"
                // ... (labels for other variables)
            ];

            // Data for the academy-wise bar chart
            const academyData = data.academywiseusers;
            const academyLabels = Object.keys(academyData);
            const academyValues = Object.values(academyData)?.map(arr => arr.length);

            // Data for city-wise and country-wise bar charts
            const cityWiseData = data.citywise;
            const countryWiseData = data.countrywise;
            const cityLabels = Object.keys(cityWiseData);
            const cityValues = Object.values(cityWiseData)?.map(arr => arr.length);
            const countryLabels = Object.keys(countryWiseData);
            const countryValues = Object.values(countryWiseData)?.map(arr => arr.length);
            const groupbyyearsData = data.groupbyyears
            console.log("Object.entries(groupbyyearsData)",Object.entries(groupbyyearsData),groupbyyearsData)

            const groupbyyeardatalengthonly = Object.entries(data?.groupbyyears)?.map(e => e[1]?.length)
    const groupbymonthsData=data.groupbymonths

            setPieChartdata(
                {
                    labels: pieChartLabels,
                    datasets: [{
                        data: pieChartData,
                        backgroundColor: [
                            'rgba(36, 99, 132, 0.2)',
                            'rgba(54, 162, 235, 0.2)',
                            'rgba(128, 0, 128, 0.2)',
                            'rgba(255, 206, 86, 0.2)',
                            'rgba(75, 192, 192, 0.2)',
                            'rgba(125, 159, 64, 0.2)',
                        ],
                        borderColor: [
                            'rgba(36, 99, 132, 0.2)',
                            'rgba(54, 162, 235, 0.2)',
                            'rgba(128, 0, 128, 0.2)',
                            'rgba(255, 206, 86, 0.2)',
                            'rgba(75, 192, 192, 0.2)',
                            'rgba(125, 159, 64, 0.2)',
                            // Add more colors as needed...
                        ],
                        borderWidth: 1
                    }],
                }
            )
            setChartData({
                labels: academyLabels,
                datasets: [{
                    label: 'Number of Users',
                    data: academyValues,
                    backgroundColor: [
                        'rgba(36, 99, 132, 0.2)',
                        'rgba(54, 162, 235, 0.2)',
                        'rgba(128, 0, 128, 0.2)',
                        'rgba(255, 206, 86, 0.2)',
                        'rgba(75, 192, 192, 0.2)',
                        'rgba(125, 159, 64, 0.2)',
                        // Add more colors as needed...
                    ],
                    borderColor: [
                        'rgba(36, 99, 132, 0.2)',
                        'rgba(54, 162, 235, 0.2)',
                        'rgba(128, 0, 128, 0.2)',
                        'rgba(255, 206, 86, 0.2)',
                        'rgba(75, 192, 192, 0.2)',
                        'rgba(125, 159, 64, 0.2)',
                        // Add more colors as needed...
                    ],
                    borderWidth: 1,
                }],
            });
            setCountryChart(
                {
                    labels: countryLabels,
                    datasets: [{
                        label: 'Number of Users per Country',
                        data: countryValues,
                        backgroundColor: [
                            'rgba(36, 99, 132, 0.2)',
                            'rgba(54, 162, 235, 0.2)',
                            'rgba(128, 0, 128, 0.2)',
                            'rgba(255, 206, 86, 0.2)',
                            'rgba(75, 192, 192, 0.2)',
                            'rgba(125, 159, 64, 0.2)',
                            // Add more colors as needed...
                        ],
                        borderColor: [
                            'rgba(36, 99, 132, 0.2)',
                            'rgba(54, 162, 235, 0.2)',
                            'rgba(128, 0, 128, 0.2)',
                            'rgba(255, 206, 86, 0.2)',
                            'rgba(75, 192, 192, 0.2)',
                            'rgba(125, 159, 64, 0.2)',
                            // Add more colors as needed...
                        ],
                        borderWidth: 1,
                    }],
                }
            )
            setCityChart({
                labels: cityLabels,
                datasets: [{
                    label: 'Number of Users per city',
                    data: cityValues,
                    backgroundColor: [
                        'rgba(36, 99, 132, 0.2)',
                        'rgba(54, 162, 235, 0.2)',
                        'rgba(128, 0, 128, 0.2)',
                        'rgba(255, 206, 86, 0.2)',
                        'rgba(75, 192, 192, 0.2)',
                        'rgba(125, 159, 64, 0.2)',
                        // Add more colors as needed...
                    ],
                    borderColor: [
                        'rgba(36, 99, 132, 0.2)',
                        'rgba(54, 162, 235, 0.2)',
                        'rgba(128, 0, 128, 0.2)',
                        'rgba(255, 206, 86, 0.2)',
                        'rgba(75, 192, 192, 0.2)',
                        'rgba(125, 159, 64, 0.2)',
                        // Add more colors as needed...
                    ],
                    borderWidth: 1,
                }],
            });
            setlineChartByYear(
                {
                    labels: Object.entries(groupbyyearsData),
                    datasets: [{
                      label: 'No of users',
                      data: groupbyyeardatalengthonly,
                      borderColor: 'blue',
                      borderWidth: 1,
                      lineTension: 0.1,
                    }]  
                  }
            )

            setlineChartByMonth( {
                labels: ['Jan','Feb','March','April','May','Jun','July','Aug', 'Sep', 'Oct','Nov','Dec'], // Update with appropriate month labels
                datasets: [{
                  label: 'Users in Month',
                  data: [groupbymonthsData["1"]?.length,groupbymonthsData["2"]?.length,groupbymonthsData["3"]?.length,
                  groupbymonthsData["4"]?.length,groupbymonthsData["5"]?.length,groupbymonthsData["6"]?.length
                  ,groupbymonthsData["7"]?.length,groupbymonthsData["8"]?.length,groupbymonthsData["9"]?.length,
                  groupbymonthsData["10"]?.length,groupbymonthsData["11"]?.length,groupbymonthsData["12"]?.length],
                  borderColor: 'red',
                  borderWidth: 1
                }]
              })
        }




    }


    return (
        <div>
            <Header />
            <div className="grid grid-cols-1 xl:grid-cols-5 items-start gap-x-4"></div>
            <div style={{ fontSize: "18px", background: "#FFEADC", width: "100%", padding: "10px" }}>
            <div style={{ marginTop: "100px", fontWeight:"600", fontSize:"20px" }}>
            Over All Reports
            </div>
</div>
          
            {isLoading ? (
                <LinearProgress />
            ): (
                <>
                {pieChartData?.datasets?.length > 0
                    &&
                    <Grid container spacing={2} className='p-6'>
                        <Grid item xs={12} sm={6} >
                            <PieChar chartData={pieChartData} showWidth={"60%"} displayLegend={false} />
                        </Grid>
                        {/*
                    <Grid item xs={12} sm={6} >
                            <BarChart chartData={chartData} showWidth={"80%"} displayLegend={false} />
                        </Grid>
                    */}
                        <Grid item xs={12} sm={6} >
                            <BarChart chartData={cityChart} showWidth={"80%"} displayLegend={false} />
                        </Grid>
                        <Grid item xs={12} sm={6} >
                            <BarChart chartData={countryChart} showWidth={"80%"} displayLegend={false} />
                        </Grid>
                        <Grid item xs={12} sm={6} >
                            <LineBar chartData={lineChartByYear} showWidth={"80%"} displayLegend={false} />
                        </Grid>
                        <Grid item xs={12} sm={6} >
                            <LineBar chartData={lineChartByMonth} showWidth={"80%"} displayLegend={false} />
                        </Grid>
    
                        
    
                    </Grid>
                }
                </>
            )}
            

        </div>
    )
}
export default OverAllReports
