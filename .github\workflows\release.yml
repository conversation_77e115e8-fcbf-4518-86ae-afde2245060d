name: Release - Build & Deploy (https://fit.yoska.in/)

on:
  workflow_dispatch:
  push:
    branches:
      - release

env:
  SDR_SSH_KEY: ${{ secrets.KEY }}
  SDR_SSH_HOST_USER: ${{ secrets.USERNAME }}
  SDR_SSH_HOST: ${{ secrets.HOST }}
  NODE_VERSION: 20
  DOCKER_IMAGE: ghcr.io/yoska-technology-solutions/yofit-frontend
  IMAGE_TAG: release-latest

permissions:
  pull-requests: write
  contents: read
  actions: read
  deployments: write
  packages: write

jobs:
  build:
    name: Build
    runs-on: ubuntu-latest
    timeout-minutes: 15
    steps:
      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}

      - name: Check out Git repository
        uses: actions/checkout@v4

      - name: Install Doppler CLI
        uses: dopplerhq/cli-action@v3

      - name: Log in to GitHub Docker Registry
        uses: docker/login-action@v3.3.0
        with:
          registry: ghcr.io
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Build Docker image
        run: |
          mv docker/prod/Dockerfile ./Dockerfile
          doppler run -- envsubst < .env.example > .env
          cat .env
          ls -al
          docker pull ${{ env.DOCKER_IMAGE }}:${{ env.IMAGE_TAG }} || true
          docker build --build-arg DOPPLER_TOKEN=$DOPPLER_TOKEN --build-arg BUILDKIT_INLINE_CACHE=1 \
            --cache-from ${{ env.DOCKER_IMAGE }}:${{ env.IMAGE_TAG }} \
            -t ${{ env.DOCKER_IMAGE }}:${{ env.IMAGE_TAG }} \
            -t ${{ env.DOCKER_IMAGE }}:${{ github.sha }} \
            .
        env:
          DOPPLER_TOKEN: ${{ secrets.DOPPLER_TOKEN }}

      - name: Push Docker images
        run: |
          docker push ${{ env.DOCKER_IMAGE }}:${{ env.IMAGE_TAG }}
          docker push ${{ env.DOCKER_IMAGE }}:${{ github.sha }}

  deploy:
    name: Deploy
    runs-on: ubuntu-latest
    needs: [build]
    timeout-minutes: 10
    steps:
      - name: Check out Git repository
        uses: actions/checkout@v4

      - name: Install ssh key
        run: |
          install -m 600 -D /dev/null ~/.ssh/id_rsa
          echo "${{ secrets.KEY }}" > ~/.ssh/id_rsa
          ssh-keyscan -H ${{ secrets.HOST }} > ~/.ssh/known_hosts

      - name: Connect and deploy
        run: |
          set -e  # Exit on any error
          export DOCKER_HOST=ssh://${{ secrets.USERNAME }}@${{ secrets.HOST }}

          echo ${{ secrets.GITHUB_TOKEN }} | docker login ghcr.io -u ${{ github.actor }} --password-stdin

          cd docker/prod
          ls -al

          docker compose down --rmi all
          docker compose pull
          docker compose up -d

          docker container prune -f
          docker image prune -a --force --filter "until=24h"
