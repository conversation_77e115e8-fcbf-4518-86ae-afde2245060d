import { <PERSON><PERSON>, Box, Toolbar, Typography } from '@mui/material'
import React from 'react'
import { useDispatch, useSelector } from "react-redux";
import KeyboardBackspaceIcon from '@mui/icons-material/KeyboardBackspace';
import '../../styles/navbar.css'
import MoreVertIcon from '@mui/icons-material/MoreVert';
import { useNavigate } from 'react-router-dom';
import Swal from 'sweetalert2';
import {
    serverTimestamp,
    onValue,
    query,
    limitToLast,
    orderByKey,
    orderByChild,
    equalTo,
    ref,
    update,
    get,
    set,
    remove
} from "firebase/database";
import {db} from "../../../../API/firebase.config";
import { getCurrentUserChatsAction } from "../../redux/action/userChats";
import DeleteOutlineIcon from '@mui/icons-material/DeleteOutline'; 
import PubNub from 'pubnub';
import axios from 'axios';
import { URL } from "../../../../../src/API/api-endpoint";


export default function NavGroupChat({ groupId, setOpenPage, currentUser, backButtonPath, profilePicture, groupName, membersPallate, isSearchActive, setIsSearchActive, searchKeyword, setSearchKeyword }) {
    const navigate = useNavigate()
    const dispatch = useDispatch();
    const handleGoBack = () => navigate(backButtonPath)
    const { openedGroupData } = useSelector((state) => state.group);

    const userRole = parseInt(localStorage.getItem("roleID"));

    const handleSetIsSearch = async () => {
        await setIsSearchActive(!isSearchActive ? true : false);
        const inputElement = document.getElementById('search-msg-input');
        if (inputElement && !isSearchActive) {
            inputElement.focus();
        } else {
            // Clear search keyword when closing search
            setSearchKeyword('');
        }
    };

    const handleOpenSectionThree = () => {
        setOpenPage("group-chat")
        if (window.matchMedia("(max-width: 430px)").matches) {
            const sectionThree = document.querySelector('.section-three');
            const sectionTwoMobile = document.querySelector('.section-two-mobile');
            if (sectionThree) {
                sectionThree.style.display = 'block';
            }
            if (sectionTwoMobile) {
                sectionTwoMobile.style.display = 'none';
            }
        }
    }

    const pubnub = new PubNub({
        publishKey: process.env.REACT_APP_CHAT_PUBLISH_KEY,
        subscribeKey: process.env.REACT_APP_CHAT_SUBSCRIBE_KEY,
        // uuid: 'user-' + Math.random().toString(36).substring(2, 9),
        uuid: currentUser.uid,  
      });
    
    const handleDeleteGroupChat = async (groupId) => {
        const result = await Swal.fire({
          title: 'Are you sure?',
          text: "You won't be able to revert this!",
          icon: 'warning',
          showCancelButton: true,
          confirmButtonColor: '#3085d6',
          cancelButtonColor: '#d33',
          confirmButtonText: 'Yes, delete it!'
        });
      
        if (result.isConfirmed) {
          try {
            const authToken = localStorage.getItem("token");
            let id = groupId.toString();
            const response = await axios.delete(`${URL}/group-chat/${id}`,{
                headers: {
                  'Content-Type': 'application/json',
                  'Authorization': `${authToken}`
                }
              });
            if (response.status === 200) {
                const chatRef = ref(db, `chats/${groupId}`);
                await remove(chatRef);
                console.log("Group chat deleted successfully.");
                // Optionally, remove the group information as well
                const groupRef = ref(db, `groups/${groupId}`);
                await remove(groupRef);
                dispatch(getCurrentUserChatsAction(currentUser?.uid));
                // setOpenPage("");
                pubnub.unsubscribe({
                    channels: [openedGroupData.groupDetail.channelName],
                });
                console.log("Group information deleted successfully.");
        
                Swal.fire(
                'Deleted!',
                'The group chat has been deleted.',
                'success'
                ).then(() => window.location.reload());
            }
          } catch (error) {
            console.error("Error deleting group chat:", error);
            Swal.fire(
              'Error!',
              'There was an error deleting the group chat.',
              'error'
            );
          }
        }
    };
    
    const handleShowPrevPage = () => { 
        setOpenPage("")
        if (window.matchMedia("(max-width: 430px)").matches) { 
            const mobileSection = document.querySelector('.section-two-mobile'); 
            const homeSection = document.querySelector('.section-one'); if (mobileSection) {  
                mobileSection.style.display = 'none'; } if (homeSection) {  
                    homeSection.style.display = 'block'; } } };
    return <>
        <Toolbar className="appBar">
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
                {/* <KeyboardBackspaceIcon onClick={() => handleGoBack()} /> */}
                {window.matchMedia("(max-width: 430px)").matches && <KeyboardBackspaceIcon
                    onClick={(e) => {
                    handleShowPrevPage();
                    }}
                    sx={{ cursor: "pointer", color: "orange", fontSize: "23px", marginLeft: "12px" }}
                />}
                <Avatar 
                onClick={window.matchMedia("(max-width: 430px)").matches ? () => handleOpenSectionThree() : undefined} 
                alt="Remy Sharp" sx={{ height: 33, width: 33, marginLeft: '8px' }} src={profilePicture} />
                {
                    !isSearchActive
                        ? <Box
                        //  onClick={() => navigate('/group-information')}
                         >
                            <Typography className='userName' fontWeight="fontWeightBold" sx={{ fontSize: '14px', paddingLeft: '1.2rem', lineHeight: 1.3 }}>{groupName}</Typography>
                            <Box sx={{ display: 'flex', justifyContent: "center", alignItems: 'center', flexDirection: 'row' }} s>
                                <Typography sx={{ paddingLeft: '1.2rem', fontSize: '11px', color: "#707070", display: 'inline-block' }}>
                                    {membersPallate?.slice(0, 62)}...
                                </Typography>
                            </Box>
                        </Box>
                        : <Box style={{ position: 'relative', textAlign: "center" }}>
                            <input
                                id='search-msg-input'
                                value={searchKeyword}
                                onChange={e => setSearchKeyword(e.target.value)}
                                placeholder='Search Buddies' type="text" className='search-message-input'
                            />
                        </Box>
                }
            </Box>
            <Box style={{ display: 'flex', justifyContent: "center", alignItems: "center", width: "3.5rem" }}>
                {(userRole === 1 || userRole === 3) && <DeleteOutlineIcon
                    onClick={(e) => {
                    e.stopPropagation();
                    handleDeleteGroupChat(groupId);
                    }}
                    sx={{ cursor: "pointer", color: "orange",fontSize:"23px", marginLeft:"12px" }}
                />}
            </Box>
        </Toolbar >
    </>
}
