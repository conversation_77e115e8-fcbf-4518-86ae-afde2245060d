import React, { useEffect, useState } from "react";
import DialogTitle from "@mui/material/DialogTitle";
import Dialog from "@mui/material/Dialog";
import { Line } from "react-chartjs-2";
import "chart.js/auto";
import moment from "moment";
import CloseIcon from "@mui/icons-material/Close";
import HeartBrokenIcon from "@mui/icons-material/HeartBroken";
import { BsFillHeartPulseFill } from "react-icons/bs";
import Paper from "@mui/material/Paper";
import Table from "@mui/material/Table";
import TableBody from "@mui/material/TableBody";
import TableContainer from "@mui/material/TableContainer";
import TableHead from "@mui/material/TableHead";
import TableRow from "@mui/material/TableRow";
import Grid from "@mui/material/Grid";
import TableCell, { tableCellClasses } from "@mui/material/TableCell";
import { styled } from "@mui/material/styles";
import CadenceImage from '../../../Images/cadenceIcon.jpg';
import "./simpleDialog.css";
import Tooltip from '@mui/material/Tooltip';
import ChatWithCoach from "./chatWithCoach.jsx";
import Box from '@mui/material/Box';

import VolunteerActivismIcon from "@mui/icons-material/VolunteerActivism";
import { getCadenceData } from "../../../API/api-endpoint";
import { CircularProgress, FormControl, MenuItem, OutlinedInput, Select, ToggleButton, ToggleButtonGroup } from "@mui/material";
import { calculatePace, calculateSpeed, calculateSpeedKmHr } from "../../../utils/Resubale";
import Feedback from "./Feedback";

import DistanceMetric from "./ActivityMetric/DistanceMetric.jsx";
import DurationMetric from "./ActivityMetric/DurationMetric.jsx";
import PaceMetric from "./ActivityMetric/PaceMetric.jsx";
import SpeedMetric from "./ActivityMetric/SpeedMetric.jsx";
import { parseSpeed, timeToDecimalMinutes, timeToHours } from "../../../utils/metricConversion.js";


const selectPaceArray = [{ name: "Cadence", value: "cadence" }, { name: "Elevation", value: "altitude" },
{ name: "Pace", value: "pace" },
{ name: "Power", value: "power" },
{ name: "Heart Rate", value: "heart" },
]
const selectSpeedArray = [{ name: "Cadence", value: "cadence" }, { name: "Elevation", value: "altitude" },
{ name: "Speed", value: "speed" },
{ name: "Power", value: "power" },
{ name: "Heart Rate", value: "heart" },

]
const StyledTableCell = styled(TableCell)(({ theme }) => ({
  [`&.${tableCellClasses.head}`]: {
    backgroundColor: "white",
    color: theme.palette.common.black,
    border: "1px solid #dbd6d6"
  },
  [`&.${tableCellClasses.body}`]: {
    fontSize: 14,
  },
}));
const StyledTableRow = styled(TableRow)(({ theme }) => ({
  "&:nth-of-type(odd)": {
    backgroundColor: theme.palette.action.hover,
    border: "1px solid #dbd6d6"

  },
  // hide last border
  "&:last-child td, &:last-child th": {
    border: "1px solid #dbd6d6"

  },
}));

const SimpleDialog = ({ selectedValue, assignedCocahId, onClose, open }) => {
  let activityData = JSON.parse(selectedValue?.extendedProps?.activity_json);
  const [graphCadenceData, setGraphCadenceData] = useState({});
  const [changeName, setChangeName] = useState("cadence");
  const [isLoading, setLoading] = useState(false);
  const [graphXLevel, setGraphXlevel] = useState("distance");
  const roleID = localStorage.getItem("roleID");

  let runningCadence = activityData?.average_cadence ? activityData?.average_cadence * 2 : "Not applicable";
  let allCadence = activityData?.average_cadence
    ? activityData?.average_cadence
    : "Not applicable";

  const [graphaltitudeData, setGraphaltitudeData] = useState({});
  const [graphPowerData, setGraphPowerData] = useState({});
  const [graphPace, setGraphPace] = useState({});
  const [graphHeart, setGraphHeart] = useState({});

  const [graphSpeed, setGraphSpeed] = useState({});
  const [speedY, setSpeedY] = useState([]);
  const [yAxisOptions, SetyAxisOptions] = useState({
    min: 0,
    max: speedY, // You can adjust the max value as needed
  }
  );


  const [distanceMetric, setDistanceMetric] = useState(null);
  const [durationMetric, setDurationMetric] = useState(null);
  const [paceMetric, setPaceMetric] = useState(null);
  const [speedMetric, setSpeedMetric] = useState(null);


  let findTyep = selectedValue?.extendedProps?.badge?.split(".")[0];

  const workoutId = selectedValue?._def?.publicId;
  const activityId = selectedValue?.extendedProps?.strava_id;

  const workoutCreatedDate = selectedValue?.extendedProps?.createdAt;
  const now = moment();

  const actualDistanceVal = selectedValue?.extendedProps?.actual_workout ? selectedValue?.extendedProps?.actual_workout : 0
  const actualDurationVal = timeToHours(selectedValue?.extendedProps?.actual_duration ? selectedValue?.extendedProps?.actual_duration : "00:00:00");
  const actualPaceVal = timeToDecimalMinutes(selectedValue?.extendedProps?.actualPace ? selectedValue?.extendedProps?.actualPace : "00:00:00");
  const actualSpeedVal = parseSpeed(selectedValue?.extendedProps?.actual_speed ? selectedValue?.extendedProps?.actual_speed : "00.00");

  const activityType = selectedValue?.extendedProps?.badge?.split(".")[0];

  useEffect(() => {
    if (selectedValue?.id && selectedValue?.extendedProps?.strava_id) {
      getCadence(selectedValue?.extendedProps?.strava_id)
    }
  }, [selectedValue?.id, selectedValue?.extendedProps?.process]);

  useEffect(() => {
    console.log("----- selected value -----");
    console.log(selectedValue);
    getActivityMetric();
  }, [selectedValue || activityType]);

  const getActivityMetric = () => {
    console.log("----- inside get-activity-metric -----");
    if (selectedValue) {
      // Distance value
      const plannedDistance = selectedValue?.extendedProps?.workout_planned_distance ? selectedValue?.extendedProps?.workout_planned_distance : 0;
      const actualDistance = selectedValue?.extendedProps?.actual_workout ? selectedValue?.extendedProps?.actual_workout : 0;
      const distanceUnit = selectedValue?.extendedProps?.distanceunit ? selectedValue?.extendedProps?.distanceunit.toLowerCase() : "km";
      const distance = {
        plannedDistance,
        actualDistance,
        distanceUnit
      };
      setDistanceMetric(distance);

      // Duration value
      const plannedDuration = selectedValue?.extendedProps?.workout_planned_duration ? selectedValue?.extendedProps?.workout_planned_duration : "00:00:00";
      const actualDuration = selectedValue?.extendedProps?.actual_duration ? selectedValue?.extendedProps?.actual_duration : "00:00:00";
      const durationUnit = selectedValue?.extendedProps?.distanceunit ? selectedValue?.extendedProps?.durationunit.toLowerCase() : "hr";
      const duration = {
        plannedDuration,
        actualDuration,
        durationUnit
      };
      setDurationMetric(duration);

      if (activityType === "cycling") {
        // Speed value
        const plannedSpeed = (selectedValue?.extendedProps?.planned_speed || selectedValue?.extendedProps?.planned_speed !== 0) ? selectedValue?.extendedProps?.planned_speed : "00.00";
        const actualSpeed = selectedValue?.extendedProps?.actual_speed ? selectedValue?.extendedProps?.actual_speed : "00.00";
        const speedunit = selectedValue?.extendedProps?.speedunit ? selectedValue?.extendedProps?.speedunit.toLowerCase() : "km/h";
        const speed = {
          plannedSpeed,
          actualSpeed,
          speedunit
        };
        setSpeedMetric(speed);
      } else {
        // Pace value
        const plannedPace = selectedValue?.extendedProps?.workout_planned_pace ? selectedValue?.extendedProps?.workout_planned_pace : "00:00:00";
        const actualPace = selectedValue?.extendedProps?.actualPace ? selectedValue?.extendedProps?.actualPace : "00:00:00";
        const paceUnit = selectedValue?.extendedProps?.paceunit ? selectedValue?.extendedProps?.paceunit.toLowerCase() : "min/km";
        const pace = {
          plannedPace,
          actualPace,
          paceUnit
        };
        setPaceMetric(pace);
      }
    }
  }

  const renderDistanceMetric = () => {
    return (
      <>
        {
          distanceMetric &&
          <div className="w-full md:w-3/4 lg:w-2/3">
            <DistanceMetric activity={activityType} data={distanceMetric} key={workoutId} />
          </div>
        }
      </>
    )
  };

  const renderDurationMetric = () => {
    return (
      <>
        {
          durationMetric &&
          <div className="w-full md:w-3/4 lg:w-2/3">
            <DurationMetric activity={activityType} data={durationMetric} key={workoutId} />
          </div>
        }
      </>
    )
  };

  const renderPaceMetric = () => {
    return (
      <>
        {paceMetric &&
          <div className="w-full md:w-3/4 lg:w-2/3">
            <PaceMetric activity={activityType} data={paceMetric} key={workoutId} />
          </div>}
      </>
    )
  };

  const renderSpeedMetric = () => {
    return (
      <>
        {
          speedMetric &&
          <div className="w-full md:w-3/4 lg:w-2/3">
            <SpeedMetric activity={activityType} data={speedMetric} key={workoutId} />
          </div>
        }
      </>
    )
  };

  const getCadence = async (id, alignment, xAccess) => {
    setSpeedY([])
    setLoading(true)
    const response = await getCadenceData(id, assignedCocahId)
    setLoading(false)
    if (response?.status) {
      const timeArray = response?.data?.time?.data || [];
      const distanceArray = response?.data?.distance?.data || [];
      const paceResult = calculatePace(response?.data?.velocity_smooth?.data, response?.data?.distance?.data);
      const speedResultResult = calculateSpeed(response?.data?.time?.data, response?.data?.distance?.data);
      let runningCadenceGraphValue = findTyep == "running" ? response?.data?.cadence?.data.map(value => value * 2) : response?.data?.cadence?.data;
      let speepYinKmHr = calculateSpeedKmHr(response?.data?.velocity_smooth?.data)
      console.log("speedResultResult", xAccess, paceResult);
      if (xAccess == "altitude") {
        setSpeedY(response?.data?.altitude?.data)
        SetyAxisOptions(
          {
            min: 0,
            max: response?.data?.altitude?.data, // You can adjust the max value as needed
          }
        )

      } else if (xAccess == "power") {
        setSpeedY(response?.data?.watts?.data)
        SetyAxisOptions(
          {
            min: 0,
            max: response?.data?.watts?.data, // You can adjust the max value as needed
          }
        )

      }
      else if (xAccess == "speed") {
        setSpeedY(speepYinKmHr)
        SetyAxisOptions(
          {
            min: 0,
            max: speepYinKmHr, // You can adjust the max value as needed
          }
        )

      } else if (xAccess == "pace") {
        setSpeedY(response?.data?.velocity_smooth?.data)
        console.log("Math.max(...response?.data?.velocity_smooth?.data)", Math.max(...response?.data?.velocity_smooth?.data));
        SetyAxisOptions(
          {
            type: 'linear', // Use a linear scale for numerical values
            min: 0,
            max: paceResult.length > 0 ? Math.max(...response?.data?.velocity_smooth?.data) : 22,
            ticks: {
              callback: function (value, index, values) {
                const valuuRetrn = calculatePace(value)
                // Convert numerical value back to time format (HH:mm)
                const hours = Math.floor(value / 60);
                const minutes = value % 60;
                console.log("value", value, valuuRetrn);
                return valuuRetrn;
              }
            },
            // sort: 'desc',
            // reverse: true,
          }
        )

      } else
        if (xAccess == "heart") {

          setSpeedY(response?.data?.heartrate?.data)
          SetyAxisOptions(
            {
              min: 0,
              max: response?.data?.heartrate?.data, // You can adjust the max value as needed
            }
          )
        } else {
          SetyAxisOptions(
            {
              min: 0,
              max: runningCadenceGraphValue, // You can adjust the max value as needed
            }
          )
          setSpeedY(runningCadenceGraphValue)


        }
      let defineArray = alignment ? alignment : graphXLevel
      let selectGraph = defineArray === "distance" ? distanceArray.map(value => value / 1000) : timeArray.map(value => value / 60);

      // let speed = (distance(i) - distance(i - 1)) * 18 / 5
      // let pace = (1 / 3600) / (difference in distance / 1000)
      // let swimming = (1 / 3600) / (difference in distance) * 100

      setGraphCadenceData({
        labels: selectGraph,
        datasets: [{
          label: 'Cadence',
          // fill: true,
          data: runningCadenceGraphValue,
          backgroundColor: "#d473d4",
          borderColor: "#d473d4",
          borderWidth: 1,
          // tension: 0.1,
          // pointStyle: "none",
          pointRadius: 0,

        }]
      })
      setGraphHeart({
        labels: selectGraph,
        datasets: [{
          label: 'Heart Rate',
          // fill: true,
          data: response?.data?.heartrate?.data,
          backgroundColor: "#FC6B6E",
          borderColor: "#FC6B6E",
          borderWidth: 1,
          pointRadius: 0,


        }]
      })
      setGraphaltitudeData({
        labels: selectGraph,
        datasets: [{
          label: 'Elevation',
          // fill: true,
          data: response?.data?.altitude?.data,
          backgroundColor: "#00ab66",
          borderColor: "#00ab66",
          borderWidth: 1,
          pointRadius: 0,


        }]
      })
      setGraphPowerData({
        labels: selectGraph,
        datasets: [{
          label: 'Power',
          // fill: true,
          data: response?.data?.watts?.data,
          backgroundColor: "rgb(230, 126, 34)",
          borderColor: "rgb(230, 126, 34)",
          borderWidth: 1,
          pointRadius: 0,


        }]
      })
      if (paceResult?.length > 0) {
        setGraphPace({
          labels: selectGraph,
          datasets: [{
            label: 'Pace',
            // fill: true,
            data: response?.data?.velocity_smooth?.data,
            backgroundColor: "rgb(37 99 235 / 70%)",
            borderColor: "rgb(37 99 235 / 70%)",
            borderWidth: 1,
            pointRadius: 0,


          }]
        })
      }
      if (speedResultResult?.length > 0) {
        setGraphSpeed({
          labels: selectGraph,
          datasets: [{
            label: 'Speed',
            // fill: true,
            data: speepYinKmHr,
            backgroundColor: "rgb(37 99 235 / 70%)",
            borderColor: "rgb(37 99 235 / 70%)",
            borderWidth: 1,
            pointRadius: 0,


          }]
        })
      }


    }

  }

  const options = {
    scales: {
      x: {
        type: 'linear',
        position: 'bottom',
        title: {
          display: true,
          text: graphXLevel === 'distance' ? `${findTyep == 'swimming' ? 'Distance (Strokes)' : 'Distance (Km)'}` : 'Time (minutes)',
        },
      },
      y: yAxisOptions,
    },
  };
  const handleChange = (event, newAlignment) => {
    console.log("newAlignment:", newAlignment, event.target.value);

    setGraphXlevel(newAlignment)
    getCadence(selectedValue?.extendedProps?.strava_id, newAlignment)
    // setAlignment(newAlignment);
  };
  const control = {
    value: graphXLevel,
    onChange: handleChange,
    exclusive: true,
  };

  const [isOpen, setIsOpen] = useState(false);
  return (
    <Dialog
      maxWidth="md"
      minWidth="sm"
      onClose={() => onClose(false)}
      open={open}
      PaperProps={{
        className: 'thin-scrollbar max-h-[calc(100%-150px)] overflow-y-auto'
      }}
    >
      <div className="flex justify-between">
        <DialogTitle className=" max-w-[85%] break-words whitespace-normal">
          {selectedValue?.extendedProps?.workout} |{" "}
          {moment(selectedValue?.start).format("dddd, DD-MMM-YYYY")}
        </DialogTitle>
        <DialogTitle className=" cursor-pointer" onClick={() => onClose(false)}>
          {" "}
          <CloseIcon />
        </DialogTitle>


      </div>
      {selectedValue?.extendedProps?.strava_id &&
        (<div >
          <div className="p-4">
            <Grid container spacing={2} alignItems="stretch" justifyContent="space-evenly">
              <Grid item md={6} sm={6} lg={6} xs={12} sx={{ paddingLeft: "10px" }}>
                <FormControl fullWidth>
                  <Select
                    placeholder="Distance"
                    name="activity"
                    value={changeName}
                    onChange={(e) => {
                      setChangeName(e.target.value)
                      getCadence(selectedValue?.extendedProps?.strava_id, "", e.target.value)
                    }}
                    id="form-layouts-separator-select"
                    labelId="form-layouts-separator-select-label"
                    input={<OutlinedInput id="select-multiple-language" />}
                  >
                    {findTyep != "cycling" ?
                      selectPaceArray?.map((value, index) => {
                        return (
                          <MenuItem value={value?.value}>
                            {value?.name}
                          </MenuItem>
                        );
                      })
                      : selectSpeedArray?.map((value, index) => {
                        return (
                          <MenuItem value={value?.value}>
                            {value?.name}
                          </MenuItem>
                        );
                      })}
                  </Select>
                </FormControl>
              </Grid>
              <Grid item md={4} sm={4} lg={4} xs={12} sx={{ paddingLeft: "10px" }}>
                <ToggleButtonGroup size="small" {...control} aria-label="Small sizes">
                  <ToggleButton
                    style={{
                      backgroundColor: graphXLevel === 'distance' ? 'rgb(230, 126, 34)' : 'inherit',
                      color: graphXLevel === 'distance' ? 'white' : 'inherit',
                    }}
                    value="distance" key="distance">
                    By Distance
                  </ToggleButton>,
                  <ToggleButton
                    style={{
                      backgroundColor: graphXLevel === 'time' ? 'rgb(230, 126, 34)' : 'inherit',
                      color: graphXLevel === 'time' ? 'white' : 'inherit',
                    }}
                    value="time" key="time">
                    By Time
                  </ToggleButton>
                </ToggleButtonGroup>
              </Grid>
              {roleID == 5 &&
                <Grid md={2} sm={2} lg={2} xs={12} sx={{ paddingLeft: "10px" }}>
                  <div>
                    <Tooltip title="Chat with Coach" placement="top">
                      <button
                        type="button"
                        className="btn_chat_with_coach text-white hover:bg-yellow-500 focus:outline-none focus:ring-4 focus:ring-yellow-300 font-medium rounded-full text-sm px-2.5 py-2.5 text-center dark:focus:ring-yellow-900"
                        onClick={() => setIsOpen(!isOpen)}
                      >
                        <svg
                          className="w-6 h-6 text-gray-100 dark:text-white chat_with_coach"
                          aria-hidden="true"
                          xmlns="http://www.w3.org/2000/svg"
                          width="24"
                          height="24"
                          fill="none"
                          viewBox="0 0 24 24"
                        >
                          <path
                            stroke="currentColor"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth="2"
                            d="M9 17h6l3 3v-3h2V9h-2M4 4h11v8H9l-3 3v-3H4V4Z"
                          />
                        </svg>
                      </button>
                    </Tooltip>
                    {isOpen && (
                      <ChatWithCoach isOpen={isOpen} setIsOpen={setIsOpen} />
                    )}
                  </div>
                </Grid>
              }
            </Grid>
          </div>
          {isLoading ? (<CircularProgress className="m-6" />) : <>
            {changeName === "cadence" &&
              <>
                {graphCadenceData?.labels?.length > 0 ?
                  <Box sx={{
                    paddingLeft: "30px",
                    width: {
                      xs: '300px',
                      sm: '400px',
                      md: '500px',
                      lg: '600px'
                    },
                    height: {
                      xs: '130px',
                      sm: '200px',
                      md: '200px',
                      lg: '300px'
                    },
                    maxWidth: '100%'
                  }}>
                    <Line data={graphCadenceData} options={options} />
                  </Box>
                  : <div className="text-[20px] p-2 font-semibold">No Data Found {changeName?.charAt(0).toUpperCase() + changeName?.slice(1)} </div>
                }
              </>
            }
            {changeName === "altitude" &&
              <>
                {graphaltitudeData?.labels?.length > 0 ?
                  <Box sx={{
                    paddingLeft: "30px",
                    width: {
                      xs: '300px',
                      sm: '400px',
                      md: '500px',
                      lg: '600px'
                    },
                    height: {
                      xs: '130px',
                      sm: '200px',
                      md: '200px',
                      lg: '300px'
                    },
                    maxWidth: '100%'
                  }}>
                    <Line data={graphaltitudeData} options={options} />
                  </Box>
                  : <div className="text-[20px] p-2 font-semibold">No Data Found {changeName?.charAt(0).toUpperCase() + changeName?.slice(1)}</div>
                }
              </>
            }
            {changeName === "power" &&
              <>
                {graphPowerData?.labels?.length > 0 ?
                  <Box sx={{
                    paddingLeft: "30px",
                    width: {
                      xs: '300px',
                      sm: '400px',
                      md: '500px',
                      lg: '600px'
                    },
                    height: {
                      xs: '130px',
                      sm: '200px',
                      md: '200px',
                      lg: '300px'
                    },
                    maxWidth: '100%'
                  }}>
                    <Line data={graphPowerData} options={options} />
                  </Box>
                  : <div className="text-[20px] p-2 font-semibold">No Data Found {changeName?.charAt(0).toUpperCase() + changeName?.slice(1)}</div>
                }
              </>
            }

            {changeName === "pace" &&
              <>
                {graphPace?.labels?.length > 0 ?
                  <Box sx={{
                    paddingLeft: "30px",
                    width: {
                      xs: '300px',
                      sm: '400px',
                      md: '500px',
                      lg: '600px'
                    },
                    height: {
                      xs: '130px',
                      sm: '200px',
                      md: '200px',
                      lg: '300px'
                    },
                    maxWidth: '100%'
                  }}>
                    <Line data={graphPace} options={options} />
                  </Box>
                  : <div className="text-[20px] p-2 font-semibold">No Data Found {changeName?.charAt(0).toUpperCase() + changeName?.slice(1)}</div>
                }
              </>
            }
            {changeName === "speed" &&
              <>
                {graphSpeed?.labels?.length > 0 ?
                  <Box sx={{
                    paddingLeft: "30px",
                    width: {
                      xs: '300px',
                      sm: '400px',
                      md: '500px',
                      lg: '600px'
                    },
                    height: {
                      xs: '130px',
                      sm: '200px',
                      md: '200px',
                      lg: '300px'
                    },
                    maxWidth: '100%'
                  }}>
                    <Line data={graphSpeed} options={options} />
                  </Box>
                  : <div className="text-[20px] p-2 font-semibold">No Data Found {changeName?.charAt(0).toUpperCase() + changeName?.slice(1)}</div>
                }
              </>
            }
            {changeName === "heart" &&
              <>
                {graphHeart?.labels?.length > 0 ?
                  <Box sx={{
                    paddingLeft: "30px",
                    width: {
                      xs: '300px',
                      sm: '400px',
                      md: '500px',
                      lg: '600px'
                    },
                    height: {
                      xs: '130px',
                      sm: '200px',
                      md: '200px',
                      lg: '300px'
                    },
                    maxWidth: '100%'
                  }}>
                    <Line data={graphHeart} options={options} />
                  </Box>
                  : <div className="text-[20px] p-2 font-semibold">No Data Found {changeName?.charAt(0).toUpperCase() + changeName?.slice(1)}</div>
                }
              </>
            }
          </>}
        </div>)
      }
      {
        ((moment(workoutCreatedDate).isBefore(now) || !moment(workoutCreatedDate).isAfter(now)) &&
          (actualDistanceVal > 0 || actualDurationVal > 0 || actualPaceVal > 0 || actualSpeedVal > 0)) &&
        <Grid item md={12} sm={12} lg={12} xs={12} sx={{ paddingLeft: "10px" }}>
          <Feedback
            workoutId={activityId ? activityId : workoutId}
            workoutType={activityId ? "strava" : "manual"} />
        </Grid>
      }
      {findTyep == "note" ? (<div className="p-4 border-2 border-[#d2caca] m-[7px] rounded-[10px] shadow-[0_15px_25px_rgba(0,0,0,0.15),0_5px_10px_rgba(0,0,0,0.05)]"
      >
        <label dangerouslySetInnerHTML={{
          __html: selectedValue?.extendedProps?.description,
        }}></label>
      </div>) : (
        <div>
          <div className="p-4 border-2 border-[#d2caca] m-[7px] rounded-[10px] shadow-[0_15px_25px_rgba(0,0,0,0.15),0_5px_10px_rgba(0,0,0,0.05)]"
          >
            <label dangerouslySetInnerHTML={{
              __html: selectedValue?.extendedProps?.description,
            }}></label>
          </div>

          {findTyep != "nutrition" &&
            <div className="flex text-sm">
              <p className="flex items-center">
                {" "}
                &nbsp;
                <BsFillHeartPulseFill style={{ color: "red" }} /> Average Heart Rate:{" "}
                {activityData?.average_heartrate
                  ? activityData?.average_heartrate
                  : "Not applicable"}
              </p> &nbsp;&nbsp;&nbsp;
              <p className="flex items-center">
                &nbsp;
                <BsFillHeartPulseFill style={{ color: "red" }} /> Max Heart Rate:
                {activityData?.max_heartrate
                  ? activityData?.max_heartrate
                  : "Not applicable"}
              </p> &nbsp; &nbsp;
              <p style={{ display: "flex", alignItems: "center" }}>
                &nbsp;
                <div>

                  <img style={{ outline: 'none', width: "45px" }} src={CadenceImage} className="p-3 d-block img" alt="Slide 3" />
                </div>
                <div>

                  Average Cadence:{" "}
                  {findTyep == "running" ? runningCadence : allCadence}
                </div>
              </p>
            </div>
          }
          {/* ------------------------------------------------------------------------------------------------------------------- */}

          &nbsp;
          {/* displaying activity metric */}
          <div className="flex flex-row justify-start flex-wrap md:flex-nowrap gap-1 p-2">
            {renderDistanceMetric()}
            {renderDurationMetric()}
            {(activityType === "cycling") ? renderSpeedMetric() : renderPaceMetric()}
          </div>
        </div>
      )}
    </Dialog>
  );
};

export default SimpleDialog;
