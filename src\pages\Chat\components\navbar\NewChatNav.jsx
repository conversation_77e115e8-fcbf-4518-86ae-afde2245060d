import { Box, Fade, FormControlLabel, IconButton, Toolbar, Typography } from '@mui/material'
import React, { useState } from 'react'
import KeyboardBackspaceIcon from '@mui/icons-material/KeyboardBackspace';
import '../../styles/navbar.css'
import Switch from '@mui/material/Switch';
import SearchIcon from '@mui/icons-material/Search';
import MoreVertIcon from '@mui/icons-material/MoreVert';

export default function NewChatNav() {
    const [open, setopen] = useState(false)
    const [checked, setchecked] = useState(false)

    return <>
        <Toolbar className="appBar">
            <Box sx={{ display: 'flex' }}>
                <KeyboardBackspaceIcon />
                <Typography className='chatTitle' style={{ paddingLeft: "1rem", fontSize: "16px" }}>Select Buddies to Chat</Typography>
            </Box>
            <Box>
                <img style={{ paddingRight: '0.5rem' }} height={20} src="/images/info-icon.png" alt="info" />
            </Box>
        </Toolbar >
    </>
}
