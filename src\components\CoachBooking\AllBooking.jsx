import React, { useEffect, useState } from "react";
import Paper from "@mui/material/Paper";
import Table from "@mui/material/Table";
import TableBody from "@mui/material/TableBody";
import TableContainer from "@mui/material/TableContainer";
import TableHead from "@mui/material/TableHead";
import TableRow from "@mui/material/TableRow";
import Grid from "@mui/material/Grid";
import TableCell, { tableCellClasses } from "@mui/material/TableCell";
import { styled } from "@mui/material/styles";
import { Box } from "@mui/system";
import { FormLabel, MenuItem, OutlinedInput, TextField } from "@mui/material";
import { useNavigate } from "react-router";
import { getAllBookingData, createPostLeaderBoard, approveBooking, cancelBooking } from "../../API/api-endpoint";
import Header from "../Header";
import { Button, Modal } from "antd";
import { showError, showSuccess } from "../Messages";
import moment from "moment";

const StyledTableCell = styled(TableCell)(({ theme }) => ({
  [`&.${tableCellClasses.head}`]: {
    backgroundColor: "#1e40af",
    color: theme.palette.common.white,
  },
  [`&.${tableCellClasses.body}`]: {
    fontSize: 14,
  },
}));
const StyledTableRow = styled(TableRow)(({ theme }) => ({
  "&:nth-of-type(odd)": {
    backgroundColor: theme.palette.action.hover,
  },
  // hide last border
  "&:last-child td, &:last-child th": {
    border: 0,
  },
}));

const AllBooking = ({ slotID }) => {
  const navigate = useNavigate();

  const [getList, setGetList] = useState()
  const [selectChallenge, setSelectChallenge] = useState()
  const [fetchLeaderBoard, setFetchLeaderBoard] = useState()
  const [isOpenModal, setIsOpenModal] = useState({ isOpen: false, data: {} })


  useEffect(() => {
    allAtheletes()

  }, [])
  console.log("getList", isOpenModal);
  const allAtheletes = async () => {

    const response = await getAllBookingData()
    setGetList(response)
    console.log("response", response);
  }

  const ApprovSlots = async (id) => {
    const response = await approveBooking(id)
    if (response?.status) {
      showSuccess(response?.message)
      setIsOpenModal({ isOpen: false, data: null })
    } else {
      showError(response?.message)
    }
  }
  const CancelSlots = async (id) => {
    const response = await cancelBooking(id)
    if (response?.status) {
      showSuccess(response?.message)
      setIsOpenModal({ isOpen: false, data: null })
    } else {
      showError(response?.message)
    }
  }
  return (
    <div className="zones-form-container absolute top-20 w-[100vw]">
      <Header />
      <div
        className="zones-form-main"
        style={{ fontSize: "2rem", textAlign: "center" }}
      >
        All Booking
      </div>

      <Grid xs={12} md={12}>
        <div className="table-section">
          <div style={{padding:"10px", background:"#FFEADC"}}>
            <Grid container>
              <Grid item xs={12} md={9} sm={9} sx={{ textAlign: "end" }}>
              </Grid>
              <Grid item xs={12} md={3} sm={3} sx={{ textAlign: "end" }}>
                <Button type="primary" onClick={() => navigate(-1)}>Back</Button>
              </Grid>
            </Grid>
          </div>
          <div className="zone-table">
            <TableContainer component={Paper}>

              <Table sx={{ minWidth: 700, padding: "10px" }} aria-label="customized table">
                <TableHead>
                  <TableRow>
                    <StyledTableCell align="left">Sr NO</StyledTableCell>
                    <StyledTableCell align="left">Coach Name</StyledTableCell>
                    <StyledTableCell align="left">User Name</StyledTableCell>
                    <StyledTableCell align="left">Date </StyledTableCell>
                    <StyledTableCell align="left">Start Time </StyledTableCell>
                    <StyledTableCell align="left">End Time </StyledTableCell>
                    <StyledTableCell align="left">Action</StyledTableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {getList?.length > 0 ? (
                    <>
                      {getList?.map((row, index) => (
                        <StyledTableRow key={row?.subscription?.subscription_id}>
                          <StyledTableCell align="left">
                            {index + 1}
                          </StyledTableCell>
                          <StyledTableCell align="left">
                            {row?.coach?.firstname} {row?.coach?.lastname}

                          </StyledTableCell>
                          <StyledTableCell align="left">
                            {row?.user?.firstname} {row?.user?.lastname}

                          </StyledTableCell>
                          <StyledTableCell align="left">
  {moment(row?.date).format("DD-MM-YYYY")}
</StyledTableCell>

                          <StyledTableCell align="left">
                            {row?.start_time}

                          </StyledTableCell>
                          <StyledTableCell align="left">
                            {row?.end_time}
                          </StyledTableCell>
                          <StyledTableCell align="left">
                            <Button type="primary" onClick={() => setIsOpenModal({ isOpen: true, data: row })}>
                              View Details
                            </Button>
                          </StyledTableCell>
                        </StyledTableRow>
                      ))}
                    </>
                  ) : (
                    <div className="p-4">No data found</div>
                  )}
                </TableBody>
              </Table>
            </TableContainer>
          </div>

        </div>
      </Grid>

      <Modal
        title="Booking Details"
        centered
        open={isOpenModal?.isOpen}
        onOk={() => setIsOpenModal({ isOpen: false, data: {} })}
        onCancel={() => setIsOpenModal({ isOpen: false, data: {} })}
        footer={[
          <Button key="cancel" onClick={() => setIsOpenModal({ isOpen: false, data: {} })}>
            Close
          </Button>,
          <Button key="book" type="primary" onClick={() => ApprovSlots(isOpenModal?.data?.id)}>
            Approve
          </Button>,
          <Button key="book" type="primary" danger onClick={() => CancelSlots(isOpenModal?.data?.id)}>
            Reject
          </Button>,
        ]}
      >
        <div style={{ borderBottom: "1px solid gray", paddingBottom: "24px" }}>
          <div style={{ display: "flex" }}>
            <h3 style={{ fontWeight: 600 }}>Coach Name:</h3>&nbsp;
            <h3>{isOpenModal?.data?.coach?.firstname} {isOpenModal?.data?.coach?.lastname}</h3>
          </div>
          <div style={{ display: "flex" }}>
            <h3 style={{ fontWeight: 600 }}>User Name:</h3> &nbsp;
            <h3>{isOpenModal?.data?.user?.firstname} {isOpenModal?.data?.user?.lastname}</h3>
          </div>
          <div style={{ display: "flex" }}>
            <h3 style={{ fontWeight: 600 }}>Start Date :</h3>&nbsp;
            <h3>{isOpenModal?.data?.date}</h3>
          </div>
          <div style={{ display: "flex" }}>
            <h3 style={{ fontWeight: 600 }}>Start Time :</h3>&nbsp;
            <h3>{isOpenModal?.data?.start_time}</h3>
          </div>
          <div style={{ display: "flex" }}>
            <h3 style={{ fontWeight: 600 }}>End Time :</h3> &nbsp;
            <h3>{isOpenModal?.data?.end_time}</h3>
          </div>
          <div style={{ display: "flex" }}>
            <h3 style={{ fontWeight: 600 }}>Status :</h3> &nbsp;
            <h3>{isOpenModal?.data?.status == 1 ? "Approve" : "Reject"}</h3>
          </div>
        </div>
      </Modal>


    </div>
  );
};
export default AllBooking
