import { useState, useEffect } from "react";
import { Button } from "../ui/button";
import { Input } from "../ui/input";
import { Label } from "../ui/label";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "../ui/dialog";
import {
	createActivityTrackdata,
	updateActivityTrackdata,
	uploadsaveFile,
	URL,
} from "../../API/api-endpoint";
import Swal from "sweetalert2";

export const ActivityTrackDialog = ({
	open,
	onClose,
	onSuccess,
	editingItem,
}) => {
	const [formData, setFormData] = useState({
		activity_track: "",
		badge: "",
	});
	const [isLoading, setIsLoading] = useState(false);
	const [previewImage, setPreviewImage] = useState(null);
	const [selectedFile, setSelectedFile] = useState(null);

	useEffect(() => {
		if (open) {
			if (editingItem?.id) {
				const editData = {
					activity_track: editingItem.activity_track || "",
					badge: editingItem.badge || "",
				};
				setFormData(editData);
				setSelectedFile(null);

				if (editingItem.badge) {
					setPreviewImage(
						`${URL}/static/public/userimages/${editingItem.badge}`
					);
				} else {
					setPreviewImage(null);
				}
			} else {
				const newData = {
					activity_track: "",
					badge: "",
				};
				setFormData(newData);
				setSelectedFile(null);
				setPreviewImage(null);
			}
		}
	}, [open, editingItem]);

	const handleInputChange = (field, value) => {
		setFormData((prev) => ({
			...prev,
			[field]: value,
		}));
	};

	const handleFileChange = async (e) => {
		const file = e.target.files[0];
		if (file) {
			setSelectedFile(file);

			const reader = new FileReader();
			reader.onload = (e) => {
				setPreviewImage(e.target.result);
			};
			reader.readAsDataURL(file);

			try {
				const formData = new FormData();
				formData.append("file", file);

				const response = await uploadsaveFile(formData);
				if (response?.status) {
					setFormData((prev) => ({
						...prev,
						badge: response.file,
					}));
				}
			} catch (error) {
				console.error("Error uploading file:", error);
				Swal.fire({
					title: "Error",
					text: "Failed to upload image. Please try again.",
					icon: "error",
					timer: 3000,
					showConfirmButton: false,
				});
			}
		}
	};

	const handleSubmit = async (e) => {
		e.preventDefault();

		if (!formData.activity_track.trim()) {
			Swal.fire({
				title: "Error",
				text: "Activity track name is required",
				icon: "error",
				timer: 1800,
				showConfirmButton: false,
			});
			return;
		}

		if (!editingItem?.id && !formData.badge) {
			Swal.fire({
				title: "Error",
				text: "Badge image is required",
				icon: "error",
				timer: 1800,
				showConfirmButton: false,
			});
			return;
		}

		try {
			setIsLoading(true);

			const apiData = {
				activity_track: formData.activity_track.trim(),
				badge: formData.badge,
			};

			let response;
			if (editingItem?.id) {
				apiData.id = editingItem.id;
				response = await updateActivityTrackdata(apiData);
			} else {
				response = await createActivityTrackdata(apiData);
			}

			if (response?.status) {
				Swal.fire({
					title: "Success",
					text:
						response.message ||
						`Activity track ${
							editingItem?.id ? "updated" : "created"
						} successfully`,
					icon: "success",
					timer: 2000,
					showConfirmButton: false,
				});
				onSuccess();
			} else {
				Swal.fire({
					title: "Error",
					text: response?.message || "Failed to save activity track",
					icon: "error",
					timer: 3000,
					showConfirmButton: false,
				});
			}
		} catch (error) {
			console.error("Error saving activity track:", error);
			Swal.fire({
				title: "Error",
				text: "An error occurred while saving the activity track",
				icon: "error",
				timer: 3000,
				showConfirmButton: false,
			});
		} finally {
			setIsLoading(false);
		}
	};

	return (
		<Dialog open={open} onOpenChange={onClose}>
			<DialogContent className='sm:max-w-2xl bg-white max-h-[90vh] overflow-y-auto'>
				<DialogHeader>
					<DialogTitle className='text-lg font-semibold text-gray-900'>
						{editingItem?.id
							? "Edit Activity Track"
							: "Create Activity Track"}
					</DialogTitle>
				</DialogHeader>

				<form onSubmit={handleSubmit} className='space-y-4'>
					<div className='grid gap-4'>
						<div className='space-y-2'>
							<Label
								htmlFor='activity_track'
								className='text-sm font-semibold'
							>
								Activity Track Name{" "}
								<span className='text-red-500'>*</span>
							</Label>
							<Input
								id='activity_track'
								className='w-full text-sm'
								value={formData.activity_track}
								onChange={(e) =>
									handleInputChange(
										"activity_track",
										e.target.value
									)
								}
								placeholder='Enter activity track name'
								disabled={isLoading}
								required
							/>
						</div>

						<div className='space-y-2'>
							<Label
								htmlFor='badge'
								className='text-sm font-semibold'
							>
								Badge Image{" "}
								{!editingItem?.id && (
									<span className='text-red-500'>*</span>
								)}
							</Label>
							<Input
								id='badge'
								type='file'
								accept='image/*'
								className='w-full text-sm'
								onChange={handleFileChange}
								disabled={isLoading}
								required={!editingItem?.id}
							/>
							{previewImage && (
								<div className='mt-2'>
									<img
										src={previewImage}
										alt='Badge Preview'
										className='w-20 h-20 rounded-full border-2 object-cover'
									/>
								</div>
							)}
						</div>
					</div>

					<div className='flex justify-end gap-3 pt-4'>
						<Button
							type='button'
							variant='outline'
							onClick={onClose}
							disabled={isLoading}
						>
							Cancel
						</Button>
						<Button
							type='submit'
							className='bg-orange-600 hover:bg-orange-700 text-white'
							disabled={isLoading}
						>
							{isLoading
								? "Saving..."
								: editingItem?.id
								? "Update Activity Track"
								: "Create Activity Track"}
						</Button>
					</div>
				</form>
			</DialogContent>
		</Dialog>
	);
};
