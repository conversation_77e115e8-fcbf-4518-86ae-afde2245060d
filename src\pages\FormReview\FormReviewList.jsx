import React, { useEffect, useMemo, useState } from "react";
import Paper from "@mui/material/Paper";
import Table from "@mui/material/Table";
import TableBody from "@mui/material/TableBody";
import TableContainer from "@mui/material/TableContainer";
import TableHead from "@mui/material/TableHead";
import TableRow from "@mui/material/TableRow";
import TableCell, { tableCellClasses } from "@mui/material/TableCell";
import { styled } from "@mui/material/styles";
import { Button, CircularProgress, FormLabel, Grid, Pagination, TextField } from "@mui/material";
import "../../components/AssignCoach/Assign.css";
import { ExclamationCircleFilled } from '@ant-design/icons';
import Header from "../../components/Header";
import { deleteLevel, deletePrograms, fetchSavedAssesmnetGetDataCoach,  } from "../../API/api-endpoint";
import { IconEdit, IconTrash } from "@tabler/icons";
import { Modal } from "antd";
import CreateLevel from "../../components/FeedData/CreateLevel";
import MenuItem from "@mui/material/MenuItem";
import moment from "moment";
import { Link, useNavigate } from "react-router-dom";

// let PageSize = 15;
const StyledTableCell = styled(TableCell)(({ theme }) => ({
    [`&.${tableCellClasses.head}`]: {
        backgroundColor: "#1e40af",
        color: theme.palette.common.white,
    },
    [`&.${tableCellClasses.body}`]: {
        fontSize: 14,
    },
}));
const StyledTableRow = styled(TableRow)(({ theme }) => ({
    "&:nth-of-type(odd)": {
        backgroundColor: theme.palette.action.hover,
    },
    // hide last border
    "&:last-child td, &:last-child th": {
        border: 0,
    },
}));
const FormReviewList = () => {
  const navigate = useNavigate()
    const [reportData, setReportData] = useState()
    const [editData, setEditData] = useState()
    const [isLoading, setIsLoading] = useState(true)
    const [searchTerm, setSearchTerm] = useState('');
    const [showAssesmentModal, setShowAssesmentModal] = useState(false);

    console.log("reportData", reportData);

    const fetchReport = async () => {
        const response = await fetchSavedAssesmnetGetDataCoach()
        console.log("response", response);
        setIsLoading(false)

        setReportData(response?.newModified)
    }
    useEffect(() => {
        fetchReport()
    }, [])
    const [currentPage, setCurrentPage] = useState(1);
    const [selectedActivityFilter, setSelectedActivityFilter] = useState('All');


    const filteredList = useMemo(() => {
        return reportData?.filter((row) => {
                return (
                    row?.athelete?.firstname?.toLowerCase().includes(searchTerm.toLowerCase()) 
                );
        });
    }, [reportData, selectedActivityFilter, searchTerm]);
    let PageSize = searchTerm || selectedActivityFilter !== 'All' ? filteredList?.length : 15;
    const checkLastPage = useMemo(() => {
        let frstPgae = (currentPage - 1) * PageSize;
        let lastPage = frstPgae + PageSize;
        return filteredList?.slice(frstPgae, lastPage)?.map((row, index) => ({
            ...row,
            srID: index + 1 + (currentPage > 1 ? frstPgae : 0),
        }));
    }, [currentPage, reportData,filteredList]);
    const handlePageChange = (event, page) => {
        setCurrentPage(page);
    };

 

      const handleSearchChange = (event) => {
        setSearchTerm(event.target.value);
        setCurrentPage(1); // Reset the current page when the search term changes
    };
    
    return (
        <div>
            <Header />
            <div className="grid grid-cols-1 xl:grid-cols-5 items-start gap-x-4"></div>
            <div style={{ marginTop: "100px", padding: "20px" }}>
                <TableContainer component={Paper}>
                    <div style={{ fontSize: "18px", background: "#FFEADC", width: "100%", padding: "10px" }}>
                        <Grid container spacing={2}>
                            <Grid item xs={12} sm={4} sx={{ marginTop: "30px" }}>
                           
                            </Grid>
                            <Grid item xs={12} sm={6} sx={{ textAlign: "end",marginTop: "30px" }}>
</Grid>
                            <Grid item xs={12} sm={2} sx={{ textAlign: "end",marginTop: "30px" }}>
                            <TextField type="text" size="small" value={searchTerm} onChange={handleSearchChange} placeholder="Search By Activity.." />
                        </Grid>
                        </Grid>
                    </div>
                    <TableContainer style={{ maxHeight: 550,scrollbarWidth:"none" }}>
                    <Table stickyHeader sx={{ minWidth: 700,}} aria-label="customized table">
                        <TableHead>
                            <TableRow>
                                <StyledTableCell align="left">Sr ID</StyledTableCell>
                                <StyledTableCell align="left">Activity Name</StyledTableCell>
                                <StyledTableCell align="left">Athelete Name</StyledTableCell>
                                <StyledTableCell align="left">Submited at</StyledTableCell>
                                <StyledTableCell align="left">Reviewed at </StyledTableCell>
                                <StyledTableCell align="left">Action</StyledTableCell>



                            </TableRow>
                        </TableHead>
                        <TableBody>
                        {isLoading?(
                            <CircularProgress className="m-6" />):(
                                <>
                            {checkLastPage?.length > 0 ? (
                                <>
                                    {checkLastPage?.map((row, index) => (
                                        <StyledTableRow key={index}>
                                            <StyledTableCell align="left">
                                                {row?.srID}
                                            </StyledTableCell>
                                            <StyledTableCell align="left">
                                                {row?.activity?.activity_name}
                                            </StyledTableCell>
                                            <StyledTableCell align="left">
                                                {row?.athelete?.firstname}

                                            </StyledTableCell>
                                            <StyledTableCell align="left">
                                                {moment(row?.createdAt).format('MM-DD-YYYY')}

                                            </StyledTableCell>
                                            {row?.createdAt!=row?.updatedAt
                                            ?
                                            <StyledTableCell align="left">
                                                {moment(row?.updatedAt).format('MM-DD-YYYY')}

                                            </StyledTableCell>
                                            :
                                            <StyledTableCell align="left">
                                            NA
                                        </StyledTableCell>}
                                            <StyledTableCell align="left">
                                            <div className="flex ">
                                            <span className="px-2 cursor-pointer">
                                            <Button variant="contained" onClick={() =>navigate(`/savedreview?id=${row?.id}`)}>View</Button>
                                              
                                            </span>
                                            {row?.reviewed_by &&
                                            <span className="px-2 cursor-pointer">
                                              <Button variant="contained" disabled>Completed</Button>
                                            </span>
                                            }
                                          </div>

                                            </StyledTableCell>
                                        </StyledTableRow>
                                    ))}
                                </>
                            ) : (
                                <div className="p-4">No data found</div>
                            )}
                            </>
                            )}
                        </TableBody>
                    </Table>
                    </TableContainer>
                </TableContainer>
                &nbsp;
                <div className="flex justify-end">
                    <Pagination
                        count={Math.ceil(filteredList?.length / PageSize)} // Calculate total number of pages
                        color="primary"
                        page={currentPage}
                        onChange={handlePageChange}
                    />
                </div>
            </div>
            {
                <CreateLevel setEditData={setEditData} editData={editData} fetchReport={fetchReport} showAssesmentModal={showAssesmentModal} setShowAssesmentModal={setShowAssesmentModal} />}
        </div>
    )
}
export default FormReviewList
