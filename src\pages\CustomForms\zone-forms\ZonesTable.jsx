import React from "react";
import {
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableHeader,
	TableRow,
} from "../../../components/ui/table";

const ZonesTable = ({ title, unit, data }) => {
	return (
		<div className='bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden h-full'>
			<div className='bg-orange-50 p-4 border-b border-orange-100'>
				<h3 className='text-lg font-semibold text-orange-900 m-0'>
					{title}
				</h3>
				<p className='text-sm text-gray-700 m-0'>{unit}</p>
			</div>

			<div className='overflow-x-auto'>
				<Table className='w-full table-auto'>
					<TableHeader className='bg-gray-50'>
						<TableRow>
							<TableHead className='text-gray-500 text-xs uppercase tracking-wider w-1/6'>
								Zone
							</TableHead>
							<TableHead className='text-gray-500 text-xs uppercase tracking-wider w-2/5'>
								Name
							</TableHead>
							<TableHead className='text-right text-gray-500 text-xs uppercase tracking-wider w-2/5'>
								Range ({unit})
							</TableHead>
						</TableRow>
					</TableHeader>
					<TableBody>
						{data.map((zone) => (
							<TableRow
								key={zone.key}
								className='hover:bg-orange-50 transition'
							>
								<TableCell className='font-medium text-gray-900 whitespace-nowrap'>
									{zone.zone}
								</TableCell>
								<TableCell className='text-gray-700 break-words'>
									{zone.name}
								</TableCell>
								<TableCell className='text-right text-gray-700 whitespace-nowrap'>
									{zone.range}
								</TableCell>
							</TableRow>
						))}
					</TableBody>
				</Table>
			</div>
		</div>
	);
};

export default ZonesTable;
