import React, { useEffect, useState } from 'react'
import Header from '../components/Header'
import { Button } from 'antd'
import { Card, CardContent, FormLabel, Grid, MenuItem, OutlinedInput, Select, TextField, useTheme } from '@mui/material'
import { useFormik } from 'formik'
import Swal from 'sweetalert2'
import { getAllPrograms, registerCoach } from '../API/api-endpoint'
import SlickCarousel from './SlickCarousel'
import InputLabel from '@mui/material/InputLabel';

const ITEM_HEIGHT = 48;
const ITEM_PADDING_TOP = 8;
const MenuProps = {
  PaperProps: {
    style: {
      maxHeight: ITEM_HEIGHT * 4.5 + ITEM_PADDING_TOP,
      width: 250,
      overflowY: 'scroll'
    },
  },
};


function getStyles(name, personName, theme) {
  return {
    fontWeight:
      personName?.indexOf(name) === -1
        ? theme?.typography?.fontWeightRegular
        : theme?.typography?.fontWeightMedium,
  };
}
const RegisterCoach = () => {
  const theme = useTheme();

  const [reportData, setReportData] = useState()

  const formik = useFormik({
    initialValues: {
      firstname: "",
      lastname: "",
      email: "",
      password: "",
      coachcommission: "",
      coachtype: "",

    }, validate: (values) => {
      const errors = {};
      if (!values.firstname) {
        errors.firstname = "First name is required";
      }
      if (!values.lastname) {
        errors.lastname = "Last name is required";
      }
      if (!values.email) {
        errors.email = "Email is required";
      } else if (!/^\S+@\S+\.\S+$/.test(values.email)) {
        errors.email = "Invalid email address";
      }

      if (!values.password) {
        errors.password = "Password is required";
      } else if (
        !/^(?=.*[a-zA-Z])(?=.*\d)(?=.*[\W_])\S{8,}$/.test(
          values.password
        )
      ) {
        errors.password =
          "Password must be at least 8 characters long and contain a character, number, and a special character without spaces";
      }
      if (!values.coachcommission) {
        errors.coachcommission = "Coach commission is required";
      } else if (values.coachcommission <= 0) {
        errors.coachcommission = "Coach commission must be greater than 0";
      }
      else if (values.coachcommission > 100) {
        errors.coachcommission = "Coach commission must be less than or equal to 100";
      }
      if (!values.coachtype) {
        errors.coachtype = "Coach type is required";
      }
      return errors;
    },
    // validationSchema: {},
    onSubmit: (values, { resetForm }) => {
      handleSubmitAssesmentForm(values, resetForm)

    },
  });

  console.log("formik", formik?.values);
  const handleSubmitAssesmentForm = async (data, resetForm) => {
    let response = ""
    let allDatadata = { ...data, role: 3 }
    response = await registerCoach(allDatadata)

    if (response?.status) {
      Swal.fire({
        title: "Success",
        text: response.message,
        icon: "success",
      });
      // fetchReport()
      resetForm()
      formik?.resetForm()
      formik?.setValues({ firstname: "", lastname: "", email: "", password: "", coachcommission: "", coachtype: "" })
    } else {
      Swal.fire({
        title: "Error",
        text: response.message,
        icon: "error",
      });
    }
    console.log("response", response);
  }
  const fetchReport = async () => {
    const response = await getAllPrograms()
    console.log("response?.rows", response?.rows);
    setReportData(response?.rows)
  }
  useEffect(() => {
    fetchReport()
  }, [])
  const handleUserChange = async (event, newValue, name) => {
    formik.setFieldValue(name, newValue)
  };
  return (
    <div>
      <Header />
      <div className="grid grid-cols-1 xl:grid-cols-5 items-start gap-x-4 " style={{ marginTop: "100px" }}>

      </div>
      <div className='p-4 m-[2% 6% 2% 6%]'>
        <div className='flex justify-center'>
          <Card sx={{ width: "1200px", margin: "2% 6% 2% 6%", boxShadow: "rgba(60, 64, 67, 0.3) 0px 1px 2px 0px, rgba(60, 64, 67, 0.15) 0px 2px 6px 2px" }}>
            <CardContent>
              <div className="headingCont">
                <span className="heading">Coach </span>{" "}
                <span className="orange heading">Registration</span>
              </div>
              <div className="parentCont">
                <form className='form1'>
                  <Grid container spacing={2}>
                    <Grid item xs={12} sm={11}>
                      <FormLabel >First Name:</FormLabel>
                      <TextField
                        fullWidth
                        placeholder="First Name"
                        size="small"
                        type="text"
                        name="firstname"
                        value={formik?.values?.firstname}
                        onChange={formik.handleChange}
                        error={formik.touched.firstname && formik.errors.firstname}
                        helperText={
                          formik.touched.firstname && formik.errors.firstname
                        }

                      />
                    </Grid>

                    <Grid item xs={12} sm={11}>
                      <FormLabel >Last Name:</FormLabel>
                      <TextField
                        fullWidth
                        placeholder="Last Name"
                        size="small"
                        type="text"
                        name="lastname"
                        value={formik?.values?.lastname}
                        onChange={formik.handleChange}
                        error={formik.touched.lastname && formik.errors.lastname}
                        helperText={
                          formik.touched.lastname && formik.errors.lastname
                        }

                      />
                    </Grid>

                    <Grid item xs={12} sm={5.5}>
                      <FormLabel >Email:</FormLabel>
                      <TextField
                        fullWidth
                        placeholder="Email"
                        size="small"
                        type="text"
                        name="email"
                        value={formik?.values?.email}
                        onChange={formik.handleChange}
                        error={formik.touched.email && formik.errors.email}
                        helperText={
                          formik.touched.email && formik.errors.email
                        }

                      />
                    </Grid>

                    <Grid item xs={12} sm={5.5}>
                      <FormLabel >Password:</FormLabel>
                      <TextField
                        fullWidth
                        placeholder="Password"
                        size="small"
                        type="text"
                        name="password"
                        value={formik?.values?.password}
                        onChange={formik.handleChange}
                        error={formik.touched.password && formik.errors.password}
                        helperText={
                          formik.touched.password && formik.errors.password
                        }

                      />
                    </Grid>

                    <Grid item xs={12} sm={5.5}>
                      <FormLabel >Coach Commission:</FormLabel>
                      <TextField
                        fullWidth
                        placeholder="Coach Commission"
                        size="small"
                        type="text"
                        name="coachcommission"
                        value={formik?.values?.coachcommission}
                        onChange={formik.handleChange}
                        error={formik.touched.coachcommission && formik.errors.coachcommission}
                        helperText={
                          formik.touched.coachcommission && formik.errors.coachcommission
                        }

                      />
                    </Grid>

                    <Grid item xs={12} sm={5.5} >
                      <FormLabel >Coach Type:</FormLabel>
                      <TextField
                        fullWidth
                        size="small"
                        select
                        name="coachtype"
                        value={formik?.values?.coachtype}
                        onChange={formik.handleChange}
                        error={formik.touched.coachtype && formik.errors.coachtype}
                        helperText={
                          formik.touched.coachtype && formik.errors.coachtype
                        }

                        id="form-layouts-separator-select"
                        labelId="form-layouts-separator-select-label"
                        input={<OutlinedInput id="select-multiple-language" />}
                      >
                        <MenuItem value={""} disabled>
                          Select Coach Type
                        </MenuItem>
                        <MenuItem value={"parttime"}>
                          Part Time
                        </MenuItem>
                        <MenuItem value={"fulltime"}>
                          Full Time
                        </MenuItem>

                      </TextField>
                    </Grid>
                    <Grid item xs={12} sm={11} >
                      <FormLabel >Program Name</FormLabel>
                      <Select
                        fullWidth
                        id="demo-multiple-name"
                        // value={personName}
                        onChange={(e) => formik?.setFieldValue("program_id", e.target.value)}
                        input={<OutlinedInput label="Name" />}
                        MenuProps={MenuProps}
                      >
                        {reportData?.map((name) => (
                          <MenuItem
                            key={name}
                            value={name?.program_id}
                            style={getStyles(name, formik?.values?.program_name, theme)}
                          >
                            {name?.program_name}
                          </MenuItem>))}
                      </Select>
                    </Grid>
                    <Grid item xs={12} md={3} sm={3} sx={{ textAlign: "left" }}>
                      <Button className="btn" key="submit" type="primary" onClick={() => formik.handleSubmit()}>Submit</Button>
                    </Grid>
                  </Grid>
                </form>
                <div className="slick-container">
                  <SlickCarousel />
                </div>
              </div>
            </CardContent></Card>
        </div>



      </div>
    </div>
  )
}

export default RegisterCoach
