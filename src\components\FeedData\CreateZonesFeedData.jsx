import {
    FormLabel,
    Grid,
    MenuItem,
    OutlinedInput,
    TextField,
  } from "@mui/material";
  import { <PERSON><PERSON>, Modal, TimePicker } from "antd";
  import React, { useEffect, useState } from "react";
  import {
    CreateZonesClasification,
    createOptiondata,
    getAllPrograms,
    getAllQuestionData,
    getAlllevels,
    updateOptiondata,
    updateZonesClasification,
  } from "../../API/api-endpoint";
  import { useFormik } from "formik";
  import Swal from "sweetalert2";
  import { handleTimeChange } from "../../utils/Resubale";
  import SlickCarousel from "../../pages/SlickCarousel";
  const CreateZonesFeedData = ({
    fetchReport,
    setShowAssesmentModal,
    showAssesmentModal,
    editData,
    setEditData,
  }) => {
    const [questionList, setQuestionList] = useState([]);
    const [programList, setProgramList] = useState([]);
    const [allLevelList, setAlllevelList] = useState([]);
    const [IsSubmit, setIsSubmit] = useState(false);

  
    console.log("editData", editData);
    const formik = useFormik({
      initialValues: {
        activity_id: "",
        distance: "",
        level_id: "",
        from_range: "",
        end_range: "",
      },
      validate: (values) => {
        const errors = {};
        if (!values.activity_id) {
          errors.activity_id = "Activity is required";
        }
        if (values.distance === "") {
          errors.distance = "Distance is required";
        } else if (values.distance <= 0) {
          errors.distance = "Distance must be greater than or equal to 0";
        }
        if (!values.level_id) {
          errors.level_id = "Level Name is required";
        }
        if (!values.from_range) {
          errors.from_range = "Start Range is required";
        }
        if (!values.end_range) {
          errors.end_range = "End Range is required";
        }
        return errors;
      },
      // validationSchema: {},
      onSubmit: (values, { resetForm }) => {
        handleSubmitAssesmentForm(values, resetForm);
      },
    });
    console.log("formik", formik?.values);
    const QuestionData = async () => {
      const response = await getAllQuestionData();
      console.log("response", response);
      setQuestionList(response);
    };
    useEffect(() => {
      QuestionData();
      getAllProgramsData();
      getAllLevelsData();
    }, []);
    const getAllProgramsData = async () => {
      const response = await getAllPrograms();
      setProgramList(response?.rows);
    };
    const getAllLevelsData = async () => {
      const response = await getAlllevels();
      console.log("response", response);
      setAlllevelList(response?.rows);
    };
  
    const handleSubmitAssesmentForm = async (data, resetForm) => {
      let response = "";
      setIsSubmit(true)
      if (editData?.id) {
        response = await updateZonesClasification(data);
      } else {
        response = await CreateZonesClasification(data);
      }
      if (response?.status) {
        Swal.fire({
          title: "Success",
          text: response.message,
          icon: "success",
        });
      setIsSubmit(false)

        setEditData({});
        setShowAssesmentModal(false);
        fetchReport();
        formik.resetForm();
        formik?.setValues({
          activity_id: "",
          distance: "",
          level_id: "",
          from_range: "",
          end_range: "",
        });
      } else {
        Swal.fire({
          title: "Error",
          text: response.message,
          icon: "error",
        });
      }
      console.log("response", response);
    };
    useEffect(() => {
      if (editData?.id) {
        const { srID,activity,level, ...data } = editData;
        formik?.setValues(data);
      }
    }, [editData?.id]);
  
    return (
      <Modal
        width={1200}
        open={showAssesmentModal}
        onCancel={() => {
          setShowAssesmentModal(false);
          formik.resetForm();
          setEditData({});
          formik?.setValues({
            activity_id: "",
            distance: "",
            level_id: "",
            from_range: "",
            end_range: "",
          });
        }}
        footer={
          <div></div>
          //   loading={isLoading}
        }
      >
        <div className="headingCont">
          <span className="heading">{editData?.id ? "Edit " : "Create"}</span>{" "}
          <span className="orange heading">Zones</span>
        </div>
        {/* <h1>{editData ? editData.challengeId : values.challengeId}</h1> */}
        <div className="parentCont">
          <form className="form1" onSubmit={formik.handleSubmit}>
            <Grid container spacing={2}>
              <Grid item xs={12} sm={11}>
                <FormLabel>Activity Name<span className="text-[red]">*</span></FormLabel>
  
                <TextField
                  fullWidth
                  size="small"
                  select
                  name="activity_id"
                  SelectProps={{
                    MenuProps: {
                      PaperProps: {
                        style: {
                           scrollbarColor:"#E67E22 white",
                           scrollbarWidth:"thin"
                        },
                      },
                    },
                  }}
                  value={formik?.values?.activity_id}
                  onChange={formik.handleChange}
                  error={formik.touched.activity_id && formik.errors.activity_id}
                  helperText={
                    formik.touched.activity_id && formik.errors.activity_id
                  }
                  id="form-layouts-separator-select"
                  labelId="form-layouts-separator-select-label"
                  input={<OutlinedInput id="select-multiple-language" />}
                >
                  <MenuItem value={""} disabled>
                    Select Activity
                  </MenuItem>
                  {programList?.map((value, index) => {
                    return (
                      <MenuItem value={value?.program_id}>
                        {value?.program_name}
                      </MenuItem>
                    );
                  })}
                </TextField>
              </Grid>
              <Grid item xs={12} sm={11}>
                <FormLabel>Distance<span className="text-[red]">*</span></FormLabel>
  
                <TextField
                  fullWidth
                  placeholder="distance"
                  size="small"
                  type="number"
                  name="distance"
                  value={formik?.values?.distance}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  error={formik.touched.distance && formik.errors.distance}
                  helperText={formik.touched.distance && formik.errors.distance}
                />
              </Grid>
              <Grid item xs={12} sm={11}>
                <FormLabel>Level Name<span className="text-[red]">*</span></FormLabel>
  
                <TextField
                  fullWidth
                  size="small"
                  select
                  name="level_id"
                  SelectProps={{
                    MenuProps: {
                      PaperProps: {
                        style: {
                           scrollbarColor:"#E67E22 white",
                           scrollbarWidth:"thin"
                        },
                      },
                    },
                  }}
                  value={formik?.values?.level_id ? formik?.values?.level_id : ""}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  error={formik.touched.level_id && formik.errors.level_id}
                  helperText={formik.touched.level_id && formik.errors.level_id}
                  id="form-layouts-separator-select"
                  labelId="form-layouts-separator-select-label"
                  input={<OutlinedInput id="select-multiple-language" />}
                >
                  <MenuItem value={""} disabled>
                    Select Level
                  </MenuItem>
                  {allLevelList?.map((value, index) => {
                    return <MenuItem value={value?.id}>{value?.level}</MenuItem>;
                  })}
                </TextField>
              </Grid>
  
              <Grid item xs={12} sm={11}>
                <FormLabel>Start Range<span className="text-[red]">*</span></FormLabel>
  
                <TextField
                  fullWidth
                  size="small"
                  name="from_range"
                  value={formik?.values?.from_range}
                  onBlur={formik.handleBlur}
                  onChange={(e) => {
                    const formattedTime = handleTimeChange(e);
                    formik.setFieldValue("from_range", formattedTime);
                  }}
                  error={formik.touched.from_range && formik.errors.from_range}
                  helperText={
                    formik.touched.from_range && formik.errors.from_range
                  }
                  placeholder="(hh:mm:ss)"
                />
              </Grid>
              <Grid item xs={12} sm={11}>
                <FormLabel>End Range<span className="text-[red]">*</span></FormLabel>
  
                <TextField
                  fullWidth
                  size="small"
                  name="end_range"
                  value={formik?.values?.end_range}
                  onBlur={formik.handleBlur}
                  onChange={(e) => {
                    const formattedTime = handleTimeChange(e);
                    formik.setFieldValue("end_range", formattedTime);
                  }}
                  error={formik.touched.end_range && formik.errors.end_range}
                  helperText={formik.touched.end_range && formik.errors.end_range}
                  placeholder="(hh:mm:ss)"
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <Button
                  className="btn"
                  key="submit"
                  type="primary"
                  disabled={IsSubmit}
                  onClick={() => formik.handleSubmit()}
                >
                  Submit
                </Button>
              </Grid>
            </Grid>
          </form>
          <div className="slick-container">
            <SlickCarousel />
          </div>
        </div>
      </Modal>
    );
  };
  export default CreateZonesFeedData;
  