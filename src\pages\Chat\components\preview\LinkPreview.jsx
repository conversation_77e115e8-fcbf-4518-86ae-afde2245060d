import React, { useEffect, useState } from 'react'
import { Card, CardActionArea, CardContent, CardMedia, Typography } from '@mui/material'
import Cookies from 'js-cookie';
import AOS from 'aos';
import 'aos/dist/aos.css';

const LinkPreview = ({ url, previewData, setPreviewData }) => {

    const getLinkPreview = async (url) => {
        const allOriginsUrl = `https://api.allorigins.win/get?url=${encodeURIComponent(url)}`;

        try {
            const response = await fetch(allOriginsUrl);
            const data = await response.json();
            const { contents } = data;

            // Parse the HTML string to extract the relevant data
            const parser = new DOMParser();
            const htmlDocument = parser.parseFromString(contents, 'text/html');

            const titleElement = htmlDocument.querySelector('head > title');
            const title = titleElement ? titleElement.textContent : '';

            const descriptionElement = htmlDocument.querySelector('meta[name="description"]');
            const description = descriptionElement ? descriptionElement.getAttribute('content') : '';

            const imageElement = htmlDocument.querySelector('meta[property="og:image"]');
            const image = imageElement ? imageElement.getAttribute('content') : '';

            // Return an object with the extracted data
            return {
                title,
                description,
                image,
            };
        } catch (error) {
            // Handle any errors that occurred during the request
            console.error(error);
            throw new Error('Failed to fetch link preview');
        }
    }

    const handleGetPreview = () => {
        getLinkPreview(url)
            .then(linkPreview => {
                // Use the linkPreview object containing the image, title, and description
                console.log(linkPreview);
                setPreviewData(linkPreview)
                Cookies.set(url, JSON.stringify(linkPreview))
            })
            .catch(error => {
                // Handle any errors that occurred during the request
                console.error(error);
                setPreviewData(null)
            });
    }

    useEffect(() => {
        if (Cookies.get(url)) {
            setPreviewData(JSON.parse(Cookies.get(url)))
        } else {
            handleGetPreview()
        }

    }, [url])

    useEffect(() => {
        AOS.init();
    }, [])

    return <>{
        previewData &&
        <Card data-aos="fade-up" sx={{ marginTop: '5rem', border: '0px solid black', width: '100%', boxShadow: '0px -4px 10px rgba(0, 0, 0, 0.1);' }
        }>
            <CardActionArea href={url} target="_blank">
                <CardContent>
                    {previewData.image && (
                        <img src={previewData.image} height={'150px'} alt="preview" />
                    )}
                    <Typography gutterBottom variant="body2" component="div">
                        {previewData.title}
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                        {`${previewData.description?.slice(0, 80)}...`}
                    </Typography>
                </CardContent>
            </CardActionArea>
        </Card >
    }
    </>
}

export default LinkPreview