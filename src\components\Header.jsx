import React, { useContext, useEffect, useState } from "react";
import { Link, useNavigate } from "react-router-dom";

import { Collapse, Drawer, Text } from "@mantine/core";
import {
	IconBell,
	IconCalendar,
	IconReport,
	IconSettings,
	IconUsers,
	IconX,
	IconYoga,
	IconCheck,
	IconMessage,
	IconMenu2,
	IconChevronUp,
	IconChevronDown,
	IconChecklist,
	IconServerCog,
	IconSwimming,
	IconRun,
	IconListCheck,
} from "@tabler/icons";
import { RiUserSettingsLine } from "react-icons/ri";
import { IoMdLogOut } from "react-icons/io";
import { FaChalkboardTeacher } from "react-icons/fa";
import GlobalContext from "../context/GlobalContext";
import { useMessageContext } from "../context/MessageContext";
import LOGO from "../Images/logo.png";
// import CommunityLogo from "../Images/fitswaggers.png";
import { MdGroups } from "react-icons/md";
import {
	URL,
	getAllActivityData,
	getAllYoskaActivitiesUser,
	getCoachProfile,
	getPrograms,
	getcoachGrantAccess,
	saveCoachProfile,
	updateProgram,
} from "../API/api-endpoint";
import { ExclamationCircleFilled } from "@ant-design/icons";
import { Modal } from "antd";
import Swal from "sweetalert2";
import { useDispatch, useSelector } from "react-redux";
import { getAllUsersAction } from "../pages/Chat/redux/action/usersAction";
import {
	FormLabel,
	Menu,
	MenuItem,
	OutlinedInput,
	TextField,
} from "@mui/material";
import { showError, showSuccess } from "./Messages";
import { Diversity1Sharp } from "@mui/icons-material";
import PubNub from "pubnub";
import _ from "lodash";
import axios from "axios";

const Header = ({ setFetchActivePrograms, setprogramIdChange }) => {
	const { confirm } = Modal;
	const navigate = useNavigate();
	const roleID = localStorage.getItem("roleID");
	const [username, setUsername] = useState("");
	const [opened, setOpened] = useState(false);
	const [programList, setProgramList] = useState();
	const [programLists, setProgramLists] = useState();
	console.log("programList", programList);
	const [yraSubscribed, setYraSubscribed] = useState(false);
	// console.log("programList", IsProfileCoach, programList);
	const [openReport, setOpenReport] = useState(false);
	const [openSettings, setOpenSettings] = useState(false);
	const [reportClick, seReportClick] = useState(false);
	const [settings, setSettings] = useState(false);
	const [feedData, setFeedData] = useState(false);
	const [isCoach, setIsCoach] = useState(false);
	const [isCoachOne, setIsCoachOne] = useState(false);
	const [isCommunity, setIsCommunity] = useState(false);
	const [isAthlete, setIsAthlete] = useState(false);

	const [profile, setProfile] = useState(false);
	const reportRef = React.useRef();
	const settingsRef = React.useRef();
	const feedDataRef = React.useRef();
	const coachRef = React.useRef();
	const athleteRef = React.useRef();
	const communityRef = React.useRef();

	const profileRef = React.useRef();
	const dispatch = useDispatch();

	const { user, setWorkoutMode } = useContext(GlobalContext);
	const { totalUnseenMessageCount } = useMessageContext();

	const handleClick = () => seReportClick(!reportClick);
	const handleSettingsClick = () => setSettings(!settings);
	const handleFeedDataClick = () => setFeedData(!feedData);
	const handleCoachClick = () => setIsCoachOne(!isCoachOne);
	const handleCommunityClick = () => setIsCommunity(!isCommunity);
	const handleAthleteClick = () => setIsAthlete(!isAthlete);
	const [isModalOpen, setIsModalOpen] = useState(false);
	const [activityName, setActivityName] = useState(false);
	const [coachActiveProgram, setActiveCoachProgram] = useState();
	const [grantAccessData, setGrantAccess] = useState();
	const { setPubnubUnseenCount } = useMessageContext();
	const [isChallengesOpen, setIsChallengesOpen] = useState(false);
	const [anchorEl, setAnchorEl] = useState(null);
	const [yoskaActivitieList, setYoskaActivitieList] = useState([]);

	const [userOnboardingState, setUserOnboardingState] = useState(null);
	const [groupDetail, setGroupDetail] = useState(null);
	const [communityLogo, setCommunityLogo] = useState(null);

	useEffect(() => {
		// Load onboardingState on mount
		const onboardingState = localStorage.getItem("onboardingState");
		setUserOnboardingState(onboardingState);
	}, []);

	useEffect(() => {
		// Only run this when userOnboardingState has updated
		if (userOnboardingState === "community") {
			const group = localStorage.getItem("groupDetail");
			if (group) {
				const parsedGroup = JSON.parse(group);
				setGroupDetail(parsedGroup);
			}
		}
	}, [userOnboardingState]);

	useEffect(() => {
		if (groupDetail) {
			const { communityLogo } = groupDetail;
			setCommunityLogo(
				`${URL}/static/public/athleteCommunityGroups/${communityLogo}`
			);
		}
	}, [groupDetail]);

	console.log("yoskaActivitieList", yoskaActivitieList);
	const handleProfile = () => setProfile(!profile);
	const open = Boolean(isCoach);
	const handleAdmin = (event) => {
		setIsCoach(event.currentTarget);
	};
	const handleBlur = (event) => {
		if (!event.currentTarget.contains(event.relatedTarget)) {
			// Outside click
			setProfile(false);
		}
	};
	useEffect(() => {
		const handleClickOutside = (event) => {
			if (
				profileRef.current &&
				!profileRef.current.contains(event.target)
			) {
				setProfile(false);
			}
		};

		document.addEventListener("mousedown", handleClickOutside);
		return () => {
			document.removeEventListener("mousedown", handleClickOutside);
		};
	}, [profileRef]);

	const fetchProgram = async () => {
		const response = await getPrograms();
		setProgramList(response);
		let data = response?.filter((ele) => ele.active == 1);
		console.log("yra-subscribed");
		console.log(data[0]);
		const pumaLogo = data.find(
			(program) => program.prgram_name.toLowerCase() === "yra"
		);
		if (pumaLogo) {
			setYraSubscribed(true);
		}
		localStorage.setItem("activeProgram", JSON.stringify(data[0]));

		if (roleID == 5 && data?.length > 0) {
			localStorage.setItem("programID", data[0]?.program_id);
		} else {
			const response = await getCoachProfile();
			setActiveCoachProgram(response.data);
			localStorage.setItem("programID", response?.data?.program_id);
			if (setFetchActivePrograms) {
				setFetchActivePrograms(response.data);
			}
		}
		console.log("response", response);
	};
	const showConfirm = (id) => {
		confirm({
			title: "Are you sure want to switch program?",
			icon: <ExclamationCircleFilled />,
			// content: 'Some descriptions',
			onOk() {
				updateProfile(id);
				console.log("OK");
			},
			onCancel() {
				console.log("Cancel");
			},
		});
	};
	const updateProfile = async (id) => {
		const response = await updateProgram(id);
		if (response?.status) {
			fetchProgram();
			Swal.fire({
				title: "Success",
				text: response.message,
				icon: "success",
			});
			// window.location.reload()
		}
		console.log("response", response);
	};
	const getAllProgramsData = async () => {
		const response = await getAllActivityData();
		console.log("response", response);
		setProgramLists(response?.rows);
	};
	const getCoachProfiles = async () => {
		const response = await getCoachProfile();
		// localStorage.setItem("programID", response?.data?.program_id ? response?.data?.program_id : 1);
		if (response.status) {
			setActiveCoachProgram(response.data);
			console.log("response>>>>>>>>>>>>", response.data);
			localStorage.setItem("programID", response?.data?.program_id);
			if (setFetchActivePrograms) {
				setFetchActivePrograms(response.data);
			}
		}
		// setProgramLists(response?.rows);
	};
	const getYoskaActivities = async () => {
		let response = await getAllYoskaActivitiesUser();
		console.log("response", response);
		let filterData = response?.filter((ele) => ele?.is_under_coach_profile);
		setYoskaActivitieList(filterData);
	};

	useEffect(() => {
		// getCoachProfiles()
		getYoskaActivities();
		getAllProgramsData();
		fetchProgram();
		let handler = (e) => {
			// --- Old Logic ReportRef ---
			// if (!reportRef.current.contains(e.target)) {
			//   seReportClick(false);
			// }
			//  --- New Logic ReportRef---
			if (
				!communityRef.current ||
				!reportRef.current ||
				!settingsRef.current ||
				!coachRef.current ||
				!athleteRef.current ||
				!feedDataRef.current ||
				!profileRef.current
			) {
				return;
			}
			// By adding ref.current && before each access, you ensure that the code does not attempt to access null
			if (reportRef.current && !reportRef.current.contains(e.target)) {
				seReportClick(false);
			}
			if (
				settingsRef.current &&
				!settingsRef.current.contains(e.target)
			) {
				setSettings(false);
			}
			if (coachRef.current && !coachRef.current.contains(e.target)) {
				setIsCoachOne(false);
			}
			if (
				communityRef.current &&
				!communityRef.current.contains(e.target)
			) {
				setIsCommunity(false);
			}
			if (athleteRef.current && !athleteRef.current.contains(e.target)) {
				setIsAthlete(false);
			}
			if (
				feedDataRef.current &&
				!feedDataRef.current.contains(e.target)
			) {
				setFeedData(false);
			}
			if (profileRef.current && !profileRef.current.contains(e.target)) {
				setProfile(false);
			}
		};
		document.addEventListener("mousedown", handler);

		setUsername(localStorage.getItem("username"));

		return () => {
			document.removeEventListener("mousedown", handler);
		};
	}, [username]);

	const handleLogout = () => {
		localStorage.clear();
		setWorkoutMode(false);
		navigate("/");
	};

	const handleUpdate = () => {
		setWorkoutMode(true);
	};
	const handleUpdateGoal = () => {
		if (activityName) {
			navigate(`/yragoals?activityname=${activityName}`);
			setIsModalOpen(false);
		}
	};
	const saveCoachProgram = async (id) => {
		const response = await saveCoachProfile(id);
		console.log("response", response);
		if (response.status) {
			// window.location.reload()
			showSuccess("Updated Successfully!");
			getCoachProfiles();
			dispatch(getAllUsersAction(roleID));
			setprogramIdChange(true);
		} else {
			showError(response.message);
		}
	};
	const getoachAccess = async () => {
		const response = await getcoachGrantAccess();
		console.log("response", response);
		if (response.status) {
			setGrantAccess(response?.data);
			// setActiveCoachProgram(response.data)
		}
		// setProgramLists(response?.rows);
	};
	useEffect(() => {
		if (roleID == 3) {
			getoachAccess();
		}
	}, [roleID]);

	let myProfile = localStorage.getItem("profileImage");
	let currentUser = JSON.parse(localStorage.getItem("currentUser"));

	const getSubscribedGroup = async () => {
		const authToken = localStorage.getItem("token");
		const response = await axios.get(`${URL}/subscribe-group`, {
			headers: {
				Authorization: authToken,
			},
		});

		if (response.status === 200) {
			return response.data.data;
		}

		if (response.status !== 200) {
			throw new Error(`Axios API failed with status: ${response.status}`);
		}
	};

	useEffect(() => {
		if (!currentUser?.uid) return;
		const pubnub = new PubNub({
			publishKey: process.env.REACT_APP_CHAT_PUBLISH_KEY,
			subscribeKey: process.env.REACT_APP_CHAT_SUBSCRIBE_KEY,
			uuid: currentUser?.uid,
		});
		const userId = pubnub.getUserId();
		const generateId = () => Math.random().toString(36).substring(2, 9);
		const loggedInUserId = localStorage.getItem("userId");
		let isSubscribed = true;

		const manageSubscriptions = async () => {
			try {
				const groupList = await getSubscribedGroup();
				const channels = groupList.map(
					(group) => group.communitygroup.channelName
				);

				if (channels.length === 0) return;

				pubnub.subscribe({ channels });

				const fetchUnseenCount = async () => {
					try {
						let newUnseenCount = 0;
						for (const group of groupList) {
							const channelName =
								group.communitygroup.channelName;

							const [actionsResponse, messagesResponse] =
								await Promise.all([
									pubnub.getMessageActions({
										channel: channelName,
									}),
									pubnub.fetchMessages({
										channels: [channelName],
										count: 100,
									}),
								]);

							const allMessages =
								messagesResponse.channels[channelName] || [];
							const seenActionsMap = new Map();

							actionsResponse.data.forEach((action) => {
								if (action.type === "seen") {
									const key = action.messageTimetoken;
									const existing =
										seenActionsMap.get(key) || [];
									existing.push(action.value);
									seenActionsMap.set(key, existing);
								}
							});

							allMessages.forEach((msg) => {
								const seenByList =
									seenActionsMap.get(msg.timetoken) || [];
								console.log("seenByList", seenByList);

								if (
									!seenByList.includes(loggedInUserId) &&
									msg.message.userId?.toString() !==
										loggedInUserId
								) {
									newUnseenCount++;
								}
							});
						}
						if (isSubscribed) {
							setPubnubUnseenCount(newUnseenCount);
							console.log("newUnseenCount", newUnseenCount);
						}
					} catch (error) {
						console.error("Error updating unseen count:", error);
					}
				};

				const debouncedUpdate = _.debounce(fetchUnseenCount, 300, {
					leading: true,
					trailing: true,
				});

				const messageListener = {
					message: debouncedUpdate,
					messageAction: debouncedUpdate,
				};

				pubnub.addListener(messageListener);
				fetchUnseenCount(); // Initial fetch

				return () => {
					isSubscribed = false;
					debouncedUpdate.cancel();
					pubnub.removeListener(messageListener);
					pubnub.unsubscribe({ channels });
				};
			} catch (error) {
				console.error("Error managing subscriptions:", error);
			}
		};

		const cleanup = manageSubscriptions();

		return () => {
			cleanup.then((fn) => fn && fn()); // Ensuring cleanup runs properly
		};
	}, [currentUser?.uid]);

	return (
		<header
			className='px-4 py-1 bg-blue-600 fixed left-0 top-0 right-0 z-50'
			style={{ backgroundColor: "#E67E22" }}
		>
			<div className='flex justify-between items-center gap-x-4'>
				<div className='flex gap-x-4 lg:gap-x-10 items-center'>
					<div className='xl:hidden'>
						{opened ? (
							<IconX
								size={24}
								color={"white"}
								onClick={() => setOpened(false)}
								className='block xl:hidden'
							/>
						) : (
							<IconMenu2
								size={24}
								color={"white"}
								onClick={() => setOpened(true)}
								className='block xl:hidden'
							/>
						)}
					</div>
					<Link to={"/"}>
						<div className='flex items-center'>
							{groupDetail && groupDetail?.communityLogo ? (
								<img
									src={communityLogo}
									alt={groupDetail.communityName}
									style={{ width: "40px" }}
								/>
							) : groupDetail && !groupDetail?.communityLogo ? (
								<MdGroups color='#1c1c84' fontSize={"20px"} />
							) : (
								<img
									src={LOGO}
									alt='...'
									style={{ width: "40px" }}
								/>
							)}
							{/* <div className="text-slate-100 text-2xl">Yoska</div> */}
						</div>
					</Link>

					<div className='hidden xl:flex gap-x-4 xl:gap-x-5 text-sm font-regular items-center'>
						{userOnboardingState === "community" &&
							(roleID == 6 || roleID == 5) && (
								<Link to='/assignechallenges-user'>
									<button
										className='flex flex-cols items-center gap-x-1'
										// onClick={handleUpdate}
									>
										<span>
											<IconReport
												size={23}
												color='white'
											/>
										</span>
										<span className='text-white font-bold'>
											Challenges
										</span>
									</button>
								</Link>
							)}
						{/* {roleID != 3 && roleID != 5 && (
              <Link to="/assignechallenges-user">
                <button
                  className="flex flex-cols items-center gap-x-1"
                // onClick={handleUpdate}
                >
                  <span>
                    <IconReport size={23} color="white" />
                  </span>
                  <span className="text-white font-bold">Challenges</span>
                </button>
              </Link>
            )} */}
						{userOnboardingState == "community" && (
							<Link to='/leaderboard'>
								<button className='flex flex-cols items-center gap-x-1'>
									<span>
										<IconChecklist
											size={23}
											color='white'
										/>
									</span>
									<span className='text-white font-bold'>
										Leader Board
									</span>
								</button>
							</Link>
						)}

						{roleID != 5 && (
							<Link to='/coach-yoska'>
								<button
									className='flex flex-cols items-center gap-x-1'
									onClick={() => setWorkoutMode(false)}
								>
									<span>
										<IconUsers size={23} color='white' />
									</span>
									<span className='text-white font-bold'>
										Athlete
									</span>
								</button>
							</Link>
						)}
						<Link to='/workout'>
							<button
								className='flex flex-cols items-center gap-x-1'
								onClick={handleUpdate}
							>
								<span>
									<IconYoga size={23} color='white' />
								</span>
								<span className='text-white font-bold'>
									Workouts
								</span>
							</button>
						</Link>

						{roleID == 3 ||
							(roleID == 5 && programList?.length > 0 && (
								<Link to='/assignechallenges-user'>
									<button
										className='flex flex-cols items-center gap-x-1'
										// onClick={handleUpdate}
									>
										<span>
											<IconReport
												size={23}
												color='white'
											/>
										</span>
										<span className='text-white font-bold'>
											Challenges
										</span>
									</button>
								</Link>
							))}

						<Link to='/chat-new'>
							<button className='flex flex-cols items-center gap-x-1'>
								<span>
									<IconMessage size={23} color='white' />
								</span>
								<span className='text-white font-bold'>
									Messages
								</span>
								{totalUnseenMessageCount > 0 ? (
									<span className='bg-white w-6 h-6 text-xs rounded-full flex justify-center items-center'>
										{totalUnseenMessageCount}
									</span>
								) : null}
							</button>
						</Link>

						{userOnboardingState == "yoska_academy" &&
							roleID != 3 && (
								<Link to='/leaderboard'>
									<button className='flex flex-cols items-center gap-x-1'>
										<span>
											<IconChecklist
												size={23}
												color='white'
											/>
										</span>
										<span className='text-white font-bold'>
											Leader Board
										</span>
									</button>
								</Link>
							)}

						{
							!(roleID == 5 || (userOnboardingState == "community" && roleID == 6)) && (
								<Link to='/training-blocks-1'>
									<button className='flex flex-cols items-center gap-x-1'>
										<span>
											<IconCalendar size={23} color='white' />
										</span>
										<span className='text-white font-bold'>
											Training Blocks
										</span>
									</button>
								</Link>
							)
						}
						{roleID == 6 && (
							<div className='relative'>
								<button
									className='flex flex-cols items-center gap-x-2'
									onClick={(e) => {
										setIsChallengesOpen(!isChallengesOpen);
										setAnchorEl(e.currentTarget);
									}}
								>
									<span>
										<IconUsers size={23} color='white' />
									</span>
									<span className='text-white font-bold'>
										Community
									</span>
								</button>
								{isChallengesOpen && (
									<Menu
										id='basic-menu'
										anchorEl={anchorEl}
										open={isChallengesOpen}
										onClose={() =>
											setIsChallengesOpen(false)
										}
										disableScrollLock={true}
										MenuListProps={{
											"aria-labelledby": "basic-button",
										}}
									>
										<div>
											<MenuItem>
												<Link to='/community-group-challenge'>
													Group Challenges
												</Link>
											</MenuItem>
											<MenuItem>
												<Link to='/community-group-challenge-level'>
													Challenge Level
												</Link>
											</MenuItem>
											<MenuItem>
												<Link to='/community-group-challenge-target'>
													Challenge Target
												</Link>
											</MenuItem>
											<MenuItem>
												<Link to='/commmunity-group-invite'>
													Invite User
												</Link>
											</MenuItem>
											<MenuItem>
												<Link to='/list-athlete'>
													View Athletes
												</Link>
											</MenuItem>
										</div>
									</Menu>
								)}
							</div>
						)}
						{roleID == 1 && (
							<button
								className='flex flex-cols items-center gap-x-2'
								onClick={handleAdmin}
							>
								<span>
									<IconCalendar size={23} color='white' />
								</span>
								<span className='text-white font-bold'>
									Admin
								</span>
							</button>
						)}
						{roleID == 3 && grantAccessData?.coach_id && (
							<button
								className='flex flex-cols items-center gap-x-2'
								onClick={handleAdmin}
							>
								<span>
									<IconCalendar size={23} color='white' />
								</span>
								<span className='text-white font-bold'>
									Admin
								</span>
							</button>
						)}
						{isCoach ? (
							<Menu
								id='basic-menu'
								anchorEl={isCoach}
								open={open}
								onClose={() => setIsCoach(false)}
								disableScrollLock={true}
								MenuListProps={{
									"aria-labelledby": "basic-button",
								}}
							>
								<MenuItem>
									<div ref={reportRef}>
										<button
											className='flex items-center gap-x-1'
											onClick={handleClick}
										>
											<span>
												<IconReport
													size={23}
													color='#E67E22'
												/>
											</span>
											<div>Reports</div>
											<div>
												{reportClick ? (
													<IconChevronUp
														size={23}
														color='#E67E22'
													/>
												) : (
													<IconChevronDown
														size={23}
														color='#E67E22'
													/>
												)}
											</div>
										</button>
										{reportClick ? (
											<div
												className={`fixed py-2 px-3 flex flex-col gap-y-2 bg-gray-50 shadow-md w-max rounded-md border-2 mt-1 z-10 text-sm`}
											>
												<div>
													<Link to='/revenuereportbytimerange'>
														Revenue Report
													</Link>
												</div>
												<div>
													<Link to='/usergrowthnumbers'>
														User Growth Report
													</Link>
												</div>
												<div>
													<Link to='/overall-report'>
														Over All Report
													</Link>
												</div>
												<div>
													<Link to='/coachrevenue'>
														Coach Revenue Report
													</Link>
												</div>
												<div>
													<Link to='/coach-payment-page'>
														Coach Payment Report
													</Link>
												</div>
												<div>
													<Link to='/subscription-with-payments'>
														Subscription With
														Payments Report
													</Link>
												</div>
											</div>
										) : (
											""
										)}
									</div>
								</MenuItem>
								<MenuItem>
									<div ref={settingsRef}>
										<button
											className='flex items-center gap-x-1'
											onClick={handleSettingsClick}
										>
											<span>
												<IconSettings
													size={23}
													color='#E67E22'
												/>
											</span>
											<div>Settings</div>
											<div>
												{settings ? (
													<IconChevronUp
														size={23}
														color='#E67E22'
													/>
												) : (
													<IconChevronDown
														size={23}
														color='#E67E22'
													/>
												)}
											</div>
										</button>
										{settings ? (
											<div
												style={{
													scrollbarColor:
														"#E67E22 transparent",
													scrollbarWidth: "thin",
												}}
												className={`fixed py-2 px-3 flex flex-col gap-y-2 bg-gray-50 shadow-md w-max rounded-md border-2 mt-1 z-10 text-sm`}
											>
												<div>
													<Link to='/zone'>
														Zones
													</Link>
												</div>
												<div>
													<Link to='/admincreatechallenge'>
														Admin Create Challenge
													</Link>
												</div>

												<div>
													<Link to='/wellness'>
														Wellness
													</Link>
												</div>
												<div>
													<Link to='/automation-training-block'>
														Automation Training
														Block
													</Link>
												</div>
												<div>
													<Link to='/raccesRedness'>
														Race Readiness{" "}
													</Link>
												</div>
												<div>
													<Link to='/swimming'>
														Swimming and race
													</Link>
												</div>

												<div>
													<Link to='/racecalculator'>
														Calculator
													</Link>
												</div>
												<div>
													<Link to='/admin-register'>
														Register Admin
													</Link>
												</div>

												<div
													onClick={() => {
														localStorage.clear();
													}}
												>
													<Link to='/login'>
														Login With Admin
													</Link>
												</div>

												<div>
													<Link to='/subscription-report'>
														Manage Subscription
													</Link>
												</div>
												<div>
													<Link to='/admin-privileges'>
														Admin Privileges
													</Link>
												</div>
											</div>
										) : (
											""
										)}
									</div>
								</MenuItem>
								<MenuItem>
									<div ref={feedDataRef}>
										<button
											className='flex items-center gap-x-1'
											onClick={handleFeedDataClick}
										>
											<span>
												<IconSettings
													size={23}
													color='#E67E22'
												/>
											</span>
											<div>Feed Data</div>
											<div>
												{feedData ? (
													<IconChevronUp
														size={23}
														color='#E67E22'
													/>
												) : (
													<IconChevronDown
														size={23}
														color='#E67E22'
													/>
												)}
											</div>
										</button>
										{feedData ? (
											<div
												style={{
													scrollbarColor:
														"#E67E22 transparent",
													scrollbarWidth: "thin",
												}}
												className={`fixed py-2 px-3 flex flex-col gap-y-2 bg-gray-50 shadow-md w-max rounded-md border-2 mt-1 z-10 text-sm max-h-[60%] overflow-y-scroll`}
											>
												<div>
													<Link to='/zonesclasification'>
														Zones Feed Data
													</Link>
												</div>
												<div>
													<Link to='/weeklyfeeddata'>
														Days Feed Data
													</Link>
												</div>
												<div>
													<Link to='/programs'>
														Programs Feed Data
													</Link>
												</div>
												<div>
													<Link to='/levels'>
														Levels Feed Data
													</Link>
												</div>
												<div>
													<Link to='/activity'>
														Activity Feed Data
													</Link>
												</div>
												<div>
													<Link to='/racecalculation'>
														Race Calculation Feed
														Data
													</Link>
												</div>
												<div>
													<div
														onClick={() => {
															setIsModalOpen(
																true
															);
															setFeedData(false);
															setIsCoach(false);
														}}
													>
														YRA/YCA/YSA Goals Feed
														Data
													</div>
													{/*
                        <Link to="/yragoals"></Link>
                        */}
												</div>
												<div>
													<Link to='/phase-block'>
														Phase Block
													</Link>
												</div>
												<div>
													<Link to='/ytagoal'>
														YTA Goal Volume
													</Link>
												</div>
												<div>
													<Link to='/subactivity'>
														Sub Activity
													</Link>
												</div>
												<div>
													<Link to='/workoutactivity'>
														Workout Data
													</Link>
												</div>
												<div>
													<Link to='/subworkout'>
														Sub Workout Data
													</Link>
												</div>

												<div>
													<Link to='/assesment'>
														Assessment
													</Link>
												</div>
												<div>
													<Link to='/readness'>
														Race Readiness
													</Link>
												</div>
												<div>
													<Link to='/challenge'>
														Challenge
													</Link>
												</div>
												<div>
													<Link to='/goal-without-volume'>
														Fitness Goal Data
													</Link>
												</div>
												<div>
													<Link to='/zones-heart'>
														Zones Heart
													</Link>
												</div>
												<div>
													<Link to='/zones-power'>
														Zones Power
													</Link>
												</div>

												<div>
													<Link to='/activity-group'>
														Activity Group
													</Link>
												</div>
												<div>
													<Link to='/activity-track'>
														Activity Track
													</Link>
												</div>
												<div>
													<Link to='/discount-coupon'>
														Discount Coupon
													</Link>
												</div>
												<div>
													<Link to='/promotors'>
														Promoters{" "}
													</Link>
												</div>
												<div>
													<Link to='/payment-creds'>
														Payment Creds
													</Link>
												</div>
												<div>
													<Link to='/tags'>
														Tags Cloud
													</Link>
												</div>
												<div>
													<Link to='/uoms'>Uoms</Link>
												</div>
												<div>
													<Link to='/activity-metric'>
														Activity Metric
													</Link>
												</div>
												<div>
													<Link to='/system-configration'>
														System Configuration
													</Link>
												</div>
												<div>
													<Link to='/subscription'>
														Subscription
													</Link>
												</div>
												<div>
													<Link to='/group-registration'>
														Group Registration
													</Link>
												</div>
												<div>
													<Link to='/bounds'>
														Bounds Feed Data
													</Link>
												</div>
												<div>
													<Link to='/program-feature'>
														Program Feature
													</Link>
												</div>
												<div>
													<Link to='/program-points'>
														Program Points
													</Link>
												</div>
												<div>
													<Link to='/distribution'>
														Workout Distribution
													</Link>
												</div>
												<div>
													<Link to='/user-goal'>
														User Goal
													</Link>
												</div>
												<div>
													<Link to='/races'>
														Race Type Feed Data
													</Link>
												</div>
												<div>
													<Link to='/formreview-section'>
														Form Review Section
													</Link>
												</div>
												<div>
													<Link to='/formreview-section-option'>
														Form Review Section
														Option
													</Link>
												</div>
											</div>
										) : null}
									</div>
								</MenuItem>

								<MenuItem>
									<div ref={coachRef}>
										<button
											className='flex items-center gap-x-1'
											onClick={handleCoachClick}
										>
											<span>
												<IconUsers
													size={23}
													color='#E67E22'
												/>
											</span>
											<div>Manage Coach</div>
											<div>
												{isCoachOne ? (
													<IconChevronUp
														size={23}
														color='#E67E22'
													/>
												) : (
													<IconChevronDown
														size={23}
														color='#E67E22'
													/>
												)}
											</div>
										</button>
										{isCoachOne ? (
											<div
												className={`fixed py-2 px-3 flex flex-col gap-y-2 bg-gray-50 shadow-md w-max rounded-md border-2 mt-1 z-10 text-sm`}
											>
												<div>
													<Link to='/coach-list'>
														Manage Coach
													</Link>
												</div>
												<div>
													<Link to='/coach-register'>
														Register Coach
													</Link>
												</div>
												<div>
													<Link to='/coach-admin-booking'>
														Coach Schedule
													</Link>
												</div>
												<div>
													<Link to='/coach-booking'>
														Coach User Schedule
														Booking
													</Link>
												</div>
												<div>
													<Link to='/coach'>
														Assign Coach
													</Link>
												</div>
											</div>
										) : (
											""
										)}
									</div>
								</MenuItem>

								<MenuItem>
									<div ref={athleteRef}>
										<button
											className='flex items-center gap-x-1'
											onClick={handleAthleteClick}
										>
											<span>
												<IconUsers
													size={23}
													color='#E67E22'
												/>
											</span>
											<div>Manage Athletes</div>
											<div>
												{isAthlete ? (
													<IconChevronUp
														size={23}
														color='#E67E22'
													/>
												) : (
													<IconChevronDown
														size={23}
														color='#E67E22'
													/>
												)}
											</div>
										</button>
										{isAthlete ? (
											<div
												className={`fixed py-2 px-3 flex flex-col gap-y-2 bg-gray-50 shadow-md w-max rounded-md border-2 mt-1 z-10 text-sm`}
											>
												<div style={{ width: "150px" }}>
													<Link to='/athletes'>
														Manage Athletes
													</Link>
												</div>
												<div style={{ width: "200px" }}>
													<Link to='/today-failed'>
														Today Failed Payments
														Athletes
													</Link>
												</div>
												<div style={{ width: "150px" }}>
													<Link to='/athlete-list'>
														Non-Payment Athletes
													</Link>
												</div>
												<div style={{ width: "150px" }}>
													<Link to='/expire-payment'>
														Payment Expiry Athletes
													</Link>
												</div>
											</div>
										) : (
											""
										)}
									</div>
								</MenuItem>
								<MenuItem>
									<div ref={communityRef}>
										<button
											className='flex items-center gap-x-1'
											onClick={handleCommunityClick}
										>
											<span>
												<IconUsers
													size={23}
													color='#E67E22'
												/>
											</span>
											<div>Community Groups</div>
											<div>
												{isCommunity ? (
													<IconChevronUp
														size={23}
														color='#E67E22'
													/>
												) : (
													<IconChevronDown
														size={23}
														color='#E67E22'
													/>
												)}
											</div>
										</button>
										{isCommunity ? (
											<div
												className={`fixed py-2 px-3 flex flex-col gap-y-2 bg-gray-50 shadow-md w-max rounded-md border-2 mt-1 z-10 text-sm`}
											>
												<div>
													<Link to='/community-groups'>
														Manage Groups
													</Link>
												</div>
												<div>
													<Link to='/community-group-challenge'>
														Group Challenges
													</Link>
												</div>
												<div>
													<Link to='/list-mentor'>
														View Mentors
													</Link>
												</div>
											</div>
										) : (
											""
										)}
									</div>
								</MenuItem>
							</Menu>
						) : null}

						<Modal
							title='Select Program'
							open={isModalOpen}
							onOk={handleUpdateGoal}
							onCancel={() => setIsModalOpen(false)}
						>
							<FormLabel>Activity Name:</FormLabel>
							<TextField
								fullWidth
								size='small'
								select
								name='activity_id'
								value={activityName}
								onChange={(e) =>
									setActivityName(e.target.value)
								}
								id='form-layouts-separator-select'
								labelId='form-layouts-separator-select-label'
								input={
									<OutlinedInput id='select-multiple-language' />
								}
							>
								<MenuItem value={""} disabled>
									Select Activity
								</MenuItem>
								{programLists?.map((value, index) => {
									return (
										<MenuItem
											key={index}
											value={value?.activity_name}
										>
											{value?.activity_name}
										</MenuItem>
									);
								})}
							</TextField>
						</Modal>

						{/*
          
          <Link to="/notification">
              <button className="flex flex-cols items-center gap-x-1">
                <span>
                  <IconBell size={23} color="white" />
                </span>
                <span className="text-white">Notification</span>
              </button>
            </Link>
          */}

						{roleID == 5 && (
							<Link to='/athlete-race'>
								<button className='flex flex-cols items-center gap-x-1'>
									<span>
										<IconRun size={23} color='white' />
									</span>
									<span className='text-white font-bold'>
										Races
									</span>
								</button>
							</Link>
						)}

						{roleID == 5 && (
							<Link to='/swimming-pool'>
								<button className='flex flex-cols items-center gap-x-1'>
									<span>
										<IconSwimming size={23} color='white' />
									</span>
									<span className='text-white font-bold'>
										Swimming Pool
									</span>
								</button>
							</Link>
						)}
						{userOnboardingState == "yoska_academy" &&
							roleID == 3 && (
								<Link to='/leaderboard'>
									<button className='flex flex-cols items-center gap-x-1'>
										<span>
											<IconChecklist
												size={23}
												color='white'
											/>
										</span>
										<span className='text-white font-bold'>
											Leader Board
										</span>
									</button>
								</Link>
							)}
						{roleID == 5 && (
							<Link to='/form-review'>
								<button className='flex flex-cols items-center gap-x-1'>
									<span>
										<IconListCheck
											size={23}
											color='white'
										/>
									</span>
									<span className='text-white font-bold'>
										Form Review
									</span>
								</button>
							</Link>
						)}

						{roleID != 5 && (
							<Link to='/formreview'>
								<button className='flex flex-cols items-center gap-x-1'>
									<span>
										<IconListCheck
											size={23}
											color='white'
										/>
									</span>
									<span className='text-white font-bold'>
										Form Review
									</span>
								</button>
							</Link>
						)}

						{roleID == 5 && (
							<>
								<Link to='/zone'>
									<button className='flex flex-cols items-center gap-x-1'>
										<span>
											<IconChecklist
												size={23}
												color='white'
											/>
										</span>
										<span className='text-white font-bold'>
											Zones
										</span>
									</button>
								</Link>
							</>
						)}

						{/*
          
          <Link to="/master">
              <button className="flex flex-cols items-center gap-x-1">
                <span>
                  <IconChecklist size={23} color="white" />
                </span>
                <span className="text-white">Masters</span>
              </button>
            </Link>
          */}
					</div>
				</div>
				<div>
					<div className='flex flex-cols items-center gap-x-3'>
						{roleID == 5 && yraSubscribed && (
							<div className='hidden xl:flex flex-cols items-center justify-items-center gap-x-1'>
								<span className='text-white text-sm font-extrabold'>
									Powered by
								</span>
								<span>
									<img
										src='puma_logo.svg'
										alt='puma logo'
										height={85}
										width={85}
										style={{
											position: "relative",
											bottom: "25%",
										}}
									/>
								</span>
							</div>
						)}
						<div className='text-white text-base capitalize hidden md:flex'>
							{username === "<EMAIL>"
								? "Toby Somerville"
								: username === "<EMAIL>"
								? "Sankar"
								: username === "<EMAIL>"
								? "Usha Hegde"
								: username === "<EMAIL>"
								? "Neil Dsilva"
								: username === "<EMAIL>"
								? "Krishna Kohli"
								: username}
						</div>
						<div ref={profileRef} onBlur={handleBlur} tabIndex='0'>
							<img
								src={
									myProfile !== "undefined" &&
									myProfile !== "null" &&
									myProfile
										? `${URL}/static/public/userimages/${myProfile}`
										: "https://i.ibb.co/5xCF7vx/u-https-spng-pngfind-com-pngs-s-610-6104451-image-placeholder-png-user-profile-placeholder-image-png.jpg"
								}
								alt='...'
								className='w-10 h-10 rounded-full border-2 cursor-pointer'
								onClick={handleProfile}
							/>
							{profile ? (
								<div
									className={`fixed right-4 py-2 px-3 flex flex-col gap-y-2 bg-gray-50 shadow-md w-max rounded-md border-2 mt-1  text-sm`}
								>
									<div className='font-semibold'>
										<Link to='/profile-page'>
											<div className='flex flex-cols items-center gap-x-2'>
												<span>
													<RiUserSettingsLine
														color='#E67E22'
														fontSize={"20px"}
													/>{" "}
												</span>
												<span>Profile Settings</span>
											</div>
										</Link>
									</div>
									{/*
                <div>
                    <Link to="/change-password">Change Password</Link>
                  </div>
                */}
									<hr />
									{roleID == 5 && (
										<div>
											{programList?.length > 0 && (
												<div className='font-semibold'>
													<span className='flex flex-cols items-center gap-x-2'>
														<FaChalkboardTeacher
															color='#E67E22'
															fontSize={"20px"}
														/>
														<span>
															Active Programs
														</span>
													</span>

													{programList?.map(
														(program) => {
															return (
																<div
																	key={
																		program.program_id
																	}
																	style={{
																		paddingLeft:
																			"28px",
																	}}
																	className={
																		program?.active ==
																		1
																			? "text-[#E67E22]"
																			: "cursor-pointer"
																	}
																	onClick={() =>
																		showConfirm(
																			program?.program_id
																		)
																	}
																>
																	{
																		program?.prgram_name
																	}{" "}
																	Program
																</div>
															);
														}
													)}
												</div>
											)}
										</div>
									)}
									{userOnboardingState === "community" && (
										<div>
											<div className='font-semibold'>
												<span className='flex flex-cols items-center gap-x-2'>
													<FaChalkboardTeacher
														color='#E67E22'
														fontSize={"20px"}
													/>
													<span>Athlete Group</span>
												</span>
												<div
													key={groupDetail?.id}
													style={{
														paddingLeft: "28px",
													}}
													className={"text-[#E67E22]"}
												>
													{groupDetail?.communityName}
												</div>
											</div>
										</div>
									)}

									{roleID != 5 && (
										<>
											{yoskaActivitieList?.map(
												(activity) => {
													return (
														<div
															key={activity.id}
															style={{
																paddingLeft:
																	"28px",
															}}
															className={
																activity?.id ==
																coachActiveProgram?.program_id
																	? "text-[#E67E22]"
																	: "cursor-pointer"
															}
															onClick={() =>
																saveCoachProgram(
																	activity.id
																)
															}
														>
															Coach - Yoska{" "}
															{
																activity?.activity_name
															}{" "}
															Program
														</div>
													);
												}
											)}
										</>
									)}
									<hr />
									<div className='cursor-pointer font-semibold'>
										<div
											onClick={handleLogout}
											className='flex flex-cols items-center gap-x-2'
										>
											<span>
												<IoMdLogOut
													color='#E67E22'
													fontSize={"20px"}
												/>
											</span>
											<span> Logout</span>
										</div>
									</div>
								</div>
							) : (
								""
							)}
						</div>
					</div>
				</div>
			</div>
			<Drawer
				opened={opened}
				onClose={() => setOpened(false)}
				title={user}
				padding='xl'
				size='xl'
			>
				<br />
				<div className='flex flex-col gap-y-5 items-start'>
					<Link to='#'>
						<button className='flex flex-cols items-center gap-x-2'>
							<span>
								<IconUsers size={23} color='black' />
							</span>
							<span className='text-black'>Athlete</span>
						</button>
					</Link>
					{/* <Link to="/ticket-you">
            <button className="flex flex-cols items-center gap-x-2">
              <span>
                <IconCheck size={23} color="black" />
              </span>
              <span className="text-black">Fit Profile</span>
            </button>
          </Link> */}
					<Link to='/workout'>
						<button className='flex flex-cols items-center gap-x-2'>
							<span>
								<IconYoga size={23} color='black' />
							</span>
							<span className='text-black'>Workouts</span>
						</button>
					</Link>
					<Link to='/training'>
						<button className='flex flex-cols items-center gap-x-2'>
							<span>
								<IconCalendar size={23} color='black' />
							</span>
							<span className='text-black'>Training Blocks</span>
						</button>
					</Link>
					<Link to='/chat-new'>
						<button className='flex flex-cols items-center gap-x-2'>
							<span>
								<IconMessage size={23} color='black' />
							</span>
							<span className='text-black'>Messages</span>
						</button>
					</Link>
					<Link to='/training-blocks-1'>
						<button className='flex flex-cols items-center gap-x-2'>
							<span>
								<IconCalendar size={23} color='black' />
							</span>
							<span className='text-black'>
								Training Blocks 1
							</span>
						</button>
					</Link>
					<div>
						<button
							className='flex flex-cols items-center gap-x-2'
							onClick={() => setOpenReport((o) => !o)}
						>
							<span>
								<IconReport size={23} color='black' />
							</span>
							<span className='text-black'>Reports</span>
							<span>
								{openReport ? (
									<IconChevronUp size={23} color='black' />
								) : (
									<IconChevronDown size={23} color='black' />
								)}
							</span>
						</button>
						<Collapse in={openReport}>
							<div className='flex flex-col gap-y-2 mt-4 text-sm'>
								{/*
              <Link to={"/athlete-dashboard"}>
                  <Text>Athlete dashboard</Text>
                </Link>
              */}
								<Link to={"/last-feedback"}>
									<Text>Last feedback</Text>
								</Link>
								<Link to={"/race-details"}>
									<Text>Race details</Text>
								</Link>
								<Link to={"/coach-payment"}>
									<Text>Coach payment</Text>
								</Link>
							</div>
						</Collapse>
					</div>
					<div>
						<button
							className='flex flex-cols items-center gap-x-2'
							onClick={() => setOpenSettings((o) => !o)}
						>
							<span>
								<IconSettings size={23} color='black' />
							</span>
							<span className='text-black'>Settings</span>
							<span>
								{openSettings ? (
									<IconChevronUp size={23} color='black' />
								) : (
									<IconChevronDown size={23} color='black' />
								)}
							</span>
						</button>
						<Collapse in={openSettings}>
							<div className='flex flex-col gap-y-2 mt-4 text-sm'>
								<Link to={"/instructions"}>
									<Text>Instructions</Text>
								</Link>
							</div>
						</Collapse>
					</div>
					<Link to='/notification'>
						<button className='flex flex-cols items-center gap-x-2'>
							<span>
								<IconBell size={23} color='black' />
							</span>
							<span className='text-black'>Notification</span>
						</button>
					</Link>
					<Link to='/leaderboard'>
						<button className='flex flex-cols items-center gap-x-2'>
							<span>
								<IconChecklist size={23} color='black' />
							</span>
							<span className='text-black'>Leader Board</span>
						</button>
					</Link>

					<Link to='/master'>
						<button className='flex flex-cols items-center gap-x-2'>
							<span>
								<IconChecklist size={23} color='black' />
							</span>
							<span className='text-black'>Master</span>
						</button>
					</Link>
					{roleID == 5 && yraSubscribed && (
						<div className='flex flex-cols items-center gap-x-2'>
							<span
								className='text-black'
								style={{
									marginLeft: "7%",
									width: "20vw",
								}}
							>
								Powered by
							</span>
							<span
								style={{
									position: "absolute",
									left: "23%",
								}}
							>
								<img
									src='puma_logo.svg'
									alt='puma logo'
									className='mb-4'
									height={87}
									width={87}
								/>
							</span>
						</div>
					)}
				</div>
			</Drawer>
		</header>
	);
};

export default Header;
