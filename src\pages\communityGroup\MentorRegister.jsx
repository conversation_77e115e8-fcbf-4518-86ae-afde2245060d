import React, { useEffect, useState } from 'react';
import { <PERSON><PERSON>, But<PERSON> } from 'antd';
import {
  Card,
  CardContent,
  FormLabel,
  Grid,
  MenuItem,
  OutlinedInput,
  Select,
  TextField,
  useTheme,
} from '@mui/material';
import { useFormik } from 'formik';
import Swal from 'sweetalert2';
import SlickCarousel from '.././SlickCarousel';
import { fetchCommunityGroups, createAthleteCommunityGroupMember } from "../../API/api-endpoint";

const ITEM_HEIGHT = 48;
const ITEM_PADDING_TOP = 8;
const MenuProps = {
  PaperProps: {
    style: {
      maxHeight: ITEM_HEIGHT * 4.5 + ITEM_PADDING_TOP,
      width: 250,
      overflowY: 'scroll',
    },
  },
};

function getStyles(label, selectedValue, theme) {
  return {
    fontWeight:
      selectedValue === label
        ? theme.typography.fontWeightMedium
        : theme.typography.fontWeightRegular,
  };
}

const RegisterMentor = ({isModalOpen, handleCancel, onRefresh}) => {
  const theme = useTheme();
  const [communityGroups, setCommunityGroups] = useState([]);

  // Fetch community groups using axios GET
  const handleFetchCommunityGroups = async () => {
    try {
      let response = await fetchCommunityGroups();
      response = response?.data?.data
      const responseData = response;
      const groups =
        responseData.data && Array.isArray(responseData.data)
          ? responseData.data
          : Array.isArray(responseData.rows)
          ? responseData.rows
          : Array.isArray(responseData)
          ? responseData
          : [];
      console.log("Fetched groups:", groups);
      setCommunityGroups(groups);
    } catch (error) {
      console.error('Error fetching community groups:', error);
    }
  };

  useEffect(() => {
    handleFetchCommunityGroups();
  }, []);

  const formik = useFormik({
    initialValues: {
      firstname: '',
      lastname: '',
      email: '',
      password: '',
      program_id: '', // This will store the selected community group id
    },
    validate: (values) => {
      const errors = {};
      if (!values.firstname) {
        errors.firstname = 'First name is required';
      }
      if (!values.lastname) {
        errors.lastname = 'Last name is required';
      }
      if (!values.email) {
        errors.email = 'Email is required';
      } else if (!/^\S+@\S+\.\S+$/.test(values.email)) {
        errors.email = 'Invalid email address';
      }
      if (!values.password) {
        errors.password = 'Password is required';
      } else if (
        !/^(?=.*[a-zA-Z])(?=.*\d)(?=.*[\W_])\S{8,}$/.test(values.password)
      ) {
        errors.password =
          'Password must be at least 8 characters long and contain a letter, number, and special character without spaces';
      }
      if (!values.program_id) {
        errors.program_id = 'Community group is required';
      }
      return errors;
    },
    onSubmit: (values, { resetForm }) => {
      handleSubmitMentor(values, resetForm);
    },
  });

  // Submit mentor registration using axios POST with token in headers
  const handleSubmitMentor = async (data, resetForm) => {
    const payload = {
      firstname: data.firstname,
      lastname: data.lastname,
      email: data.email,
      password: data.password,
      // Pass the selected community group id as athleteCommunityGroupId
      athleteCommunityGroupId: parseInt(data.program_id, 10),
    };
    
    try {
      const response = await createAthleteCommunityGroupMember(payload);
      const result = response.data;

      if (result.status) {
        Swal.fire({
          title: 'Success',
          text: result.message,
          icon: 'success',
        });
        resetForm();
        formik.setValues({
          firstname: '',
          lastname: '',
          email: '',
          password: '',
          program_id: '',
        });
      // trigger a fresh fetch of mentors
       if (onRefresh) {
        onRefresh();
       }
      // optionally close the modal
       handleCancel();
      } else {
        Swal.fire({
          title: 'Error',
          text: result.message,
          icon: 'error',
        });
      }
    } catch (error) {
      Swal.fire({
        title: 'Error',
        text: error.response?.data?.message || error.message || 'Internal server error occurred.',
        icon: 'error',
      });
    }
  };

  return (
    <Modal
        width={1200}
        open={isModalOpen}
        onCancel={handleCancel}
        footer={<div></div>}
    >
    
      <div className="flex justify-center">
        <Card
        sx={{
          width: '1200px',
          margin: '2% 6% 2% 6%',
          boxShadow:
            'rgba(60, 64, 67, 0.3) 0px 1px 2px 0px, rgba(60, 64, 67, 0.15) 0px 2px 6px 2px',
        }}
      >
        <CardContent>
          <div className="headingCont">
            <span className="heading">Mentor </span>{' '}
            <span className="orange heading">Registration</span>
          </div>
          <div className="parentCont">
            <form className="form1" onSubmit={formik.handleSubmit}>
              <Grid container spacing={2}>
                <Grid item xs={12} sm={11}>
                  <FormLabel>First Name:</FormLabel>
                  <TextField
                    fullWidth
                    placeholder="First Name"
                    size="small"
                    type="text"
                    name="firstname"
                    value={formik.values.firstname}
                    onChange={formik.handleChange}
                    error={formik.touched.firstname && formik.errors.firstname}
                    helperText={formik.touched.firstname && formik.errors.firstname}
                  />
                </Grid>

                <Grid item xs={12} sm={11}>
                  <FormLabel>Last Name:</FormLabel>
                  <TextField
                    fullWidth
                    placeholder="Last Name"
                    size="small"
                    type="text"
                    name="lastname"
                    value={formik.values.lastname}
                    onChange={formik.handleChange}
                    error={formik.touched.lastname && formik.errors.lastname}
                    helperText={formik.touched.lastname && formik.errors.lastname}
                  />
                </Grid>

                <Grid item xs={12} sm={5.5}>
                  <FormLabel>Email:</FormLabel>
                  <TextField
                    fullWidth
                    placeholder="Email"
                    size="small"
                    type="text"
                    name="email"
                    value={formik.values.email}
                    onChange={formik.handleChange}
                    error={formik.touched.email && formik.errors.email}
                    helperText={formik.touched.email && formik.errors.email}
                  />
                </Grid>

                <Grid item xs={12} sm={5.5}>
                  <FormLabel>Password:</FormLabel>
                  <TextField
                    fullWidth
                    placeholder="Password"
                    size="small"
                    type="password"
                    name="password"
                    value={formik.values.password}
                    onChange={formik.handleChange}
                    error={formik.touched.password && formik.errors.password}
                    helperText={formik.touched.password && formik.errors.password}
                  />
                </Grid>

                <Grid item xs={12} sm={11}>
                  <FormLabel>Community Group</FormLabel>
                  <Select
                    fullWidth
                    id="community-group-dropdown"
                    value={formik.values.program_id}
                    onChange={(e) => formik.setFieldValue('program_id', e.target.value)}
                    input={<OutlinedInput label="Community Group" />}
                    MenuProps={MenuProps}
                  >
                    {communityGroups.map((group) => (
                      <MenuItem
                        key={group.id}
                        value={group.id}
                        style={getStyles(group.communityName, formik.values.program_id, theme)}
                      >
                        {group.communityName}
                      </MenuItem>
                    ))}
                  </Select>
                  {formik.touched.program_id && formik.errors.program_id && (
                    <div style={{ color: 'red', fontSize: '0.8rem' }}>
                      {formik.errors.program_id}
                    </div>
                  )}
                </Grid>

                <Grid item xs={12} md={3} sm={3} sx={{ textAlign: 'left' }}>
                  <Button className="btn" key="submit" htmlType="submit">
                  <span style={{ color: '#fff' }}>Submit</span>
                  </Button>
                </Grid>
              </Grid>
            </form>
            <div className="slick-container">
              <SlickCarousel />
            </div>
          </div>
        </CardContent>
      </Card>
      </div>
    </Modal>
)
};

export default RegisterMentor;
