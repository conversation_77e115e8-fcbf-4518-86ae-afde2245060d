import {
  <PERSON>,
  Fade,
  FormControlLabel,
  IconButton,
  Tool<PERSON>,
  Typography,
} from "@mui/material";
import React, { useState } from "react";
import KeyboardBackspaceIcon from "@mui/icons-material/KeyboardBackspace";
import "../styles/navbar.css";
import Switch from "@mui/material/Switch";
import SearchIcon from "@mui/icons-material/Search";
import MoreVertIcon from "@mui/icons-material/MoreVert";
import { useNavigate } from "react-router-dom";
import {getGroupInfoAction} from "../redux/action/groupAction";
import { useDispatch } from "react-redux";

export default function Navbar({
  setIsUnRead,
  isUnRead,
  setSearchKeyword,
  searchKeyword,
  setIsSearchActive,
  isSearchActive,
  isAddChat,
  setIsAddChat,
}) {
  const dispatch = useDispatch();
  const navigate = useNavigate();

  // const handleLogOut = () => {
  //   localStorage.clear();
  //   navigate("/login");
  // };

  const handleSetIsSearch = async () => {
    if (isSearchActive) {
      // Clear the input value only if isSearchActive is true
      setSearchKeyword("");
    }
    await setIsSearchActive(!isSearchActive ? true : false);
    const inputElement = document.getElementById("search-msg-input");
    if (inputElement) {
      inputElement.focus();
    }
  };


  return (
    <>
      <Toolbar className="appBar">
        <Box sx={{ display: "flex" }}>
          <KeyboardBackspaceIcon className="cursor-pointer" onClick={() => navigate("/workout")} /> {/*onClick={handleLogOut} */}
          {/* <Typography className="chatTitle">Chat</Typography> */}
        </Box>
        {!isSearchActive ? (
          <Box className="readUnreadButton">
            <span
              className="readUnreadButton"
              style={!isUnRead ? { color: "#E67E22" } : {}}
            >
              {" "}
              All Messages
            </span>
            <Switch
              style={{ color: "#E67E22" }}
              value={isUnRead}
              checked={isUnRead}
              onChange={(e) => setIsUnRead(e.target.checked)}
              inputProps={{ "aria-label": "controlled" }}
            />
            <span
              className={`readUnreadButton ${isUnRead ? 'text-orange-500' : ''}`}
            >
              {" "}
              Unread
            </span>
          </Box>
        ) : (
          <Box className="relative text-center w-[300px]">
            <input
              id="search-msg-input"
              value={searchKeyword}
              onChange={(e) => setSearchKeyword(e.target.value)}
              placeholder="Search Buddies"
              type="text"
              className="search-message-input tracking-[1px]"
            />
          </Box>
        )}
      <Box
        className="flex items-center justify-center gap-1 cursor-pointer w-auto"
      >
        <img
          onClick={handleSetIsSearch}
          src={
            !isSearchActive
              ? "/images/searcher-icon.svg"
              : "/images/close-icon.png"
          }
          alt="search"
        />
        <svg
          onClick={() => setIsAddChat((prev) => !prev)}
          className="w-6 h-6 text-gray-800 dark:text-white"
          aria-hidden="true"
          xmlns="http://www.w3.org/2000/svg"
          width="24"
          height="24"
          fill="none"
          viewBox="0 0 24 24"
        >
          <path
            stroke="currentColor"
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="2"
            d="M16 12h4m-2 2v-4M4 18v-1a3 3 0 0 1 3-3h4a3 3 0 0 1 3 3v1a1 1 0 0 1-1 1H5a1 1 0 0 1-1-1Zm8-10a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"
          />
        </svg>
      </Box>
      </Toolbar>
    </>
  );
}
