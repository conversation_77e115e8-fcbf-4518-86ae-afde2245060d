import { Avatar, Box, Paper, Typography } from '@mui/material'
import React, { useEffect, useState } from 'react'
import PhotoOutlinedIcon from '@mui/icons-material/PhotoOutlined';
import EastOutlinedIcon from '@mui/icons-material/EastOutlined';
import FlightTakeoffOutlinedIcon from '@mui/icons-material/FlightTakeoffOutlined';
import "./../../../styles/individualChat.css"
import FooterFunctions from '../../../components/FooterFunctions';
import NavGroupChat from '../../../components/navbar/NavGroupChat';
import PlusToolbox from '../../../components/PlusToolbox';

export default function GroupChatOthers() {
    const paper = {
        padding: "0rem",
        minHeight: "100vh",
        maxWidth: '27rem',
        paddingBottom: "5rem",
        // position: 'relative',
        backgroundImage: 'url("https://cdn.wallpapersafari.com/80/83/K7l2qB.jpg")'
    };

    const data = [
        {
            date: 'Today',
            badge: '<PERSON><PERSON> created this group & added you',
            messages: [
                // {
                //     senderId: 1,
                //     msgType: 'text',
                //     msgText: "Hi Aisha! I’m Poornima",
                //     timeStamp: '10:30 AM'
                // },
                // {
                //     senderId: 1,
                //     msgType: 'text',
                //     msgText: "I am travelling to Chennai and wanted to connect with you!",
                //     timeStamp: '10:30 AM'
                // },
                // {
                //     senderId: 0,
                //     msgType: 'text',
                //     msgText: "Hi Poornima!",
                //     timeStamp: '10:30 AM'
                // },
                // {
                //     senderId: 1,
                //     msgType: 'photo',
                //     name: 'Tarun chawla',
                //     imageUrl: "https://vietnam.travel/sites/default/files/inline-images/Wallpaper_Ha%20Giang_Vietnam%20Tourism_0.jpg",
                //     profileUrl: "https://img.rawpixel.com/s3fs-private/rawpixel_images/website_content/rm328-366-tong-08_1.jpg?w=1200&h=1200&dpr=1&fit=clip&crop=default&fm=jpg&q=75&vib=3&con=3&usm=15&cs=srgb&bg=F4F4F3&ixlib=js-2.2.1&s=0d97acc6730f08828afe9434228cfa1c",
                //     timeAbout: '12 days ago',
                //     touristPlace: 'Rishikesh',
                //     timeStamp: '10:30 AM'
                // }
            ]
        }
    ]

    const [showToolBox, setShowToolBox] = useState(false)
    const [shouldHandleClick, setShouldHandleClick] = useState(false);


    useEffect(() => {
        const handleClick = () => {
            if (shouldHandleClick) {
                setShowToolBox(false)
            }
        };

        document.addEventListener('click', handleClick);

        return () => {
            document.removeEventListener('click', handleClick);
        };
    }, [shouldHandleClick]);

    return <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
        <Paper sx={{ flexGrow: 1 }} elevation={10} style={paper}>
            <NavGroupChat />
            {
                data?.map((item, index) => <React.Fragment key={index}>
                    <Box className="date-box-parent">
                        <Box className="date-box">
                            <Typography className='ind-chat-date'>{item.date}</Typography>
                        </Box>
                    </Box>
                    <Box className="date-box-parent">
                        <Box className="date-box" style={{ backgroundColor: "#FFFFFF", marginTop: '-0.8rem' }}>
                            <Typography className='creator-badge-text'>{item.badge}</Typography>
                        </Box>
                    </Box>
                    {
                        item.messages?.map((msg, index) => <React.Fragment>
                            {msg.msgType === "text" &&
                                <Box key={index} sx={
                                    msg.senderId === 0
                                        ? { paddingLeft: '1rem', paddingRight: "1rem", textAlign: "right", marginBottom: '6px', color: 'white' }
                                        : { paddingLeft: '1rem', paddingRight: "1rem", textAlign: "left", marginBottom: '6px' }
                                } >
                                    <Box className='text-msg-box' sx={msg.senderId === 0 ? { backgroundColor: "#056B6B" } : { backgroundColor: "#FFF388" }}>
                                        {msg.msgText}
                                        <Box className="text-msg-timing-parent">
                                            <Typography className='text-msg-timing'>{msg.timeStamp}</Typography>
                                        </Box>
                                    </Box>
                                </Box>}

                            {msg.msgType === "photo" &&
                                <Box className="photo-msg-parent" style={{ marginTop: "1.4rem" }}>
                                    <Box className="photo-msg-img-box" style={{ backgroundImage: `url(${msg.imageUrl})` }}>
                                        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-evenly' }}>
                                            <Box style={{ display: "flex", justifyContent: 'center', }}>
                                                <Avatar alt="Remy Sharp" sx={{ height: 25, width: 25 }} src={msg.profileUrl} />
                                                <Box className="photo-img-texts-aligns">
                                                    <Typography className='userName' fontWeight="fontWeightBold" sx={{ color: 'white', fontSize: '12px', paddingLeft: '0.6rem', lineHeight: 1.3 }}>{msg.name}</Typography>
                                                    <Typography className='userName' fontWeight="fontWeightNormal" sx={{ color: 'white', fontSize: '10px', lineHeight: 1.3 }}>{msg.touristPlace}</Typography>
                                                    <Typography className='userName' fontWeight="fontWeightNormal" sx={{ color: 'white', fontSize: '10px', lineHeight: 1.3 }}>{msg.timeAbout}</Typography>
                                                </Box>
                                            </Box>
                                            <Box className="photo-img-icon">
                                                <PhotoOutlinedIcon fontSize='20' />
                                            </Box>
                                        </Box>
                                    </Box>
                                    <Box className="photo-msg-view-post-btn">
                                        <Typography className='view-post-text'>
                                            View Post
                                        </Typography>
                                        <EastOutlinedIcon sx={{ fontSize: '14px', paddingLeft: '0.24rem', color: "#056B6B", marginTop: "-2px" }} />
                                    </Box>
                                    <Box className="text-msg-timing-parent">
                                        <Typography className='text-msg-timing'>{msg.timeStamp}</Typography>
                                    </Box>
                                </Box>}

                            {msg.msgType === "video" &&
                                <Box key={index} sx={
                                    msg.senderId === 0
                                        ? { paddingLeft: '1rem', paddingRight: "1rem", textAlign: "right", marginBottom: '6px', color: 'white' }
                                        : { paddingLeft: '1rem', paddingRight: "1rem", textAlign: "left", marginBottom: '6px' }
                                } >
                                    <Box className='video-msg-box' sx={msg.senderId === 0 ? { backgroundColor: "#056B6B" } : { backgroundColor: "#FFF388" }}>
                                        {msg.msgText}
                                        <Box className="video-box" style={{ backgroundImage: `url(${msg.videoThumbnail})` }} >
                                        </Box>
                                        <Box >
                                            <Typography className='text-msg-timing' style={{ paddingTop: '5px' }}>{msg.timeStamp}</Typography>
                                        </Box>
                                    </Box>
                                </Box>}

                            {msg.msgType === "joinPlanPost" &&
                                <Box key={index} sx={
                                    msg.senderId === 0
                                        ? { paddingLeft: '1rem', paddingRight: "1rem", textAlign: "right", marginBottom: '6px', color: 'white' }
                                        : { paddingLeft: '1rem', paddingRight: "1rem", textAlign: "left", marginBottom: '6px' }
                                } >
                                    <Box className='joinplan-msg-box' sx={msg.senderId === 0 ? { backgroundColor: "#056B6B" } : { backgroundColor: "#FFF388" }}>
                                        {msg.msgText}
                                        <Box className="joinplan-box" style={{ backgroundImage: `url(${msg.videoThumbnail})` }} >

                                            <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', padding: '0.5rem' }}>
                                                <Box style={{ display: "flex", justifyContent: 'center', }}>
                                                    <Avatar alt="Remy Sharp" sx={{ height: 25, width: 25 }} src={msg.profileUrl} />
                                                    <Box className="photo-img-texts-aligns" >
                                                        <Box style={{ display: "flex" }}>
                                                            <Typography className='userName' fontWeight="fontWeightBold" sx={{ fontSize: '12px', paddingLeft: '0.6rem', lineHeight: 1.3 }}>Tarun Chandra</Typography>
                                                            <Typography className='isTravellingTotext' >is travelling to</Typography>
                                                        </Box>
                                                        <Typography className='isTravellingTotext' >
                                                            {msg.travellingToPlace}
                                                        </Typography>
                                                    </Box>
                                                </Box>
                                                <Box className="photo-img-icon">
                                                    <FlightTakeoffOutlinedIcon fontSize='20' />
                                                </Box>
                                            </Box>

                                            <Typography className='join-plan-desc'>
                                                {msg.desc}
                                            </Typography>

                                        </Box>
                                        <Box style={{ display: 'flex' }}>
                                            <Box className='join-plan-btns'>Join this plan</Box>
                                            <Box className="view-post-btns">View Post</Box>
                                        </Box>
                                        <Box >
                                            <Typography className='text-msg-timing' style={{ paddingTop: '5px', textAlign: 'right' }}>{msg.timeStamp}</Typography>
                                        </Box>
                                    </Box>
                                </Box>}
                        </React.Fragment>)
                    }
                </React.Fragment>)
            }
            {showToolBox && <PlusToolbox />}
            <FooterFunctions setShowToolBox={setShowToolBox} />
        </Paper >
    </Box >
}