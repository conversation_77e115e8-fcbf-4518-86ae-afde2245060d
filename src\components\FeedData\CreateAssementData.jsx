import {
  Chip,
  FormControl,
  FormLabel,
  Grid,
  MenuItem,
  OutlinedInput,
  Select,
  TextField,
} from "@mui/material";
import { But<PERSON>, Modal } from "antd";
import React, { useEffect, useState } from "react";
import {
  CreateLevels,
  CreatePrograms,
  CreateZonesClasification,
  createAssesmentdata,
  getAllPrograms,
  getAlllevels,
  updateAssesmentdata,
  updateLevel,
  updatePrograms,
  updateZonesClasification,
  weeklyFeedDataPattern,
  weeklyFeedDataProgram,
} from "../../API/api-endpoint";
import { useFormik } from "formik";
import Swal from "sweetalert2";
import SlickCarousel from "../../pages/SlickCarousel";
const scoreData = [1, 2, 3, 4, 5];
const CreateAssementData = ({
  fetchReport,
  setShowAssesmentModal,
  showAssesmentModal,
  editData,
  setEditData,
}) => {
  const [programList, setProgramList] = useState([]);
  console.log("editData", editData);
  const formik = useFormik({
    initialValues: {
      name: "",
    },
    validate: (values) => {
      const errors = {};
      if (!values.name) {
        errors.name = "Name is required";
      }
      return errors;
    },
    // validationSchema: {},
    onSubmit: (values, { resetForm }) => {
      handleSubmitAssesmentForm(values, resetForm);
    },
  });
  console.log("formik", formik?.values);
  const getAllProgramsData = async () => {
    const response = await getAllPrograms();
    console.log("response", response);
    setProgramList(response);
  };
  useEffect(() => {
    getAllProgramsData();
  }, []);

  const handleSubmitAssesmentForm = async (data, resetForm) => {
    let response = "";
    if (editData?.id) {
      response = await updateAssesmentdata(data);
    } else {
      response = await createAssesmentdata(data);
    }
    if (response?.status) {
      Swal.fire({
        title: "Success",
        text: response.message,
        icon: "success",
      });
      setShowAssesmentModal(false);
      setEditData({});
      fetchReport();
      resetForm();
      formik?.setValues({ level: "" });
    } else {
      Swal.fire({
        title: "Error",
        text: response.message,
        icon: "error",
      });
    }
    console.log("response", response);
  };
  useEffect(() => {
    if (editData?.id) {
      const { srID, ...data } = editData;
      console.log("data", data);
      formik?.setValues(data);
    } else {
      setEditData({});
    }
  }, [editData?.id]);
  return (
    <Modal
      width={1200}
      open={showAssesmentModal}
      onCancel={() => {
        setShowAssesmentModal(false);
        setEditData({});
        formik.resetForm();
        formik?.setValues({ name: "" });
      }}
      footer={
        <div></div>
        //   loading={isLoading}
      }
    >
      <div className="headingCont">
        <span className="heading">{editData?.id ? "Edit " : "Create"}</span>{" "}
        <span className="orange heading">Assessment</span>
      </div>

      <div className="parentCont">
        <form className="form1" onSubmit={formik.handleSubmit}>
          <Grid container spacing={2}>
            <Grid item xs={12} sm={11}>
              <FormLabel>Name<span className="text-[red]">*</span></FormLabel>

              <TextField
                fullWidth
                placeholder="Wellness name"
                size="small"
                type="text"
                name="name"
                value={formik?.values?.name}
                onChange={formik.handleChange}
                error={formik.touched.name && formik.errors.name}
                helperText={formik.touched.name && formik.errors.name}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <Button
                className="btn"
                key="submit"
                type="primary"
                onClick={() => formik.handleSubmit()}
              >
                Submit
              </Button>
            </Grid>
          </Grid>
        </form>
        <div className="slick-container">
          <SlickCarousel />
        </div>
      </div>
    </Modal>
  );
};

export default CreateAssementData;
