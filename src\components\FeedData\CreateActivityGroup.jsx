import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>rid, TextField } from '@mui/material';
import { <PERSON><PERSON>, <PERSON>, TimePicker } from 'antd'
import React, { useEffect, useState } from 'react'
import { createActivityGroupdata,  updateActivityGroupdata, uploadsaveFile } from '../../API/api-endpoint';
import { useFormik } from 'formik';
import Swal from 'sweetalert2';
import { IoCloseSharp } from "react-icons/io5";
import SlickCarousel from "../../pages/SlickCarousel";

const CreateActivityGroup = ({ fetchReport, setShowAssesmentModal, showAssesmentModal, editData, setEditData }) => {
    const formik = useFormik({
        initialValues: {
            activity_group: "",
            badge: "",
        }, validate: (values) => {
            const errors = {};
            if (!values.activity_group) {
                errors.activity_group = "Activity Group is required";
            }
           
            if (!values.badge) {
                errors.badge = "Badge is required";
            }
        
            return errors;
        },
        // validationSchema: {},
        onSubmit: (values, { resetForm }) => {
            handleSubmitAssesmentForm(values, resetForm)

        },
    });
    const handleSubmitAssesmentForm = async (data, resetForm) => {
        let response = ""
        if (editData?.id) {

            response = await updateActivityGroupdata(data)

        } else {
            response = await createActivityGroupdata(data)

        }
        if (response?.status) {
            Swal.fire({
                title: "Success",
                text: response.message,
                icon: "success",
            });
            setEditData({})
            setShowAssesmentModal(false)
            fetchReport()
            formik.resetForm()
            formik?.setValues({
                activity_group: "",
                badge: "",
            })
        } else {
            Swal.fire({
                title: "Error",
                text: response.message,
                icon: "error",
            });
        }
        console.log("response", response);
    }
    useEffect(() => {
        if (editData?.id) {
            const { srID, ...data } = editData
            formik?.setValues(data)
        }
    }, [editData?.id])

    const handleFileUpload = async (event) => {
        const file = event.target.files[0];
        if (file) {

            const formData = new FormData();
            formData.append('file', file);

            const responst = await uploadsaveFile(formData)
            if (responst?.status) {

                formik.setFieldValue("badge", responst?.file)
            }
        }
    }
    return (
        <Modal
      width={1200}
      open={showAssesmentModal}
      onCancel={() => {
        setShowAssesmentModal(false);
        formik.resetForm();
        setEditData({});
        formik?.setValues({
          activity_group: "",
          badge: "",
        });
      }}
      footer={
        <div></div>
        //   loading={isLoading}
      }
    >
      <div className="headingCont">
        <span className="heading">{editData?.id ? "Edit " : "Create"}</span>{" "}
        <span className="orange heading">Activity Group</span>
       </div>

      <div className="parentCont">
        <form className="form1" onSubmit={formik.handleSubmit}>
          <Grid container spacing={2}>
            <Grid item xs={12} sm={11}>
              <FormLabel>Activity Group Name<span className="text-[red]">*</span></FormLabel>

              <TextField
                fullWidth
                placeholder="Activity Group Name"
                size="small"
                type="text"
                name="activity_group"
                value={formik?.values?.activity_group}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                error={
                  formik.touched.activity_group && formik.errors.activity_group
                }
                helperText={
                  formik.touched.activity_group && formik.errors.activity_group
                }
              />
            </Grid>
            <Grid item xs={12} sm={11} className="relative">
              <FormLabel>Upload Badge<span className="text-[red]">*</span></FormLabel>

              <TextField
                fullWidth
                placeholder="Zone"
                size="small"
                type={formik?.values?.badge ? "text" : "file"}
                name="badge"
                disabled={formik?.values?.badge}
                value={formik?.values?.badge}
                onChange={handleFileUpload}
                error={formik.touched.badge && formik.errors.badge}
                helperText={formik.touched.badge && formik.errors.badge}
                inputProps={{
                  accept: ".jpg, .png .jpeg", // Specify accepted file formats here
                }}
              />
              {formik?.values?.badge && (
                <IoCloseSharp
                  onClick={() => {
                    formik.setFieldValue("badge", "");
                  }}
                  color="darkgray"
                  className="cursor-pointer absolute top-0 right-[-10px]"
                  style={{ fontSize: "24px" }}
                />
              )}
            </Grid>
            <Grid item xs={12} sm={6}>
              <Button
                className="btn"
                key="submit"
                type="primary"
                onClick={() => formik.handleSubmit()}
              >
                Submit
              </Button>
            </Grid>
          </Grid>
        </form>

        <div className="slick-container">
          <SlickCarousel />
        </div>
      </div>
    </Modal>
  );
};
export default CreateActivityGroup;
