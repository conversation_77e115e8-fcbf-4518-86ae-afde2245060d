import React from 'react'
import { Select } from '@mantine/core'
import Header from '../components/Header'

const YFAReportPage = () => {
  return (
    <>
      <Header />
      {/* Topbar */}
      <div className='p-6'>
        <div className='flex items-start gap-x-2'>
          <h1 className='font-medium text-xl'>YFA Report</h1>
        </div>
        <br />
        <div className='flex flex-wrap gap-4 md:gap-8 items-end'>
          <Select
            label='Duration'
            data={[
              { value: 'Today', label: 'Today' },
              { value: 'Yesterday', label: 'Yesterday' },
              { value: 'This week', label: 'This week' },
              { value: 'Last 3 days', label: 'Last 3 days' },
              { value: 'Last 5 days', label: 'Last 5 days' },
              { value: 'Last 7 days', label: 'Last 7 days' },
              { value: 'Last week', label: 'Last week' }
            ]}
          />
          <Select
            label='Program'
            data={[
              { value: 'IPC', label: 'IPC' },
              { value: '12 Weeks', label: '12 Weeks' },
              { value: '24 Weeks', label: '24 Weeks' },
              { value: '100 Days challenge', label: '100 Days challenge' }
            ]}
          />
          <Select label='Select company' data={[{ value: '', label: '' }]} />
          <Select
            label='Workout status'
            data={[
              { value: 'All', label: 'All' },
              { value: 'None', label: 'None' },
              { value: 'Partial', label: 'Partial' }
            ]}
          />
          <Select
            label='Level'
            data={[
              { value: 'Level 1', label: 'Level 1' },
              { value: 'Level 2', label: 'Level 2' },
              { value: 'Level 3', label: 'Level 3' },
              { value: 'Level 4', label: 'Level 4' },
              { value: 'Level 5', label: 'Level 5' }
            ]}
          />
          <Select
            label='Smiley status'
            data={[
              { value: 'Smiley updated', label: 'Smiley updated' },
              { value: 'Smiley not updated', label: 'Smiley not updated' },
              { value: 'Smiley 1', label: 'Smiley 1' },
              { value: 'Smiley 2', label: 'Smiley 2' },
              { value: 'Smiley 3', label: 'Smiley 3' },
              { value: 'Smiley 4', label: 'Smiley 4' },
              { value: 'Smiley 5', label: 'Smiley 5' },
              { value: 'Any', label: 'Any' }
            ]}
          />
          <button className='py-1.5 px-5 bg-orange-500 text-slate-50 rounded text-base'>
            Submit
          </button>
        </div>
        <br />
        <div className='flex flex-wrap gap-4 items-end'>
          <button className='py-2 px-5 bg-green-600 text-slate-50 rounded text-sm'>
            Notify
          </button>
          <button className='py-2 px-5 bg-green-600 text-slate-50 rounded text-sm'>
            Export as CSV
          </button>
        </div>
        {/* Table */}
        <div className='w-full mb-6'>
          <div className='my-6'>
            <div className='flex flex-col'>
              <div className='overflow-x-auto shadow-md'>
                <div className='inline-block min-w-full align-middle'>
                  <div className='overflow-hidden '>
                    <table className='min-w-full divide-y divide-gray-200 table-fixed dark:divide-gray-700'>
                      <thead className='bg-gray-100 '>
                        <tr>
                          <th
                            scope='col'
                            className='py-3 px-6 text-xs font-medium tracking-wider text-left text-gray-700 uppercase dark:text-gray-400'
                          >
                            Name
                          </th>
                          <th
                            scope='col'
                            className='py-3 px-6 text-xs font-medium tracking-wider text-left text-gray-700 uppercase dark:text-gray-400'
                          >
                            Email
                          </th>
                          <th
                            scope='col'
                            className='py-3 px-6 text-xs font-medium tracking-wider text-left text-gray-700 uppercase dark:text-gray-400'
                          >
                            Phone Number
                          </th>
                          <th
                            scope='col'
                            className='py-3 px-6 text-xs font-medium tracking-wider text-left text-gray-700 uppercase dark:text-gray-400'
                          >
                            Fitness Level
                          </th>
                          <th
                            scope='col'
                            className='py-3 px-6 text-xs font-medium tracking-wider text-left text-gray-700 uppercase dark:text-gray-400'
                          >
                            Duration
                          </th>
                        </tr>
                      </thead>
                      <tbody className='bg-white divide-y divide-gray-200 '></tbody>
                    </table>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  )
}

export default YFAReportPage
