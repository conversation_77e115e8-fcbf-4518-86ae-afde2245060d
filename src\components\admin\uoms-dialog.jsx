import { useState, useEffect } from "react";
import { But<PERSON> } from "../ui/button";
import { Input } from "../ui/input";
import { Label } from "../ui/label";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "../ui/select";
import { <PERSON>alog, DialogContent, DialogHeader, DialogTitle } from "../ui/dialog";
import { createuomsdata, updateuomsdata } from "../../API/api-endpoint";
import Swal from "sweetalert2";

export const UomsDialog = ({ open, onClose, onSuccess, editingItem }) => {
	const [formData, setFormData] = useState({
		uom_name: "",
		status: true,
	});
	const [isLoading, setIsLoading] = useState(false);

	useEffect(() => {
		if (open) {
			if (editingItem?.uom_id) {
				const editData = {
					uom_name: editingItem.uom_name || "",
					status:
						editingItem.status !== undefined
							? editingItem.status
							: true,
				};
				setFormData(editData);
			} else {
				const newData = {
					uom_name: "",
					status: true,
				};
				setFormData(newData);
			}
		}
	}, [open, editingItem]);

	const handleInputChange = (field, value) => {
		setFormData((prev) => ({
			...prev,
			[field]: value,
		}));
	};

	const handleSubmit = async (e) => {
		e.preventDefault();

		if (!formData.uom_name.trim()) {
			Swal.fire({
				title: "Error",
				text: "UOM name is required",
				icon: "error",
				timer: 1800,
				showConfirmButton: false,
			});
			return;
		}

		try {
			setIsLoading(true);

			const apiData = {
				uom_name: formData.uom_name.trim(),
				status: formData.status,
			};

			let response;
			if (editingItem?.uom_id) {
				apiData.uom_id = editingItem.uom_id;
				response = await updateuomsdata(apiData);
			} else {
				response = await createuomsdata(apiData);
			}

			if (response?.status) {
				Swal.fire({
					title: "Success",
					text:
						response.message ||
						`UOM ${
							editingItem?.uom_id ? "updated" : "created"
						} successfully`,
					icon: "success",
					timer: 2000,
					showConfirmButton: false,
				});
				onSuccess();
			} else {
				Swal.fire({
					title: "Error",
					text: response?.message || "Failed to save UOM",
					icon: "error",
					timer: 3000,
					showConfirmButton: false,
				});
			}
		} catch (error) {
			console.error("Error saving UOM:", error);
			Swal.fire({
				title: "Error",
				text: "An error occurred while saving the UOM",
				icon: "error",
				timer: 3000,
				showConfirmButton: false,
			});
		} finally {
			setIsLoading(false);
		}
	};

	return (
		<Dialog open={open} onOpenChange={onClose}>
			<DialogContent className='sm:max-w-md bg-white max-h-[90vh] overflow-y-auto'>
				<DialogHeader>
					<DialogTitle className='text-lg font-semibold text-gray-900'>
						{editingItem?.uom_id ? "Edit UOM" : "Create UOM"}
					</DialogTitle>
				</DialogHeader>

				<form onSubmit={handleSubmit} className='space-y-4'>
					<div className='grid gap-4'>
						<div className='space-y-2'>
							<Label
								htmlFor='uom_name'
								className='text-sm font-semibold'
							>
								UOM Name <span className='text-red-500'>*</span>
							</Label>
							<Input
								id='uom_name'
								className='w-full text-sm'
								value={formData.uom_name}
								onChange={(e) =>
									handleInputChange(
										"uom_name",
										e.target.value
									)
								}
								placeholder='Enter UOM name'
								disabled={isLoading}
								required
							/>
						</div>

						<div className='space-y-2'>
							<Label
								htmlFor='status'
								className='text-sm font-semibold'
							>
								Status
							</Label>
							<Select
								value={formData.status.toString()}
								onValueChange={(value) =>
									handleInputChange(
										"status",
										value === "true"
									)
								}
								disabled={isLoading}
							>
								<SelectTrigger className='w-full text-sm'>
									<SelectValue placeholder='Select Status' />
								</SelectTrigger>
								<SelectContent className='bg-white'>
									<SelectItem value='true'>Yes</SelectItem>
									<SelectItem value='false'>No</SelectItem>
								</SelectContent>
							</Select>
						</div>
					</div>

					<div className='flex justify-end gap-3 pt-4'>
						<Button
							type='button'
							variant='outline'
							onClick={onClose}
							disabled={isLoading}
						>
							Cancel
						</Button>
						<Button
							type='submit'
							className='bg-orange-600 hover:bg-orange-700 text-white'
							disabled={isLoading}
						>
							{isLoading
								? "Saving..."
								: editingItem?.uom_id
								? "Update UOM"
								: "Create UOM"}
						</Button>
					</div>
				</form>
			</DialogContent>
		</Dialog>
	);
};
