import React from 'react'
import { Navigate } from 'react-router-dom';
import { useSearchParams } from "react-router-dom";


export const ProtectedRoutes = ({children}) => {
  const token = localStorage.getItem('token');
  const userSubsription = localStorage.getItem('userSubsription');
  // const invitationParams = localStorage.getItem('invitationParams');
  // const parsedParams = JSON.parse(invitationParams);
  // const channelId = parsedParams?.channelId;
  const [searchParams] = useSearchParams();
  const channelId = searchParams.get("channelId");
  // const channelName = searchParams.get("channelName");
  
  if (token === null && (channelId === undefined || channelId === null)) {
    return <Navigate to="/" replace />
  }

  return children
}