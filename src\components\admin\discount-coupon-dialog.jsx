import { useState, useEffect } from "react";
import { But<PERSON> } from "../ui/button";
import { Input } from "../ui/input";
import { Label } from "../ui/label";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "../ui/select";
import { <PERSON>alog, DialogContent, DialogHeader, DialogTitle } from "../ui/dialog";
import {
	creatediscountcoupondata,
	updatediscountcoupondata,
	getsubspackageData,
} from "../../API/api-endpoint";
import Swal from "sweetalert2";
import moment from "moment";

export const DiscountCouponDialog = ({
	open,
	onClose,
	onSuccess,
	editingItem,
}) => {
	const [formData, setFormData] = useState({
		coupon: "",
		type: "",
		subscription_package_id: "",
		valid_to: "",
		discount: "",
		max_usage: "",
		status: true,
	});
	const [isLoading, setIsLoading] = useState(false);
	const [subscriptionPackages, setSubscriptionPackages] = useState([]);

	useEffect(() => {
		const fetchSubscriptionPackages = async () => {
			try {
				const response = await getsubspackageData();
				setSubscriptionPackages(response || []);
			} catch (error) {
				console.error("Error fetching subscription packages:", error);
			}
		};

		if (open) {
			fetchSubscriptionPackages();
		}
	}, [open]);

	useEffect(() => {
		if (open) {
			if (editingItem?.id) {
				const editData = {
					coupon: editingItem.coupon || "",
					type: editingItem.type || "",
					subscription_package_id:
						editingItem.subscription_package_id?.toString() || "",
					valid_to: editingItem.valid_to
						? moment(editingItem.valid_to).format("YYYY-MM-DD")
						: "",
					discount: editingItem.discount?.toString() || "",
					max_usage: editingItem.max_usage?.toString() || "",
					status:
						editingItem.status !== undefined
							? editingItem.status
							: true,
				};
				setFormData(editData);
			} else {
				const newData = {
					coupon: "",
					type: "",
					subscription_package_id: "",
					valid_to: "",
					discount: "",
					max_usage: "",
					status: true,
				};
				setFormData(newData);
			}
		}
	}, [open, editingItem]);

	const handleInputChange = (field, value) => {
		setFormData((prev) => ({
			...prev,
			[field]: value,
		}));
	};

	const handleSubmit = async (e) => {
		e.preventDefault();

		if (!formData.coupon.trim()) {
			Swal.fire({
				title: "Error",
				text: "Coupon code is required",
				icon: "error",
				timer: 1800,
				showConfirmButton: false,
			});
			return;
		}

		if (!formData.type) {
			Swal.fire({
				title: "Error",
				text: "Type is required",
				icon: "error",
				timer: 1800,
				showConfirmButton: false,
			});
			return;
		}

		if (!formData.subscription_package_id) {
			Swal.fire({
				title: "Error",
				text: "Subscription package is required",
				icon: "error",
				timer: 1800,
				showConfirmButton: false,
			});
			return;
		}

		if (!formData.discount.trim()) {
			Swal.fire({
				title: "Error",
				text: "Discount is required",
				icon: "error",
				timer: 1800,
				showConfirmButton: false,
			});
			return;
		}

		if (!formData.max_usage.trim()) {
			Swal.fire({
				title: "Error",
				text: "Max usage is required",
				icon: "error",
				timer: 1800,
				showConfirmButton: false,
			});
			return;
		}

		if (!formData.valid_to) {
			Swal.fire({
				title: "Error",
				text: "Valid to date is required",
				icon: "error",
				timer: 1800,
				showConfirmButton: false,
			});
			return;
		}

		try {
			setIsLoading(true);

			const apiData = {
				coupon: formData.coupon.trim(),
				type: formData.type,
				subscription_package_id: parseInt(
					formData.subscription_package_id
				),
				valid_to: formData.valid_to,
				discount: parseFloat(formData.discount),
				max_usage: parseInt(formData.max_usage),
				status: formData.status,
				current_usage: 0,
			};

			let response;
			if (editingItem?.id) {
				apiData.id = editingItem.id;
				response = await updatediscountcoupondata(apiData);
			} else {
				response = await creatediscountcoupondata(apiData);
			}

			if (response?.status) {
				Swal.fire({
					title: "Success",
					text:
						response.message ||
						`Discount coupon ${
							editingItem?.id ? "updated" : "created"
						} successfully`,
					icon: "success",
					timer: 2000,
					showConfirmButton: false,
				});
				onSuccess();
			} else {
				Swal.fire({
					title: "Error",
					text: response?.message || "Failed to save discount coupon",
					icon: "error",
					timer: 3000,
					showConfirmButton: false,
				});
			}
		} catch (error) {
			console.error("Error saving discount coupon:", error);
			Swal.fire({
				title: "Error",
				text: "An error occurred while saving the discount coupon",
				icon: "error",
				timer: 3000,
				showConfirmButton: false,
			});
		} finally {
			setIsLoading(false);
		}
	};

	return (
		<Dialog open={open} onOpenChange={onClose}>
			<DialogContent className='sm:max-w-2xl bg-white max-h-[90vh] overflow-y-auto'>
				<DialogHeader>
					<DialogTitle className='text-lg font-semibold text-gray-900'>
						{editingItem?.id
							? "Edit Discount Coupon"
							: "Create Discount Coupon"}
					</DialogTitle>
				</DialogHeader>

				<form onSubmit={handleSubmit} className='space-y-4'>
					<div className='grid gap-4'>
						<div className='grid grid-cols-1 sm:grid-cols-2 gap-4'>
							<div className='space-y-2'>
								<Label
									htmlFor='subscription_package_id'
									className='text-sm font-semibold'
								>
									Subscription Package{" "}
									<span className='text-red-500'>*</span>
								</Label>
								<Select
									value={formData.subscription_package_id}
									onValueChange={(value) =>
										handleInputChange(
											"subscription_package_id",
											value
										)
									}
									disabled={isLoading}
								>
									<SelectTrigger className='w-full text-sm'>
										<SelectValue placeholder='Select Subscription Package' />
									</SelectTrigger>
									<SelectContent className='bg-white'>
										{subscriptionPackages.map((pkg) => (
											<SelectItem
												key={pkg.id}
												value={pkg.id.toString()}
											>
												{pkg.name}
											</SelectItem>
										))}
									</SelectContent>
								</Select>
							</div>
							<div className='space-y-2'>
								<Label
									htmlFor='coupon'
									className='text-sm font-semibold'
								>
									Coupon Code{" "}
									<span className='text-red-500'>*</span>
								</Label>
								<Input
									id='coupon'
									className='w-full text-sm'
									value={formData.coupon}
									onChange={(e) =>
										handleInputChange(
											"coupon",
											e.target.value
										)
									}
									placeholder='Enter coupon code'
									disabled={isLoading}
									required
								/>
							</div>

							<div className='space-y-2'>
								<Label
									htmlFor='type'
									className='text-sm font-semibold'
								>
									Type <span className='text-red-500'>*</span>
								</Label>
								<Select
									value={formData.type}
									onValueChange={(value) =>
										handleInputChange("type", value)
									}
									disabled={isLoading}
								>
									<SelectTrigger className='w-full text-sm'>
										<SelectValue placeholder='Select Type' />
									</SelectTrigger>
									<SelectContent className='bg-white'>
										<SelectItem value='percentage'>
											Percentage
										</SelectItem>
										<SelectItem value='fixed'>
											Fixed
										</SelectItem>
									</SelectContent>
								</Select>
								<div className='space-y-2'>
									<Label
										htmlFor='discount'
										className='text-sm font-semibold'
									>
										Discount{" "}
										<span className='text-red-500'>*</span>
									</Label>
									<Input
										id='discount'
										type='number'
										step='0.01'
										className='w-full text-sm'
										value={formData.discount}
										onChange={(e) =>
											handleInputChange(
												"discount",
												e.target.value
											)
										}
										placeholder='Enter discount amount'
										disabled={isLoading}
										required
									/>
								</div>
							</div>
						</div>

						<div className='grid grid-cols-1 sm:grid-cols-2 gap-4'>
							<div className='space-y-2'>
								<Label
									htmlFor='max_usage'
									className='text-sm font-semibold'
								>
									Max Usage{" "}
									<span className='text-red-500'>*</span>
								</Label>
								<Input
									id='max_usage'
									type='number'
									className='w-full text-sm'
									value={formData.max_usage}
									onChange={(e) =>
										handleInputChange(
											"max_usage",
											e.target.value
										)
									}
									placeholder='Enter max usage'
									disabled={isLoading}
									required
								/>
							</div>
						</div>

					
					</div>

					<div className='flex justify-end gap-3 pt-4'>
						<Button
							type='button'
							variant='outline'
							onClick={onClose}
							disabled={isLoading}
						>
							Cancel
						</Button>
						<Button
							type='submit'
							className='bg-orange-600 hover:bg-orange-700 text-white'
							disabled={isLoading}
						>
							{isLoading
								? "Saving..."
								: editingItem?.id
								? "Update Coupon"
								: "Create Coupon"}
						</Button>
					</div>
				</form>
			</DialogContent>
		</Dialog>
	);
};
