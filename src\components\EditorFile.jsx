import React, { useState, useEffect } from "react";
import { Editor } from "react-draft-wysiwyg";
import "react-draft-wysiwyg/dist/react-draft-wysiwyg.css";
import {
  EditorState,
  RichUtils,
  getDefaultKeyBinding,
  ContentState,
  convertFromHTML,
} from "draft-js";
import draftToHtml from "draftjs-to-html";
import { convertToRaw } from "draft-js";
import htmlToDraft from "html-to-draftjs";

const RichTextEditor = ({
  initialContent = null,
  placeholder = "Start typing...",
  onContentChange,
  toolbarOptions = {},
  customClassNames = {},
  initial,
  setInitial
}) => {
  const [editorState, setEditorState] = useState(EditorState.createEmpty());

  const handleKeyBindings = (e) => {
    if (e.keyCode === 9) {
      setEditorState(RichUtils.onTab(e, editorState, 4));
    } else {
      return getDefaultKeyBinding(e);
    }
  };

  const handleEditorChange = (newEditorState) => {
    setEditorState(newEditorState);
  
    try {
      // Convert the editor's content state to raw JS object
      const contentState = newEditorState.getCurrentContent();
      const rawContentState = convertToRaw(contentState);
  
      // Convert the raw content state to HTML
      const html = draftToHtml(rawContentState);
  
      console.log("HTML content:", html); // Log HTML content for debugging
  
      if (onContentChange) {
        onContentChange(html);
      }
    } catch (error) {
      console.error("Error converting to HTML:", error);
    }
  };
  useEffect(() => {
    if (initial ) {
      if (typeof initialContent === "string") {
        const { contentBlocks, entityMap } = htmlToDraft(initialContent);
        const contentState = ContentState.createFromBlockArray(contentBlocks, entityMap);
        const editorState = EditorState.createWithContent(contentState);
        setEditorState(editorState);
        setInitial(false);
      }
    }
  }, [initial, initialContent, setInitial]);

  return (
    <div className="RichTextEditor">
      <Editor
        editorState={editorState}
        toolbarClassName={customClassNames.toolbar || "toolbar-class"}
        wrapperClassName={customClassNames.wrapper || "wrapper-class"}
        editorClassName={customClassNames.editor || "editor-class"}
        onEditorStateChange={handleEditorChange}
        onTab={handleKeyBindings}
        placeholder={placeholder}
        toolbar={toolbarOptions}
      />
    </div>
  );
};

export default RichTextEditor;
