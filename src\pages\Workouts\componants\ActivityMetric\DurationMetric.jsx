import React from "react";
import { Bar } from "react-chartjs-2";
import {
    Chart as ChartJS,
    CategoryScale,
    LinearScale,
    BarElement,
    Title,
    Tooltip,
    Legend,
} from "chart.js";
import { timeToHours } from "../../../../utils/metricConversion";

// Register necessary Chart.js components
ChartJS.register(CategoryScale, LinearScale, BarElement, Title, Tooltip, Legend);

const DurationMetric = ({ activity, data }) => {
    const { plannedDuration, actualDuration, durationUnit } = data;
    console.log("activity --> ", activity);

    // Convert time values to decimal hours
    const plannedHours = timeToHours(plannedDuration);
    const actualHours = timeToHours(actualDuration);

    // Chart Data
    const chartData = {
        labels: ["Duration"],
        datasets: [
            {
                label: "Planned",
                data: [plannedHours],
                backgroundColor: "rgba(54, 162, 235, 0.7)",
                barThickness: 20
            },
            {
                label: "Actual",
                data: [actualHours],
                backgroundColor: "rgba(255, 99, 132, 0.7)",
                barThickness: 20
            },
        ],
    };

    // Chart Options
    const options = {
        responsive: true,
        plugins: {
            title: {
                display: true,
                text: `Duration [planned(${plannedHours}${durationUnit}) v/s actual(${actualHours.toFixed(2)}${durationUnit})]`,
            },
            tooltip: {
                callbacks: {
                    label: function (context) {
                        return `${(context.raw < 1) ? (context.raw * 60).toFixed(1) : context.raw.toFixed(2)} ${(context.raw < 1) ? "minutes" : durationUnit}`; // Display with 2 decimal places
                    },
                },
            },
        },
        scales: {
            x: {
                barPercentage: 0.8, // Adjust bar width (0.1 to 1.0)
                categoryPercentage: 0.6, // Adjust category spacing (0.1 to 1.0)
            },
            y: {
                beginAtZero: true,
                title: {
                    display: true,
                    text: `Duration (${durationUnit})`,
                },
            },
        },
    };

    return <Bar data={chartData} options={options} />;
};

export default DurationMetric;
