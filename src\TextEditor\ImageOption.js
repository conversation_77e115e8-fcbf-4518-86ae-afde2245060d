import React, { Component } from "react";
import PropTypes from "prop-types";
import { EditorState, AtomicBlockUtils } from "draft-js";

import { getClearSelectionEditor } from "./EditFile";

class ImageOption extends Component {
  constructor(props) {
    super(props);
    this.addImage = this.addImage.bind(this);
    this.uploadImage = this.uploadImage.bind(this);
    this.fileInput = React.createRef();
  }

  uploadImage(event) {
    const file = event.target.files[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = () => {
        this.addImage(reader.result);
      };
      reader.readAsDataURL(file);
    }
  }

  addImage(src) {
    const { editorState, onChange } = this.props;
    const contentStateWithEntity = editorState
      .getCurrentContent()
      .createEntity("IMAGE", "IMMUTABLE", {
        src,
        alt: "Uploaded image",
        width: "250px", // Default width
        height: "auto", // Default height to maintain aspect ratio
      });
    const entityKey = contentStateWithEntity.getLastCreatedEntityKey();
    const tmpEditorState = EditorState.set(editorState, {
      currentContent: contentStateWithEntity,
    });
    const newEditorState = AtomicBlockUtils.insertAtomicBlock(
      tmpEditorState,
      entityKey,
      " "
    );
    onChange(getClearSelectionEditor(newEditorState));
  }

  render() {
    return (
      <div>
        <input
          type="file"
          ref={this.fileInput}
          onChange={this.uploadImage}
          style={{ display: "none" }}
          accept="image/*"
        />
        <div
          className="rdw-option-wrapper"
          title="Upload Image"
          onClick={() => this.fileInput.current.click()}
        >
          <img
            src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTUiIGhlaWdodD0iMTQiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGcgZmlsbD0iIzAwMCIgZmlsbC1ydWxlPSJldmVub2RkIj48cGF0aCBkPSJNMTQuNzQxIDBILjI2Qy4xMTYgMCAwIC4xMzYgMCAuMzA0djEzLjM5MmMwIC4xNjguMTE2LjMwNC4yNTkuMzA0SDE0Ljc0Yy4xNDMgMCAuMjU5LS4xMzYuMjU5LS4zMDRWLjMwNEMxNSAuMTM2IDE0Ljg4NCAwIDE0Ljc0MSAwem0tLjI1OCAxMy4zOTFILjUxN1YuNjFoMTMuOTY2VjEzLjM5eiIvPjxwYXRoIGQ9Ik00LjEzOCA2LjczOGMuNzk0IDAgMS40NC0uNzYgMS40NC0xLjY5NXMtLjY0Ni0xLjY5NS0xLjQ0LTEuNjk1Yy0uNzk0IDAtMS40NC43Ni0xLjQ0IDEuNjk1IDAgLjkzNC42NDYgMS42OTUgMS40NCAxLjY5NXptMC0yLjc4MWMuNTA5IDAgLjkyMy40ODcuOTIzIDEuMDg2IDAgLjU5OC0uNDE0IDEuMDg2LS45MjMgMS4wODYtLjUwOSAwLS45MjMtLjQ4Ny0uOTIzLTEuMDg2IDAtLjU5OS40MTQtMS4wODYuOTIzLTEuMDg2ek0xLjgxIDEyLjE3NGMuMDYgMCAuMTIyLS4wMjUuMTcxLS4wNzZMNi4yIDcuNzI4bDIuNjY0IDMuMTM0YS4yMzIuMjMyIDAgMCAwIC4zNjYgMCAuMzQzLjM0MyAwIDAgMCAwLS40M0w3Ljk4NyA4Ljk2OWwyLjM3NC0zLjA2IDIuOTEyIDMuMTQyYy4xMDYuMTEzLjI3LjEwNS4zNjYtLjAyYS4zNDMuMzQzIDAgMCAwLS4wMTYtLjQzbC0zLjEwNC0zLjM0N2EuMjQ0LjI0NCAwIDAgMC0uMTg2LS4wOC4yNDUuMjQ1IDAgMCAwLS4xOC4xTDcuNjIyIDguNTM3IDYuMzk0IDcuMDk0YS4yMzIuMjMyIDAgMCAwLS4zNTQtLjAxM2wtNC40IDQuNTZhLjM0My4zNDMgMCAwIDAtLjAyNC40My4yNDMuMjQzIDAgMCAwIC4xOTQuMTAzeiIvPjwvZz48L3N2Zz4="
            alt="Upload"
          />
        </div>
      </div>
    );
  }
}

ImageOption.propTypes = {
  onChange: PropTypes.func.isRequired,
  editorState: PropTypes.object.isRequired,
};

export default ImageOption;
