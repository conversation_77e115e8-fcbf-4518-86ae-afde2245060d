import { useState, useEffect } from "react";
import { But<PERSON> } from "../ui/button";
import { Input } from "../ui/input";
import { Label } from "../ui/label";
import { Textarea } from "../ui/textarea";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "../ui/select";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "../ui/dialog";
import {
	createQuestiondata,
	updateQuestiondata,
	getAllSubSugmentData,
} from "../../API/api-endpoint";
import Swal from "sweetalert2";

export const QuestionDialog = ({ open, onClose, onSuccess, editingItem }) => {
	const [formData, setFormData] = useState({
		question: "",
		subsegment_id: "",
		user_response_type: "",
	});
	const [isLoading, setIsLoading] = useState(false);
	const [subSegmentList, setSubSegmentList] = useState([]);
	const [isLoadingSubSegments, setIsLoadingSubSegments] = useState(false);

	const responseTypes = [
		{ value: "radio", label: "Radio" },
		{ value: "input", label: "Input" },
	];

	useEffect(() => {
		if (open) {
			fetchSubSegments();
		}
	}, [open]);

	useEffect(() => {
		if (open) {
			if (editingItem?.id) {
				const editData = {
					question: editingItem.question || "",
					subsegment_id: String(editingItem.subsegment_id || ""),
					user_response_type: editingItem.user_response_type || "",
				};
				setFormData(editData);
			} else {
				const newData = {
					question: "",
					subsegment_id: "",
					user_response_type: "",
				};
				setFormData(newData);
			}
		}
	}, [open, editingItem]);

	const fetchSubSegments = async () => {
		try {
			setIsLoadingSubSegments(true);
			const response = await getAllSubSugmentData();
			if (response) {
				setSubSegmentList(response || []);
			}
		} catch (error) {
			console.error("Error fetching sub segments:", error);
			Swal.fire({
				title: "Error",
				text: "Failed to fetch sub segments",
				icon: "error",
				timer: 3000,
				showConfirmButton: false,
			});
		} finally {
			setIsLoadingSubSegments(false);
		}
	};

	const handleInputChange = (field, value) => {
		setFormData((prev) => ({
			...prev,
			[field]: value,
		}));
	};

	const handleSubmit = async (e) => {
		e.preventDefault();

		if (!formData.subsegment_id) {
			Swal.fire({
				title: "Error",
				text: "Sub segment name is required",
				icon: "error",
				timer: 1800,
				showConfirmButton: false,
			});
			return;
		}

		if (!formData.question.trim()) {
			Swal.fire({
				title: "Error",
				text: "Question is required",
				icon: "error",
				timer: 1800,
				showConfirmButton: false,
			});
			return;
		}

		if (!formData.user_response_type) {
			Swal.fire({
				title: "Error",
				text: "User response type is required",
				icon: "error",
				timer: 1800,
				showConfirmButton: false,
			});
			return;
		}

		try {
			setIsLoading(true);

			const apiData = {
				question: formData.question.trim(),
				subsegment_id: parseInt(formData.subsegment_id),
				user_response_type: formData.user_response_type,
			};

			let response;
			if (editingItem?.id) {
				apiData.id = editingItem.id;
				response = await updateQuestiondata(apiData);
			} else {
				response = await createQuestiondata(apiData);
			}

			if (response?.status) {
				Swal.fire({
					title: "Success",
					text:
						response.message ||
						`Question ${
							editingItem?.id ? "updated" : "created"
						} successfully`,
					icon: "success",
					timer: 2000,
					showConfirmButton: false,
				});
				onSuccess();
			} else {
				Swal.fire({
					title: "Error",
					text: response?.message || "Failed to save question",
					icon: "error",
					timer: 3000,
					showConfirmButton: false,
				});
			}
		} catch (error) {
			console.error("Error saving question:", error);
			Swal.fire({
				title: "Error",
				text: "An error occurred while saving the question",
				icon: "error",
				timer: 3000,
				showConfirmButton: false,
			});
		} finally {
			setIsLoading(false);
		}
	};

	return (
		<Dialog open={open} onOpenChange={onClose}>
			<DialogContent className='sm:max-w-md bg-white'>
				<DialogHeader>
					<DialogTitle className='text-lg font-semibold text-gray-900'>
						{editingItem?.id ? "Edit Question" : "Create Question"}
					</DialogTitle>
				</DialogHeader>

				<form onSubmit={handleSubmit} className='space-y-4'>
					<div className='grid gap-4'>
						<div className='space-y-2'>
							<Label
								htmlFor='subsegment_id'
								className='text-sm font-semibold'
							>
								Sub Segment Name{" "}
							</Label>
							<Select
								value={formData.subsegment_id}
								onValueChange={(value) =>
									handleInputChange("subsegment_id", value)
								}
								disabled={isLoading || isLoadingSubSegments}
							>
								<SelectTrigger>
									<SelectValue placeholder='Select a sub segment' />
								</SelectTrigger>
								<SelectContent className='bg-white'>
									{subSegmentList.map((subSegment) => (
										<SelectItem
											key={subSegment.id}
											value={subSegment.id.toString()}
										>
											{subSegment.name}
										</SelectItem>
									))}
								</SelectContent>
							</Select>
						</div>

						<div className='space-y-2'>
							<Label
								htmlFor='question'
								className='text-sm font-semibold'
							>
								Question
							</Label>
							<Textarea
								id='question'
								className='w-full text-sm'
								value={formData.question}
								onChange={(e) =>
									handleInputChange(
										"question",
										e.target.value
									)
								}
								placeholder='Enter your question'
								disabled={isLoading}
								required
								rows={3}
							/>
						</div>

						<div className='space-y-2'>
							<Label
								htmlFor='user_response_type'
								className='text-sm font-semibold'
							>
								User Response Type{" "}
							</Label>
							<Select
								value={formData.user_response_type}
								onValueChange={(value) =>
									handleInputChange(
										"user_response_type",
										value
									)
								}
								disabled={isLoading}
							>
								<SelectTrigger>
									<SelectValue placeholder='Select response type' />
								</SelectTrigger>
								<SelectContent className='bg-white'>
									{responseTypes.map((type) => (
										<SelectItem
											key={type.value}
											value={type.value}
										>
											{type.label}
										</SelectItem>
									))}
								</SelectContent>
							</Select>
						</div>
					</div>

					<div className='flex justify-end gap-3 pt-4'>
						<Button
							type='button'
							variant='outline'
							onClick={onClose}
							disabled={isLoading}
						>
							Cancel
						</Button>
						<Button
							type='submit'
							className='bg-orange-600 hover:bg-orange-700 text-white'
							disabled={isLoading}
						>
							{isLoading
								? "Saving..."
								: editingItem?.id
								? "Update Question"
								: "Create Question"}
						</Button>
					</div>
				</form>
			</DialogContent>
		</Dialog>
	);
};
