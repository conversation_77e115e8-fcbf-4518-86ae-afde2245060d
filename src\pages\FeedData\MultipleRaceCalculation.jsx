import { useEffect, useState, useMemo } from "react";
import { <PERSON><PERSON> } from "../../components/ui/button";
import { Input } from "../../components/ui/input";
import { Card, CardContent, CardHeader } from "../../components/ui/card";
import {
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableHeader,
	TableRow,
} from "../../components/ui/table";
import { Edit, Trash2, ChevronLeft, ChevronRight } from "lucide-react";
import { MultipleRaceCalculatorDialog } from "../../components/admin/multiple-race-calculator-dialog";
import { DeleteConfirmDialog } from "../../components/admin/delete-confirm-dialog";
import Header from "../../components/Header";
import {
	getAllMultipleRaceCalculation,
	deleteMultipleRaceCalculation,
} from "../../API/api-endpoint";
import Swal from "sweetalert2";
import { Link } from "react-router-dom";

const MultipleRaceCalculation = () => {
	const [multipleraceCalculations, setMultipleRaceCalculations] = useState(
		[]
	);
	const [isLoading, setIsLoading] = useState(true);
	const [showDialog, setShowDialog] = useState(false);
	const [editingRaceCalculation, setEditingRaceCalculation] = useState(null);
	const [deleteId, setDeleteId] = useState(null);
	const [searchTerm, setSearchTerm] = useState("");
	const [currentPage, setCurrentPage] = useState(1);
	const pageSize = 10;

	// Fetch race calculations data
	const fetchMultipleRaceCalculations = async () => {
		try {
			setIsLoading(true);
			const response = await getAllMultipleRaceCalculation();
			console.log("response for the all multiple", response);
			if (response) {
				setMultipleRaceCalculations(response || []);
			} else {
				console.log("Response status is false or missing");
				setMultipleRaceCalculations([]);
			}
		} catch (error) {
			console.error("Error fetching race calculations:", error);
			Swal.fire({
				title: "Error",
				text: "Failed to fetch race calculations",
				icon: "error",
			});
		} finally {
			setIsLoading(false);
		}
	};

	useEffect(() => {
		fetchMultipleRaceCalculations();
	}, []);

	// Filter and search logic
	const filteredRaceCalculations = useMemo(() => {
		console.log("Raw multipleraceCalculations:", multipleraceCalculations);
		let filtered = multipleraceCalculations;

		// Search filter
		if (searchTerm) {
			filtered = filtered.filter((calc) =>
				calc.goal_name?.toLowerCase().includes(searchTerm.toLowerCase())
			);
		}

		console.log("Filtered result:", filtered);
		return filtered;
	}, [multipleraceCalculations, searchTerm]);
	console.log("Filtered Calculations:", filteredRaceCalculations);

	// Pagination (matching Program page)
	const paginatedRaceCalculations = useMemo(() => {
		const startIndex = (currentPage - 1) * pageSize;
		const paginated = filteredRaceCalculations.slice(
			startIndex,
			startIndex + pageSize
		);
		console.log("Paginated calculations:", paginated);
		return paginated;
	}, [filteredRaceCalculations, currentPage, pageSize]);

	const totalPages = Math.ceil(filteredRaceCalculations.length / pageSize);

	// Handle edit
	const handleEdit = (raceCalculation) => {
		console.log("Editing race calculation:", raceCalculation);
		setEditingRaceCalculation(raceCalculation);
		setTimeout(() => {
			setShowDialog(true);
		}, 0);
	};

	// Handle delete
	const handleDelete = async (id) => {
		try {
			const response = await deleteMultipleRaceCalculation(id);
			if (response?.status) {
				Swal.fire({
					title: "Success",
					text: "Race calculation deleted successfully",
					icon: "success",
					timer: 1800,
					showConfirmButton: false,
				});
				fetchMultipleRaceCalculations();
				setCurrentPage(1);
			} else {
				Swal.fire({
					title: "Error",
					text:
						response?.message ||
						"Failed to delete race calculation",
					icon: "error",
				});
			}
		} catch (error) {
			console.error("Error deleting race calculation:", error);
			Swal.fire({
				title: "Error",
				text: "Failed to delete race calculation",
				icon: "error",
			});
		}
		setDeleteId(null);
	};

	// Handle dialog close
	const handleDialogClose = () => {
		setShowDialog(false);
		setEditingRaceCalculation(null);
	};

	// Handle successful save
	const handleSaveSuccess = () => {
		fetchMultipleRaceCalculations();
		handleDialogClose();
	};

	return (
		<div>
			<Header />
			<div className='mx-auto p-6 max-w-[1300px] mt-16'>
				<Card>
					<CardHeader className='bg-orange-50 border-b'>
						<div className='flex justify-between items-center flex-wrap gap-4 px-1 py-1'>
							<Button
								onClick={() => setShowDialog(true)}
								className='bg-orange-600 hover:bg-orange-700 text-white'
							>
								Create Multiple Race Calculation
							</Button>

							<div className='flex items-center gap-2'>
								<Input
									placeholder='Search By Goal Name..'
									value={searchTerm}
									onChange={(e) => {
										setSearchTerm(e.target.value);
										setCurrentPage(1);
									}}
									className='text-sm w-[230px]'
								/>
								<Link to='/racecalculation'>
									<Button className='bg-orange-600 hover:bg-orange-700 text-white'>
										Back
									</Button>
								</Link>
							</div>
						</div>
					</CardHeader>

					<CardContent className='p-0'>
						<div className='overflow-x-auto'>
							<Table className='min-w-full w-full'>
								<TableHeader>
									<TableRow className='bg-blue-600 hover:bg-blue-600'>
										<TableHead className='text-white font-semibold'>
											Sr No
										</TableHead>
										<TableHead className='text-white font-semibold'>
											Goal Name
										</TableHead>
										<TableHead className='text-white font-semibold'>
											Running Distance
										</TableHead>
										<TableHead className='text-white font-semibold'>
											Running Time
										</TableHead>
										<TableHead className='text-white font-semibold'>
											Cycling Distance
										</TableHead>
										<TableHead className='text-white font-semibold'>
											Cycling Time
										</TableHead>
										<TableHead className='text-white font-semibold'>
											Swimming Distance
										</TableHead>
										<TableHead className='text-white font-semibold'>
											Swimming Time
										</TableHead>
										<TableHead className='text-white font-semibold'>
											Actions
										</TableHead>
									</TableRow>
								</TableHeader>
								<TableBody>
									{isLoading ? (
										<TableRow>
											<TableCell
												colSpan={9}
												className='text-center py-8'
											>
												Loading...
											</TableCell>
										</TableRow>
									) : paginatedRaceCalculations.length > 0 ? (
										paginatedRaceCalculations.map(
											(calc, index) => (
												<TableRow
													key={calc.id}
													className='hover:bg-gray-50'
												>
													<TableCell className='font-medium'>
														{(currentPage - 1) *
															pageSize +
															index +
															1}
													</TableCell>
													<TableCell className='font-medium'>
														{calc.goal_name}
													</TableCell>
													<TableCell>
														{calc.running_quota ??
															"-"}
													</TableCell>
													<TableCell>
														{calc.running_time ??
															"-"}
													</TableCell>
													<TableCell>
														{calc.cycling_quota ??
															"-"}
													</TableCell>
													<TableCell>
														{calc.cycling_time ??
															"-"}
													</TableCell>
													<TableCell>
														{calc.swimming_quota ??
															"-"}
													</TableCell>
													<TableCell>
														{calc.swimming_time ??
															"-"}
													</TableCell>
													<TableCell>
														<div className='flex gap-2'>
															<Button
																variant='ghost'
																size='sm'
																onClick={() =>
																	handleEdit(
																		calc
																	)
																}
																className='text-blue-600 hover:text-blue-800'
															>
																<Edit className='h-4 w-4' />
															</Button>
															<Button
																variant='ghost'
																size='sm'
																onClick={() =>
																	setDeleteId(
																		calc.id
																	)
																}
																className='text-red-700 hover:text-red-800'
															>
																<Trash2 className='h-4 w-4' />
															</Button>
														</div>
													</TableCell>
												</TableRow>
											)
										)
									) : (
										<TableRow>
											<TableCell
												colSpan={9}
												className='text-center py-8 text-gray-500'
											>
												No race calculations found
											</TableCell>
										</TableRow>
									)}
								</TableBody>
							</Table>
						</div>
					</CardContent>
				</Card>

				{/* Pagination - matching Program page */}
				{totalPages > 1 && (
					<div className='flex items-center justify-center gap-2 mt-6'>
						<Button
							variant='outline'
							size='sm'
							onClick={() =>
								setCurrentPage(Math.max(1, currentPage - 1))
							}
							disabled={currentPage === 1}
						>
							<ChevronLeft className='h-4 w-4 mr-1' />
							Previous
						</Button>

						{Array.from({ length: 5 }, (_, i) => {
							const startPage =
								Math.floor((currentPage - 1) / 5) * 5 + 1;
							const page = startPage + i;
							if (page > totalPages) return null;

							return (
								<Button
									key={page}
									variant={
										currentPage === page
											? "default"
											: "outline"
									}
									size='sm'
									onClick={() => setCurrentPage(page)}
									className={
										currentPage === page
											? "bg-orange-500 hover:bg-orange-600 text-white"
											: ""
									}
								>
									{page}
								</Button>
							);
						})}

						<Button
							variant='outline'
							size='sm'
							onClick={() =>
								setCurrentPage(
									Math.min(totalPages, currentPage + 1)
								)
							}
							disabled={currentPage === totalPages}
						>
							Next
							<ChevronRight className='h-4 w-4 ml-1' />
						</Button>
					</div>
				)}
			</div>

			{/* Multiple Race Calculator Dialog */}
			<MultipleRaceCalculatorDialog
				open={showDialog}
				onClose={handleDialogClose}
				onSave={handleSaveSuccess}
				editingRaceCalculation={editingRaceCalculation}
			/>

			{/* Delete Confirmation Dialog */}
			<DeleteConfirmDialog
				open={!!deleteId}
				onOpenChange={(open) => !open && setDeleteId(null)}
				onConfirm={() => handleDelete(deleteId)}
				title='Delete Race Calculation'
				description='Are you sure you want to delete this race calculation? This action cannot be undone.'
			/>
		</div>
	);
};

export default MultipleRaceCalculation;
