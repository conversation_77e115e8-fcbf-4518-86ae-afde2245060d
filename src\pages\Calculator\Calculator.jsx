import * as React from 'react';
import Box from '@mui/material/Box';
import Tab from '@mui/material/Tab';
import TabContext from '@mui/lab/TabContext';
import TabList from '@mui/lab/TabList';
import TabPanel from '@mui/lab/TabPanel';
import RunCalculate from './RunCalculate';
import { createCalculator, getCalculatorGoals, getFechAlGoals } from '../../API/api-endpoint';
import { useState } from 'react';
import { useEffect } from 'react';
import Header from '../../components/Header';
import Triathlon from './Triathlon';
import Swal from 'sweetalert2';
import SlickCarousel from '../SlickCarousel';

export default function Calculator() {
  const [value, setValue] = React.useState('1');
  const [fetchGoals, setFechGoals] = useState([]);
  const [distanceUnit, setDistanceUnit] = useState([]);
  const [formValue, setFormValue] = useState({ type: "running", sporttype: "single",inputTime:"" });
  const [runningCalculateValue, setRunningCalculateValue] = useState();
  const [cyclingCalculateValue, setCyclingCalculateValue] = useState();
  const [swimmingCalculateValue, setSwimmingCalculateValue] = useState();
  const [error, setError] = useState('');
  const [errorOne, setErrorOne] = useState('');




  console.log("fetchGoals", formValue, fetchGoals, value);
  const handleChange = (newValue, type) => {
    setValue(newValue);
    setFormValue({ type: type, sporttype: "single" })
    getGoals(type)
    if (newValue == "3") {
      setDistanceUnit([{
        name: "100 Yards",
        value: "yards"
      },
      {
        name: "100 Meters",
        value: "meters"
      }])
    } else {
      setDistanceUnit([{
        name: "Miles",
        value: "miles"
      },
      {
        name: "Km",
        value: "km"
      }])

    }

  };
  const getGoals = async (type) => {
    const response = await getCalculatorGoals()
    if (response) {
      if (type === "running") {
        setFechGoals(response?.rungoals)

      } else if (type === "cycling") {
        setFechGoals(response?.cyclinggoals)
      }
      else if (type === "swimming") {
        setFechGoals(response?.swimgoals)
      }
      else if (type === "triathlon") {
        setFechGoals(response?.trigoals)
      } else {
        setFechGoals(response?.rungoals)
      }
    }
    console.log("response", response?.rungoals);
  }
  useEffect(() => {
    getGoals()
    setDistanceUnit([{
      name: "Miles",
      value: "miles"
    },
    {
      name: "Km",
      value: "km"
    }])

  }, [])


  const calculateValue = async () => {
    if (formValue?.goal  && formValue.unit  && error === '' && errorOne === '') {
      
   
    const response = await createCalculator(formValue)

    if (response?.status) {
      if (response?.data?.length>0) {
        let data = response.data?.map((ele)=>{
          return{
            unit:ele.paceperminunitrun,
            time:ele.runningtimebasedongivenpace,
            paceSpeed:ele.paceperminuterun,
            totlaDistance:ele.totaldistance,
            totalDistancenit:ele.totaldistanceunit
  
          }
        })
        console.log("data",data);
        setRunningCalculateValue(data)
    }else{
      Swal.fire({
        title: "Succes",
        text: "Data Not Found",
        icon: "success",
      });
    }
     
    }
  }else{
    Swal.fire({
      title: "Error",
      text: "Please fill all details",
      icon: "error",
    });
  }
  }
  const calculateCyclingValue = async () => {
    if (formValue?.goal  && formValue.unit  && error === '') {

    const response = await createCalculator(formValue)
    console.log("response", response);
    if (response?.status) {
      let data = response.data?.map((ele)=>{
        return{
          unit:ele.speedperhourunit,
          time:ele.cyclingtimebasedongivenspeed,
          paceSpeed:ele.speedperhour,
          totlaDistance:ele.totaldistance,
          totalDistancenit:ele.totaldistanceunit

        }
      })
      setCyclingCalculateValue(data)
    }
  }else{
    Swal.fire({
      title: "Error",
      text: "Please fill all details",
      icon: "error",
    });
  }
  }
  const calculateSwimingValue = async () => {
    if (formValue?.goal  && formValue.unit  && error === '' && errorOne === '') {

    const response = await createCalculator(formValue)
    console.log("response", response,response?.data?.length);
    if (response?.status) {
      if (response?.data?.length>0) {
        let data = response.data?.map((ele)=>{
          return{
            unit:ele.paceperminunitswim,
            time:ele.swimmingtimebasedongivenpace,
            paceSpeed:ele.paceperminuteswim,
            totlaDistance:ele.totaldistance,
            totalDistancenit:ele.totaldistanceunit
  
          }
        })
        setSwimmingCalculateValue(data)
      }else{
        Swal.fire({
          title: "Succes",
          text: "Data Not Found",
          icon: "success",
        });
      }
     
    }
  }else{
    Swal.fire({
      title: "Error",
      text: "Please fill all details",
      icon: "error",
    });
  }
  }
  return (
    <div>
      <Header />
      <div style={{ marginTop: '90px', padding: '10px' }}>
      <div className="parentCont">
        <div className='' style={{width:"70%"}}>
          <Box sx={{ width: '100%', typography: 'body1', }}>
            <TabContext value={value}>
              <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
                <TabList aria-label="lab API tabs example">
                  <Tab onClick={() => handleChange("1", "running")} label="Run" value="1" />
                  <Tab onClick={() => handleChange("2", "cycling")} label="Cycle" value="2" />
                  <Tab onClick={() => handleChange("3", "swimming")} label="Swim" value="3" />
                  <Tab onClick={() => handleChange("6", "triathlon")} label="Triathlon" value="6" />

                </TabList>
              </Box>
              <TabPanel value="1">
                <RunCalculate errorOne={errorOne} setErrorOne={setErrorOne} error={error} setError={setError} fetchGoals={fetchGoals} distanceUnit={distanceUnit} calculateValues={runningCalculateValue} calculateValue={calculateValue} setFormValue={setFormValue} formValue={formValue} />
              </TabPanel>
              <TabPanel value="2"><RunCalculate errorOne={errorOne} setErrorOne={setErrorOne} error={error} setError={setError}  fetchGoals={fetchGoals} calculateValues={cyclingCalculateValue} distanceUnit={distanceUnit} calculateValue={calculateCyclingValue} setFormValue={setFormValue} formValue={formValue} /></TabPanel>
              <TabPanel value="3"><RunCalculate errorOne={errorOne} setErrorOne={setErrorOne} error={error} setError={setError}  fetchGoals={fetchGoals} calculateValues={swimmingCalculateValue} distanceUnit={distanceUnit} calculateValue={calculateSwimingValue} setFormValue={setFormValue} formValue={formValue} /></TabPanel>
              <TabPanel value="6"><Triathlon fetchGoals={fetchGoals} calculateValues={swimmingCalculateValue} distanceUnit={distanceUnit} calculateValue={calculateSwimingValue} setFormValue={setFormValue} formValue={formValue} /></TabPanel>

            </TabContext>
          </Box>
        </div>
        <div className="slick-container">
        <SlickCarousel />
      </div>
    </div>
      </div>
    </div>
  );
}
