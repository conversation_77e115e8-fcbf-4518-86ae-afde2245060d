services:
  app:
    image: ghcr.io/yoska-technology-solutions/yofit-frontend:develop-latest
    container_name: yofit-frontend-nginx-develop
    labels:
      - "traefik.enable=true"
      - "traefik.docker.network=traefik_network"
      - "traefik.http.routers.yofit-frontend-dev.entrypoints=websecure"
      - "traefik.http.routers.yofit-frontend-dev.rule=Host(`dev.fit.yoska.in`)"
      - "traefik.http.routers.yofit-frontend-dev.tls.certresolver=lets-encrypt"
      - "traefik.http.services.yofit-frontend-dev.loadbalancer.server.port=80"
    restart: unless-stopped
    healthcheck:
      test: "curl -f http://localhost/healthcheck || exit 1"
    networks:
      - "traefik_network"
    security_opt:
      - "no-new-privileges:true"
    expose:
      - "80"
networks:
  traefik_network:
    external: true
