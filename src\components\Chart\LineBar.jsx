import React from "react";
import { Bar, Line, Pie } from "react-chartjs-2";

function LineBar(props) {
    console.log("props.chartData",props.chartData);
  return (
    <div className={props?.showWidth ?`w-[${props?.showWidth}]`:"w-[40vw]"}>
    <Line
    data={props.chartData}
    options={{
      title: {
        display: props.displayTitle,
        text: "Largest Cities in Massachusetts",
        fontSize: 25,
      },
      tooltips: {
        enabled: false, // Disable tooltips on hover
      },
      legend: {
        display: props.displayLegend,
        position: props.legendPosition,
        labels: {
          fontColor: "#000",
        },
      },
     
    }}
  />
    </div>
  );
}

LineBar.defaultProps = {
  displayTitle: true,
  displayLegend: false,
  legendPosition: "bottom",
};

export default LineBar
