import React, { useState } from 'react'
import Header from '../components/Header'
import { Menu } from '@mantine/core'
import {
  IconApps,
  IconCast,
  IconFilter,
  IconPlus,
  IconSearch,
  IconUser,
  IconUserPlus,
  IconUsers,
  IconX
} from '@tabler/icons'

const ConversationPage = () => {
  const [conDetails, setConDetails] = useState(false)

  const handleReveal = () => {
    setConDetails(true)
  }

  const athletes = [
    { id: 1, name: 'Yoska alert', icon: <IconUsers size={20} /> },
    { id: 2, name: '<PERSON><PERSON><PERSON>', icon: <IconUser size={20} /> },
    { id: 3, name: '<PERSON><PERSON><PERSON>rod<PERSON>', icon: <IconUser size={20} /> },
    { id: 4, name: '<PERSON><PERSON><PERSON>', icon: <IconUser size={20} /> },
    { id: 5, name: '<PERSON>eeth DS', icon: <IconUser size={20} />, unread: 3 },
    { id: 6, name: '<PERSON><PERSON><PERSON> <PERSON>', icon: <IconUser size={20} /> },
    {
      id: 7,
      name: '<PERSON><PERSON><PERSON>',
      icon: <IconUser size={20} />,
      unread: 1
    },
    {
      id: 8,
      name: 'Yoska Triathlon Academy',
      icon: <IconUsers size={20} />,
      unread: 4
    },
    { id: 9, name: 'Suresh Kumar', icon: <IconUser size={20} /> },
    { id: 10, name: 'Ajit Sivaram', icon: <IconUser size={20} /> }
  ]

  const actions = [
    {
      id: 1,
      title: 'Delete'
    },
    {
      id: 2,
      title: 'More'
    }
  ]

  return (
    <>
      <Header />
      <div className='grid grid-cols-1 md:grid-cols-5 items-start'>
        <div className='p-4 bg-slate-100 sticky top-20 h-screen'>
          <div className='flex items-center justify-between'>
            <div className='flex items-center space-x-8'>
              <div>
                <IconApps size={22} color='dodgerblue' />
              </div>
              <div>
                <IconUserPlus size={22} color='dodgerblue' />
              </div>
              <div>
                <IconSearch size={22} color='dodgerblue' />
              </div>
              <div>
                <IconCast size={22} color='dodgerblue' />
              </div>
              <div>
                <IconFilter size={24} color='dodgerblue' />
              </div>
            </div>
            <div>
              <IconX size={24} color='dodgerblue' />
            </div>
          </div>
          <br />
          <hr />
          <div className='flex flex-col gap-y-6 items-start my-6 '>
            {athletes.map((item) => (
              <div
                className='flex justify-between items-center gap-x-4 w-full cursor-pointer'
                key={item.id}
              >
                <div
                  className='flex items-center gap-x-4'
                  onClick={handleReveal}
                >
                  <span>{item.icon}</span>
                  <span className='text-slate-700 text-sm'>{item.name}</span>
                </div>
                <div className='flex space-x-4 items-center'>
                  {item.unread ? (
                    <div className='text-xs bg-blue-600 text-slate-50 w-6 h-6 flex justify-center items-center rounded-full'>
                      {item.unread}
                    </div>
                  ) : (
                    ''
                  )}
                  <div>
                    <Menu shadow='md' width={100} position='bottom-end'>
                      <Menu.Target className='cursor-pointer'>
                        <svg
                          xmlns='http://www.w3.org/2000/svg'
                          className='icon icon-tabler icon-tabler-dots-vertical'
                          width='20'
                          height='20'
                          viewBox='0 0 24 24'
                          strokeWidth='1.5'
                          stroke='#2c3e50'
                          fill='none'
                          strokeLinecap='round'
                          strokeLinejoin='round'
                        >
                          <path stroke='none' d='M0 0h24v24H0z' fill='none' />
                          <circle cx='12' cy='12' r='1' />
                          <circle cx='12' cy='19' r='1' />
                          <circle cx='12' cy='5' r='1' />
                        </svg>
                      </Menu.Target>

                      <Menu.Dropdown>
                        {actions.map((item) => (
                          <Menu.Item
                            color={'gray'}
                            icon={item.icon}
                            key={item.id}
                          >
                            {item.title}
                          </Menu.Item>
                        ))}
                      </Menu.Dropdown>
                    </Menu>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
        <div
          className='hidden lg:block w-full p-4 bg-slate-100 sticky top-20 border-l-2'
          style={{ display: conDetails ? 'block' : 'none' }}
        >
          <div className='flex items-center justify-between'>
            <div className='flex items-center space-x-8'>
              <div className='cursor-pointer'>
                <IconPlus size={22} color='dodgerblue' />
              </div>
              <div className='cursor-pointer'>
                <IconSearch size={22} color='dodgerblue' />
              </div>
            </div>
            <div
              onClick={() => setConDetails(false)}
              className='cursor-pointer'
            >
              <IconX size={24} color='dodgerblue' />
            </div>
          </div>
          <br />
          <hr />
          <div className='flex flex-col gap-y-6 items-start my-6'>
            <div className='flex justify-between items-center gap-x-4 w-full'>
              <div className='flex items-center'>
                <span className='text-slate-700 text-sm pl-1'>
                  Activity comment
                </span>
              </div>
              <div></div>
            </div>
            <div className='flex justify-between items-center gap-x-4 w-full'>
              <div className='flex items-center'>
                <span className='text-slate-700 text-sm pl-1'>
                  Weekly feedback
                </span>
              </div>
              <div>
                <Menu shadow='md' width={100} position='bottom-end'>
                  <Menu.Target className='cursor-pointer'>
                    <svg
                      xmlns='http://www.w3.org/2000/svg'
                      className='icon icon-tabler icon-tabler-dots-vertical'
                      width='20'
                      height='20'
                      viewBox='0 0 24 24'
                      strokeWidth='1.5'
                      stroke='#2c3e50'
                      fill='none'
                      strokeLinecap='round'
                      strokeLinejoin='round'
                    >
                      <path stroke='none' d='M0 0h24v24H0z' fill='none' />
                      <circle cx='12' cy='12' r='1' />
                      <circle cx='12' cy='19' r='1' />
                      <circle cx='12' cy='5' r='1' />
                    </svg>
                  </Menu.Target>

                  <Menu.Dropdown>
                    <Menu.Item color={'gray'}>Delete</Menu.Item>
                  </Menu.Dropdown>
                </Menu>
              </div>
            </div>
          </div>
        </div>
        <div className='flex items-center justify-center p-4 w-full'></div>
      </div>
    </>
  )
}

export default ConversationPage
