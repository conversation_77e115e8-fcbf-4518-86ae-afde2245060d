import React from 'react'
import Header from '../components/Header'
import { CreateAssignCoach, getAllCoachesList, updateAssignCoach } from '../API/api-endpoint'
import { useEffect } from 'react'
import { useFormik } from 'formik'
import { assignAthleteScema, assignAthleteinitialValue, profileScema, profileinitialValue } from '../components/formik/FormikValidationSceema'
import Swal from 'sweetalert2'
import { useState } from 'react'
import { Navigate, useNavigate } from 'react-router'
import SlickCarousel from './SlickCarousel'
import { Grid } from '@mantine/core'
import { FormLabel, MenuItem, OutlinedInput, TextField, Typography } from '@mui/material'
import { Button } from 'antd'

const CoachPage = () => {

  let athleteDetails = localStorage.getItem("athleteDetails")
  const [coachList, setgetAllcoachList] = useState();
  const [isLoading, setIsLoading] = useState(false)

  const navigate = useNavigate()

  const formik = useFormik({
    initialValues: assignAthleteinitialValue,
    validationSchema: assignAthleteScema,
    onSubmit: (values) => {
      handleOnSubmitForm(values)
    },
  });
  console.log("formik", formik?.values);
  useEffect(() => {
    let data = JSON.parse(athleteDetails)
    console.log("data",data);
    if (data?.atheleteid) {
      formik.setValues(data)

    }
    if (data?.athlete_id) {
      formik.setValues(data)
      formik.setFieldValue("newcoach_id",data?.oldcoach_id?.coach_id?data?.oldcoach_id?.coach_id: data?.oldcoach_id?.coach?.id)
      formik.setFieldValue("oldcoach_id", data?.oldcoach_id?.coach_id?data?.oldcoach_id?.coach_id: data?.oldcoach_id?.coach?.id)
      formik.setFieldValue("athlete_id", data?.athlete_id)
      formik.setFieldValue("data", data)
    }
    getCoachList()
  }, [athleteDetails])
  const getCoachList = async () => {
    const response = await getAllCoachesList()
    setgetAllcoachList(response)
  }

  const handleOnSubmitForm = async (file) => {
    try {
      setIsLoading(true)
      let Result = ""
      if (formik?.values?.oldcoach_id) {
        const updatedData = { ...formik.values, data: undefined };
        Result = await updateAssignCoach(updatedData);

      } else {

        Result = await CreateAssignCoach(formik.values);
      }
      if (Result.status) {
        showSuccess(Result.message);
        // setIsEditProfile(false)
        navigate('/coach')
        setIsLoading(false)
        // const response = await getUsersProfile(assignedCocahId)

      } else {
        showErrorAndRedirect(Result?.message)
      }

    } catch (error) {
      setIsLoading(false)



    }
  };
  const showSuccess = (message) => {
    Swal.fire({
      title: "Success",
      text: message,
      icon: "success",
    });
  }

  const showErrorAndRedirect = (message) => {
    Swal.fire({
      title: "Error",
      text: message,
      icon: "error",
    });

  };
  console.log("sfbsdfh", formik.values);
  return (

    <>
      {formik?.values?.oldcoach_id ? (
        <div
          item
          className="w-[100vw] fixed top-20"
        >
          <Header />
          <div className='flex justify-center items-center'>
            <form className='p-4' style={{ width: "50%" }} onSubmit={formik.handleSubmit}>
              <div className="headingCont  mb-6 ">
                <Typography variant="h4" className="heading">{formik?.values?.oldcoach_id ? "Change" : "Assign"}<span style={{ color: "orange" }}> Coach</span></Typography>{" "}
              </div>
              <div>
              


                {formik?.values?.oldcoach_id ? (
                  <div>
                    <FormLabel>Select Coach</FormLabel>

                    <TextField
                      fullWidth
                      size="small"
                      select
                      name="newcoach_id"
                      value={formik?.values?.newcoach_id}
                      onChange={formik.handleChange}
                      error={formik.touched.newcoach_id && formik.errors.newcoach_id}
                      helperText={formik.touched.newcoach_id && formik.errors.newcoach_id}
                      id="form-layouts-separator-select"
                      labelId="form-layouts-separator-select-label"
                      input={<OutlinedInput id="select-multiple-language" />}
                    >
                      <MenuItem value={""} disabled>
                        Select Coach
                      </MenuItem>
                      {coachList?.map((option, index) => {
                        return (
                          <MenuItem key={option.id} value={option.id}>{option.firstname} {option.lastname}</MenuItem>
                        );
                      })}
                    </TextField>
                  </div>
                ) : (
                  <div className='flex flex-col items-start gap-y-1'>
                    <label htmlFor='organization_name' className='text-sm font-normal'>
                      Select Coach
                    </label>
                    <select
                      name="coachid"
                      onChange={formik.handleChange}
                      value={formik.values.coachid}
                      id='enableProfile'
                      className='p-3 border rounded-md text-sm focus:outline-none bg-slate-100 w-full'
                    >
                      <option value='select'>--Select--</option>
                      {coachList?.map((option) => (
                        <option key={option.id} value={option.id}>
                          {option.firstname} {option.lastname}
                        </option>
                      ))}
                    </select>
                  </div>
                )}
                {/*
            
             <div className='flex flex-col items-start gap-y-1'>
                <label htmlFor='organization_name' className='text-sm font-normal'>
                  Coaching location
                </label>
                <input
                  type='text'
                  name='location'
                  onChange={formik.handleChange}
                  value={formik.values.location}
                  id='coachingLocation'
                  placeholder='Enter coaching location'
                  className='p-3 border rounded-md text-sm focus:outline-none bg-slate-100 w-full'
                />
              </div>
            */}
              </div>

            <div className='mt-6'>
            <button onClick={()=>navigate("/coach")}
            type='submit'
            className='px-6 py-2 bg-gray-500 text-white rounded-md text-sm'
          >
            Back
          </button> &nbsp;
              <button
                type='submit'
                className='px-6 py-2 bg-[#ffa654] text-white rounded-md text-sm'
              >
                Submit
              </button>
            </div>
            </form>
          </div>

        </div >

      ) : (
        <div
          item
          className="w-[100vw] fixed top-20"
        >
          <Header />
          <div className='flex justify-center items-center'>
            <form className='p-4' style={{ width: "50%" }} onSubmit={formik.handleSubmit}>
              <div className="headingCont  mb-6 ">
                <Typography variant="h4" className="heading">Assign<span style={{ color: "orange" }}> Coach</span></Typography>{" "}
              </div>
              <div>
                <FormLabel>Select Coach</FormLabel>

                <TextField
                  fullWidth
                  size="small"
                  select
                  name="coachid"
                  value={formik?.values?.coachid}
                  onChange={formik.handleChange}
                  error={formik.touched.coachid && formik.errors.coachid}
                  helperText={formik.touched.coachid && formik.errors.coachid}
                  id="form-layouts-separator-select"
                  labelId="form-layouts-separator-select-label"
                  input={<OutlinedInput id="select-multiple-language" />}
                >
                  <MenuItem value={""} disabled>
                    Select Coach
                  </MenuItem>
                  {coachList?.map((option, index) => {
                    return (
                      <MenuItem key={option.id} value={option.id}>{option.firstname} {option.lastname}</MenuItem>
                    );
                  })}
                </TextField>
              </div>
              <div className='mt-6'>
                <button onClick={() => navigate("/coach")}
                  type='submit'
                  className='px-6 py-2 bg-gray-500 text-white rounded-md text-sm'
                >
                  Back
                </button> &nbsp;
                <button
                  type='submit'
                  className='px-6 py-2 bg-[#ffa654] text-white rounded-md text-sm'
                >
                  Submit
                </button>
              </div>
            </form>
            <div className="slick-container">
              <SlickCarousel />
            </div>
          </div>
        </div >
      )}


    </>
  )
}

export default CoachPage
