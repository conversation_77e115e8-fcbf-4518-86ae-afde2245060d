import {
    Chip,
    FormControl,
    FormLabel,
    Grid,
    MenuItem,
    OutlinedInput,
    Select,
    TextField,
  } from "@mui/material";
  import { But<PERSON>, Modal } from "antd";
  import React, { useEffect, useState } from "react";
  import {
    createPhaseBloackdata,
    getAllActivityData,
    getAllPhaseNameData,
    updatePhaseBloackdata,
  } from "../../API/api-endpoint";
  import { useFormik } from "formik";
  import Swal from "sweetalert2";
  import SlickCarousel from "../../pages/SlickCarousel";
  const scoreData = [1, 2, 3, 4, 5];
  const CreatePhaseBloack = ({
    fetchReport,
    setShowAssesmentModal,
    showAssesmentModal,
    editData,
    setEditData,
  }) => {
    const [programList, setProgramList] = useState([]);
    const [phaseList, setPaseNameList] = useState([]);
  
    console.log("editData", editData);
    const formik = useFormik({
      initialValues: {
        activities: "",
        phase: "",
        id: 1,
        week: "",
        phaseno: "",
      },
      validate: (values) => {
        const errors = {};
  
        // Validation for 'phase'
        if (!values.phase) {
          errors.phase = "Phase name is required";
        }
  
        // Validation for 'activities'
        if (!values.activities) {
          errors.activities = "Activities are required";
        }
  
        // Validation for 'week'
        if (!values.week) {
          errors.week = "Week is required";
        }
  
        // Validation for 'phaseno'
        if (!values.phaseno) {
          errors.phaseno = "Phase number is required";
        }
  
        return errors;
      },
      // validationSchema: {},
      onSubmit: (values, { resetForm }) => {
        handleSubmitAssesmentForm(values, resetForm);
      },
    });
    console.log("formik", formik?.values);
    const getAllProgramsData = async () => {
      const response = await getAllActivityData();
      console.log("response", response);
      setProgramList(response?.rows);
    };
    const getPhaseName = async () => {
      const response = await getAllPhaseNameData();
      console.log("response", response);
      setPaseNameList(response);
    };
    useEffect(() => {
      getAllProgramsData();
      getPhaseName();
    }, []);
  
    const handleSubmitAssesmentForm = async (data, resetForm) => {
      let response = "";
      if (editData?.id) {
        response = await updatePhaseBloackdata(data);
      } else {
        response = await createPhaseBloackdata(data);
        console.log("response", response);
      }
      if (response?.status) {
        Swal.fire({
          title: "Success",
          text: response.message,
          icon: "success",
        });
        setShowAssesmentModal(false);
        setEditData({});
        fetchReport();
        resetForm();
        // formik?.setValues({activities:"",program_name:"",activity_ids:""})
      } else {
        Swal.fire({
          title: "Error",
          text: response.message,
          icon: "error",
        });
      }
      console.log("response", response);
    };
    useEffect(() => {
      if (editData?.id) {
        const { srID, activity, phasename, ...data } = editData;
        console.log("data", data);
        formik?.setValues(data);
        formik.setFieldValue("phase", phasename?.id);
        formik.setFieldValue("activities", activity?.id);
      }
    }, [editData?.id]);
    return (
      <Modal
        width={1200}
        open={showAssesmentModal}
        onCancel={() => {
          setShowAssesmentModal(false);
          formik.resetForm();
          setEditData({});
          // formik?.setValues({activities:"",program_name:"",activity_ids:""})
        }}
        footer={
          <div></div>
          //   loading={isLoading}
        }
      >
        <div className="headingCont">
          <span className="heading">{editData?.id ? "Edit " : "Create"}</span>{" "}
          <span className="orange heading">Phase Block</span>
        </div>
        {/* <h1>{editData ? editData.challengeId : values.challengeId}</h1> */}
        <div className="parentCont">
          <form className="form1" onSubmit={formik.handleSubmit}>
            <Grid container spacing={2}>
              <Grid item xs={12} sm={11}>
                <FormLabel>Activity Name<span className="text-[red]">*</span></FormLabel>
  
                <TextField
                  fullWidth
                  size="small"
                  select
                  name="activities"
                  SelectProps={{
                    MenuProps: {
                      PaperProps: {
                        style: {
                           scrollbarColor:"#E67E22 white",
                           scrollbarWidth:"thin"
                        },
                      },
                    },
                  }}
                  value={
                    formik?.values?.activities ? formik?.values?.activities : ""
                  }
                  onChange={formik.handleChange}
                  error={formik.touched.activities && formik.errors.activities}
                  helperText={
                    formik.touched.activities && formik.errors.activities
                  }
                  id="form-layouts-separator-select"
                  labelId="form-layouts-separator-select-label"
                  input={<OutlinedInput id="select-multiple-language" />}
                >
                  <MenuItem value={""} disabled>
                    Select Activity
                  </MenuItem>
                  {programList?.map((value, index) => {
                    return (
                      <MenuItem value={value?.id}>
                        {value?.activity_name}
                      </MenuItem>
                    );
                  })}
                </TextField>
              </Grid>
              <Grid item xs={12} sm={11}>
                <FormLabel>Phase Name<span className="text-[red]">*</span></FormLabel>
  
                <TextField
                  fullWidth
                  size="small"
                  select
                  name="phase"
                  SelectProps={{
                    MenuProps: {
                      PaperProps: {
                        style: {
                           scrollbarColor:"#E67E22 white",
                           scrollbarWidth:"thin"
                        },
                      },
                    },
                  }}
                  value={formik?.values?.phase}
                  onChange={formik.handleChange}
                  error={formik.touched.phase && formik.errors.phase}
                  helperText={formik.touched.phase && formik.errors.phase}
                  id="form-layouts-separator-select"
                  labelId="form-layouts-separator-select-label"
                  input={<OutlinedInput id="select-multiple-language" />}
                >
                  <MenuItem value={""} disabled>
                    Select Name
                  </MenuItem>
                  {phaseList?.map((value, index) => {
                    return <MenuItem value={value?.id}>{value?.phase}</MenuItem>;
                  })}
                </TextField>
              </Grid>
              <Grid item xs={12} sm={11}>
                <FormLabel>Week<span className="text-[red]">*</span></FormLabel>
  
                <TextField
                  fullWidth
                  placeholder="Week"
                  size="small"
                  type="number"
                  name="week"
                  value={formik?.values?.week}
                  onChange={formik.handleChange}
                  error={formik.touched.week && formik.errors.week}
                  helperText={formik.touched.week && formik.errors.week}
                />
              </Grid>
              <Grid item xs={12} sm={11}>
                <FormLabel>Phase Number<span className="text-[red]">*</span></FormLabel>
  
                <TextField
                  fullWidth
                  placeholder="Phase Number"
                  size="small"
                  type="number"
                  name="phaseno"
                  value={formik?.values?.phaseno}
                  onChange={formik.handleChange}
                  error={formik.touched.phaseno && formik.errors.phaseno}
                  helperText={formik.touched.phaseno && formik.errors.phaseno}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <Button
                  className="btn"
                  key="submit"
                  type="primary"
                  onClick={() => formik.handleSubmit()}
                >
                  Submit
                </Button>
              </Grid>
            </Grid>
          </form>
  
          <div className="slick-container">
            <SlickCarousel />
          </div>
        </div>
      </Modal>
    );
  };
  
  export default CreatePhaseBloack;
  