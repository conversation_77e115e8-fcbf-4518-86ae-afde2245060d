import React, { useEffect, useMemo, useState } from "react";
import { Table } from "antd";
import { Grid, TextField } from "@mui/material";
import Header from "../../components/Header";
import { fetchCommunityGroupEnrolment } from "../../API/api-endpoint";

const Athletes = () => {
  const [athletes, setAthletes] = useState([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [pagination, setPagination] = useState({ current: 1, pageSize: 10 });

  useEffect(() => {
    fetchCommunityGroupEnrolment()
      .then(({ data }) => {
        if (data.status) {
          // data.data is now an array of { id, firstname, lastname, email, … }
          setAthletes(data.data);
        } else {
          console.error("Failed to fetch community group enrolment:", data);
        }
      })
      .catch((error) => {
        console.error("Error fetching community group enrolment:", error);
      });
  }, []);

  const filteredAthletes = useMemo(() => {
    const term = searchTerm.toLowerCase();
    return athletes.filter(({ firstname, lastname, email }) => {
      const fullName = `${firstname} ${lastname}`.toLowerCase();
      return (
        fullName.includes(term) ||
        (email || "").toLowerCase().includes(term)
      );
    });
  }, [athletes, searchTerm]);

  const handleTableChange = ({ current, pageSize }) => {
    setPagination({ current, pageSize });
  };

  const columns = [
    {
      title: "No.",
      key: "serial",
      render: (_, __, index) =>
        (pagination.current - 1) * pagination.pageSize + index + 1,
    },
    {
      title: "Name",
      key: "name",
      render: (_, { firstname, lastname }) =>
        `${firstname} ${lastname}`,
    },
    {
      title: "Email",
      dataIndex: "email",
      key: "email",
    },
  ];

  return (
    <>
      <Header />
      <div style={{ marginTop: 100, padding: 20 }}>
        <Grid container spacing={2} style={{ marginBottom: 20 }}>
          <Grid item xs={12} sm={4}>
            <TextField
              size="small"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              placeholder="Search by name or email..."
              fullWidth
            />
          </Grid>
        </Grid>
        <Table
          columns={columns}
          dataSource={filteredAthletes}
          pagination={{
            current: pagination.current,
            pageSize: pagination.pageSize,
            total: filteredAthletes.length,
          }}
          rowKey="id"
          onChange={handleTableChange}
        />
      </div>
    </>
  );
};

export default Athletes;
