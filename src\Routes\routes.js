import React, { useContext, useEffect, useState } from "react";
import {
	Navigate,
	Route,
	Routes,
	useLocation,
	useNavigate,
} from "react-router-dom";
import { ProtectedRoutes } from "./protectedRoutes";
import Box from "@mui/material/Box";
import { PlansPage } from "../pages/PlansPage";
import RunningProgram from "../pages/RunningProgram";
import FitnessProgram from "../pages/FitnessProgram";
import TriathlonProgram from "../pages/TriathlonProgram";
import CyclingProgram from "../pages/CyclingProgram";
import NextTriProgram from "../pages/NextTriProgram";
import LeaderBoard from "../pages/LeaderBoard";
import AdminCreateChallenge from "../pages/AdminCreateChallenge";
import Workout from "../pages/Workout";
import TraningBlock from "../pages/TraningBlock/CustomCalender";
import Calender from "../pages/Workouts/Workouts";
import Masters from "../pages/master/Master";
import AddWorkout from "../pages/Workouts/componants/ManageWorkouts/AddWorkout";
import Test from "../pages/CustomForms/tranning-profile/TranningProfile";
import GlobalContext from "../context/GlobalContext";
import SignupPage from "../pages/Auth/SignupPage";
import LoginPage from "../pages/Auth/LoginPage";
import PhoneOtpVerification from "../pages/Auth/PhoneOtpVerification";
import Flow from "../pages/OnBoardingFlow/Flow";
import ForgotPassword from "../pages/Auth/ForgotPassword";
import ResetPassword from "../pages/Auth/ResetPassword";
import DashboardMaintence from "../pages/DashboardMaintence";
import Chat from "../pages/Chat/chat";
import ChatLanding from "../pages/Chat/screens/ChatLanding";
import StartNewChat from "../pages/Chat/screens/group/chat/StartNewChat";
import IndividualChat from "../pages/Chat/screens/individual/IndividualChat";
import CameraIndividualChat from "../pages/Chat/components/camera/CameraIndividualChat";
import SelectMsgForwardUsers from "../pages/Chat/screens/individual/SelectMsgForwardUsers";
import IndividualChatSecond from "../pages/Chat/screens/individual/IndividualChatSecond";
import IndividualChatThird from "../pages/Chat/screens/individual/IndividualChatThird";
import NewChat from "../pages/Chat/screens/newChat/NewChat";
import CreateNewGroup from "../pages/Chat/screens/group/CreateNewGroup";
import CreateNewGroupStepTwo from "../pages/Chat/screens/group/CreateNewGroupStepTwo";
import GroupChatCreator from "../pages/Chat/screens/group/chat/GroupChatCreator";
import GroupInfo from "../pages/Chat/screens/group/GroupInfo";
import AddNewMember from "../pages/Chat/components/group/AddNewMember";
import CameraGroupChat from "../pages/Chat/components/camera/CameraGroupChat";
import ShowPost from "../pages/Chat/screens/posts/ShowPost";
import { ShareWith } from "../pages/Chat/screens/posts/ShareWith";
import TempChatPage from "../pages/Chat/screens/TempChatPage";
import TranningProfile from "../pages/CustomForms/tranning-profile/TranningProfile";
import ZonesForm from "../pages/CustomForms/zone-forms/ZonesForm";
import EventsCalendar from "../pages/events-calendar/EventsCalendar";
import WalkJog from "../pages/walk-jog/WalkJog";
import CreatePhases from "../pages/phases/CreatePhases";
import AthletesPlanPage from "../pages/AthletePlanPage";
import DetailsPage from "../pages/DetailsPage";
import AthleteDashboardPage from "../pages/AthleteDashboardPage";
import AthletesPage from "../pages/AthletesPage";
import AthletesPageNew from "../pages/AthletesPageNew";
import AutomationSummaryPage from "../pages/AutomationSummaryPage";
import CoachPage from "../pages/CoachPage";
import CoachReportPage from "../pages/CoachReportPage";
import ConversationPage from "../pages/ConversationPage";
import CyclingProgramPage from "../pages/CyclingProgramPage";
import FitnessProgramPage from "../pages/FitnessProgramPage";
import FitProfilePage from "../pages/FitProfilePage";
import InstructionsPage from "../pages/InstructionsPage";
import LastFeedbackPage from "../pages/LastFeedbackPage";
import NotificationPage from "../pages/NotificationPage";
import PendingPayment from "../pages/PendingPayment";
import PlanEndPage from "../pages/PlanEndPage";
import PlanStatusPage from "../pages/PlanStatusPage";
import ProfilePage from "../pages/ProfilePage";
import RaceDetailsPage from "../pages/RaceDetailsPage";
import ResetPage from "../pages/ResetPage";
import RunningProgramPage from "../pages/RunningProgramPage";
import TraineeManagementPage from "../pages/TraineeManagementPage";
import TrainingPage from "../pages/TrainingPage";
import TriathlonProgramPage from "../pages/TriathlonProgramPage";
import WorkoutsPage from "../pages/WorkoutsPage";
import YFAReportPage from "../pages/YFAReportPage";
import YFASummaryReport from "../pages/YFASummaryPage";
import ZonePage from "../pages/ZonePage";
import CoachPaymentPage from "../pages/CoachPaymentPage";
import CreateActivity from "../pages/activity/CreateActivity";
import CreateSubActivity from "../pages/activity/CreateSubActivity";
import Strava from "../pages/Strava";
import AssignCoach from "../components/AssignCoach/AssignCoach";
import Wellnes from "../pages/Wellness/Wellnes";
import WellResult from "../pages/Wellness/WellResult";
import CreateAssement from "../pages/raccesAssesment/CreateAssement";
import RaccesScore from "../pages/raccesAssesment/RaccesScore";
import Calculator from "../pages/Calculator/Calculator";
import SwimmingPage from "../pages/Swimminng/SwimmingPage";
import CoachRevenue from "../pages/CoachRevenue";
import RevenueReporptByTime from "../pages/Report/RevenueReporptByTime";
import UserGrowthReport from "../pages/Report/UserGrowthReport";
import FormReview from "../pages/FormReview/FormReview";
import SavedReview from "../pages/FormReview/SavedReview";
import LeaderBoardUser from "../pages/LeaderBoardUser";
import SubscriptionFlow from "../components/subscription/Flow";
import AssignedUserChallneges from "../pages/Challenges/AssignedUserChallneges";
import OngoingUser from "../pages/Challenges/OngoingUser";
import OldWeeklyFeedData from "../pages/FeedData/OldWeeklyFeedData";
import WeeklyFeedData from "../pages/FeedData/WeeklyFeedData";
import OldZonesClasification from "../pages/FeedData/OldZonesClasification";
import ZonesClasification from "../pages/FeedData/ZonesClassification";
import OldProgram from "../pages/FeedData/OldProgram";
import Program from "../pages/FeedData/Program";
import OldLevels from "../pages/FeedData/OldLevels";
import Levels from "../pages/FeedData/Levels";
import Activity from "../pages/FeedData/Activity";
import OldRaceCalculator from "../pages/FeedData/OldRaceCalculator";
import RaceCalculator from "../pages/FeedData/RaceCalculator";
import YrcsafeedData from "../pages/FeedData/YrcsafeedData";
import OldPhaseBlock from "../pages/FeedData/OldPhaseBlock";
import PhaseBlock from "../pages/FeedData/PhaseBlock";
import OldYtaGoalValoum from "../pages/FeedData/OldYtaGoalValoum";
import YtaGoalValoum from "../pages/FeedData/YtaGoalValoum";
import OldPhaseSubActivity from "../pages/FeedData/OldPhaseSubActivity";
import PhaseSubActivity from "../pages/FeedData/PhaseSubActivity";
import WorkoutData from "../pages/FeedData/WorkoutData";
import OldWorkoutData from "../pages/FeedData/OldWorkoutData";
import SubWorkout from "../pages/FeedData/SubWorkout";
import OldSubWorkout from "../pages/FeedData/OldSubWorkout";
import OldAssesmentName from "../pages/FeedData/OldAssesmentName";
import AssesmentName from "../pages/FeedData/AssesmentName";
import OldSugmentsName from "../pages/FeedData/OldSugmentsName";
import SugmentsName from "../pages/FeedData/SugmentsName";
import OldSubSugments from "../pages/FeedData/OldSubSugments";
import SubSugments from "../pages/FeedData/SubSugments";
import OldQuestions from "../pages/FeedData/OldQuestions";
import Questions from "../pages/FeedData/Questions";
import OldOption from "../pages/FeedData/OldOption";
import Option from "../pages/FeedData/Option";
import OldRenderAssesmentData from "../pages/FeedData/OldRenderAssesmentData";
import RenderAssesmentData from "../pages/FeedData/RenderAssesmentData";
import OldMultipleRaceCalculation from "../pages/FeedData/OldMultipleRaceCalculation";
import MultipleRaceCalculation from "../pages/FeedData/MultipleRaceCalculation";
import OldRenderreadiness from "../pages/FeedData/OldRenderreadiness";
import Renderreadiness from "../pages/FeedData/Renderreadiness";
import OldSegmentReadness from "../pages/FeedData/OldSegmentReadness";
import SegmentReadness from "../pages/FeedData/SegmentReadness";
import OldSubSegmentReadness from "../pages/FeedData/OldSubSegmentReadness";
import SubSegmentReadiness from "../pages/FeedData/SubSegmentReadiness";
import OldOptionReadness from "../pages/FeedData/OldOptionReadness";
import OptionReadness from "../pages/FeedData/OptionReadness";
import OldScorData from "../pages/FeedData/OldScorData";
import ScorData from "../pages/FeedData/ScorData";
import OldChallengeData from "../pages/FeedData/OldChallengeData";
import ChallengeData from "../pages/FeedData/ChallengeData";
import OldGoalWithoutVloume from "../pages/FeedData/OldGoalWithoutVloume";
import GoalWithoutVolume from "../pages/FeedData/GoalWithoutVolume";
import OldZonesPower from "../pages/FeedData/OldZonesPower";
import ZonesPower from "../pages/FeedData/ZonesPower";
import OldZoneHeart from "../pages/FeedData/OldZoneHeart";
import ZoneHeart from "../pages/FeedData/ZoneHeart";
import FormImageRview from "../pages/FeedData/FormImageRview";
import OldActivityGroup from "../pages/FeedData/OldActivityGroup";
import ActivityGroup from "../pages/FeedData/ActivityGroup";
import OldActivityTrack from "../pages/FeedData/OldActivityTrack";
import ActivityTrack from "../pages/FeedData/ActivityTrack";
import Promotors from "../pages/FeedData/Promotors";
import PaymentCred from "../pages/FeedData/PaymentCred";
import OldDiscountCoupon from "../pages/FeedData/OldDiscountCoupon";
import DiscountCoupon from "../pages/FeedData/DiscountCoupon";
import Subscription from "../pages/FeedData/Subscription";
import Uoms from "../pages/FeedData/Uoms";
import Tags from "../pages/FeedData/Tags";
import SystemConfigration from "../pages/FeedData/SystemConfigration";
import ActivityMetric from "../pages/FeedData/ActivityMetric";
import CreateGroupRegistration from "../components/FeedData/CreateGroupRegistration";
import AdminLoginPage from "../pages/Auth/AdminLoginPage";
import CoachPyment from "../pages/Report/CoachPyment";
import SubscriptionDetails from "../pages/Report/SubscriptionDetails";
import AdminLogin from "../pages/Auth/AdminLogin";
import CoachBooking from "../components/CoachBooking/CoachBooking";
import AllBooking from "../components/CoachBooking/AllBooking";
import CoachAdminBooking from "../components/CoachBooking/CoachAdminBooking";
import RegisterCoach from "../pages/RegisterCoach";
import AdminRegstration from "../pages/AdminRegstration";
import OverAllReports from "../pages/Report/OverAllReports";
import LoginNewPage from "../pages/Auth/LoginNewPage";
import AutomationTrainingBlock from "../pages/FeedData/AutomationTrainingBlock";
import BoundsData from "../pages/FeedData/BoundsData";
import ProgramFeature from "../pages/FeedData/ProgramFeature";
import ProgramPoints from "../pages/FeedData/ProgramPoints";
import WorkoutDistribution from "../pages/FeedData/WorkoutDistribution";
import CoachAccess from "../pages/Report/CoachAccess";
import UserAdminGoal from "../pages/FeedData/UserAdminGoal";
import SubscriptionWithPayments from "../pages/SubscriptionWithPayments";
import CoachList from "../pages/CoachList";
import ChatLandingNew from "../pages/Chat/screens/ChatLandingNew";
import AthleteList from "../pages/AthleteList";
import AllAthleteList from "../pages/AllAthleteList";
import PaymentExpireAthletes from "../pages/PaymentExpireAthletes";
import Races from "../pages/FeedData/Races";
import SwimmingPool from "../pages/Athlete/SwimmingPool";
import AthletesRacesData from "../pages/Athlete/AthletesRacesData";
import AthleteformReview from "../pages/Athlete/AthleteformReview";
import FormReviewSection from "../pages/FeedData/FormReviewSection";
import FormReviewSectionOption from "../pages/FeedData/FormReviewSectionOption";
import AthleteFormReview from "../pages/FormReview/AthleteFormReview";
import AthletesavedReview from "../pages/Athlete/AthletesavedReview";
import REnewSubscription from "../pages/OnBoardingFlow/REnewSubscription";
import FormReviewList from "../pages/FormReview/FormReviewList";
import TodayExpiryList from "../pages/TodayExpiryList";
import InviteUser from "../pages/Chat/screens/group/InviteUser";
import CommunityGroups from "../pages/communityGroup/ManageGroup";
import CommunityGroupChallenge from "../pages/communityGroup/Challenges";
import MentorRegister from "../pages/communityGroup/MentorRegister";
import AthleteInvite from "../pages/communityGroup/AthleteInvite";
import Mentor from "../pages/communityGroup/Mentor";
import Athlete from "../pages/communityGroup/Athlete";
import Challenges from "../pages/Challenges/Challenges";
import CommunityGroupChallengeLevel from "../pages/communityGroup/ChallengeLevel";
import CommunityGroupChallengeTarget from "../pages/communityGroup/ChallengeTarget";

export const FitProRoutes = () => {
	const { setUser, isLoggedIn, setIsLoggedIn } = useContext(GlobalContext);
	const [path, setPath] = useState("");

	const sampleLocation = useLocation();
	console.log("sampleLocation", sampleLocation);
	useEffect(() => {
		setIsLoggedIn(localStorage.getItem("isLoggedIn"));
		setPath(sampleLocation.pathname);
	}, [sampleLocation.pathname, setIsLoggedIn]);

	// useEffect(() => {
	//   path === "/coach-yoska"
	//     ? localStorage.setItem("userId", 50)
	//     : path === "/triathlon-program"
	//     ? localStorage.setItem("userId", 47)
	//     : path === "/fitness-program"
	//     ? localStorage.setItem("userId", 60)
	//     : path === "/cycling-program"
	//     ? localStorage.setItem("userId", 45)
	//     : path === "/nextlevel-tri-program" && localStorage.setItem("userId", 3);
	// }, [path]);

	let token = localStorage.getItem("token");

	return (
		<Routes>
			<Route
				path='/'
				element={
					token == undefined ? (
						<LoginNewPage />
					) : (
						<Navigate to='/workout' />
					)
				}
			/>
			<Route path='/sign-up' element={<SignupPage />} />
			<Route path='/admin-login' element={<AdminLogin />} />
			<Route path='/login' element={<AdminLoginPage />} />

			<Route
				path='/invite-user'
				element={
					<ProtectedRoutes>
						<InviteUser />
					</ProtectedRoutes>
				}
			/>

			<Route path='/sign-up/:id' element={<SignupPage />} />
			<Route
				path='/phone-otp-verification'
				element={<PhoneOtpVerification />}
			/>
			<Route path='/forgot-password' element={<ForgotPassword />} />
			<Route path='/reset-password' element={<ResetPassword />} />
			<Route
				path='/dashboard'
				element={
					<ProtectedRoutes>
						<DashboardMaintence />
					</ProtectedRoutes>
				}
			/>
			<Route path='/subscription-flow' element={<SubscriptionFlow />} />

			<Route path='/onboarding-flow' element={<Flow />} />
			<Route path='/renew-flow' element={<REnewSubscription />} />

			<Route
				path='/chat-new'
				element={
					<ProtectedRoutes>
						<ChatLandingNew />
					</ProtectedRoutes>
				}
			/>
			{/* Chat Routes Start*/}
			<Route
				path='/chat'
				element={
					<ProtectedRoutes>
						<ChatLanding />
					</ProtectedRoutes>
				}
			/>
			<Route
				path='/start-new-chat'
				element={
					<ProtectedRoutes>
						<StartNewChat />
					</ProtectedRoutes>
				}
			/>

			<Route
				path='/individual-chat'
				element={
					<ProtectedRoutes>
						<IndividualChat />
					</ProtectedRoutes>
				}
			/>
			<Route
				path='/individual-chat/camera'
				element={
					<ProtectedRoutes>
						<CameraIndividualChat />
					</ProtectedRoutes>
				}
			/>
			<Route
				path='/individual-chat/forward-message'
				element={
					<ProtectedRoutes>
						<SelectMsgForwardUsers />
					</ProtectedRoutes>
				}
			/>
			<Route
				path='/individual-chat/next'
				element={
					<ProtectedRoutes>
						<IndividualChatSecond />
					</ProtectedRoutes>
				}
			/>
			<Route
				path='/individual-chat/third'
				element={
					<ProtectedRoutes>
						<IndividualChatThird />
					</ProtectedRoutes>
				}
			/>
			<Route
				path='/new-chat'
				element={
					<ProtectedRoutes>
						<NewChat />
					</ProtectedRoutes>
				}
			/>
			<Route
				path='/create-new-group/step-first'
				element={
					<ProtectedRoutes>
						<CreateNewGroup />
					</ProtectedRoutes>
				}
			/>
			<Route
				path='/create-new-group/step-final'
				element={
					<ProtectedRoutes>
						<CreateNewGroupStepTwo />
					</ProtectedRoutes>
				}
			/>
			<Route
				path='/group-chat/creator'
				element={
					<ProtectedRoutes>
						<GroupChatCreator />
					</ProtectedRoutes>
				}
			/>
			<Route
				path='/group-information'
				element={
					<ProtectedRoutes>
						<GroupInfo />
					</ProtectedRoutes>
				}
			/>
			<Route
				path='/group-information/add-new-member'
				element={
					<ProtectedRoutes>
						<AddNewMember />
					</ProtectedRoutes>
				}
			/>
			<Route
				path='/group-chat/camera'
				element={
					<ProtectedRoutes>
						<CameraGroupChat />
					</ProtectedRoutes>
				}
			/>
			<Route
				path='/view-post'
				element={
					<ProtectedRoutes>
						<ShowPost />
					</ProtectedRoutes>
				}
			/>
			<Route
				path='/show-post/pick-chats'
				element={
					<ProtectedRoutes>
						<ShareWith />
					</ProtectedRoutes>
				}
			/>
			<Route
				path='/temp-users-chats'
				element={
					<ProtectedRoutes>
						<TempChatPage />
					</ProtectedRoutes>
				}
			/>
			{/* Chat Routes Start*/}
			<Route
				path='/coach-yoska'
				element={
					<ProtectedRoutes>
						<RunningProgram />
					</ProtectedRoutes>
				}
			/>
			<Route
				path='/fitness-program'
				element={
					<ProtectedRoutes>
						<FitnessProgram />
					</ProtectedRoutes>
				}
			/>
			<Route
				path='/triathlon-program'
				element={
					<ProtectedRoutes>
						<TriathlonProgram />
					</ProtectedRoutes>
				}
			/>
			<Route
				path='/cycling-program'
				element={
					<ProtectedRoutes>
						<CyclingProgram />
					</ProtectedRoutes>
				}
			/>
			<Route
				path='/nextlevel-tri-program'
				element={
					<ProtectedRoutes>
						<NextTriProgram />
					</ProtectedRoutes>
				}
			/>
			<Route
				path='/leaderboard'
				element={
					<ProtectedRoutes>
						<LeaderBoardUser />
					</ProtectedRoutes>
				}
			/>
			{/*
  
    <Route
        path="/leaderboard"
        element={
          <ProtectedRoutes>
            <LeaderBoard />
          </ProtectedRoutes>
        }
      />
  */}

			<Route
				path='/admincreatechallenge'
				element={
					<ProtectedRoutes>
						<AdminCreateChallenge />
					</ProtectedRoutes>
				}
			/>

			<Route
				path='/assignechallenges-user'
				element={
					<ProtectedRoutes>
						{/* <AssignedUserChallneges /> */}
						<Challenges />
					</ProtectedRoutes>
				}
			/>
			<Route
				path='/swimming-pool'
				element={
					<ProtectedRoutes>
						<SwimmingPool />
					</ProtectedRoutes>
				}
			/>
			<Route
				path='/athlete-race'
				element={
					<ProtectedRoutes>
						<AthletesRacesData />
					</ProtectedRoutes>
				}
			/>
			<Route
				path='/form-review'
				element={
					<ProtectedRoutes>
						<AthleteFormReview />
					</ProtectedRoutes>
				}
			/>
			<Route
				path='/saved-review'
				element={
					<ProtectedRoutes>
						<AthletesavedReview />
					</ProtectedRoutes>
				}
			/>
			<Route
				path='/ongoing-challenge'
				element={
					<ProtectedRoutes>
						<OngoingUser />
					</ProtectedRoutes>
				}
			/>
			<Route
				path='/wellness'
				element={
					<ProtectedRoutes>
						<Wellnes />
					</ProtectedRoutes>
				}
			/>
			<Route
				path='/wellnessScore'
				element={
					<ProtectedRoutes>
						<WellResult />
					</ProtectedRoutes>
				}
			/>
			<Route
				path='/raccesRedness'
				element={
					<ProtectedRoutes>
						<CreateAssement />
					</ProtectedRoutes>
				}
			/>
			<Route
				path='/racessScore'
				element={
					<ProtectedRoutes>
						<RaccesScore />
					</ProtectedRoutes>
				}
			/>
			<Route
				path='/racecalculator'
				element={
					<ProtectedRoutes>
						<Calculator />
					</ProtectedRoutes>
				}
			/>
			<Route
				path='/swimming'
				element={
					<ProtectedRoutes>
						<SwimmingPage />
					</ProtectedRoutes>
				}
			/>
			<Route
				path='/coachrevenue'
				element={
					<ProtectedRoutes>
						<CoachRevenue />
					</ProtectedRoutes>
				}
			/>
			<Route
				path='/subscription-with-payments'
				element={
					<ProtectedRoutes>
						<SubscriptionWithPayments />
					</ProtectedRoutes>
				}
			/>
			<Route
				path='/coach-payment-page'
				element={
					<ProtectedRoutes>
						<CoachPyment />
					</ProtectedRoutes>
				}
			/>
			<Route
				path='/subscription-report'
				element={
					<ProtectedRoutes>
						<SubscriptionDetails />
					</ProtectedRoutes>
				}
			/>
			<Route
				path='/admin-privileges'
				element={
					<ProtectedRoutes>
						<CoachAccess />
					</ProtectedRoutes>
				}
			/>
			<Route
				path='/oldweeklyfeeddata'
				element={
					<ProtectedRoutes>
						<OldWeeklyFeedData />
					</ProtectedRoutes>
				}
			/>
			<Route
				path='/weeklyfeeddata'
				element={
					<ProtectedRoutes>
						<WeeklyFeedData />
					</ProtectedRoutes>
				}
			/>
			<Route
				path='/oldzonesclasification'
				element={
					<ProtectedRoutes>
						<OldZonesClasification />
					</ProtectedRoutes>
				}
			/>
			<Route
				path='/zonesclasification'
				element={
					<ProtectedRoutes>
						<ZonesClasification />
					</ProtectedRoutes>
				}
			/>
			<Route
				path='/bounds'
				element={
					<ProtectedRoutes>
						<BoundsData />
					</ProtectedRoutes>
				}
			/>
			<Route
				path='/distribution'
				element={
					<ProtectedRoutes>
						<WorkoutDistribution />
					</ProtectedRoutes>
				}
			/>
			<Route
				path='/oldprograms'
				element={
					<ProtectedRoutes>
						<OldProgram />
					</ProtectedRoutes>
				}
			/>
			<Route
				path='/programs'
				element={
					<ProtectedRoutes>
						<Program />
					</ProtectedRoutes>
				}
			/>
			<Route
				path='/automation-training-block'
				element={
					<ProtectedRoutes>
						<AutomationTrainingBlock />
					</ProtectedRoutes>
				}
			/>
			<Route
				path='/levels'
				element={
					<ProtectedRoutes>
						<Levels />
					</ProtectedRoutes>
				}
			/>
			<Route
				path='/oldlevels'
				element={
					<ProtectedRoutes>
						<OldLevels />
					</ProtectedRoutes>
				}
			/>
			<Route
				path='/formreview-section'
				element={
					<ProtectedRoutes>
						<FormReviewSection />
					</ProtectedRoutes>
				}
			/>
			<Route
				path='/formreview-section-option'
				element={
					<ProtectedRoutes>
						<FormReviewSectionOption />
					</ProtectedRoutes>
				}
			/>
			<Route
				path='/races'
				element={
					<ProtectedRoutes>
						<Races />
					</ProtectedRoutes>
				}
			/>
			<Route
				path='/activity'
				element={
					<ProtectedRoutes>
						<Activity />
					</ProtectedRoutes>
				}
			/>
			<Route
				path='/program-feature'
				element={
					<ProtectedRoutes>
						<ProgramFeature />
					</ProtectedRoutes>
				}
			/>
			<Route
				path='/program-points'
				element={
					<ProtectedRoutes>
						<ProgramPoints />
					</ProtectedRoutes>
				}
			/>
			<Route
				path='/oldracecalculation'
				element={
					<ProtectedRoutes>
						<OldRaceCalculator />
					</ProtectedRoutes>
				}
			/>
			<Route
				path='/racecalculation'
				element={
					<ProtectedRoutes>
						<RaceCalculator />
					</ProtectedRoutes>
				}
			/>
			<Route
				path='/oldmultiple-racecalculation'
				element={
					<ProtectedRoutes>
						<OldMultipleRaceCalculation />
					</ProtectedRoutes>
				}
			/>
			<Route
				path='/multiple-racecalculation'
				element={
					<ProtectedRoutes>
						<MultipleRaceCalculation />
					</ProtectedRoutes>
				}
			/>
			<Route
				path='/yragoals'
				element={
					<ProtectedRoutes>
						<YrcsafeedData />
					</ProtectedRoutes>
				}
			/>
			<Route
				path='/oldphase-block'
				element={
					<ProtectedRoutes>
						<OldPhaseBlock />
					</ProtectedRoutes>
				}
			/>
			<Route
				path='/phase-block'
				element={
					<ProtectedRoutes>
						<PhaseBlock />
					</ProtectedRoutes>
				}
			/>
			<Route
				path='/oldytagoal'
				element={
					<ProtectedRoutes>
						<OldYtaGoalValoum />
					</ProtectedRoutes>
				}
			/>
			<Route
				path='/ytagoal'
				element={
					<ProtectedRoutes>
						<YtaGoalValoum />
					</ProtectedRoutes>
				}
			/>
			<Route
				path='/user-goal'
				element={
					<ProtectedRoutes>
						<UserAdminGoal />
					</ProtectedRoutes>
				}
			/>
			<Route
				path='/Oldsubactivity'
				element={
					<ProtectedRoutes>
						<OldPhaseSubActivity />
					</ProtectedRoutes>
				}
			/>
			<Route
				path='/subactivity'
				element={
					<ProtectedRoutes>
						<PhaseSubActivity />
					</ProtectedRoutes>
				}
			/>
			<Route
				path='/oldworkoutactivity'
				element={
					<ProtectedRoutes>
						<OldWorkoutData />
					</ProtectedRoutes>
				}
			/>
			<Route
				path='/workoutactivity'
				element={
					<ProtectedRoutes>
						<WorkoutData />
					</ProtectedRoutes>
				}
			/>
			<Route
				path='/oldsubworkout'
				element={
					<ProtectedRoutes>
						<OldSubWorkout />
					</ProtectedRoutes>
				}
			/>
			<Route
				path='/subworkout'
				element={
					<ProtectedRoutes>
						<SubWorkout />
					</ProtectedRoutes>
				}
			/>
			<Route
				path='/oldassesment'
				element={
					<ProtectedRoutes>
						<OldRenderAssesmentData />
					</ProtectedRoutes>
				}
			/>
			<Route
				path='/assesment'
				element={
					<ProtectedRoutes>
						<RenderAssesmentData />
					</ProtectedRoutes>
				}
			/>
			<Route
				path='/oldassesmentname'
				element={
					<ProtectedRoutes>
						<OldAssesmentName />
					</ProtectedRoutes>
				}
			/>
			<Route
				path='/assesmentname'
				element={
					<ProtectedRoutes>
						<AssesmentName />
					</ProtectedRoutes>
				}
			/>
			<Route
				path='/oldsegments'
				element={
					<ProtectedRoutes>
						<OldSugmentsName />
					</ProtectedRoutes>
				}
			/>
			<Route
				path='/segments'
				element={
					<ProtectedRoutes>
						<SugmentsName />
					</ProtectedRoutes>
				}
			/>
			<Route
				path='/oldsub-segments'
				element={
					<ProtectedRoutes>
						<OldSubSugments />
					</ProtectedRoutes>
				}
			/>
			<Route
				path='/sub-segments'
				element={
					<ProtectedRoutes>
						<SubSugments />
					</ProtectedRoutes>
				}
			/>
			<Route
				path='/oldquestions'
				element={
					<ProtectedRoutes>
						<OldQuestions />
					</ProtectedRoutes>
				}
			/>
			<Route
				path='/questions'
				element={
					<ProtectedRoutes>
						<Questions />
					</ProtectedRoutes>
				}
			/>
			<Route
				path='/oldoption'
				element={
					<ProtectedRoutes>
						<OldOption />
					</ProtectedRoutes>
				}
			/>
			<Route
				path='/option'
				element={
					<ProtectedRoutes>
						<Option />
					</ProtectedRoutes>
				}
			/>
			<Route
				path='/oldreadness'
				element={
					<ProtectedRoutes>
						<OldRenderreadiness />
					</ProtectedRoutes>
				}
			/>
			<Route
				path='/readness'
				element={
					<ProtectedRoutes>
						<Renderreadiness />
					</ProtectedRoutes>
				}
			/>
			<Route
				path='/oldscore-readness'
				element={
					<ProtectedRoutes>
						<OldScorData />
					</ProtectedRoutes>
				}
			/>
			<Route
				path='/score-readness'
				element={
					<ProtectedRoutes>
						<ScorData />
					</ProtectedRoutes>
				}
			/>
			<Route
				path='/oldsegments-readness'
				element={
					<ProtectedRoutes>
						<OldSegmentReadness />
					</ProtectedRoutes>
				}
			/>
			<Route
				path='/segments-readness'
				element={
					<ProtectedRoutes>
						<SegmentReadness />
					</ProtectedRoutes>
				}
			/>
			<Route
				path='/oldsubsegments-readness'
				element={
					<ProtectedRoutes>
						<OldSubSegmentReadness />
					</ProtectedRoutes>
				}
			/>
			<Route
				path='/subsegments-readness'
				element={
					<ProtectedRoutes>
						<SubSegmentReadiness />
					</ProtectedRoutes>
				}
			/>
			<Route
				path='/oldoption-readness'
				element={
					<ProtectedRoutes>
						<OldOptionReadness />
					</ProtectedRoutes>
				}
			/>
			<Route
				path='/option-readness'
				element={
					<ProtectedRoutes>
						<OptionReadness />
					</ProtectedRoutes>
				}
			/>

			<Route
				path='/oldzones-power'
				element={
					<ProtectedRoutes>
						<OldZonesPower />
					</ProtectedRoutes>
				}
			/>
			<Route
				path='/zones-power'
				element={
					<ProtectedRoutes>
						<ZonesPower />
					</ProtectedRoutes>
				}
			/>
			<Route
				path='/oldzones-heart'
				element={
					<ProtectedRoutes>
						<OldZoneHeart />
					</ProtectedRoutes>
				}
			/>
			<Route
				path='/zones-heart'
				element={
					<ProtectedRoutes>
						<ZoneHeart />
					</ProtectedRoutes>
				}
			/>
			<Route
				path='/oldchallenge'
				element={
					<ProtectedRoutes>
						<OldChallengeData />
					</ProtectedRoutes>
				}
			/>
			<Route
				path='/challenge'
				element={
					<ProtectedRoutes>
						<ChallengeData />
					</ProtectedRoutes>
				}
			/>
			<Route
				path='/goal-without-volume'
				element={
					<ProtectedRoutes>
						<GoalWithoutVolume />
					</ProtectedRoutes>
				}
			/>
			<Route
				path='/oldgoal-without-volume'
				element={
					<ProtectedRoutes>
						<OldGoalWithoutVloume />
					</ProtectedRoutes>
				}
			/>
			<Route
				path='/oldactivity-group'
				element={
					<ProtectedRoutes>
						<OldActivityGroup />
					</ProtectedRoutes>
				}
			/>
			<Route
				path='/activity-group'
				element={
					<ProtectedRoutes>
						<ActivityGroup />
					</ProtectedRoutes>
				}
			/>
			<Route
				path='/oldactivity-track'
				element={
					<ProtectedRoutes>
						<OldActivityTrack />
					</ProtectedRoutes>
				}
			/>
			<Route
				path='/activity-track'
				element={
					<ProtectedRoutes>
						<ActivityTrack />
					</ProtectedRoutes>
				}
			/>
			<Route
				path='/formreview'
				element={
					<ProtectedRoutes>
						<FormReviewList />
					</ProtectedRoutes>
				}
			/>
			<Route
				path='/form-image-review'
				element={
					<ProtectedRoutes>
						<FormImageRview />
					</ProtectedRoutes>
				}
			/>
			<Route
				path='/promotors'
				element={
					<ProtectedRoutes>
						<Promotors />
					</ProtectedRoutes>
				}
			/>
			<Route
				path='/payment-creds'
				element={
					<ProtectedRoutes>
						<PaymentCred />
					</ProtectedRoutes>
				}
			/>
			<Route
				path='/uoms'
				element={
					<ProtectedRoutes>
						<Uoms />
					</ProtectedRoutes>
				}
			/>
			<Route
				path='/tags'
				element={
					<ProtectedRoutes>
						<Tags />
					</ProtectedRoutes>
				}
			/>
			<Route
				path='/group-registration'
				element={
					<ProtectedRoutes>
						<CreateGroupRegistration />
					</ProtectedRoutes>
				}
			/>
			<Route
				path='/activity-metric'
				element={
					<ProtectedRoutes>
						<ActivityMetric />
					</ProtectedRoutes>
				}
			/>
			<Route
				path='/system-configration'
				element={
					<ProtectedRoutes>
						<SystemConfigration />
					</ProtectedRoutes>
				}
			/>
			<Route
				path='/olddiscount-coupon'
				element={
					<ProtectedRoutes>
						<OldDiscountCoupon />
					</ProtectedRoutes>
				}
			/>
			<Route
				path='/discount-coupon'
				element={
					<ProtectedRoutes>
						<DiscountCoupon />
					</ProtectedRoutes>
				}
			/>
			<Route
				path='/subscription'
				element={
					<ProtectedRoutes>
						<Subscription />
					</ProtectedRoutes>
				}
			/>

			<Route
				path='/savedreview'
				element={
					<ProtectedRoutes>
						<SavedReview />
					</ProtectedRoutes>
				}
			/>
			<Route
				path='/revenuereportbytimerange'
				element={
					<ProtectedRoutes>
						<RevenueReporptByTime />
					</ProtectedRoutes>
				}
			/>
			<Route
				path='/usergrowthnumbers'
				element={
					<ProtectedRoutes>
						<UserGrowthReport />
					</ProtectedRoutes>
				}
			/>
			<Route
				path='/overall-report'
				element={
					<ProtectedRoutes>
						<OverAllReports />
					</ProtectedRoutes>
				}
			/>
			<Route
				path='/workouts'
				element={
					<ProtectedRoutes>
						<Workout />
					</ProtectedRoutes>
				}
			/>
			<Route
				path='/athlete/:id'
				element={
					<ProtectedRoutes>
						<DashboardMaintence path={path} />
					</ProtectedRoutes>
				}
			/>
			<Route
				path='/training-blocks-1'
				element={
					<ProtectedRoutes>
						<TraningBlock path={path} />
					</ProtectedRoutes>
				}
			/>
			<Route
				path='/workout'
				element={
					<ProtectedRoutes>
						<Calender path={path} />
					</ProtectedRoutes>
				}
			/>
			<Route
				path='/master'
				element={
					<ProtectedRoutes>
						<Masters path={path} />
					</ProtectedRoutes>
				}
			/>
			<Route
				path='/tranning-profile'
				element={
					<ProtectedRoutes>
						<TranningProfile path={path} />
					</ProtectedRoutes>
				}
			/>
			<Route
				path='/zone'
				element={
					<ProtectedRoutes>
						<ZonesForm path={path} />
					</ProtectedRoutes>
				}
			/>

			<Route
				path='/events-calendar'
				element={
					<ProtectedRoutes>
						<EventsCalendar path={path} />
					</ProtectedRoutes>
				}
			/>

			{/*******************New routes ******************/}

			<Route
				path='/walk-jog'
				element={
					<ProtectedRoutes>
						<WalkJog path={path} />
					</ProtectedRoutes>
				}
			/>
			<Route
				path='/phases'
				element={
					<ProtectedRoutes>
						<CreatePhases path={path} />
					</ProtectedRoutes>
				}
			/>

			<Route
				path='/athlete'
				element={
					<ProtectedRoutes>
						<AthletesPage path={path} />
					</ProtectedRoutes>
				}
			/>
			<Route
				path='/athlete-plan-page'
				element={
					<ProtectedRoutes>
						<AthletesPlanPage path={path} />
					</ProtectedRoutes>
				}
			/>
			<Route
				path='/details-Page'
				element={
					<ProtectedRoutes>
						<DetailsPage path={path} />
					</ProtectedRoutes>
				}
			/>
			<Route
				path='/plans-Page'
				element={
					<ProtectedRoutes>
						<PlansPage path={path} />
					</ProtectedRoutes>
				}
			/>

			{/*******************New routes From ******************/}

			<Route
				path='/athlete-dashboard'
				element={
					<ProtectedRoutes>
						<AthleteDashboardPage path={path} />
					</ProtectedRoutes>
				}
			/>
			<Route
				path='/athlete-page-new'
				element={
					<ProtectedRoutes>
						<AthletesPageNew path={path} />
					</ProtectedRoutes>
				}
			/>
			<Route
				path='/automation-summary-page'
				element={
					<ProtectedRoutes>
						<AutomationSummaryPage path={path} />
					</ProtectedRoutes>
				}
			/>
			<Route
				path='/coach-page'
				element={
					<ProtectedRoutes>
						<CoachPage path={path} />
					</ProtectedRoutes>
				}
			/>
			<Route
				path='/coach'
				element={
					<ProtectedRoutes>
						<AssignCoach />
					</ProtectedRoutes>
				}
			/>
			<Route
				path='/coach-list'
				element={
					<ProtectedRoutes>
						<CoachList />
					</ProtectedRoutes>
				}
			/>
			<Route
				path='/athlete-list'
				element={
					<ProtectedRoutes>
						<AthleteList />
					</ProtectedRoutes>
				}
			/>
			<Route
				path='/today-failed'
				element={
					<ProtectedRoutes>
						<TodayExpiryList />
					</ProtectedRoutes>
				}
			/>
			<Route
				path='/expire-payment'
				element={
					<ProtectedRoutes>
						<PaymentExpireAthletes />
					</ProtectedRoutes>
				}
			/>
			<Route
				path='/athletes'
				element={
					<ProtectedRoutes>
						<AllAthleteList />
					</ProtectedRoutes>
				}
			/>

			<Route
				path='/coach-report-page'
				element={
					<ProtectedRoutes>
						<CoachReportPage path={path} />
					</ProtectedRoutes>
				}
			/>
			<Route
				path='/conversation-page'
				element={
					<ProtectedRoutes>
						<ConversationPage path={path} />
					</ProtectedRoutes>
				}
			/>

			<Route
				path='/cycling-program-page'
				element={
					<ProtectedRoutes>
						<CyclingProgramPage path={path} />
					</ProtectedRoutes>
				}
			/>
			<Route
				path='/fitness-program-page'
				element={
					<ProtectedRoutes>
						<FitnessProgramPage path={path} />
					</ProtectedRoutes>
				}
			/>

			<Route
				path='/fit-profile-page'
				element={
					<ProtectedRoutes>
						<FitProfilePage path={path} />
					</ProtectedRoutes>
				}
			/>
			<Route
				path='/instruction-page'
				element={
					<ProtectedRoutes>
						<InstructionsPage path={path} />
					</ProtectedRoutes>
				}
			/>
			<Route
				path='/lastfeedback-page'
				element={
					<ProtectedRoutes>
						<LastFeedbackPage path={path} />
					</ProtectedRoutes>
				}
			/>
			<Route
				path='/notification-page'
				element={
					<ProtectedRoutes>
						<NotificationPage path={path} />
					</ProtectedRoutes>
				}
			/>
			<Route
				path='/pending-payment-page'
				element={
					<ProtectedRoutes>
						<PendingPayment path={path} />
					</ProtectedRoutes>
				}
			/>
			<Route
				path='/plan-end-page'
				element={
					<ProtectedRoutes>
						<PlanEndPage path={path} />
					</ProtectedRoutes>
				}
			/>
			<Route
				path='/plan-status-page'
				element={
					<ProtectedRoutes>
						<PlanStatusPage path={path} />
					</ProtectedRoutes>
				}
			/>
			<Route
				path='/profile-page'
				element={
					<ProtectedRoutes>
						<ProfilePage path={path} />
					</ProtectedRoutes>
				}
			/>
			<Route
				path='/race-deatils-page'
				element={
					<ProtectedRoutes>
						<RaceDetailsPage path={path} />
					</ProtectedRoutes>
				}
			/>
			<Route
				path='/reset-page'
				element={
					<ProtectedRoutes>
						<ResetPage path={path} />
					</ProtectedRoutes>
				}
			/>

			<Route
				path='/coach-yoska-page'
				element={
					<ProtectedRoutes>
						<RunningProgramPage path={path} />
					</ProtectedRoutes>
				}
			/>
			<Route
				path='/trainee-managment-page'
				element={
					<ProtectedRoutes>
						<TraineeManagementPage path={path} />
					</ProtectedRoutes>
				}
			/>
			<Route
				path='/tarining-page'
				element={
					<ProtectedRoutes>
						<TrainingPage path={path} />
					</ProtectedRoutes>
				}
			/>
			<Route
				path='/traithlon-program-page'
				element={
					<ProtectedRoutes>
						<TriathlonProgramPage path={path} />
					</ProtectedRoutes>
				}
			/>
			<Route
				path='/workouts-page'
				element={
					<ProtectedRoutes>
						<WorkoutsPage path={path} />
					</ProtectedRoutes>
				}
			/>
			<Route
				path='/yfa-report-page'
				element={
					<ProtectedRoutes>
						<YFAReportPage path={path} />
					</ProtectedRoutes>
				}
			/>
			<Route
				path='/yfa-summary-page'
				element={
					<ProtectedRoutes>
						<YFASummaryReport path={path} />
					</ProtectedRoutes>
				}
			/>
			<Route
				path='/zone-page'
				element={
					<ProtectedRoutes>
						<ZonePage path={path} />
					</ProtectedRoutes>
				}
			/>
			<Route
				path='/coach-booking'
				element={
					<ProtectedRoutes>
						<CoachBooking path={path} />
					</ProtectedRoutes>
				}
			/>
			<Route
				path='/coach-admin-booking'
				element={
					<ProtectedRoutes>
						<CoachAdminBooking path={path} />
					</ProtectedRoutes>
				}
			/>
			<Route
				path='/all-coach-booking'
				element={
					<ProtectedRoutes>
						<AllBooking path={path} />
					</ProtectedRoutes>
				}
			/>
			<Route
				path='/create-activity'
				element={
					<ProtectedRoutes>
						<CreateActivity path={path} />
					</ProtectedRoutes>
				}
			/>
			<Route
				path='/create-sub-activity'
				element={
					<ProtectedRoutes>
						<CreateSubActivity path={path} />
					</ProtectedRoutes>
				}
			/>

			<Route
				path='/strava'
				element={
					<ProtectedRoutes>
						<Strava path={path} />
					</ProtectedRoutes>
				}
			/>
			<Route
				path='/coach-register'
				element={
					<ProtectedRoutes>
						<RegisterCoach />
					</ProtectedRoutes>
				}
			/>
			<Route
				path='/admin-register'
				element={
					<ProtectedRoutes>
						<AdminRegstration />
					</ProtectedRoutes>
				}
			/>
			<Route
				path='/community-groups'
				element={
					<ProtectedRoutes>
						<CommunityGroups />
					</ProtectedRoutes>
				}
			/>
			<Route
				path='/community-group-challenge'
				element={
					<ProtectedRoutes>
						<CommunityGroupChallenge />
					</ProtectedRoutes>
				}
			/>
			<Route
				path='/commmunity-group-invite'
				element={
					<ProtectedRoutes>
						<AthleteInvite />
					</ProtectedRoutes>
				}
			/>
			<Route
				path='/list-mentor'
				element={
					<ProtectedRoutes>
						<Mentor />
					</ProtectedRoutes>
				}
			/>
			<Route
				path='/list-athlete'
				element={
					<ProtectedRoutes>
						<Athlete />
					</ProtectedRoutes>
				}
			/>
			<Route
				path='/community-group-challenge-level'
				element={
					<ProtectedRoutes>
						<CommunityGroupChallengeLevel />
					</ProtectedRoutes>
				}
			/>
			<Route
				path='/community-group-challenge-target'
				element={
					<ProtectedRoutes>
						<CommunityGroupChallengeTarget />
					</ProtectedRoutes>
				}
			/>
		</Routes>
	);
};
