import React from "react";
import Header from "../../components/Header";
import MasterSidebar from "../../components/MasterSidebar";
import {
  IconEdit,
  IconLiveView,
  IconMessage,
  IconTrash,
  IconX,
} from "@tabler/icons";
import { useState } from "react";
import TagInput from "../../components/Taginput";
import "./Master.css";
import { Table, Tag } from "antd";

import FormComponants from "./FormComponants";

const actions = [
  {
    id: 1,
    title: "Quick view",
    icon: <IconLiveView size={18} />,
  },
  {
    id: 2,
    title: "Chat",
    icon: <IconMessage size={18} />,
  },
  {
    id: 3,
    title: "Details",
    icon: <IconTrash size={18} />,
  },
];

const Workout = () => {
  const [createModal, setCreateModal] = useState(false);
  const [tags, setTags] = useState([]);
  const [inputValue, setInputValue] = useState("");
  const [selectedLibraryItem, setSelectedLibraryItem] = useState(null);
  const [isOpenHandleWoerkOut, setIsOpenHandleWoerkOut] = useState(false);
  const [isOpenWoerkOut, setIsOpenWoerkOut] = useState("showTable");
  console.log(selectedLibraryItem?.workouts, "selectedLibraryItem");
  const handleModalReveal = () => {
    setCreateModal(true);
  };

  const [selectedActivity, setSelectedActivity] = useState("");


  const handleActivityChange = (event) => {
    setSelectedActivity(event.target.value);
  };



  const columns = [
    {
      title: "Id",
      dataIndex: "id",
      key: "id",
    },
    {
      title: "Name",
      dataIndex: "name",
      key: "name",
    },
    {
      title: "Duration",
      dataIndex: "duration",
      key: "duration",
    },
    {
      title: "Activity Description",
      dataIndex: ["activity", "description"],
      key: "activityDescription",
    },
    {
      title: "Has Zone",
      dataIndex: "activity",
      key: "hasZone",
      render: (activity) => (
        <Tag color={activity.hasZone ? "green" : "red"}>
          {activity.hasZone ? "Yes" : "No"}
        </Tag>
      ),
    },
    {
      title: "Actions",
      dataIndex: "actions",
      key: "actions",
      render: (_, record) => (
        <div className="flex ">
          <span className="px-2">
            <IconEdit color="blue" />
          </span>
          <span className="px-2">
            <IconTrash color="red" />
          </span>
        </div>
      ),
    },
  ];

  return (
    <div>
      <Header />
      <div className="grid grid-cols-1 xl:grid-cols-5 items-start gap-x-4">
        <div className="p-4 bg-slate-100 fixed top-24 w-full lg:w-4/12 xl:w-2/12 left-0 overflow-y-scroll h-full">
          <MasterSidebar
            actions={actions}
            setIsOpenWoerkOut={setIsOpenWoerkOut}
            handleModalReveal={handleModalReveal}
            setSelectedLibraryItem={setSelectedLibraryItem}
            setIsOpenHandleWoerkOut={setIsOpenHandleWoerkOut}
          />
        </div>
        <div className="flex items-center justify-center p-4 w-full md:w-10/12 lg:col-span-4 absolute right-0 overflow-y-scroll">
          {/* Create modal */}
          <div
            className={`${createModal ? "md:fixed" : "md:absolute"
              } md:top-32 md:right-2/2 px-4 py-6 bg-slate-50 drop-shadow-2xl rounded-md border-2 border-slate-200/75 w-full md:w-8/12 lg:w-6/12 xl:w-4/12 z-50`}
            style={{ display: createModal ? "block" : "none" }}
          >
            <div className="flex items-center justify-between">
              <span className="text-xl font-medium m-0 pb-2">
                Create Library
              </span>
              <span className="text-slate-600 ">
                <IconX
                  size={22}
                  className="mb-2 cursor-pointer"
                  onClick={() => setCreateModal(false)}
                />
              </span>
            </div>
            <hr />
            <br />

            <div className="mb-5">
              <div>
                <p className="mb-1 text-slate-600 px-1 font-medium">
                  Description <span className="text-red-500">*</span>{" "}
                </p>
                <input
                  type="text"
                  placeholder="Enter your Description"
                  className="w-full border-b-2 py-2 rounded-md focus:outline-none text-slate-600 px-1"
                />
              </div>
            </div>
            <div className="mb-5">
              <div>
                <p className="mb-1 text-slate-600 px-1 font-medium">
                  Tags <span className="text-red-500">*</span>{" "}
                </p>
                <TagInput
                  tags={tags}
                  setTags={setTags}
                  inputValue={inputValue}
                  setInputValue={setInputValue}
                />
              </div>
            </div>
            <div className="mb-2">
              <button className="p-2.5 bg-orange-500 text-slate-50 w-full rounded-sm">
                Create
              </button>
            </div>
          </div>
        </div>
      </div>
      {isOpenHandleWoerkOut && (
        <div
          style={{
            marginTop: "100px",
            marginLeft: "270px",
            width: "calc(100% - 270px)",
          }}
        >
          <div className="workout-screen-section">
            <div className="text-center py-4">
              <h1 className="font-bold text-xl text-slate-600">
                {selectedLibraryItem?.description}
              </h1>
            </div>
            <div className="workout-button-section">
              <button
                className="bg-blue-500 hover:bg-blue-700 text-white font-semibold py-2 px-4 rounded"
                onClick={() => {
                  setIsOpenWoerkOut("showForm");
                }}
              >
                Add Training Volume
              </button>
              <button className="bg-green-500 hover:bg-green-700 text-white font-semibold py-2 px-4 rounded">
                Copy Training Volume
              </button>
              <button className="bg-red-500 hover:bg-red-700 text-white font-semibold py-2 px-4 rounded">
                Cut Training Volume
              </button>
              <button className="bg-cyan-500 hover:bg-cyan-700 text-white font-semibold py-2 px-4 rounded">
                Paste Training Volume
              </button>
            </div>
            {isOpenWoerkOut === "showTable" && (
              <div className="workout-table-section py-4">
                <Table
                  columns={columns}
                  dataSource={selectedLibraryItem?.workouts}
                  pagination={true}
                />
              </div>
            )}
            {isOpenWoerkOut === "showForm" && <FormComponants />}
          </div>
        </div>
      )}
    </div>
  );
};

export default Workout;
