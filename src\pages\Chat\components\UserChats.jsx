import { Avatar, Box, Typography } from "@mui/material";
import React, { memo, useEffect, useState, useRef, useCallback } from "react";
import InsertLinkOutlinedIcon from "@mui/icons-material/InsertLinkOutlined";
import { MdOutlineGroup } from "react-icons/md";
import DeleteOutlineIcon from '@mui/icons-material/DeleteOutline'; 
import NotificationsActiveOutlinedIcon from "@mui/icons-material/NotificationsActiveOutlined";
import { useDispatch } from "react-redux";
import { storeOpenedUserInfoAction } from "../redux/action/usersAction";
import {
  serverTimestamp,
  onValue,
  query,
  limitToLast,
  orderByKey,
  orderByChild,
  equalTo,
  ref,
  update,
  get,
  set,
  remove
} from "firebase/database";
import PubNub from 'pubnub';
import { useNavigate } from "react-router-dom";
import { getGroupInfoAction, storeOpenedGroupMembers } from "../redux/action/groupAction";
import { db } from "../../../API/firebase.config";
import { useSelector } from "react-redux";
import { URL } from "../../../API/api-endpoint";
import { useMessageContext } from "../../../context/MessageContext";
import Swal from 'sweetalert2';
import { getCurrentUserChatsAction } from "../redux/action/userChats";
import axios from 'axios';
import { SET_OPENED_GROUP_MEMBERS } from "../redux/constant/groupConstants";
import "../styles/userChats.css";
import _ from 'lodash';

const UserChats = ({ item,currentUser,isUnRead,setOpenPage,openPage,setIsUnRead,group }) => {
  
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const [lastMessage, setLastMessage] = useState("");
  const [lastMessageTime, setLastMessageTime] = useState("");
  const [unSeenMessageCount, setUnSeenMessageCount] = useState(0);
  const [userInfo, setUserInfo] = useState({});
  const [groupInfo, setGroupInfo] = useState({});
  const [totalUnseenMessageCount, setTotalUnseenMessageCount] = useState(0);
  const { updateTotalUnseenMessageCount } = useMessageContext();
  const { setFirebaseUnseenCount } = useMessageContext(); 
  const { setPubnubUnseenCount } = useMessageContext();
  const[totalMessageCount,setTotalMessageCount]= useState(0)
  const { openedUser } = useSelector(state => state.users)
  const { openedGroupInfo } = useSelector((state) => state.group);
  const roleID = localStorage.getItem("roleID");
  const userDetail = JSON.parse(localStorage.getItem("user"));
  
  const formatRelativeTime = (isoTimestamp) => {
    const messageDate = new Date(isoTimestamp);
    const now = new Date();
  
    // Calculate time difference in milliseconds
    const differenceInMs = now - messageDate;
  
    // Define time thresholds in milliseconds
    const oneDay = 24 * 60 * 60 * 1000;
    const oneWeek = 7 * oneDay;
    const oneMonth = 30 * oneDay;
  
    if (differenceInMs < oneDay && now.getDate() === messageDate.getDate()) {
      // Format the time as "HH:MM AM/PM"
      return messageDate.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    } else if (differenceInMs < oneDay * 2) {
      return "Yesterday";
    } else if (differenceInMs < oneWeek) {
      const daysAgo = Math.floor(differenceInMs / oneDay);
      return `${daysAgo} days ago`;
    } else if (differenceInMs < oneMonth) {
      const weeksAgo = Math.floor(differenceInMs / oneWeek);
      return `${weeksAgo} week${weeksAgo > 1 ? "s" : ""} ago`;
    } else {
      const monthsAgo = Math.floor(differenceInMs / oneMonth);
      return `${monthsAgo} month${monthsAgo > 1 ? "s" : ""} ago`;
    }
  };


  const handleSelect = async (user) => {
    dispatch(storeOpenedUserInfoAction(user));

    const combinedId =
      currentUser.uid > user.uid
        ? currentUser.uid + user.uid
        : user.uid + currentUser.uid;
    
    try {
      const chatRef = ref(db, "chats/" + combinedId);
      const snapshot = await get(chatRef);

      if (!snapshot.exists()) {
        const currentUserChatsRef = ref(db, `userChats/${currentUser.uid}`);
        const userChatsRef = ref(db, `userChats/${user.uid}`);

        await update(currentUserChatsRef, {
          [`${combinedId}/userInfo`]: {
            uid: user.uid,
            displayName: user.displayName,
            photoURL: user.photoURL,
          },
          [`${combinedId}/date`]: serverTimestamp(),
          [`${combinedId}/isMsgReqAccepted`]: true,
          [`${combinedId}/isMsgReqDeclined`]: false,
          [`${combinedId}/lastMessage`]: "",
          [`${combinedId}/chatType`]: "personal",
        });

        await update(userChatsRef, {
          [`${combinedId}/userInfo`]: {
            uid: currentUser.uid,
            displayName: currentUser.displayName,
            photoURL: currentUser.profileImageUrl,
          },
          [`${combinedId}/date`]: serverTimestamp(),
          [`${combinedId}/isMsgReqAccepted`]: false,
          [`${combinedId}/isMsgReqDeclined`]: false,
          [`${combinedId}/lastMessage`]: "",
          [`${combinedId}/chatType`]: "personal",
        });

        // navigate("/individual-chat");
        setOpenPage("individual")
        setTimeout(() => {
          setIsUnRead(false);
        }, 3000);
        
      }
      // navigate("/individual-chat");
      if (window.matchMedia("(max-width: 430px)").matches) {
        setOpenPage("individual")
        if (window.matchMedia("(max-width: 430px)").matches) { 
            const mobileSection = document.querySelector('.section-two-mobile'); 
            const homeSection = document.querySelector('.section-one'); if (mobileSection) {  
                mobileSection.style.display = 'block'; } if (homeSection) {  
                    homeSection.style.display = 'none'; } } 
      } else {
        setOpenPage("individual")
        setTimeout(() => {
          setIsUnRead(false);
        }, 3000);
      }
    } catch (err) {
      console.log(err);
    }
  };

  const handleGotoGroupChat = async (groupId) => {
    dispatch(getGroupInfoAction(groupId));

    const messagesRef = ref(db, `chats/${groupId}/messages`);
    const messagesSnapshot = await get(messagesRef);

    if (messagesSnapshot.exists()) {
      const messages = messagesSnapshot.val();

      const updates = {};
      for (const messageId in messages) {
        updates[`${messageId}/isSeen`] = true;
      }
      await update(ref(db, `chats/${groupId}/messages`), updates);
    }
    
    // navigate("/group-chat/creator");
    setOpenPage("group-chat")
    if (window.matchMedia("(max-width: 430px)").matches) { const mobileSection = document.querySelector('.section-two-mobile'); const homeSection = document.querySelector('.section-one'); if (mobileSection) { mobileSection.style.display = 'block';  } if (homeSection) { homeSection.style.display = 'none'; } }
    setTimeout(() => {
      setIsUnRead(false);
    }, 3000);
  };


  const decodeHtml = (html) => {
    const parser = new DOMParser();
    const doc = parser.parseFromString(html, "text/html");
    return doc.documentElement.textContent;
  };
  const decodeHtmlRecursively = (html) => {
    let decoded = decodeHtml(html);
    while (decoded !== html) {
      html = decoded;
      decoded = decodeHtml(html);
    }
    return decoded;
  };
  
  const getLastMessage = (userUid) => {
    const combinedId =
      currentUser.uid > userUid
        ? currentUser.uid + userUid
        : userUid + currentUser.uid;
  
    let lastMessage = "";
    let lastMessageTime = 0;
    const messagesRef = ref(db, `chats/${combinedId}/messages`);
    const lastMessageQuery = query(
      messagesRef,
      orderByChild("timeStamp"),
      limitToLast(50) // Increase the limit to ensure we can find a message that is not deleted
    );
  
    const callback = (snapshot) => {
      if (snapshot.exists()) {
        const messages = snapshot.val();
        if (messages && typeof messages === 'object') {
          // Convert the messages object to an array
          const messagesArray = Object.keys(messages).map(key => messages[key]);
          // Sort the messages based on timeStamp
          messagesArray.sort((a, b) => b.timeStamp - a.timeStamp);
  
          // Find the last valid message
          const lastValidMessage = messagesArray.find(message => !message.isDeleted);
  
          if (lastValidMessage) {
            const type = lastValidMessage.type;
            lastMessage = type === "text" ? lastValidMessage.message : type;
            lastMessageTime = lastValidMessage.timeStamp;
            
            setLastMessage(decodeHtmlRecursively(lastMessage));
  
            const formattedTime = new Date(lastMessageTime).toLocaleString("en-US", {
              hour: "numeric",
              minute: "numeric",
              hour12: true,
            });
            setLastMessageTime(formattedTime);
          } else {
            console.log("No valid messages found.");
          }
        } else {
          console.log("Invalid message structure.");
        }
      } else {
        console.log("No messages found.");
      }
    };
  
    onValue(lastMessageQuery, callback);
  
    return;
  };
  
  const getUnseenMessageCount = (userUid) => {
    const combinedId =
      currentUser.uid > userUid
        ? currentUser.uid + userUid
        : userUid + currentUser.uid;

    const messagesRef = ref(db, `chats/${combinedId}/messages`);

    const unseenMessagesQuery = query(
      messagesRef,
      orderByChild("isSeen"),
      equalTo(false)
    );

    let unseenMsgesCount = 0;

    const callback = (snapshot) => {
      unseenMsgesCount = 0; // Reset count before updating
      snapshot.forEach((childSnapshot) => {
        const message = childSnapshot.val();
        if (message.senderId !== currentUser.uid && !message.isSeen) {
          unseenMsgesCount++; // Increment count
        }
      });

      // playAudio();
      
      setUnSeenMessageCount(unseenMsgesCount); // Set count after processing
    };

    onValue(unseenMessagesQuery, callback);
    return;
  };
  

  const getTotalMessageCount = (userUid) => {
    const combinedId =
      currentUser.uid > userUid
        ? currentUser.uid + userUid
        : userUid + currentUser.uid;
  
    const messagesRef = ref(db, `chats/${combinedId}/messages`);
  
    let totalMsgesCount = 0;
  
    const callback = (snapshot) => {
      totalMsgesCount = 0; // Reset count before updating
      snapshot.forEach((childSnapshot) => {
        totalMsgesCount++; // Increment count
      });
  
      setTotalMessageCount(totalMsgesCount); // Set count after processing
    };
  
    onValue(messagesRef, callback);
    return;
  };
  

  const playAudio = () => {
    const audio = new Audio("/audio/new-msg.mp3");
    if (audio.paused) {
      audio.play();
    }
  };

  const fetchGrouplastMessage = async(groupId) =>{
    const authToken = localStorage.getItem("token");
    const response = await axios.get(`${URL}/group-message/${groupId}`, {
      headers: {
        Authorization: authToken,
        "Content-Type": "application/json",
      },
    });
    if (response.status !== 200) {
      throw new Error(`Axios API failed with status: ${response.status}`);
    }

    const messages = response.data.data; 
    
    if (messages.length > 0) {
      const latestMessage = messages.reduce((latest, current) =>
        new Date(current.timestamp) > new Date(latest.timestamp) ? current : latest
      );
      
      const finalMessage = {
        "message":latestMessage.message,
        "time":latestMessage.msgTimestamp
      };
      return finalMessage;
    }
    return null;
  }

  const getLastMessageForGroupChat = async(groupId) => {
    let lastMessage = "";
    let lastMessageTime = 0;
    const messagesRef = ref(db, `chats/${groupId}/messages`);
    const lastMessageQuery = query(
      messagesRef,
      orderByChild("timeStamp"),
      limitToLast(1)
    );

    const callback = async (snapshot) => {
      if (snapshot.exists()) {
        const lastMessageKey = Object.keys(snapshot.val())[0];
        const type = snapshot.val()[lastMessageKey].type;
        lastMessage = type === "text" ? snapshot.val()[lastMessageKey].message : type;
        lastMessageTime = snapshot.val()[lastMessageKey].timeStamp;
        const isCurrentUserExist =
          currentUser.uid in snapshot.val()[lastMessageKey].seenBy;
        setLastMessage(
          isCurrentUserExist ? lastMessage : "You no longer participant"
        );

        const formattedTime = await new Date(
          parseInt(lastMessageTime)
        ).toLocaleString("en-US", {
          hour: "numeric",
          minute: "numeric",
          hour12: true,
        });
        setLastMessageTime(formattedTime);
      } else {
        console.log("No messages found.");
      }
    };

    onValue(lastMessageQuery, callback);
    lastMessage = await fetchGrouplastMessage(group.communitygroup.id);
    const relativeTime = formatRelativeTime(lastMessage?.time);
    
    setLastMessage(decodeHtmlRecursively(lastMessage?.message));
    setLastMessageTime(relativeTime);
    return;
  };

  async function getUserDataById(userId) {
    if (!userId) {
      console.warn("No userId provided to getUserDataById");
      return;
    }

    try {
      const userRef = ref(db, `users/${userId}`);
      const snapshot = await get(userRef);

      if (!snapshot.exists()) {
        console.warn(`User UID not found in Firebase: ${userId}`);
        return;
      }
      
      if (snapshot.exists()) {
        let userIntence = snapshot.val();

        if (userDetail.onboardingState == "community") {
          userIntence = {
            "name" : userIntence.displayName,
            "email" : userIntence.email,
            "onlineStatus" : userIntence.onlineStatus,
            "photoURL" : userIntence.photoURL,
            "uid" : userIntence.uid
          };
          setUserInfo(userIntence);
        } else {
          setUserInfo(snapshot.val());
        }
        
        return;
      } else {
        throw new Error("User data not found.");
      }
    } catch (error) {
      console.error("Error getting user data:", error);
      throw error;
    }
  }
  
  const getGroupDataById = async (groupId) => {
    try {
      const authToken = localStorage.getItem("token");
  
      // Firebase API call
      const userRef = ref(db, `groups/${groupId}`);
      const snapshot = await get(userRef);
  
      if (!snapshot.exists()) {
        throw new Error("Firebase group data not found.");
      }
  
      const firebaseResponse = snapshot.val();
      const response = await axios.get(`${URL}/subscribe-group`, {
        headers: {
          Authorization: authToken,
        },
      });
  
      if (response.status !== 200) {
        throw new Error(`Axios API failed with status: ${response.status}`);
      }
  
      const externalGroups = response.data.data;
  
      // Find the corresponding external group for the current Firebase group
      const correspondingExternalGroup = externalGroups.find(
        (group) => group.communitygroup.channelName === firebaseResponse.groupName
      );
  
      if (!correspondingExternalGroup) {
        console.warn(
          `No corresponding external group found for Firebase group: ${firebaseResponse.groupName}`
        );
      }
  
      // Merge the data
      const mergedData = {
        ...firebaseResponse,
        externalGroup: correspondingExternalGroup || null, // Include corresponding external group or null if not found
      };

      dispatch({
        type: SET_OPENED_GROUP_MEMBERS,
        payload: [mergedData],  // Make sure the payload is passed as an array
      });
      setGroupInfo(mergedData);
    } catch (error) {
      console.error("Error fetching group data:", error);
    }
  };
  
  useEffect(() => {
    const unseenCounts = {}; // Store individual unseen counts

    const handleUnseenMessages = async (snapshot, combinedId) => {
      let unseenMsgCount = 0;
    
      snapshot.forEach((childSnapshot) => {
        const message = childSnapshot.val();
        if (message.senderId !== currentUser.uid && !message.isSeen) {
          unseenMsgCount++;
        }
      });
    
      unseenCounts[combinedId] = unseenMsgCount;
    
      // Calculate the total unseen message count from Firebase
      const totalFirebaseCount = Object.values(unseenCounts).reduce((acc, count) => acc + count, 0);
      setFirebaseUnseenCount(totalFirebaseCount); // Update the Firebase unseen count
    };

    const handleIndividualChats = (snapshot) => {
      snapshot.forEach((childSnapshot) => {
        const combinedId = childSnapshot.key;
        const messagesRef = ref(db, `chats/${combinedId}/messages`);
        const unseenMessagesQuery = query(
          messagesRef,
          orderByChild("isSeen"),
          equalTo(false)
        );

        onValue(unseenMessagesQuery, (messagesSnapshot) => {
          handleUnseenMessages(messagesSnapshot, `individual_${combinedId}`);
        });
      });
    };

    // Fetch all user chats
    const userChatsRef = ref(db, `userChats/${currentUser.uid}`);
    const userChatsQuery = query(userChatsRef);

    onValue(userChatsQuery, (snapshot) => {
      handleIndividualChats(snapshot);
      // handleGroupChats(snapshot);
    });
    
    item?.userInfo?.uid && getUserDataById(item?.userInfo?.uid);
    item?.userInfo?.uid && getLastMessage(item?.userInfo?.uid);
    item?.userInfo?.uid && getUnseenMessageCount(item?.userInfo?.uid);

    item?.groupId && getGroupDataById(item?.groupId);
    item?.groupId && getLastMessageForGroupChat(item?.groupId);
    // item?.groupId && getUnseenMessageCountForGroupChat(item?.groupId);

    // Your existing code...
  }, [item, currentUser.uid]);

  useEffect(() => {
  getTotalMessageCount(item?.userInfo?.uid);
}, [item?.userInfo?.uid]);

const handleDeleteGroupChat = async (groupId) => {
  const result = await Swal.fire({
    title: 'Are you sure?',
    text: "You won't be able to revert this!",
    icon: 'warning',
    showCancelButton: true,
    confirmButtonColor: '#3085d6',
    cancelButtonColor: '#d33',
    confirmButtonText: 'Yes, delete it!'
  });

  if (result.isConfirmed) {
    try {
      const chatRef = ref(db, `chats/${groupId}`);
      await remove(chatRef);
      // Optionally, remove the group information as well
      const groupRef = ref(db, `groups/${groupId}`);
      await remove(groupRef);

      dispatch(getCurrentUserChatsAction(currentUser?.uid));
      setOpenPage("")

      Swal.fire(
        'Deleted!',
        'The group chat has been deleted.',
        'success'
      );
    } catch (error) {
      console.error("Error deleting group chat:", error);
      Swal.fire(
        'Error!',
        'There was an error deleting the group chat.',
        'error'
      );
    }
  }
};

const handleDeletePersonalChat = async (userInfo) => {
  const combinedId = currentUser.uid > userInfo.uid 
    ? currentUser.uid + userInfo.uid 
    : userInfo.uid + currentUser.uid;

  const result = await Swal.fire({
    title: 'Are you sure?',
    text: "You won't be able to revert this!",
    icon: 'warning',
    showCancelButton: true,
    confirmButtonColor: '#3085d6',
    cancelButtonColor: '#d33',
    confirmButtonText: 'Yes, delete it!'
  });
  

  if (result.isConfirmed) {
    try {
      // Remove the chat messages
      const chatRef = ref(db, `chats/${combinedId}`);
      await remove(chatRef);

      // Optionally, remove the chat reference for the user
      const userChatRef = ref(db, `users/${currentUser.uid}/chats/${combinedId}`);
      await remove(userChatRef);

      // Optionally, remove the chat reference for the other user
      const otherUserChatRef = ref(db, `users/${userInfo.uid}/chats/${combinedId}`);
      await remove(otherUserChatRef);

      // Refresh current user chats
      dispatch(getCurrentUserChatsAction(currentUser?.uid));
      setOpenPage("");

      Swal.fire(
        'Deleted!',
        'The personal chat has been deleted.',
        'success'
      );
    } catch (error) {
      console.error("Error deleting personal chat:", error);
      Swal.fire(
        'Error!',
        'There was an error deleting the personal chat.',
        'error'
      );
    }
  }
};

const [isOpen, setIsOpen] = useState(false);

const handleSettings = () => {
  setIsOpen((prev) => !prev); // Toggle the state on button click
};

  useEffect(() => {
    if (!currentUser?.uid) return;
    const pubnub = new PubNub({
      publishKey: process.env.REACT_APP_CHAT_PUBLISH_KEY,
      subscribeKey: process.env.REACT_APP_CHAT_SUBSCRIBE_KEY,
      uuid: currentUser?.uid,
    });
    const userId = pubnub.getUserId();
    const generateId = () => Math.random().toString(36).substring(2, 9);
    const loggedInUserId = localStorage.getItem("userId");
    let isSubscribed = true;

    const manageSubscriptions = async () => {
      try { 
        const channelName = item?.groupInfo?.channelName;

        // if (channelName?.length === 0) return;
        if (!channelName) return;
        
        // pubnub.subscribe({ channelName });
        pubnub.subscribe({ channels: [channelName] });

        const fetchUnseenCount = async () => {
          try {
            let newUnseenCount = 0;
              const [actionsResponse, messagesResponse] = await Promise.all([
                pubnub.getMessageActions({ channel: channelName }),
                pubnub.fetchMessages({ channels: [channelName], count: 100 }),
              ]);

              const allMessages = messagesResponse.channels[channelName] || [];
              const seenActionsMap = new Map();

              actionsResponse.data.forEach((action) => {
                if (action.type === "seen") {
                  const key = action.messageTimetoken;
                  const existing = seenActionsMap.get(key) || [];
                  existing.push(action.value);
                  seenActionsMap.set(key, existing);
                }
              });

              allMessages.forEach((msg) => {
                const seenByList = seenActionsMap.get(msg.timetoken) || [];

                if (!seenByList.includes(loggedInUserId) && msg.message.userId?.toString() !== loggedInUserId) {
                  newUnseenCount++;
                }
              });
            if (isSubscribed) {
              setUnSeenMessageCount(newUnseenCount);
            }
          } catch (error) {
            console.error("Error updating unseen count:", error);
          }
        };

        const debouncedUpdate = _.debounce(fetchUnseenCount, 300, {
          leading: true,
          trailing: true,
        });

        const messageListener = {
          message: debouncedUpdate,
          messageAction: debouncedUpdate,
        };

        pubnub.addListener(messageListener);
        fetchUnseenCount(); // Initial fetch

        return () => {
          isSubscribed = false;
          debouncedUpdate.cancel();
          pubnub.removeListener(messageListener);
          // pubnub.unsubscribe({ channelName });
          pubnub.unsubscribe({ channels: [channelName] });
        };
      } catch (error) {
        console.error("Error managing subscriptions:", error);
      }
    };

    const cleanup = manageSubscriptions();

    return () => {
      cleanup.then((fn) => fn && fn()); // Ensuring cleanup runs properly
    };
  }, [currentUser?.uid, item?.groupId]);
// --------------------------------------------------------------------
  
// useEffect(() => {
//     if (!currentUser?.uid) return;
//     const pubnub = new PubNub({
//       publishKey: process.env.REACT_APP_CHAT_PUBLISH_KEY,
//       subscribeKey: process.env.REACT_APP_CHAT_SUBSCRIBE_KEY,
//       uuid: currentUser.uid,
//     });
//     let isSubscribed = true;
//     const loggedInUserId = localStorage.getItem("userId");
//     const manageSubscriptions = async () => {
//       try {
//         const channelName = item?.groupInfo?.channelName;
//         if (!channelName) return;

//         pubnub.subscribe({ channels: [channelName] });

//         const fetchUnseenCount = async () => {
//           try {
//             let newUnseenCount = 0;
//             // approximate current PubNub timetoken
//             const nowTt = (Date.now() * 10000).toString();

//             const [actionsResponse, messagesResponse] = await Promise.all([
//               pubnub.getMessageActions({
//                 channel: channelName,
//                 start: nowTt,
//                 end: "0",
//                 limit: 100,
//               }),
//               pubnub.fetchMessages({ channels: [channelName], count: 100 }),
//             ]);

//             // Normalize actions array for different SDK versions
//             const actionsArray = actionsResponse?.data?.actions ?? actionsResponse?.actions ?? [];
//             const allMessages = messagesResponse.channels?.[channelName] ?? [];
//             const seenActionsMap = new Map();

//             actionsArray.forEach((action) => {
//               if (action.type === "seen") {
//                 const key = action.messageTimetoken;
//                 const existing = seenActionsMap.get(key) || [];
//                 existing.push(action.value);
//                 seenActionsMap.set(key, existing);
//               }
//             });

//             allMessages.forEach((msg) => {
//               const seenByList = seenActionsMap.get(msg.timetoken) || [];
//               if (!seenByList.includes(loggedInUserId) && msg.message.userId?.toString() !== loggedInUserId) {
//                 newUnseenCount++;
//               }
//             });

//             if (isSubscribed) {
//               setUnSeenMessageCount(newUnseenCount);
//               setPubnubUnseenCount(newUnseenCount);
//             }
//           } catch (error) {
//             console.error("Error updating unseen count:", error);
//           }
//         };

//         const debouncedUpdate = _.debounce(fetchUnseenCount, 300, { leading: true, trailing: true });
//         pubnub.addListener({ message: debouncedUpdate, messageAction: debouncedUpdate });
//         fetchUnseenCount();

//         return () => {
//           isSubscribed = false;
//           debouncedUpdate.cancel();
//           pubnub.removeListener({ message: debouncedUpdate, messageAction: debouncedUpdate });
//           pubnub.unsubscribe({ channels: [channelName] });
//         };
//       } catch (error) {
//         console.error("Error managing subscriptions:", error);
//       }
//     };

//     const cleanup = manageSubscriptions();
//     return () => { cleanup.then(fn => fn && fn()); };
//   }, [currentUser?.uid, item?.groupId]);

  return (
    <Box
    sx={{
      marginLeft: "10px",
      "@media (max-width: 430px)": {
        marginLeft: "unset",
      },
    }}>
      {isUnRead
        ? 
        unSeenMessageCount !== 0 &&
          (item.chatType === "personal" ? (
            (userDetail.onboardingState == "community") ? (
              <Box
              onClick={() => handleSelect(userInfo)}
              sx={
                item.isMsgReqAccepted
                  ? {
                      cursor: "pointer",
                      padding: "8px",
                      paddingTop: "8px",
                      display: "flex",
                      justifyContent: "space-between",
                      alignItems: "center",
                    }
                  : {
                      backgroundColor: "#FFEADC",
                      cursor: "pointer",
                      padding: "8px",
                      paddingTop: "10px",
                      display: "flex",
                      justifyContent: "space-between",
                      alignItems: "center",
                      marginTop:"5px",
                      borderRadius: "10px"
                    }
              }
            >
              <Box
                sx={{ display: "flex", alignItems: "center", padding: "6px" }}
              >
                
                <Avatar alt={userInfo.name}
                     src={`${URL}/static/public/userimages/${userInfo?.photoURL}`} />
                <Box>
                  <Typography
                    className="userName"
                    fontWeight="fontWeightBold"
                    sx={{ paddingLeft: "1.2rem" }}
                  >
                    {userInfo?.name}
                  </Typography>
                  <Typography
                    sx={{
                      paddingLeft: "1.2rem",
                      fontSize: "14px",
                      paddingTop: "4px",
                    }}
                  >
                    {lastMessage?.length > 40
                      ? `${lastMessage?.slice(0, 40)}...`
                      : lastMessage}
                  </Typography>
                  {!item?.isMsgReqAccepted && (
                    <Typography
                      className="acceptReqPrompt"
                      sx={{ paddingLeft: "1.2rem" }}
                    >
                      Message Request : Tap to view & then you can accept /
                      decline
                    </Typography>
                  )}
                </Box>
              </Box>
              <Box sx={{ textAlign: "right" }}>
                <Box
                  className="timeStamp"
                  style={
                    unSeenMessageCount !== 0 ||
                    lastMessage === "photo" ||
                    lastMessage === "document"
                      ? { marginTop: "2.5px" }
                      : { marginTop: "-19px", color: "var(--grey-2, #707070)" }
                  }
                >
                  {lastMessageTime !== 0 && lastMessageTime}
                </Box>
                <Box
                  sx={{
                    textAlign: "right",
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "flex-end",
                    paddingTop: "4px",
                  }}
                >
                  {(lastMessage === "photo" || lastMessage === "document") && (
                    <InsertLinkOutlinedIcon className="chat-icon" />
                  )}
                  {item.showBellIcon && (
                    <NotificationsActiveOutlinedIcon className="chat-icon" />
                  )}
                  {unSeenMessageCount !== 0 && (
                    <Box
                      className="badgeIcon chat-icon"
                      sx={{
                        fontSize: "11px",
                        display: "flex",
                        justifyContent: "center",
                        alignItems: "center",
                      }}
                    >
                      {unSeenMessageCount}
                    </Box>
                  )}
                  {
                    roleID == 1 &&
                  
                  <DeleteOutlineIcon
                     onClick={(e) => {
                       e.stopPropagation(); // Prevents triggering the onClick of the parent Box
                       handleDeletePersonalChat(userInfo);
                     }}
                     sx={{ cursor: "pointer", color: "red", marginLeft: 1,fontSize:"15px" }}
                   />
                   }
                </Box>
              </Box>
            </Box>
            ) : (
              <Box
              onClick={() => handleSelect(userInfo)}
              sx={
                item.isMsgReqAccepted
                  ? {
                      cursor: "pointer",
                      padding: "8px",
                      paddingTop: "8px",
                      display: "flex",
                      justifyContent: "space-between",
                      alignItems: "center",
                    }
                  : {
                      backgroundColor: "#FFEADC",
                      cursor: "pointer",
                      padding: "8px",
                      paddingTop: "10px",
                      display: "flex",
                      justifyContent: "space-between",
                      alignItems: "center",
                      marginTop:"5px",
                      borderRadius: "10px"
                    }
              }
            >
              <Box
                sx={{ display: "flex", alignItems: "center", padding: "6px" }}
              >
                
                <Avatar alt={userInfo.displayName}
                     src={`${URL}/static/public/userimages/${userInfo?.photoURL}`} />
                <Box>
                  <Typography
                    className="userName"
                    fontWeight="fontWeightBold"
                    sx={{ paddingLeft: "1.2rem" }}
                  >
                    {userInfo?.displayName}
                  </Typography>
                  <Typography
                    sx={{
                      paddingLeft: "1.2rem",
                      fontSize: "14px",
                      paddingTop: "4px",
                    }}
                  >
                    {lastMessage?.length > 40
                      ? `${lastMessage?.slice(0, 40)}...`
                      : lastMessage}
                  </Typography>
                  {!item?.isMsgReqAccepted && (
                    <Typography
                      className="acceptReqPrompt"
                      sx={{ paddingLeft: "1.2rem" }}
                    >
                      Message Request : Tap to view & then you can accept /
                      decline
                    </Typography>
                  )}
                </Box>
              </Box>
              <Box sx={{ textAlign: "right" }}>
                <Box
                  className="timeStamp"
                  style={
                    unSeenMessageCount !== 0 ||
                    lastMessage === "photo" ||
                    lastMessage === "document"
                      ? { marginTop: "2.5px" }
                      : { marginTop: "-19px", color: "var(--grey-2, #707070)" }
                  }
                >
                  {lastMessageTime !== 0 && lastMessageTime}
                </Box>
                <Box
                  sx={{
                    textAlign: "right",
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "flex-end",
                    paddingTop: "4px",
                  }}
                >
                  {(lastMessage === "photo" || lastMessage === "document") && (
                    <InsertLinkOutlinedIcon className="chat-icon" />
                  )}
                  {item.showBellIcon && (
                    <NotificationsActiveOutlinedIcon className="chat-icon" />
                  )}
                  {unSeenMessageCount !== 0 && (
                    <Box
                      className="badgeIcon chat-icon"
                      sx={{
                        fontSize: "11px",
                        display: "flex",
                        justifyContent: "center",
                        alignItems: "center",
                      }}
                    >
                      {unSeenMessageCount}
                    </Box>
                  )}
                  {
                    roleID == 1 &&
                  
                  <DeleteOutlineIcon
                     onClick={(e) => {
                       e.stopPropagation(); // Prevents triggering the onClick of the parent Box
                       handleDeletePersonalChat(userInfo);
                     }}
                     sx={{ cursor: "pointer", color: "red", marginLeft: 1,fontSize:"15px" }}
                   />
                   }
                </Box>
              </Box>
            </Box>
            ) 
          ) : (
            <Box
              onClick={() => handleGotoGroupChat(item?.groupId)}
              sx={
                item.isMsgReqAccepted
                  ? {
                      cursor: "pointer",
                      padding: "8px",
                      paddingTop: "15px",
                      display: "flex",
                      justifyContent: "space-between",
                      alignItems: "center",
                    }
                  : {
                      backgroundColor: "#FDFBE7",
                      cursor: "pointer",
                      padding: "8px",
                      paddingTop: "10px",
                      display: "flex",
                      justifyContent: "space-between",
                      alignItems: "center",
                    }
              }
            >
              <Box
                sx={{ display: "flex", alignItems: "center", padding: "6px" }}
              >
                <Box position="relative" display="inline-block">
                  <Avatar alt="Remy Sharp" src={groupInfo?.groupProfileURL} />
                  <Box
                    position="absolute"
                    bottom={0}
                    right={0}
                    bgcolor="transparent" // Optional: To make the background of the icon white
                    borderRadius="50%" 
                    p={0.5} // Optional: Padding around the icon
                  >
                    <MdOutlineGroup fontSize="16" style={{backgroundColor:"orange",borderRadius:"9999px"}} />
                  </Box>
                </Box>
                <Box>
                  <Typography
                    className="userName"
                    fontWeight="fontWeightBold"
                    sx={{ paddingLeft: "1.2rem" }}
                  >
                    {groupInfo?.groupName}
                  </Typography>
                  <Typography sx={{ paddingLeft: "1.2rem", fontSize: "14px" }}>
                    {lastMessage?.length > 40
                      ? `${lastMessage?.slice(0, 40)}...`
                      : lastMessage}
                  </Typography>
                </Box>
              </Box>
              <Box sx={{ textAlign: "right" }}>
                <Box
                  className="timeStamp"
                  style={
                    unSeenMessageCount !== 0
                      ? { marginTop: "2.5px" }
                      : { marginTop: "-18px", color: "var(--grey-2, #707070)" }
                  }
                >
                  {lastMessageTime !== 0 && lastMessageTime}
                </Box>
                <Box
                  sx={{
                    textAlign: "right",
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "flex-end",
                  }}
                >
                  {item.showMediaIcon && (
                    <InsertLinkOutlinedIcon className="chat-icon" />
                  )}
                  {item.showBellIcon && (
                    <NotificationsActiveOutlinedIcon className="chat-icon" />
                  )}
                  {unSeenMessageCount > 0 && (
                    <Box
                      className="badgeIcon chat-icon"
                      sx={{
                        fontSize: "11px",
                        display: "flex",
                        justifyContent: "center",
                        alignItems: "center",
                      }}
                    >
                      {unSeenMessageCount}
                    </Box>
                  )}
                  {
                    roleID == 1 &&
                  <DeleteOutlineIcon
                     onClick={(e) => {
                       e.stopPropagation(); // Prevents triggering the onClick of the parent Box
                       handleDeleteGroupChat(item?.groupId);
                     }}
                    sx={{ cursor: "pointer", color: "red", marginLeft: 1,fontSize:"15px" }}
                  />
                  }
                </Box>
              </Box>
            </Box>
          ))
        : unSeenMessageCount === 0  &&
          ((item.chatType === "personal") ? (
            (totalMessageCount !== 0) ? (
            (userDetail.onboardingState == "community") ? (
              <Box
              onClick={() => handleSelect(userInfo)}
              sx={
                (openedUser?.uid === item?.userInfo?.uid && openPage === "individual")
                  ? {
                      backgroundColor: "#E67E22",
                      borderRadius:"10px",
                      color: "white",
                      cursor: "pointer",
                      padding: "8px",
                      paddingTop: "10px",
                      display: "flex",
                      justifyContent: "space-between",
                      alignItems: "center",
                    }
                  : item.isMsgReqAccepted
                  ? {
                      cursor: "pointer",
                      padding: "8px",
                      paddingTop: "8px",
                      display: "flex",
                      justifyContent: "space-between",
                      alignItems: "center",
                      "@media (max-width: 430px)": {
                        backgroundColor: "#F5F5F5",
                        marginBottom: "10px",
                        marginLeft: "10px",
                        marginRight: "10px",
                        borderRadius: "6px"
                      }
                    }
                  : {
                      backgroundColor: "#FFEADC",
                      cursor: "pointer",
                      padding: "8px",
                      paddingTop: "10px",
                      display: "flex",
                      justifyContent: "space-between",
                      alignItems: "center",
                      borderRadius: "10px",
                      margin: "5px 0"
                    }
              }
              >
              <Box
                sx={{ display: "flex", alignItems: "center", padding: "6px" }}
              >
                <Avatar alt={userInfo.name}
                     src={`${URL}/static/public/userimages/${userInfo?.photoURL}`} />
                <Box>
                  <Typography
                    className="userName"
                    fontWeight="fontWeightBold"
                    sx={
                      (openedUser?.uid === item?.userInfo?.uid && openPage === "individual") ?
                      {
                        paddingLeft: "1.2rem",color: "white !important"
                      }
                      :
                      {paddingLeft: "1.2rem",color:"gray !important"} }
                  >
                    {userInfo?.name}
                  </Typography>
                  <Typography
                    sx={
                      (openedUser?.uid === item?.userInfo?.uid && openPage === "individual") ?
                      {
                        paddingLeft: "1.2rem",
                      fontSize: "14px",
                      paddingTop: "4px",
                      color:"white !important"
                      }
                      :
                      {
                      paddingLeft: "1.2rem",
                      fontSize: "14px",
                      paddingTop: "4px",
                      color:"gray !important"
                    }}
                  >
                    {lastMessage?.length > 40
                      ? `${lastMessage?.slice(0, 40)}...`
                      : lastMessage}
                  </Typography>
                  {!item?.isMsgReqAccepted && (
                    <Typography
                      className="acceptReqPrompt"
                      sx={
                        (openedUser?.uid === item?.userInfo?.uid && openPage === "individual") ?
                        { paddingLeft: "1.2rem",color: "white !important" }
                        :
                        { paddingLeft: "1.2rem" }}
                    >
                      Message Request : Tap to view & then you can accept /
                      decline
                    </Typography>
                  )}
                </Box>
              </Box>
              <Box sx={{ textAlign: "right" }}>
                <Box
                  className="timeStamp"
                  style={
                    openedUser?.uid === item?.userInfo?.uid && openPage === "individual"
                      ? { color: "white" ,marginTop: "-19px",}
                      : unSeenMessageCount !== 0 ||
                        lastMessage === "photo" ||
                        lastMessage === "document"
                      ? { marginTop: "2.5px" , color: "var(--grey-2, #707070)"}
                      : { marginTop: "-19px", color: "var(--grey-2, #707070)" }
                  }
                  
                >
                  {lastMessageTime !== 0 && lastMessageTime}
                </Box>
                <Box
                  sx={{
                    textAlign: "right",
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "flex-end",
                    paddingTop: "4px",
                  }}
                >
                  {(lastMessage === "photo" || lastMessage === "document") && (
                    <InsertLinkOutlinedIcon className="chat-icon" />
                  )}
                  {item.showBellIcon && (
                    <NotificationsActiveOutlinedIcon className="chat-icon" />
                  )}
                  {unSeenMessageCount !== 0 && (
                    <Box
                      className="badgeIcon chat-icon"
                      sx={{
                        fontSize: "11px",
                        display: "flex",
                        justifyContent: "center",
                        alignItems: "center",
                      }}
                    >
                      {unSeenMessageCount}
                    </Box>
                  )}
                  {
                    roleID == 1 &&
                  <DeleteOutlineIcon
                    onClick={(e) => {
                      e.stopPropagation(); // Prevents triggering the onClick of the parent Box
                      handleDeletePersonalChat(userInfo);
                    }}
                    sx={{ cursor: "pointer", color: "red", marginLeft: 1,fontSize:"15px" }}
                  />
                  }
                </Box>
              </Box>
            </Box>
            ) : (
              <Box
              onClick={() => handleSelect(userInfo)}
              sx={
                (openedUser?.uid === item?.userInfo?.uid && openPage === "individual")
                  ? {
                      backgroundColor: "#E67E22",
                      borderRadius:"10px",
                      color: "white",
                      cursor: "pointer",
                      padding: "8px",
                      paddingTop: "10px",
                      display: "flex",
                      justifyContent: "space-between",
                      alignItems: "center",
                    }
                  : item.isMsgReqAccepted
                  ? {
                      cursor: "pointer",
                      padding: "8px",
                      paddingTop: "8px",
                      display: "flex",
                      justifyContent: "space-between",
                      alignItems: "center",
                      "@media (max-width: 430px)": {
                        backgroundColor: "#F5F5F5",
                        marginBottom: "10px",
                        marginLeft: "10px",
                        marginRight: "10px",
                        borderRadius: "6px"
                      }
                    }
                  : {
                      backgroundColor: "#FFEADC",
                      cursor: "pointer",
                      padding: "8px",
                      paddingTop: "10px",
                      display: "flex",
                      justifyContent: "space-between",
                      alignItems: "center",
                      borderRadius: "10px",
                      margin: "5px 0"
                    }
              }
              >
              <Box
                sx={{ display: "flex", alignItems: "center", padding: "6px" }}
              >
                <Avatar alt={userInfo.displayName}
                     src={`${URL}/static/public/userimages/${userInfo?.photoURL}`} />
                <Box>
                  <Typography
                    className="userName"
                    fontWeight="fontWeightBold"
                    sx={
                      (openedUser?.uid === item?.userInfo?.uid && openPage === "individual") ?
                      {
                        paddingLeft: "1.2rem",color: "white !important"
                      }
                      :
                      {paddingLeft: "1.2rem",color:"gray !important"} }
                  >
                    {userInfo?.displayName}
                  </Typography>
                  <Typography
                    sx={
                      (openedUser?.uid === item?.userInfo?.uid && openPage === "individual") ?
                      {
                        paddingLeft: "1.2rem",
                      fontSize: "14px",
                      paddingTop: "4px",
                      color:"white !important"
                      }
                      :
                      {
                      paddingLeft: "1.2rem",
                      fontSize: "14px",
                      paddingTop: "4px",
                      color:"gray !important"
                    }}
                  >
                    {lastMessage?.length > 40
                      ? `${lastMessage?.slice(0, 40)}...`
                      : lastMessage}
                  </Typography>
                  {!item?.isMsgReqAccepted && (
                    <Typography
                      className="acceptReqPrompt"
                      sx={
                        (openedUser?.uid === item?.userInfo?.uid && openPage === "individual") ?
                        { paddingLeft: "1.2rem",color: "white !important" }
                        :
                        { paddingLeft: "1.2rem" }}
                    >
                      Message Request : Tap to view & then you can accept /
                      decline
                    </Typography>
                  )}
                </Box>
              </Box>
              <Box sx={{ textAlign: "right" }}>
                <Box
                  className="timeStamp"
                  style={
                    openedUser?.uid === item?.userInfo?.uid && openPage === "individual"
                      ? { color: "white" ,marginTop: "-19px",}
                      : unSeenMessageCount !== 0 ||
                        lastMessage === "photo" ||
                        lastMessage === "document"
                      ? { marginTop: "2.5px" , color: "var(--grey-2, #707070)"}
                      : { marginTop: "-19px", color: "var(--grey-2, #707070)" }
                  }
                  
                >
                  {lastMessageTime !== 0 && lastMessageTime}
                </Box>
                <Box
                  sx={{
                    textAlign: "right",
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "flex-end",
                    paddingTop: "4px",
                  }}
                >
                  {(lastMessage === "photo" || lastMessage === "document") && (
                    <InsertLinkOutlinedIcon className="chat-icon" />
                  )}
                  {item.showBellIcon && (
                    <NotificationsActiveOutlinedIcon className="chat-icon" />
                  )}
                  {unSeenMessageCount !== 0 && (
                    <Box
                      className="badgeIcon chat-icon"
                      sx={{
                        fontSize: "11px",
                        display: "flex",
                        justifyContent: "center",
                        alignItems: "center",
                      }}
                    >
                      {unSeenMessageCount}
                    </Box>
                  )}
                  {
                    roleID == 1 &&
                  <DeleteOutlineIcon
                    onClick={(e) => {
                      e.stopPropagation(); // Prevents triggering the onClick of the parent Box
                      handleDeletePersonalChat(userInfo);
                    }}
                    sx={{ cursor: "pointer", color: "red", marginLeft: 1,fontSize:"15px" }}
                  />
                  }
                </Box>
              </Box>
            </Box>
            )
            )
            : null
          ) : (
            <Box
              onClick={() => handleGotoGroupChat(item?.groupId)}
              sx={
                (openedGroupInfo?.groupId === item?.groupId && openPage === "group-chat")
                ? {
                    backgroundColor: "#E67E22",
                    borderRadius:"10px",
                    cursor: "pointer",
                      padding: "8px",
                      paddingTop: "10px",
                      display: "flex",
                      justifyContent: "space-between",
                      alignItems: "center",
                    }
                    :
                item.isMsgReqAccepted
                  ? {
                      cursor: "pointer",
                      padding: "8px",
                      paddingTop: "15px",
                      display: "flex",
                      justifyContent: "space-between",
                      alignItems: "center",
                      "@media (max-width: 430px)": {
                        backgroundColor: "#F5F5F5",
                        marginBottom: "10px",
                        marginLeft: "10px",
                        marginRight: "10px",
                        borderRadius: "6px"
                      }
                    }
                  : {
                      backgroundColor: "#FDFBE7",
                      cursor: "pointer",
                      padding: "8px",
                      paddingTop: "10px",
                      display: "flex",
                      justifyContent: "space-between",
                      alignItems: "center",
                    }
              }
            >
              <Box
                sx={{ display: "flex", alignItems: "center", padding: "6px" }}
              >
                <Box position="relative" display="inline-block">
                  <Avatar alt="Remy Sharp" src={groupInfo?.groupProfileURL} />
                  <Box
                    position="absolute"
                    bottom={0}
                    right={0}
                    bgcolor="transparent"
                    borderRadius="50%" 
                    p={0.5}
                  >
                    <MdOutlineGroup fontSize="16" style={{backgroundColor:"orange",borderRadius:"9999px"}} />
                  </Box>
                </Box>
                <Box>
                  <Typography
                    className="userName"
                    fontWeight="fontWeightBold"
                    sx={{
                      paddingLeft: "1.2rem",
                      color: openedGroupInfo?.groupId === item?.groupId && openPage === "group-chat" 
                        ? "white !important" 
                        : "gray !important",
                      maxWidth: "100%", // Prevents overflow
                      whiteSpace: "normal", // Allows wrapping
                      wordBreak: "break-word", // Ensures text breaks within words
                      overflow: "hidden", // Prevents content from overflowing
                      textOverflow: "ellipsis" // Optional: Truncates long text with "..."
                    }}
                  >
                    {groupInfo?.groupName}
                  </Typography>
                  <Typography 
                  sx={
                    (openedGroupInfo?.groupId === item?.groupId && openPage === "group-chat")
                      ?
                      { paddingLeft: "1.2rem", fontSize: "14px",color:"white !important" }
                      :
                    { paddingLeft: "1.2rem", fontSize: "14px",color:"gray !important" }}>
                    {(lastMessage && lastMessage?.length > 0)
                      ? `${lastMessage?.slice(0, 40)}...`
                      : ".."}
                  </Typography>
                </Box>
              </Box>
              <Box sx={{ textAlign: "right" }}>
                <Box
                  className="timeStamp"
                  style={
                    (openedGroupInfo?.groupId === item?.groupId && openPage === "group-chat")
                      ?
                       { marginTop: "-18px", color: "white" }
                      :
                    unSeenMessageCount !== 0
                      ? { marginTop: "2.5px" }
                      : { marginTop: "-18px", color: "var(--grey-2, #707070)" }
                  }
                >
                  {(lastMessageTime === 0 || isNaN(lastMessageTime)) ? "..." : lastMessageTime}
                </Box>
                <Box
                  sx={{
                    textAlign: "right",
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "flex-end",
                  }}
                >
                  {item.showMediaIcon && (
                    <InsertLinkOutlinedIcon className="chat-icon" />
                  )}
                  {item.showBellIcon && (
                    <NotificationsActiveOutlinedIcon className="chat-icon" />
                  )}
                  {unSeenMessageCount > 0 && (
                    <Box
                      className="badgeIcon chat-icon"
                      sx={{
                        fontSize: "11px",
                        display: "flex",
                        justifyContent: "center",
                        alignItems: "center",
                      }}
                    >
                      {unSeenMessageCount}
                    </Box>
                  )}
                </Box>
              </Box>
            </Box>
          ))}
    </Box>
  );
};

export default memo(UserChats);
