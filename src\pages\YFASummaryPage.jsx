import React from 'react'
import { Select } from '@mantine/core'
import Header from '../components/Header'

const YFASummaryReport = () => {
  return (
    <>
      <Header />
      {/* Topbar */}
      <div className='p-9'>
        <div className='flex items-start gap-x-2'>
          <h1 className='font-medium text-xl'>YFA Summary</h1>
        </div>
        <br />
        <div className='flex flex-wrap gap-4 md:gap-8 items-end'>
          <Select
            label='Program'
            data={[
              { value: 'IPC', label: 'IPC' },
              { value: '12 Weeks', label: '12 Weeks' },
              { value: '24 Weeks', label: '24 Weeks' },
              { value: '100 Days challenge', label: '100 Days challenge' }
            ]}
          />
          <button className='py-1.5 px-5 bg-orange-500 text-slate-50 rounded text-base'>
            Submit
          </button>
        </div>
        {/* Table */}
        <div className='w-full mb-6'>
          <div className='my-6'>
            <div className='flex flex-col'>
              <div className='overflow-x-auto shadow-md'>
                <div className='inline-block min-w-full align-middle'>
                  <div className='overflow-hidden '>
                    <table className='min-w-full divide-y divide-gray-200 table-fixed dark:divide-gray-700'>
                      <thead className='bg-gray-100 '>
                        <tr>
                          <th
                            scope='col'
                            className='py-3 px-6 text-xs font-medium tracking-wider text-left text-gray-700 uppercase dark:text-gray-400'
                          >
                            Day
                          </th>
                          <th
                            scope='col'
                            className='py-3 px-6 text-xs font-medium tracking-wider text-left text-gray-700 uppercase dark:text-gray-400'
                          >
                            Total no. of participants
                          </th>
                          <th
                            scope='col'
                            className='py-3 px-6 text-xs font-medium tracking-wider text-left text-gray-700 uppercase dark:text-gray-400'
                          >
                            Workouts not completed
                          </th>
                          <th
                            scope='col'
                            className='py-3 px-6 text-xs font-medium tracking-wider text-left text-gray-700 uppercase dark:text-gray-400'
                          >
                            Workouts completed
                          </th>
                          <th
                            scope='col'
                            className='py-3 px-6 text-xs font-medium tracking-wider text-left text-gray-700 uppercase dark:text-gray-400'
                          >
                            Workout completed but no smileys updated
                          </th>
                          <th
                            scope='col'
                            className='py-3 px-6 text-xs font-medium tracking-wider text-left text-gray-700 uppercase dark:text-gray-400'
                          >
                            Workout completed and smileys updated
                          </th>
                        </tr>
                      </thead>
                      <tbody className='bg-white divide-y divide-gray-200 '></tbody>
                    </table>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  )
}

export default YFASummaryReport
