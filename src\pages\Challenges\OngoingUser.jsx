import React, { useEffect, useState } from "react";
import Header from "../../components/Header";
import { Box, Grid } from "@mui/material";
import {
	URL,
	getUserOngoingChalenges,
	subsribeToChallenge,
} from "../../API/api-endpoint";
import { Button } from "antd";
import { showError, showSuccess } from "../../components/Messages";
import { capitalizeFirstLetter } from "../../utils/Resubale";
import Description from "../Workouts/Description";
import axios from "axios";

const OngoingUser = () => {
	const [onGoingChallenges, setOngoingChallenges] = useState();
	const [communityChallenges, setCommunityChallenges] = useState();
	const [enrolledChallenges, setEnrolledChallenges] = useState([]);
	const [isOpenDescription, setOpenDescription] = useState(false);

	const storedUser = localStorage.getItem("user");
	let user = null;
	let onboardingState = null;

	const roleID = localStorage.getItem("roleID");

	if (storedUser) {
		user = JSON.parse(storedUser);
		onboardingState = user.onboardingState;
		console.log("Onboarding State:", onboardingState);
	} else {
		console.log("User not found in localStorage.");
	}

	useEffect(() => {
		// Fetch challenges based on the onboardingState
		if (onboardingState === "community") {
			fetchCommunityChallenges();
			fetchAthleteEnrollments();
		} else if (onboardingState === "yoska_academy") {
			fetchAssignedChallenge();
		}
	}, [onboardingState]);

	// API call for yoska_academy users
	const fetchAssignedChallenge = async () => {
		const response = await getUserOngoingChalenges();
		setOngoingChallenges(response?.challenges);
	};

	// API call for community users to fetch available challenges
	const fetchCommunityChallenges = async () => {
		try {
			let athleteCommunityGroupId = 0;
			const groupDetailString = localStorage.getItem("groupDetail");

			if (groupDetailString) {
				const groupDetail = JSON.parse(groupDetailString);
				athleteCommunityGroupId = parseInt(groupDetail.id);
			}
			if (!athleteCommunityGroupId) {
				showError("Athlete Community Group ID is missing.");
				return;
			}
			const token = localStorage.getItem("token");
			const response = await axios.get(
				`${URL}/athlete-community/group-challenge/group/${athleteCommunityGroupId}`,
				{
					headers: {
						"Content-Type": "application/json",
						Authorization: token,
					},
				}
			);
			if (response.data.status) {
				console.log("CommunityChallenge", response.data.data);
				setCommunityChallenges(response.data.data);
			} else {
				showError("Failed to fetch community challenges.");
			}
		} catch (error) {
			console.error("Error fetching community challenges:", error);
			showError("Error fetching community challenges.");
		}
	};

	// New API call to fetch enrollments for the current athlete
	const fetchAthleteEnrollments = async () => {
		try {
			const token = localStorage.getItem("token");
			const athleteId = localStorage.getItem("userId");
			if (!athleteId) {
				showError("Athlete ID is missing.");
				return;
			}
			const response = await axios.get(
				`${URL}/athlete-community/group-challenge-enrolment/athlete/${athleteId}`,
				{
					headers: {
						"Content-Type": "application/json",
						Authorization: token,
					},
				}
			);
			if (response.data.status) {
				// Each enrollment contains an athletecommunitygroupchallenge object.
				setEnrolledChallenges(response.data.data);
			} else {
				showError("Failed to fetch athlete enrollments.");
			}
		} catch (error) {
			console.error("Error fetching athlete enrollments:", error);
			showError("Error fetching athlete enrollments.");
		}
	};

	// For subscriptions (assuming same logic applies to both types of challenges)
	const handleSubscribeToChannel = async (id) => {
		const data = { challengeId: id };
		const response = await subsribeToChallenge(data);
		if (response?.status) {
			showSuccess("You are Subscribed to this Challenge");
			if (onboardingState === "community") {
				fetchCommunityChallenges();
				fetchAthleteEnrollments();
			} else {
				fetchAssignedChallenge();
			}
		} else {
			showError(response?.message);
		}
		console.log("response", response);
	};

	const enrollCommunityChallenge = async (challengeId) => {
		console.log("Enrolling in community challenge with ID:", challengeId);
		try {
			const token = localStorage.getItem("token");
			const userId = localStorage.getItem("userId");
			const payload = {
				athleteCommunityGroupChallengeId: challengeId,
				userId: userId,
			};
			const response = await axios.post(
				`${URL}/athlete-community/group-challenge-enrolment`,
				payload,
				{
					headers: {
						"Content-Type": "application/json",
						Authorization: token,
					},
				}
			);
			if (response.data.status) {
				showSuccess("Successfully enrolled in community challenge.");
				// Refresh both the community challenges and enrollment status
				fetchCommunityChallenges();
				fetchAthleteEnrollments();
			} else {
				showError(
					"Enrollment failed: " +
						(response.data.message || "Unknown error")
				);
			}
		} catch (error) {
			console.error("Error enrolling in community challenge:", error);
			showError("Error enrolling in community challenge.");
		}
	};

	return (
		<div>
			<Header />
			<div className='grid grid-cols-1 xl:grid-cols-5 items-start gap-x-4 '></div>
			<div
				style={{
					marginTop: "80px",
					padding: "20px",
					display: "flex",
					background: "#FFEADC",
					justifyContent: "center",
					alignItems: "center",
				}}
			>
				<div
					style={{ width: "95%", minHeight: "90vh", padding: "20px" }}
				>
					<Grid container>
						<Grid container spacing={0}>
							<Grid
								item
								xs={6}
								sm={6}
								sx={{ padding: "24px 0px 20px 20px" }}
							>
								<div>
									<h1 style={{ fontSize: "24px" }}>
										<strong>Challenges </strong>
									</h1>
								</div>
							</Grid>
							<Grid
								item
								xs={6}
								sm={6}
								sx={{
									padding: "24px 20px 20px 20px",
									textAlign: "end",
								}}
							>
								<a href='/assignechallenges-user'>
									<Button
										type='primary'
										style={{ margin: "0% 0% 0% 2%" }}
									>
										Back
									</Button>
								</a>
							</Grid>
							{onboardingState === "yoska_academy" ? (
								<div
									style={{
										display: "flex",
										flexWrap: "wrap",
										justifyContent: "center",
									}}
								>
									{onGoingChallenges?.map((challnges) => (
										<Grid
											item
											xs={12}
											sm={2.7}
											key={challnges.id}
											sx={{
												margin: "10px",
												padding: "10px",
												minHeight: "350px",
												minWidth: "200px",
												border: "1px solid gray",
												borderRadius: "10px",
												background: "white",
											}}
										>
											<Box
												sx={{
													padding: "10px",
													minHeight: "140px",
													width: "100%",
													borderRadius: "10px",
													background: "#FFEADC",
												}}
											>
												<Grid container>
													<Grid item xs={12} sm={6}>
														{`${new Date(
															challnges?.challengeStartDate
														).toLocaleDateString(
															"en-GB"
														)} ${new Date(
															challnges?.challengeEndDate
														).toLocaleDateString(
															"en-GB"
														)}`}
													</Grid>
													<Grid
														item
														xs={12}
														sm={6}
														sx={{
															textAlign: "end",
														}}
													>
														{
															challnges
																?.activitylevel
																?.level
														}
													</Grid>
													<Grid
														item
														xs={12}
														sm={12}
														style={{
															fontSize: "18px",
														}}
													>
														{capitalizeFirstLetter(
															challnges?.challengeName
														)}
													</Grid>
													<Grid
														item
														xs={12}
														sm={9}
														style={{
															fontSize: "18px",
															color: "#E67E22",
														}}
													>
														{
															challnges?.activity
																?.activity_name
														}
													</Grid>
													&nbsp;
													<Grid
														item
														xs={12}
														sm={8}
														sx={{
															marginTop: "10px",
														}}
													>
														{
															challnges
																?.activityTrack
																?.activity_track
														}
													</Grid>
													<Grid
														item
														xs={12}
														sm={4}
														sx={{
															display: "flex",
															justifyContent:
																"end",
															marginTop: "10px",
														}}
													>
														<img
															src={`${URL}/static/public/assets/${challnges?.activity?.badge}`}
															alt='...'
															className='w-6 h-6 border-2 rounded-full'
														/>
													</Grid>
												</Grid>
											</Box>
											<Box
												sx={{
													padding: "2px",
													width: "100%",
												}}
											>
												<Grid container spacing={2}>
													<Grid item xs={12} sm={6}>
														Training block
													</Grid>
													<Grid
														item
														xs={12}
														sm={6}
														sx={{
															textAlign: "end",
														}}
													>
														{challnges?.trainingBlock
															? challnges?.trainingBlock
															: "NA"}
													</Grid>
													<Grid item xs={12} sm={6}>
														Challenge Duration
													</Grid>
													<Grid
														item
														xs={12}
														sm={6}
														sx={{
															textAlign: "end",
														}}
													>
														{
															challnges?.challengeDuration
														}{" "}
														{challnges?.durationunit
															? challnges?.durationunit
															: "Days"}
													</Grid>
													<Grid item xs={12} sm={6}>
														Challenge target
													</Grid>
													<Grid
														item
														xs={12}
														sm={6}
														sx={{
															textAlign: "end",
														}}
													>
														{challnges
															?.targetchallanges
															?.length > 0 ? (
															<>
																{challnges?.targetchallanges?.map(
																	(
																		target,
																		index
																	) => (
																		<div
																			key={
																				index
																			}
																		>
																			{
																				target?.quota
																			}{" "}
																			Km
																		</div>
																	)
																)}
															</>
														) : (
															"NA"
														)}
													</Grid>
													<Grid item xs={12} sm={6}>
														Challenge Points
													</Grid>
													<Grid
														item
														xs={12}
														sm={6}
														sx={{
															textAlign: "end",
														}}
													>
														{
															challnges?.challengePoints
														}{" "}
														Points
													</Grid>
													<Grid
														item
														xs={12}
														sm={12}
														sx={{
															textAlign: "center",
														}}
													>
														<Button
															type='primary'
															style={{
																color: "white",
															}}
															onClick={() =>
																handleSubscribeToChannel(
																	challnges?.id
																)
															}
														>
															{challnges?.is_user_enrolled
																? "Enrolled"
																: "Enroll"}
														</Button>
														&nbsp;
														<Button
															variant='outlined'
															style={{
																borderColor:
																	"#E67E22",
																color: "#E67E22",
																fontWeight: 700,
															}}
															onClick={() => {
																setOpenDescription(
																	{
																		isOpen: true,
																		description:
																			capitalizeFirstLetter(
																				challnges?.challengeDescription
																			),
																	}
																);
															}}
														>
															View Description
														</Button>
													</Grid>
												</Grid>
											</Box>
										</Grid>
									))}
								</div>
							) : onboardingState === "community" ? (
								<div
									style={{
										display: "flex",
										flexWrap: "wrap",
										justifyContent: "center",
									}}
								>
									{communityChallenges
										?.filter(
											(challnges) =>
												new Date(
													challnges?.athletecommunitygroupchallenge.challengeEndDate
												) > new Date()
										)
										.map((challnges) => {
											// Check if the current community challenge is already enrolled
											const currentChallengeName =
												challnges?.athletecommunitygroupchallenge?.challengeName?.toLowerCase();
											const isEnrolled =
												enrolledChallenges.some(
													(enrollment) => {
														const enrolledName =
															enrollment?.athletecommunitygroupchallenge?.challengeName?.toLowerCase();
														return (
															enrolledName ===
															currentChallengeName
														);
													}
												);
											return (
												<Grid
													item
													xs={12}
													sm={2.7}
													key={
														challnges
															?.athletecommunitygroupchallenge
															?.id
													}
													sx={{
														margin: "10px",
														padding: "10px",
														minHeight: "350px",
														minWidth: "200px",
														border: "1px solid gray",
														borderRadius: "10px",
														background: "white",
													}}
												>
													{/* Card content remains unchanged */}
													<Box
														sx={{
															padding: "10px",
															minHeight: "140px",
															width: "100%",
															borderRadius:
																"10px",
															background:
																"#FFEADC",
														}}
													>
														<Grid container>
															<Grid
																item
																xs={12}
																sm={6}
															>
																{`${new Date(
																	challnges?.athletecommunitygroupchallenge.challengeStartDate
																).toLocaleDateString(
																	"en-GB"
																)} ${new Date(
																	challnges?.athletecommunitygroupchallenge.challengeEndDate
																).toLocaleDateString(
																	"en-GB"
																)}`}
															</Grid>
															<Grid
																item
																xs={12}
																sm={6}
																sx={{
																	textAlign:
																		"end",
																}}
															>
																{
																	challnges
																		?.activitylevel
																		?.level
																}
															</Grid>
															<Grid
																item
																xs={12}
																sm={12}
																style={{
																	fontSize:
																		"18px",
																}}
															>
																{capitalizeFirstLetter(
																	challnges
																		?.athletecommunitygroupchallenge
																		?.challengeName
																)}
															</Grid>
															<Grid
																item
																xs={12}
																sm={9}
																style={{
																	fontSize:
																		"18px",
																	color: "#E67E22",
																}}
															>
																{
																	challnges
																		?.activity
																		?.activity_name
																}
															</Grid>
															&nbsp;
															<Grid
																item
																xs={12}
																sm={8}
																sx={{
																	marginTop:
																		"10px",
																}}
															>
																{
																	challnges
																		?.activityTrack
																		?.activity_track
																}
															</Grid>
															<Grid
																item
																xs={12}
																sm={4}
																sx={{
																	display:
																		"flex",
																	justifyContent:
																		"end",
																	marginTop:
																		"10px",
																}}
															>
																<img
																	src={`${URL}/static/public/assets/${challnges?.activity?.badge}`}
																	alt='...'
																	className='w-6 h-6 border-2 rounded-full'
																/>
															</Grid>
														</Grid>
													</Box>
													<Box
														sx={{
															padding: "2px",
															width: "100%",
														}}
													>
														<Grid
															container
															spacing={2}
														>
															<Grid
																item
																xs={12}
																sm={6}
															>
																Challenge
																Duration
															</Grid>
															<Grid
																item
																xs={12}
																sm={6}
																sx={{
																	textAlign:
																		"end",
																}}
															>
																{
																	challnges
																		?.athletecommunitygroupchallenge
																		.challengeDuration
																}{" "}
																{challnges
																	?.athletecommunitygroupchallenge
																	.challengeDurationUnit
																	? challnges
																			?.athletecommunitygroupchallenge
																			.challengeDurationUnit
																	: "Days"}
															</Grid>
															<Grid
																item
																xs={12}
																sm={6}
															>
																Challenge target
															</Grid>
															<Grid
																item
																xs={12}
																sm={6}
																sx={{
																	textAlign:
																		"end",
																}}
															>
																{challnges
																	?.athletecommunitygroupchallenge
																	.target ? (
																	<div>
																		{challnges
																			.athletecommunitygroupchallenge
																			.target
																			.quota
																			? `${challnges.athletecommunitygroupchallenge.target.quota} Km`
																			: challnges
																					.athletecommunitygroupchallenge
																					.target
																					.name}
																	</div>
																) : (
																	"NA"
																)}
															</Grid>
															<Grid
																item
																xs={12}
																sm={6}
															>
																Challenge Points
															</Grid>
															<Grid
																item
																xs={12}
																sm={6}
																sx={{
																	textAlign:
																		"end",
																}}
															>
																{
																	challnges
																		?.athletecommunitygroupchallenge
																		.challengePoints
																}{" "}
																Points
															</Grid>
															<Grid
																item
																xs={12}
																sm={12}
																sx={{
																	textAlign:
																		"center",
																}}
															>	
																{roleID == 5 && (
																// <Button
																// 	type='primary'
																// 	style={{
																// 		color: "white",
																// 	}}
																// 	disabled={
																// 		isEnrolled
																// 	}
																// 	onClick={() =>
																// 		enrollCommunityChallenge(
																// 			challnges
																// 				?.athletecommunitygroupchallenge
																// 				?.id
																// 		)
																// 	}
																// >
																// 	{isEnrolled
																// 		? "Enrolled"
																// 		: "Enroll"}
																// </Button>
																<button
																type='button'
																style={{
																	backgroundColor: isEnrolled ? "green" : "#e67e22",
																	color: "white",
																	borderColor: isEnrolled ? "green" : "#e67e22",
																	padding: "3% 5% 3% 5%",
																	borderRadius: "5px",
																	cursor: isEnrolled ? "not-allowed" : "pointer",
																	opacity: isEnrolled ? 0.6 : 1,
																	fontWeight: "bold",
																}}
																disabled={isEnrolled}
																onClick={() =>
																	enrollCommunityChallenge(
																		challnges?.athletecommunitygroupchallenge?.id
																	)
																}
															>
																{isEnrolled ? "Enrolled" : "Enroll"}
															</button>

																)}
																&nbsp;
																<Button
																	variant='outlined'
																	style={{
																		borderColor:
																			"#E67E22",
																		color: "#E67E22",
																		fontWeight: 700,
																	}}
																	onClick={() => {
																		setOpenDescription(
																			{
																				isOpen: true,
																				description:
																					capitalizeFirstLetter(
																						challnges
																							?.athletecommunitygroupchallenge
																							?.challengeDescription
																					),
																			}
																		);
																	}}
																>
																	View
																	Description
																</Button>
															</Grid>
														</Grid>
													</Box>
												</Grid>
											);
										})}
								</div>
							) : (
								<div>
									<p>
										No content available for your onboarding
										state.
									</p>
								</div>
							)}
						</Grid>
					</Grid>
				</div>
				{isOpenDescription?.isOpen && (
					<Description
						isModalOpen={isOpenDescription}
						handleCancel={setOpenDescription}
					/>
				)}
			</div>
		</div>
	);
};

export default OngoingUser;
