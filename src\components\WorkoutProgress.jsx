import * as React from "react";
import { styled } from "@mui/material/styles";
import Box from "@mui/material/Box";
import LinearProgress, {
  linearProgressClasses,
} from "@mui/material/LinearProgress";
import { Typography } from "@mui/material";
import { logDOM } from "@testing-library/react";

const BorderLinearProgress = styled(LinearProgress)(
  ({ theme, percentage }) => ({
    height: 4,
    borderRadius: 5,
    position: "relative", // Positioning for the percentage text
    [`&.${linearProgressClasses.colorPrimary}`]: {
      backgroundColor:
        theme.palette.grey[theme.palette.mode === "light" ? 200 : 800],
    },
    [`& .${linearProgressClasses.bar}`]: {
      borderRadius: 5,
      backgroundColor: percentage < 50 ? "red" : "#e67e22", // Change color based on percentage
    },
    "& .percentageText": {
      position: "absolute",
      top: "50%",
      left: "50%",
      transform: "translate(-50%, -50%)",
      color: theme.palette.mode === "light" ? "#000" : "#fff",
    },
  })
);

const WorkoutProgress = ({ completed, total,type }) => {
  // Calculate the percentage completed
  const percentage = total === 0 || total== "00:00:00" ? 0 : (completed / total) * 100;

  // Adjust the value prop for LinearProgress to ensure it shows 100% when completed is greater than total
  const progressValue = completed > total ? 100 : isNaN(percentage)?0:percentage;
  
  // Check if both total and completed are 0, and don't render the progress bar

  return (
    <Box sx={{ flexGrow: 1, marginTop:"-16px" }}>
      <br />
      <BorderLinearProgress
        variant="determinate"
        value={isNaN(progressValue)?0:progressValue}
        percentage={isNaN(progressValue)?0:progressValue}
      >
      <span className="percentageText">{Math.round(progressValue)}%</span>

        </BorderLinearProgress>
      
      <Typography variant="body2" color="textSecondary" align="end" sx={{fontSize:"10px"}}>
     <b>P</b> {total} | <b>A</b> {completed}{type}
      </Typography>
    </Box>
  );
};

export default WorkoutProgress;
