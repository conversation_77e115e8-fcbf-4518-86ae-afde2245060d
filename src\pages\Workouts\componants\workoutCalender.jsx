import React, { useEffect, useMemo, useRef, useState } from "react";
import FullCalendar from "@fullcalendar/react";
import dayGridPlugin from "@fullcalendar/daygrid";
import timeGridPlugin from "@fullcalendar/timegrid";
import interactionPlugin, { Draggable } from "@fullcalendar/interaction";
import listPlugin from "@fullcalendar/list";
import SimpleDialog from "./SimpleDialog";
import CommentBox from "./CommentBox";
import moment from "moment";
import { URL, getAllworkoutsDetails, getParticularWorkoutDetails, hideUnhideStravaWorkouts, importStravaActivity, mergeWorkout, syncStravaActivity, syncStravaActivityCoach, unMergedWorkouts, updateWorkoutsByDrag, createWorkouts, copyPasteWorkouts, getWeeklySummary, copyPasteweeklyWorkouts, deleteSelectedWorkouts, getPersonlisation, getUpdateAutomation } from "../../../API/api-endpoint";
import Swal from "sweetalert2";
import WorkoutForm from "./workoutForm/WorkoutDrawer";
import CalenderModal from "./workoutForm/CalenderModal";
import { FiEdit } from "react-icons/fi";
import { Checkbox, FormLabel, Grid, Menu, MenuItem, OutlinedInput, TextField, Tooltip } from "@mui/material";
import { Modal, Dropdown } from "antd";
import { Menu as AntMenu } from "antd";
import { IoCloseSharp, IoEyeOutline } from "react-icons/io5";
import { GoLink } from "react-icons/go";
import { GoUnlink } from "react-icons/go";
import { MdOutlineAutoDelete } from "react-icons/md";
import { MdContentCopy } from "react-icons/md";
import "../../../index.css";
import { ExclamationCircleFilled } from "@ant-design/icons";
import { IoMdAlarm, IoMdEye } from "react-icons/io";
import {
  showError,
  showInfo,
  showSuccess,
  sowWarn,
} from "../../../components/Messages";
import { FaPlus } from "react-icons/fa";
import WorkoutProgress from "../../../components/WorkoutProgress";
import { FaChevronRight } from "react-icons/fa";
import { FaChevronLeft } from "react-icons/fa";
import { HiDotsVertical } from "react-icons/hi";
import { GiHamburgerMenu } from "react-icons/gi";
import { capitalizeFirstLetter } from "../../../utils/Resubale";
import FeedbackStatus from "./FeedbackStatus";

function WorkoutCalender(props) {
  const views = {
    MyMonthView: {
      type: "dayGridMonth",
      buttonText: "Month",
    },
    MyWeekView: {
      type: "timeGridWeek",
      buttonText: "Week",
    },
    MyDayView: {
      type: "timeGridDay",
      buttonText: "Day",
    },
  };

  const { confirm } = Modal;
  const { assignedCocahId, formGoalValue, getAllWorkouts, workoutID, eventsData, draggedEvent,
    setworkoutformdrawerState, setWorkoutID, calendarModal,
    workoutformdrawerState, setAllevents, dateForworkout, setDate, draggableEl, setIsOpenSideBar, isOpenSideBar, setIsZoneOpen, setCalendarModal } = props;
  
  const [openBox, setOpenBox] = useState(false);
  const [openMergeModal, setOpenMergeModal] = useState(false);
  const [openweeklySummary, setOpenWeeklySummary] = useState(false);
  const [mergeModalData, setMergeModaldata] = useState();
  const [selectedMergeId, setSelectedMergeId] = useState();
  const [selectedStravaWorkouts, setSelectedMergData] = useState();

  const [isOpenCommentBox, setOpenCommentBox] = useState(false);
  const [selectedValue, setSelectedValue] = React.useState();
  const [CopyWorkoutData, setCopyWorkoutData] = React.useState({
    year: "",
    month: "",
    copyweekindex: "",
    pasteweekindex: "",
  });
  console.log("CopyWorkoutData", CopyWorkoutData);

  const [selecedCopyWorkoutData, setSelectedCopyWorkoutData] = React.useState();
  const [selectedDeleteWorkouts, setSelectedDeleteWorkouts] = React.useState(
    []
  );
  const [isCheckedOpen, setisCheckedOpen] = React.useState(false);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [weekNo, setWeekNo] = useState();
  console.log("isCheckedOpen", isCheckedOpen);


  const [anchorEl, setAnchorEl] = React.useState(null);
  const open = Boolean(anchorEl);
  const handleClick = (event, summary, index) => {
    console.log("data", { ...summary, index: index });

    setSelectedCopyWorkoutData({ ...summary, index: index });
    setAnchorEl(event.currentTarget);
  };
  // close delete
  const handleClose = async (id, process) => {
    let data = {
      id: parseInt(id),
      type: process == "Planned" ? "planned" : "strava",
    };
    const response = await hideUnhideStravaWorkouts(data);
    if (response?.status) {
      showSuccess(capitalizeFirstLetter(response.message));
      let result = "";
      result = await getAllworkoutsDetails(assignedCocahId);
      setAllevents(result);

    } else {
      showError(response?.message);
    }
  };
  const handleDeleteSelectedWorkouts = async () => {
    let data = { ids: selectedDeleteWorkouts, type: "planned" };
    const response = await deleteSelectedWorkouts(data);
    if (response?.status) {
      setSelectedDeleteWorkouts([]);
      setisCheckedOpen(false);
      showSuccess("Status Updated Successfully");
      let result = "";
      if (assignedCocahId) {
        result = await getAllworkoutsDetails(assignedCocahId);

        setAllevents(result);
      } else {
        result = await getAllworkoutsDetails(assignedCocahId);
        setAllevents(result);
      }
    } else {
      showError(response?.message);
    }
  };
  const [headerToolbar, setHeaderToolbar] = useState({
    left: 'prev,title,next',
    center: '',
    right: 'customButton5 customButton2 customButton3 MyMonthView,MyWeekView,MyDayView',
  });
  const roleID = localStorage.getItem("roleID");
  console.log("headerToolbar", headerToolbar);
  useEffect(() => {
    if (calendarModal) {
      setHeaderToolbar({
        ...headerToolbar,
        right:
          "customButton5 customButton2 customButton3 customButton4 customButton6 MyMonthView,MyWeekView,MyDayView",
      });
    }

    if (isCheckedOpen && calendarModal) {
      setHeaderToolbar({
        ...headerToolbar,
        right:
          "customButton5 customButton7 customButton2 customButton3 customButton4 customButton6 MyMonthView,MyWeekView,MyDayView",
      });
    }
    if (!isCheckedOpen && calendarModal) {
      setHeaderToolbar({
        ...headerToolbar,
        right:
          "customButton5 customButton2 customButton3 customButton4 customButton6 MyMonthView,MyWeekView,MyDayView",
      });
    }
    if (!isCheckedOpen && !calendarModal) {
      setHeaderToolbar({
        ...headerToolbar,
        right:
          "customButton5 customButton7 customButton2 customButton3 MyMonthView,MyWeekView,MyDayView",
      });
    }
    if (isCheckedOpen && !calendarModal) {
      setHeaderToolbar({
        ...headerToolbar,
        right:
          "customButton5 customButton7 customButton2 customButton3 MyMonthView,MyWeekView,MyDayView",
      });
    }


  }, [roleID, isOpenSideBar, selectedDeleteWorkouts, assignedCocahId, isCheckedOpen])




  // delete workouts
  const showDeleteConfirm = (id, process) => {
    confirm({
      title: "Are you sure want to delete this workout?",
      icon: <ExclamationCircleFilled />,
      okText: "Yes",
      okType: "danger",
      cancelText: "No",
      onOk() {
        handleClose(id, process);
        // console.log('OK');
      },
      onCancel() {
        console.log("Cancel");
      },
    });
  };
  const showDeleteAllConfirm = (id, process) => {
    if (selectedDeleteWorkouts.length > 0) {
      confirm({
        title: "Are you sure want to delete the selected workout?",
        icon: <ExclamationCircleFilled />,
        okText: "Yes",
        okType: "danger",
        cancelText: "No",
        onOk() {
          handleDeleteSelectedWorkouts();
          setisCheckedOpen(false)
          // console.log('OK');
        },
        onCancel() {
          setisCheckedOpen(false);
          setSelectedDeleteWorkouts([]);
        },
      });
    } else {
      showInfo("Please select workouts");
    }
  };
  const showDeleteAllButtonCheckbox = () => {
    setisCheckedOpen(true);
  };
  const CheckBackDeleteFunction = () => {
    setisCheckedOpen(false);
  };
  const showCnfirmUnmerge = (id, stravaID) => {
    let data = {
      id,
      stravaID,
    };
    setSelectedMergeId(id);
    confirm({
      title: "Do you want to unmerge this workout?",
      icon: <ExclamationCircleFilled />,
      onOk() {
        unmergeWorkouts(data);
        console.log("OK");
      },
      onCancel() {
        console.log("Cancel");
      },
    });
  };
  const handleDetailsClick = (clickedEvent) => {
    const particularId = eventsData.filter(
      (event) => event.id == clickedEvent.id
    );
    let findTyep = particularId[0]?.badge?.split(".")[0]

    setSelectedMergeId(particularId[0]);
    const eventsOnSameDay = eventsData.filter((event) =>
      moment(event.start).isSame(clickedEvent.start, "day")
    );
    const eventIDsOnDropDay = eventsOnSameDay.filter(
      (event) =>
        event.borderColor == "red" ||
        (event.borderColor == "gray" && event?.process !== "Strava" && event?.badge.includes(findTyep))
    );
    if (eventIDsOnDropDay?.length == 0) {
      sowWarn("Can't merge it");
    } else if (eventIDsOnDropDay?.length > 0) {
      setOpenMergeModal(true);
      setMergeModaldata(eventIDsOnDropDay);
    }

    // Display the events or use the data as needed
    // ... (e.g., set state to display eventsOnSameDay)
  };
  const handleSelectItemk = (event) => {
    // Check if the element already exists in the selectedDeleteWorkouts array
    const exists = selectedDeleteWorkouts.some((item) => item === event);

    if (exists) {
      // If it exists, remove the element with the same id
      setSelectedDeleteWorkouts((prev) =>
        prev.filter((item) => item !== event)
      );
    } else {
      // If it doesn't exist, add the arrry elements with a unique id
      setSelectedDeleteWorkouts((prev) => [...prev, event]);
    }
  };

  const checkColor = (type) => {
    switch (type) {
      case 'running':
        return '#FF4D7F';
      case 'cycling':
        return '#fdfa03';
      case 'swimming':
        return '#0096FF';
      case 'fitness':
        return '#00506D';
      case 'note':
        return '#8900e0';
      default:
        return 'gray';
    }
  };

  const eventContent = (eventInfo) => {
    let findTyep = eventInfo?.event?.extendedProps?.badge?.split(".")[0];
    const formattedTime = moment(eventInfo.event?.extendedProps.workoutTime, 'HH:mm:ss').format('hh:mm A');
    console.log("findTyep", checkColor(findTyep), findTyep);

    if (!eventInfo.event.title) {
      return null; // Return nothing if event title doesn't exist
    }
    const eventsOnSameDay = eventsData.filter((event) =>
      moment(event.start).isSame(eventInfo.event.start, "day")
    );
    const eventIDsOnDropDay = eventsOnSameDay.filter(
      (event) =>
        event.borderColor == "red" ||
        (event.borderColor == "gray" && event?.process != "Strava")
    );
    const showLinkButton = eventIDsOnDropDay.length > 0; // Check if there are multiple events on the same day
    const isChecked = selectedDeleteWorkouts.some(
      (item) => item === eventInfo?.event?.id
    );
    const menu = (
      <AntMenu>

        {!eventInfo?.event?.extendedProps?.strava_id &&
          eventInfo?.event?.borderColor !== "orange" &&
          eventInfo?.event?.borderColor !== "green" && (
            <AntMenu.Item key="edit" onClick={() => {
              setworkoutformdrawerState(true);
              setWorkoutID(eventInfo.event.id);
            }}>
              <div style={{ display: 'flex', alignItems: 'center' }}>
                <FiEdit style={{ color: "#E67E22" }} />
                <span className="ml-2">Edit</span>
              </div>
            </AntMenu.Item>
          )}
        {showLinkButton &&
          eventInfo?.event?.extendedProps?.strava_id &&
          eventInfo?.event?.borderColor !== "green" &&
          eventInfo?.event?.borderColor !== "red" &&
          eventInfo?.event?.borderColor !== "gray" && (
            <AntMenu.Item key="goLink" onClick={() => handleDetailsClick(eventInfo.event)}>
              <div style={{ display: 'flex', alignItems: 'center' }}>
                <GoLink style={{ color: "#E67E22" }} />
                <span className="ml-2">Link</span>
              </div>
            </AntMenu.Item>
          )}
        {eventInfo?.event?.extendedProps?.process === "Planned" &&
          eventInfo?.event?.extendedProps?.strava_id && (
            <AntMenu.Item key="goUnlink" onClick={() => showCnfirmUnmerge(eventInfo.event.id, eventInfo?.event?.extendedProps?.strava_id)}>
              <div style={{ display: 'flex', alignItems: 'center' }}>
                <GoUnlink style={{ color: "#E67E22" }} />
                <span className="ml-2">Unlink</span>
              </div>
            </AntMenu.Item>
          )}
        {eventInfo?.event?.borderColor !== "green" && (
          <AntMenu.Item key="contentCopy" onClick={() => showConfirm(eventInfo.event.id, eventInfo.event.start)}>
            <div style={{ display: 'flex', alignItems: 'center' }}>
              <MdContentCopy style={{ color: "#E67E22" }} />
              <span className="ml-2">Copy</span>
            </div>
          </AntMenu.Item>
        )}
        {eventInfo?.event?.borderColor !== "green" && (
          <AntMenu.Item key="autoDelete" onClick={() => showDeleteConfirm(eventInfo.event.id, eventInfo?.event?.extendedProps?.process)}>
            <div style={{ display: 'flex', alignItems: 'center' }}>
              <MdOutlineAutoDelete style={{ color: "#E67E22" }} />
              <span className="ml-2">Delete</span>
            </div>
          </AntMenu.Item>
        )}
      </AntMenu>
    );
    return (
      <div
        style={isCheckedOpen ?
          {
            color: "#6B7A99",
            backgroundColor: "white",
            borderRadius: "5px",
            padding: "10px",
            border: ` 3px solid ${eventInfo.event.borderColor}`,
            width: "100%",
            height: "165px"
          }
          :
          {
            color: "#6B7A99",
            backgroundColor: "white",
            borderRadius: "5px",
            padding: "10px",
            border: ` 3px solid ${eventInfo.event.borderColor}`,
            width: "100%",
            height: "155px"
          }}
      >
        <div className="example" style={{ width: "100%", overflowX: "auto", scrollbarWidth: "thin" }}>
          <div className="flex justify-end items-center">

            {isCheckedOpen && (
              <Checkbox
                checked={isChecked}
                value={selectedDeleteWorkouts}
                onClick={() => handleSelectItemk(eventInfo?.event?.id)}
                size="small"
                style={{ padding: "0", color: "#E67E22" }}
              />
            )}
            <Dropdown overlay={menu} trigger={['click']}>
              <HiDotsVertical style={{ color: "#E67E22", cursor: 'pointer' }} />
            </Dropdown>
          </div>
          <div
            className="example mb-2 pl-1"
            style={{ width: "250px", borderLeft: `3px solid ${checkColor(findTyep)}` }}
            onClick={() => {
              setOpenBox(!openBox);
              setSelectedValue(eventInfo.event);
            }}>
            <p style={{ fontWeight: "600" }}>{eventInfo.event.title} &nbsp;</p>
            {eventInfo?.event?.extendedProps?.ispartial == 2 &&
              <p>{eventInfo.event?.extendedProps.workoutTime} &nbsp;</p>
            }
            {eventInfo?.event?.extendedProps?.process == "Strava" &&
              <p>{eventInfo.event?.extendedProps.workoutTime} &nbsp;</p>
            }

            {eventInfo?.event?.extendedProps?.week > 0 &&
              <p>W-{eventInfo?.event?.extendedProps?.week} &nbsp;</p>
            }
            {eventInfo?.event?.extendedProps?.process != "Strava" && eventInfo?.event?.extendedProps?.ispartial != 2 &&
              <p style={{ color: "transparent" }}>{eventInfo.event?.extendedProps.workoutTime} &nbsp;</p>
            }


            {eventInfo?.event?.extendedProps?.week <= 0 &&
              <p style={{ color: "transparent" }}>W-{eventInfo?.event?.extendedProps?.week} &nbsp;</p>
            }
            {/* <div className="flex  items-center">
              <IoMdEye style={{ fontSize: "16px", color: "#E67E22" }} />
              {/* action to open the dialog where here */}
            {/* <IoMdEye style={{ fontSize: "16px", color: "#E67E22" }} /> */}
            {/* </div> */}

            <div className="w-[10vw] sm:w-[5vw] md:w-[6vw] lg:w-[7vw] xl:w-[8vw] flex flex-row justify-between items-center">
              <IoMdEye className="text-[#E67E22] text-lg" />
              <FeedbackStatus
                workoutId={eventInfo?.event?.extendedProps?.strava_id ?
                  eventInfo?.event?.extendedProps?.strava_id :
                  eventInfo?.event?._def?.publicId}
                workoutType={eventInfo?.event?.extendedProps?.strava_id ?
                  "strava" :
                  "manual"}
              />
            </div>

          </div>
          <hr style={{ border: "1px solid lightGray" }} />
          <div className="checkScrollBar">
            <div className=" mt-2 flex items-center" onClick={() => {
              setOpenBox(!openBox);
              setSelectedValue(eventInfo.event);
            }}>
              <div>
                {eventInfo?.event?.extendedProps?.badge && (
                  <img
                    src={`${URL}/static/public/assets/${eventInfo?.event?.extendedProps?.badge}`}
                    alt="..."
                    className="w-6 h-6  border-2 rounded-full cursor-pointer mr-2"
                    style={{ borderColor: checkColor(findTyep), minWidth: '1.5rem' }}
                  />
                )}
              </div>
              {findTyep != "note" && findTyep !== "nutrition" &&
                <span>
                  <span className="mr-2">
                    {eventInfo.event.extendedProps.workout_planned_distance &&
                      `${eventInfo.event.extendedProps.workout_planned_distance} |`}{" "}
                    {eventInfo.event.extendedProps.actual_workout}
                    <b>{eventInfo.event.extendedProps.unit}</b>
                  </span>


                  <span>
                    {eventInfo.event.extendedProps.workout_planned_duration &&
                      `${eventInfo.event.extendedProps.workout_planned_duration} |`}{" "}
                    {eventInfo.event.extendedProps.actual_duration}
                  </span>
                </span>
              }
            </div>
          </div>



          {/* <Grid container spacing={2}>
            <Grid item xs={12} md={3} sm={3}>
              <IoEyeOutline
                style={{ fontSize: "16px" }}
                onClick={() => {
                  setOpenBox(!openBox);
                  setSelectedValue(eventInfo.event);
                }}
              />
            </Grid>

            {!eventInfo?.event?.extendedProps?.strava_id &&
              eventInfo?.event?.borderColor !== "orange" &&
              eventInfo?.event?.borderColor !== "green" && (
                <Grid item xs={12} md={3} sm={3}>
                  <FiEdit
                    onClick={() => {
                      setworkoutformdrawerState(true);
                      setWorkoutID(eventInfo.event.id);
                    }}
                  />
                </Grid>
              )}

            {showLinkButton &&
              eventInfo?.event?.extendedProps?.strava_id &&
              eventInfo?.event?.borderColor != "green" &&
              eventInfo?.event?.borderColor != "red" &&
              eventInfo?.event?.borderColor != "gray" && (
                <Grid item xs={12} md={3} sm={3}>
                  <GoLink onClick={() => handleDetailsClick(eventInfo.event)} />
                </Grid>
              )}
            {eventInfo?.event?.extendedProps?.process == "Planned" &&
              eventInfo?.event?.extendedProps?.strava_id && (
                <Grid item xs={12} md={3} sm={3}>
                  <GoUnlink
                    onClick={() =>
                      showCnfirmUnmerge(
                        eventInfo.event.id,
                        eventInfo?.event?.extendedProps?.strava_id
                      )
                    }
                  />
                </Grid>
              )}

            {eventInfo?.event?.borderColor != "green" && (
              <Grid item xs={12} md={3} sm={3}>
                <MdContentCopy
                  onClick={() =>
                    showConfirm(eventInfo.event.id, eventInfo.event.start)
                  }
                />
              </Grid>
            )}
            {eventInfo?.event?.borderColor != "green" && (
              <Grid item xs={12} md={3} sm={3}>
                <MdOutlineAutoDelete
                  onClick={() =>
                    showDeleteConfirm(
                      eventInfo.event.id,
                      eventInfo?.event?.extendedProps?.process
                    )
                  }
                />
              </Grid>
            )}
          </Grid> */}

        </div>
      </div>
    );
  };
  const handleEventDrop = async (eventInfo) => {
    console.log("eventInfo.event.id", eventInfo.event.id);
    const particularId = eventsData.filter(
      (event) => event.id == eventInfo.event.id
    );
    let hideStrava = particularId[0];
    const newStartDate = eventInfo.event.start;

    if (
      eventInfo?.event?.borderColor == "black" ||
      eventInfo?.event?.borderColor == "red" ||
      eventInfo?.event?.borderColor == "gray"
    ) {
      if (
        hideStrava?.process == "Strava" &&
        eventInfo?.event?.borderColor == "black"
      ) {
        sowWarn("Strava Workout Can't Drag  on another day");

        eventInfo.revert();
      } else {
        const response = await getParticularWorkoutDetails(
          eventInfo.event.id,
          assignedCocahId
        );
        const data = {
          ...response,
          workout_planned_for_date_time:
            moment(newStartDate).format("YYYY-MM-DD"),
        };
        if (response) {
          const result = await updateWorkoutsByDrag(
            data,
            eventInfo.event.id,
            assignedCocahId
          );

          if (result.status) {
            showSuccess(result.message);
            let resultCalender = "";
            if (assignedCocahId) {
              resultCalender = await getAllworkoutsDetails(
                assignedCocahId
              );
            } else {
              resultCalender = await getAllworkoutsDetails(assignedCocahId);
            }
            if (resultCalender.length > 0) {
              setAllevents(resultCalender);
            }
          } else {
            showSuccess(result.message);
          }
        }
      }
    } else if (eventInfo?.event?.borderColor == "green") {
      showInfo("Already Actual is updated you cannot update it");

      eventInfo.revert();
    } else if (eventInfo?.event?.borderColor == "orange") {
      showInfo("Already Actual is updated you cannot update it");
      eventInfo.revert();
    }
  };
  const handleCustomButtonClick = async () => {
    // Handle custom button click logic
    setworkoutformdrawerState(true);
    setWorkoutID("");
  };
  const importActivity = async () => {
    const response = await importStravaActivity(assignedCocahId);
    if (response.status) {
      showSuccess("Successfully Imported Please Sync with Strava");
    } else {
      if (response == "Bad Request") {
        showError("Please reconnect strava in your profile to continue");
      }
      else {
        showError(response?.message ? response?.message : response);
      }

    }
  };

  const syncActivity = async () => {
    const response = await syncStravaActivity(assignedCocahId);
    if (response.status) {
      Swal.fire({
        title: "Success",
        text: "Sync Completed!",
        icon: "success",
      });
      let result = "";
      if (assignedCocahId) {
        result = await getAllworkoutsDetails(assignedCocahId);

        setAllevents(result);
      } else {
        result = await getAllworkoutsDetails(assignedCocahId);
        setAllevents(result);
      }
    } else {
      Swal.fire({
        title: "Error!!",
        text: response,
        icon: "error",
      });
    }
  };
  const eventRender = ({ event, el }) => {
    if (eventsData.length > 1) {
      const moreLink = document.createElement("a");
      moreLink.innerHTML = `+${eventsData.length - 1} more`;
      moreLink.className = "more-link";
      moreLink.addEventListener("click", () => handleCustomButtonClick(event));
      el.appendChild(moreLink);
    }
  };
  const mergeWorkouts = async (id, stravaId) => {
    if (id) {
      let data = {
        // workout_id:id,
        process: "m",
        strava_activity_id: stravaId ? stravaId : selectedMergeId?.strava_id,
      };
      const response = await mergeWorkout(data, id);
      let result = "";
      if (assignedCocahId) {
        result = await getAllworkoutsDetails(assignedCocahId);

        setAllevents(result);
      } else {
        result = await getAllworkoutsDetails(assignedCocahId);
        setAllevents(result);
      }
      if (response?.status) {
        setOpenMergeModal(false);
        Swal.fire({
          title: "Success",
          text: response.message,
          icon: "success",
        });
      } else {
        Swal.fire({
          title: "Error",
          text: response.message,
          icon: "error",
        });
      }
    }
  };
  const unmergeWorkouts = async (id) => {
    if (id) {
      let data = {
        workout_id: id.id,
        process: "m",
        strava_activity_id: id.stravaID,
      };
      const response = await unMergedWorkouts(data);
      let result = "";
      if (assignedCocahId) {
        result = await getAllworkoutsDetails(assignedCocahId);

        setAllevents(result);
      } else {
        result = await getAllworkoutsDetails(assignedCocahId);
        setAllevents(result);
      }

      if (response?.status) {
        Swal.fire({
          title: "Success",
          text: response.message,
          icon: "success",
        });
      } else {
        Swal.fire({
          title: "Error",
          text: response.message,
          icon: "error",
        });
      }
    }
  };
  const syncCoahStravaActivity = async () => {
    const getProgram = await getPersonlisation(assignedCocahId);
    let userProgramID = localStorage.getItem('programID')

    const response = await syncStravaActivityCoach(assignedCocahId, userProgramID ? userProgramID : getProgram[0].program_id);

    if (response.status) {
      info(response.messages);
      let result = "";
      result = await getAllworkoutsDetails(assignedCocahId);
      setAllevents(result);
    } else {
      showError(response.messages ? response.messages : response);
    }
  };
  const info = (data) => {
    Modal.info({
      title: "Success",
      content: (
        <div>
          {data?.length > 0 && (
            <ul>
              {data?.map((ele, index) => {
                return <li>{ele}</li>;
              })}
            </ul>
          )}
        </div>
      ),
      onOk() { },
    });
  };
  const showConfirm = (id, date) => {
    confirm({
      title: "Do you Want to copy this item?",
      icon: <ExclamationCircleFilled />,
      onOk() {
        handleCopypaseWorkouts(id, date);
      },
      onCancel() {
        console.log("Cancel");
      },
    });
  };
  const handleCopypaseWorkouts = async (id, date) => {
    let response = await copyPasteWorkouts(
      id,
      moment(date).format("YYYY-MM-DD")
    );
    if (response?.status) {
      let result = "";
      if (assignedCocahId) {
        result = await getAllworkoutsDetails(assignedCocahId);
        setAllevents(result);
      } else {
        result = await getAllworkoutsDetails(assignedCocahId);
        setAllevents(result);
      }
    }
  };
  const renderDayCell = (arg) => {
    const date = new Date(arg.date);
    const currentMonth = date.getMonth() + 1;
    const dayOfWeek = date.getDay();

    let weekCount;
    let weekCountStart;

    if (dayOfWeek === 1 && Math.ceil((date.getDate() + 6)) < 31) {
      weekCount = Math.ceil((date.getDate() + 6) / 7);
    } else {
      weekCount = Math.ceil((date.getDate() + 6) / 7);
      weekCountStart = 1;
    }
    return (
      <div>
        <div className="flex justify-between">
          <div>
            <FaPlus onMouseDown={() => {
              setDate(date);
              setworkoutformdrawerState(true);
            }}
              onClick={() => {
                setDate(date);
                setworkoutformdrawerState(true);
              }}
              onTouchStart={() => {
                setDate(date);
                setworkoutformdrawerState(true);
              }} className="cursor-pointer text-[#E67E22]"
            />
          </div>
          <div>{arg.dayNumberText}</div>
        </div>
        {dayOfWeek === 1 ?
          <div className="widthSmaller fontSmaller" style={{ textDecoration: "none", "background": "#E67E22", "width": "100px", "border": "1px solid #E67E22", "borderRadius": "2px", "textAlign": "center", "color": "white" }}>Week  {dayOfWeek === 1 && Math.ceil((date.getDate() + 6)) > 31 ? weekCountStart : weekCount}</div> : <div style={{ color: "transparent", textDecoration: "none" }}>date</div>
        }
      </div>
    )
  };
  const dayHeaderContent = (arg) => {
    const { date } = arg;
    const dayOfWeek = date.getDay();

    // If it's Sunday (day === 0), modify the header content
    if (dayOfWeek === 0) {
      return (
        <span className="custom-header">
          <div style={{ display: "flex", justifyContent: "space-between" }}>
            <div> Sun {arg.dayNumberText}</div>
            <div>
              {openweeklySummary ? (
                <FaChevronLeft
                  onClick={() => {
                    setOpenWeeklySummary(!openweeklySummary);
                  }}
                  color="#E67E22"
                  className="cursor-pointer absolute top-2 right-[10px]"
                  style={{ fontSize: "25px" }}
                />
              ) : (
                <Tooltip title="Add" arrow>
                  <FaChevronRight
                    onClick={() => {
                      setOpenWeeklySummary(!openweeklySummary);
                    }}
                    color="#E67E22"
                    className="cursor-pointer absolute top-2 right-[10px]"
                    style={{ fontSize: "25px" }}
                  />
                </Tooltip>
              )}
            </div>
          </div>
        </span>
      );
    }

    return arg.dayNumberText; // Show regular day as header
  };
  const containerEl = document?.querySelector("#events");
  useEffect(() => {
    if (containerEl) {
      console.log("sadsavd>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>");
      new Draggable(containerEl, {
        itemSelector: ".eventworkout",
        eventData: (eventEl) => {
          return {
            i: eventEl.innerText,
          };
        },
      });
    }
  }, [containerEl]);
  const handleEventReceive = async (eventInfo) => {
    console.log("eventInfo", eventInfo?.event?.id);
    if (eventInfo.draggedEl) {
      let data1 = JSON.parse(eventInfo.draggedEl.getAttribute("workoutData"));
      console.log("data1", data1);
      if (!data1) {
        const newStartDate = eventInfo.event.start;
        console.error("Dragged element data is null");
        const response = await getParticularWorkoutDetails(
          eventInfo.event.id,
          assignedCocahId
        );
        const data = {
          ...response,
          workout_planned_for_date_time:
            moment(newStartDate).format("YYYY-MM-DD"),
        };
        if (response) {
          const result = await updateWorkoutsByDrag(
            data,
            eventInfo.event.id,
            assignedCocahId
          );

          if (result.status) {
            showSuccess(result.message);
            let resultCalender = "";
            if (assignedCocahId) {
              resultCalender = await getAllworkoutsDetails(
                assignedCocahId
              );
            } else {
              resultCalender = await getAllworkoutsDetails(assignedCocahId);
            }
            if (resultCalender.length > 0) {
              setAllevents(resultCalender);
            }
          } else {
            showSuccess(result.message);
          }
        }
        return;
      }
      let addWorkout = {
        ...data1?.workout_master,
        unit: "km",
        workout_planned_for_date_time: moment(eventInfo.event.start).format(
          "YYYY-MM-DD"
        ),
      };

      const Result = await createWorkouts(addWorkout, assignedCocahId);
      Swal.fire({
        title: "Success",
        text: Result.message,
        icon: "success",
      });
      getAllWorkouts();
      eventInfo.revert();
    }
  };

  useEffect(() => {
    // This effect will be triggered whenever eventsData changes
    getWeeklyDat(monthData);
  }, [eventsData]);

  useEffect(() => {
    // This effect will be triggered whenever eventsData changes
    getWeeklyDat(monthData);
  }, [eventsData]);
  useEffect(() => {
    if (draggedEvent?.id) {
      handleEventReceive();
    }
  }, [draggedEvent]);
  const [monthlyWeeklySummary, setWeeklySummary] = useState([]); // Initial year and month
  const [monthData, setMonthData] = useState({}); // Initial year and month
  const getWeeklyDat = async (data) => {
    setWeeklySummary([]);
    const response = await getWeeklySummary(data, assignedCocahId);
    if (response?.length > 0) {
      setWeeklySummary(response);
    }
  };
  const handleDatesSet = useMemo(
    () => (arg) => {
      // Format the month using 'MMMM' token
      let firstDay = moment(arg.start);
      let monthMiddle = firstDay.add(7, "days");
      const currentMonthName = monthMiddle.format("M");
      const currentYear = monthMiddle.format("YYYY");
      if (currentYear && currentMonthName) {
        let data = { year: currentYear, month: currentMonthName };
        getWeeklyDat(data);
        setMonthData(data);
      }
    },
    []
  );
  const PasteWeeklyWorkouts = async () => {
    let user_ID = localStorage.getItem("userId");

    let toyearData = selecedCopyWorkoutData?.weekrange[4]?.split("-")[0]
    let toMonthData = selecedCopyWorkoutData?.weekrange[4]?.split("-")[1]

    console.log("selecedCopyWorkoutData", toMonthData, toyearData, selecedCopyWorkoutData, selecedCopyWorkoutData?.weekrange[selecedCopyWorkoutData?.index]);

    let data = {
      ...CopyWorkoutData,
      pasteweekindex: selecedCopyWorkoutData?.index,
      toyear: toyearData,
      tomonth: toMonthData,
      userto: assignedCocahId ? assignedCocahId : user_ID
    };
    let response = await copyPasteweeklyWorkouts(data, assignedCocahId);
    if (response?.status) {
      Swal.fire({
        title: "Success",
        text: response.message,
        icon: "success",
      });
      setSelectedCopyWorkoutData();
      setCopyWorkoutData({});
      getWeeklyDat(monthData);
      // handleDatesSet()

      let result = "";
      result = await getAllworkoutsDetails(assignedCocahId);
      setAllevents(result);
      setAnchorEl(false);
    } else {
      Swal.fire({
        title: "Error!!",
        text: response,
        icon: "error",
      });
    }
  };
  const handleCopyWeeklyWorkout = () => {
    let user_ID = localStorage.getItem("userId");

    const dateObj = moment(selecedCopyWorkoutData.weekrange[3]);
    const month = dateObj.format("MM"); // Full month name
    const year = dateObj.format("YYYY");
    let data = {
      year: year,
      month: month,
      copyweekindex: selecedCopyWorkoutData?.index,
      userfrom: assignedCocahId ? assignedCocahId : user_ID
    };
    setCopyWorkoutData(data);
    setAnchorEl(false);
  };


  const openNewTabAutomationReport = async () => {
    // Specify the URL of the page you want to open
    let userProgramID = localStorage.getItem('programID')
    const response = await getPersonlisation(assignedCocahId);
    const updateWeekdays = await getUpdateAutomation(response && response[0].user_id, userProgramID ? userProgramID : response && response[0].program_id, weekNo)
    if (updateWeekdays?.status) {
      setIsModalOpen(false)
      setWeekNo("")
      showSuccess(capitalizeFirstLetter(updateWeekdays.messages[0]))
      const reportPageUrl = `https://test1.yoska.in/program/automationlogsv3/${response && response[0].user_id}/${weekNo}/${userProgramID ? userProgramID : response && response[0].program_id}`;
      window.open(reportPageUrl, '_blank');

    }
  }
  const handleOpenAutomation = () => {
    setIsModalOpen(true)
  }
  const [initialView, setInitialView] = useState("MyMonthView");

  const handleViewChange = (newView) => {
    setHeaderToolbar((prevToolbar) => ({
      ...prevToolbar,
      right: ` customButton2 ${newView}`,
    }));
    setInitialView(newView);
  };

  useEffect(() => {
    const buttons = document.querySelectorAll('.fc-button');

    buttons.forEach(button => {
      const buttonText = button.getAttribute('data-nav');
      if (buttonText === initialView) {
        button.classList.add('active');
      } else {
        button.classList.remove('active');
      }
    });
  }, [initialView]);

  return (
    <>
      <div className="demo-app-main " style={{ width: "100%" }}>
        <div className="relative">
          {isOpenSideBar && (
            <div
              className="cursor-pointer top36px"
              style={{
                fontSize: "30px",
                background: "transparent !important",
                border: "none",
                position: "absolute",
                top: "24px",
                left: "10px",
              }}
              onClick={() => setIsOpenSideBar(!isOpenSideBar)}
            >
              <GiHamburgerMenu color="darkgray" />
            </div>
          )}

          <Grid container >
            <Grid item xs={12} className={(openweeklySummary && roleID == 5) ? "athleteLogin" : ""} sm={openweeklySummary ? 10 : 12} >
              <FullCalendar
                plugins={[
                  dayGridPlugin,
                  timeGridPlugin,
                  interactionPlugin,
                  listPlugin,
                ]}
                // allDaySlot={false}
                scrollTime={"00:00:00"}
                datesSet={handleDatesSet}
                // ref={calendarRef}
                className="custom-calendar"
                eventRender={eventRender}
                eventDrop={handleEventDrop}
                eventContent={eventContent}
                headerToolbar={headerToolbar}
                dayCellContent={renderDayCell}
                dayHeaderContent={dayHeaderContent}
                initialView={initialView}
                // weekNumbers="true"
                views={views}
                editable={true}
                // drop={handleDrop}
                selectable={true}
                selectMirror={true}
                dayMaxEvents={false}
                weekends={true}
                firstDay={1}
                // initialEvents={events}
                // droppable={true}
                eventReceive={handleEventReceive}
                events={eventsData}
                // select={() => setworkoutformdrawerState(true)}
                // eventContent={renderEventContent} // custom render function
                // eventChange={handleEventChange}
                // eventsSet={handleEvents} // called after events are initialized/added/changed/removed
                /* you can update a remote database when these fire:
            eventAdd={function(){}}
            eventChange={function(){}}
            eventRemove={function(){}}
            */
                customButtons={{
                  // : {
                  //   text: "Import Strava Activity",
                  //   click: importActivity,
                  // },
                  customButton2: {
                    text: "Sync",
                    click: syncActivity,
                  },
                  customButton3: {
                    text: "+ Add Workout",
                    click: handleCustomButtonClick,
                  },
                  customButton4: {
                    text: "Personalized Workouts",
                    click: syncCoahStravaActivity,
                  },
                  customButton6: {
                    text: ' Automation Report',
                    click: handleOpenAutomation,
                  },

                  customButton5: {
                    text: isCheckedOpen ? "Delete" : "Delete Workouts",
                    click: isCheckedOpen
                      ? showDeleteAllConfirm
                      : showDeleteAllButtonCheckbox,
                  },
                  customButton7: {
                    text: 'Back',
                    click: CheckBackDeleteFunction,
                  },

                }}
                titleFormat={{
                  month: "short",
                  year: "numeric",
                  day: "numeric",
                }}
              />
            </Grid>
            {openweeklySummary && (
              <Grid item xs={12} sm={2} sx={{ height: "90vh", overflow: "scroll" }}>
                <div className="weeklySummary pl-2 fontSmaller"
                  style={{
                    border: "1px solid rgb(229, 231, 235)",
                    marginTop: "122px",
                    height: "42px",
                    paddingTop: "5px",
                    fontWeight: "600",
                    color: "gray",
                    backgroundColor: "#fcfaff"
                  }}
                >
                  Weekly Summary
                </div>
                <div style={{
                  overflowY: "auto",
                  // maxHeight: "90vh",
                  scrollbarWidth: "thin",
                  // width:"109%"

                }}>
                  {monthlyWeeklySummary?.map((summary, index) => {
                    return (
                      <div
                        style={
                          summary?.maxEventsOnDay > 0
                            ? {
                              fontSize: "12px",
                              minHeight: "114px",
                              border: "1px solid rgb(229, 231, 235)",
                              position: "relative",
                              padding: '8px'
                            }
                            : {
                              minHeight: "114px",
                              border: "1px solid rgb(229, 231, 235)",
                              fontSize: "12px",
                              position: "relative",
                              padding: '8px'

                            }
                        }
                      >
                        <div
                          className={
                            (summary?.maxEventsOnDay <= 0 && "eventon-day") ||
                            (summary?.maxEventsOnDay === 1 && "eventon-day1") ||
                            (summary?.maxEventsOnDay === 2 && "eventon-day2") ||
                            (summary?.maxEventsOnDay === 3 && "eventon-day3") ||
                            (summary?.maxEventsOnDay === 4 && "eventon-day4")
                          }
                          style={{

                            position: "relative",
                            backgroundColor: "#fcfaff",
                          }}
                        >
                          <div className="p-[1px]">
                            <div style={{ display: "flex", justifyContent: "space-between" }}>
                              <div style={{ "background": "#E67E22", "width": "70px", "border": "1px solid #E67E22", "borderRadius": "2px", "textAlign": "center", "color": "white", marginBottom: "10px" }} ><b>Week {index + 1}</b></div>
                              <HiDotsVertical color="#E67E22" onClick={(event) => {
                                if (summary?.maxEventsOnDay > 0) {
                                  handleClick(event, summary, index)
                                }
                                else if (CopyWorkoutData?.year) {
                                  handleClick(event, summary, index)
                                }
                              }}
                                className='cursor-pointer' />
                            </div>
                            <p style={{ fontWeight: "500" }}>Running Distance:</p>
                            <WorkoutProgress
                              type={"Km"}
                              completed={summary?.totalrunningdone}
                              total={summary?.totalrunninggiven}
                            />
                            <div
                              style={
                                summary?.totalrunningdone != 0
                                  ? { marginBottom: "-10px", marginTop: "-10px" }
                                  : {}
                              }
                            >
                              <p className="runningDurationMargin" style={{ fontWeight: "500", marginTop: "8px" }}>Running Duration:</p>
                            </div>
                            <WorkoutProgress
                              completed={summary?.runningdurationdone}
                              total={summary?.runningdurationgiven}
                            />
                          </div>

                          <div>
                            <p style={{ fontWeight: "500" }}>Cycling Distance:</p>
                            <WorkoutProgress
                              type={"Km"}
                              completed={summary?.totalcyclingdone}
                              total={summary?.totalcyclinggiven}
                            />
                            <p style={{ fontWeight: "500" }}>Cycling Duration:</p>
                            <WorkoutProgress
                              completed={summary?.cyclingdurationdone}
                              total={summary?.cyclingdurationgiven}
                            />
                          </div>

                          <div>
                            <p style={{ fontWeight: "500" }}>Swimming Distance:</p>
                            <WorkoutProgress
                              type={"M"}
                              completed={summary?.totalswimmingdone}
                              total={summary?.totalswimminggiven}
                            />
                            <p style={{ fontWeight: "500" }}>Swimming Duration:</p>

                            <WorkoutProgress
                              completed={summary?.swimmingdurationdone}
                              total={summary?.swimmingdurationgive}
                            />
                          </div>{" "}

                          {/*
                       <div>
                        Swimming Distance:
                        <WorkoutProgress type={"M"} completed={summary?.totalswimmingdone} total={summary?.totalswimminggiven } />
                        <div>
                        Swimming Duration:
                        </div>
                        <WorkoutProgress completed={summary?.swimmingdurationdone} total={summary?.swimmingdurationgiven} />
                      </div>
                      */}
                        </div>
                        <Menu
                          id="basic-menu"
                          anchorEl={anchorEl}
                          open={open}
                          onClose={() => setAnchorEl(false)}
                          MenuListProps={{
                            "aria-labelledby": "basic-button",
                          }}
                        >
                          {selecedCopyWorkoutData?.maxEventsOnDay > 0 && (
                            <MenuItem onClick={() => handleCopyWeeklyWorkout()}>
                              Copy Weekly Workout
                            </MenuItem>
                          )}
                          {CopyWorkoutData?.year && (
                            <MenuItem onClick={PasteWeeklyWorkouts}>
                              Paste Weekly Workout
                            </MenuItem>
                          )}
                        </Menu>
                      </div>
                    );
                  })}
                </div>
              </Grid>
            )}
          </Grid>
        </div>

        {openMergeModal && (
          <CalenderModal
            mergeWorkouts={mergeWorkouts}
            setOpenMergeModal={setOpenMergeModal}
            openMergeModal={openMergeModal}
            selectedMergeId={selectedMergeId}
            mergeModalData={mergeModalData}
          />
        )}
        {workoutformdrawerState && (
          <WorkoutForm
            setCalendarModal={setCalendarModal}
            setIsZoneOpen={setIsZoneOpen}
            dateForworkout={dateForworkout}
            workoutID={workoutID}
            setWorkoutID={setWorkoutID}
            getAllWorkouts={getAllWorkouts}
            setworkoutformdrawerState={setworkoutformdrawerState}
            workoutformdrawerState={workoutformdrawerState}
            assignedCocahId={assignedCocahId}
          />
        )}
        {openBox && (
          <SimpleDialog
            selectedValue={selectedValue}
            open={openBox}
            onClose={setOpenBox
            }
            assignedCocahId={assignedCocahId}

          />
        )}

        {isOpenCommentBox && (
          <CommentBox
            selectedValue={selectedValue}
            open={isOpenCommentBox}
            onClose={setOpenCommentBox}
          />
        )}
      </div>

      <Modal
        title="Add Week"
        open={isModalOpen}
        onOk={openNewTabAutomationReport}
        onCancel={() => setIsModalOpen(false)}
      >
        <FormLabel>Week:</FormLabel>
        <TextField
          fullWidth
          size="small"
          type="number"
          name="activity_id"
          value={weekNo}
          onChange={(e) => setWeekNo(e.target.value)}
          id="form-layouts-separator-select"
          labelId="form-layouts-separator-select-label"
        >

        </TextField>
      </Modal>
    </>
  );
}

export default WorkoutCalender;
