.login-main {
  width: 100%;
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
}

.login-section {
  border: solid 1px lightgray;
  padding: 3% 2%;
  width: 30%;
  border-radius: 10px;
}

.login-section .login-header {
  color: #1e40af;
  text-align: center;
}

.login-form-group {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 10px;
  margin-top: 2%;
}

.login-lable {
  color: #1e40af;
}

.login-input {
  border: solid 1px lightgray !important;
  height: 30px;
  border-radius: 10px;
}

.login-footer-section {
  display: flex;
  justify-content: space-between;
  width: 100%;
  margin-top: 2%;
}

.login-footer-section .login-checkbox {
  display: flex;
  gap: 5px;
}

.login-btn-section {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 5%;
}

.login-btn-section .login-btn {
  width: 100%;
  border: solid 1px #1e40af;
  border-radius: 10px;
  padding: 10px;
  color: #1e40af;
  font-size: 16px;
  background: #ffffff;
}

.login-btn-section .login-btn:hover {
  background: #1e40af;
  color: #ffffff;
}

/* ======================================= login with google btn ==================================== */

.login-with-google-btn {
  transition: background-color 0.3s, box-shadow 0.3s;
  padding: 12px 16px 12px 42px;
  width: 100%;
  border: none;
  border-radius: 3px;
  box-shadow: 0 -1px 0 rgba(0, 0, 0, 0.04), 0 1px 1px rgba(0, 0, 0, 0.25);
  color: #757575;
  font-size: 14px;
  font-weight: 500;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen,
    Ubuntu, Cantarell, "Fira Sans", "Droid Sans", "Helvetica Neue", sans-serif;
  background-image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTgiIGhlaWdodD0iMTgiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGcgZmlsbD0ibm9uZSIgZmlsbC1ydWxlPSJldmVub2RkIj48cGF0aCBkPSJNMTcuNiA5LjJsLS4xLTEuOEg5djMuNGg0LjhDMTMuNiAxMiAxMyAxMyAxMiAxMy42djIuMmgzYTguOCA4LjggMCAwIDAgMi42LTYuNnoiIGZpbGw9IiM0Mjg1RjQiIGZpbGwtcnVsZT0ibm9uemVybyIvPjxwYXRoIGQ9Ik05IDE4YzIuNCAwIDQuNS0uOCA2LTIuMmwtMy0yLjJhNS40IDUuNCAwIDAgMS04LTIuOUgxVjEzYTkgOSAwIDAgMCA4IDV6IiBmaWxsPSIjMzRBODUzIiBmaWxsLXJ1bGU9Im5vbnplcm8iLz48cGF0aCBkPSJNNCAxMC43YTUuNCA1LjQgMCAwIDEgMC0zLjRWNUgxYTkgOSAwIDAgMCAwIDhsMy0yLjN6IiBmaWxsPSIjRkJCQzA1IiBmaWxsLXJ1bGU9Im5vbnplcm8iLz48cGF0aCBkPSJNOSAzLjZjMS4zIDAgMi41LjQgMy40IDEuM0wxNSAyLjNBOSA5IDAgMCAwIDEgNWwzIDIuNGE1LjQgNS40IDAgMCAxIDUtMy43eiIgZmlsbD0iI0VBNDMzNSIgZmlsbC1ydWxlPSJub256ZXJvIi8+PHBhdGggZD0iTTAgMGgxOHYxOEgweiIvPjwvZz48L3N2Zz4=);
  background-color: white;
  background-repeat: no-repeat;
  background-position: 12px 11px;
}
.login-with-google-btn:hover {
  box-shadow: 0 -1px 0 rgba(0, 0, 0, 0.04), 0 2px 4px rgba(0, 0, 0, 0.25);
}
.login-with-google-btn:active {
  background-color: #eeeeee;
}
.login-with-google-btn:focus {
  outline: none;
  box-shadow: 0 -1px 0 rgba(0, 0, 0, 0.04), 0 2px 4px rgba(0, 0, 0, 0.25),
    0 0 0 3px #c8dafc;
}
.login-with-google-btn:disabled {
  filter: grayscale(100%);
  background-color: #ebebeb;
  box-shadow: 0 -1px 0 rgba(0, 0, 0, 0.04), 0 1px 1px rgba(0, 0, 0, 0.25);
  cursor: not-allowed;
}

.strava-btn-main {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 20px;
}

.strava-btn {
  background: #fc4c02;
  color: #ffffff;
  border: none;
  padding: 10px 20px;
  font-size: 14px;
  border-radius: 10px;
}

.strava-btn:hover {
  background: lightgray;
  color: #000;
}

@media only screen and (max-width: 768px) {
  .login-section {
    width: 60%;
  }
}

@media only screen and (max-width: 480px) {
  .login-section {
    width: 80%;
    padding: 8% 3%;
  }
}

/* ====================================== flow =================================== */
.running-step-input{
  width: 100%;
  padding: 5px;
  border: solid 1px gray;
  border-radius: 5px;
}

.flow-flex-input{
  width: 100%;
  display: flex;
  gap: 20px;
}

.flow-running-form{
  height: 100%;
}
.promo{
  width: 97%;
}

@media only screen and (max-width: 768px) {
  .flow-flex-input{
    width: 90%;
    display: flex;
    flex-direction: column;
    gap: 10px;
  }
  
  .flow-running-form{
    max-height: 400px;
    /* overflow-y: scroll; */
    width: 80%;
    margin-top: 10px;
  }
  .promo{
    margin-left: 3%;
    width: 90%;
  }
}


.flow-section{
  width: 100%;
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center ;
}

::-webkit-scrollbar {
  width: 5px;
}

/* Track */
::-webkit-scrollbar-track {
  box-shadow: inset 0 0 2px #1e40af; 
  border-radius: 10px;
}
 
/* Handle */
::-webkit-scrollbar-thumb {
  background: #1e40af; 
  border-radius: 10px;
}