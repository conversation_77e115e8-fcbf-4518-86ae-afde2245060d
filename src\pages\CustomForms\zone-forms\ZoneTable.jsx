import * as React from "react";
import { styled } from "@mui/material/styles";
import Table from "@mui/material/Table";
import TableBody from "@mui/material/TableBody";
import TableCell, { tableCellClasses } from "@mui/material/TableCell";
import TableContainer from "@mui/material/TableContainer";
import TableHead from "@mui/material/TableHead";
import TableRow from "@mui/material/TableRow";
import Paper from "@mui/material/Paper";

const StyledTableCell = styled(TableCell)(({ theme }) => ({
  [`&.${tableCellClasses.head}`]: {
    backgroundColor: "#1e40af",
    color: theme.palette.common.white,
  },
  [`&.${tableCellClasses.body}`]: {
    fontSize: 14,
  },
}));

const StyledTableRow = styled(TableRow)(({ theme }) => ({
  "&:nth-of-type(odd)": {
    backgroundColor: theme.palette.action.hover,
  },
  // hide last border
  "&:last-child td, &:last-child th": {
    border: 0,
  },
}));

// const rows = [
//   { name: " yoghurt", calories: 159, fat: 6.0, carbs: 24 },
//   { name: "Ice cream sandwich", calories: 237, fat: 9.0, carbs: 37 },
//   { name: "Eclair", calories: 262, fat: 16.0, carbs: 24 },
//   { name: "Cupcake", calories: 305, fat: 3.7, carbs: 67 },
//   { name: "Gingerbread", calories: 356, fat: 16.0, carbs: 49 },
// ];

export default function ZoneTable({rows,title,isShowLogHedaers,isSpeed}) {
  return (
    <TableContainer component={Paper}>
      <div style={{ fontSize: "18px", background: "#FFEADC", width: "100%" }}>
        <h3 style={{ padding: "10px",margin:"0" }}>{title}</h3>
      </div>
      <Table  aria-label="customized table">
        <TableHead>
        {isShowLogHedaers ?(
          <TableRow>
           
            <StyledTableCell align="left">Activity</StyledTableCell>
            <StyledTableCell align="left">Distance</StyledTableCell>
            <StyledTableCell align="left">Duration</StyledTableCell>
            <StyledTableCell align="left">Date</StyledTableCell>
            <StyledTableCell align="left">Heart Rate</StyledTableCell>
            <StyledTableCell align="left">Power</StyledTableCell>
            <StyledTableCell align="left">Fitness Level</StyledTableCell>
            <StyledTableCell align="left">{isSpeed?'Speed':'Pace'}</StyledTableCell>

            

            

          </TableRow>
        ):(
          <TableRow>
          {/*
        <StyledTableCell align="left">Select</StyledTableCell>
        */}
          <StyledTableCell align="left">Zone Number</StyledTableCell>
          <StyledTableCell align="left">Zone Names</StyledTableCell>
          <StyledTableCell align="left">Range</StyledTableCell>
        </TableRow>
        )}
          
        </TableHead>
       {rows}
      </Table>
    </TableContainer>
  );
}
