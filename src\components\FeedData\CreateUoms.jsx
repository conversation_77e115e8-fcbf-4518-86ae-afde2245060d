import {
    <PERSON><PERSON>abel,
    <PERSON>rid,
    MenuItem,
    OutlinedInput,
    TextField,
  } from "@mui/material";
  import { <PERSON><PERSON>, Modal, TimePicker } from "antd";
  import React, { useEffect, useState } from "react";
  import {
    createuomsdata,
    getAllActivityData,
    updateuomsdata,
    uploadsaveFile,
  } from "../../API/api-endpoint";
  import { useFormik } from "formik";
  import Swal from "sweetalert2";
  import SlickCarousel from "../../pages/SlickCarousel";
  const CreateUoms = ({
    fetchReport,
    setShowAssesmentModal,
    showAssesmentModal,
    editData,
    setEditData,
  }) => {
    console.log("editData", editData);
    const formik = useFormik({
      initialValues: {
        uom_name: "",
        status: true,
      },
      validate: (values) => {
        const errors = {};
        if (!values.uom_name) {
          errors.uom_name = "Name is required";
        }
  
        return errors;
      },
      // validationSchema: {},
      onSubmit: (values, { resetForm }) => {
        handleSubmitAssesmentForm(values, resetForm);
      },
    });
    console.log("formik", formik?.values, formik?.errors);
  
    const handleSubmitAssesmentForm = async (data, resetForm) => {
      let response = "";
      if (editData?.uom_id) {
        response = await updateuomsdata(data);
      } else {
        response = await createuomsdata(data);
      }
      if (response?.status) {
        Swal.fire({
          title: "Success",
          text: response.message,
          icon: "success",
        });
        setEditData({});
        setShowAssesmentModal(false);
        fetchReport();
        formik.resetForm();
        formik?.setValues({
          uom_name: "",
          status: true,
        });
      } else {
        Swal.fire({
          title: "Error",
          text: response.message,
          icon: "error",
        });
      }
      console.log("response", response);
    };
    useEffect(() => {
      if (editData?.uom_id) {
        const { srID, createdAt, updatedAt, ...data } = editData;
        formik?.setValues(data);
      }
    }, [editData?.uom_id]);
  
    return (
      <Modal
        width={1200}
        open={showAssesmentModal}
        onCancel={() => {
          setShowAssesmentModal(false);
          formik.resetForm();
          setEditData({});
          formik?.setValues({
            uom_name: "",
            status: true,
          });
        }}
        footer={
          <div></div>
          //   loading={isLoading}
        }
      >
        <div className="headingCont">
          <span className="heading">{editData?.uom_id ? "Edit " : "Create"}</span>{" "}
          <span className="orange heading">Uoms</span>
        </div>
        {/* <h1>{editData ? editData.challengeId : values.challengeId}</h1> */}
        <div className="parentCont">
          <form className="form1" onSubmit={formik.handleSubmit}>
            <Grid container spacing={2}>
              <Grid item xs={12} sm={11}>
                <FormLabel>Name<span className="text-[red]">*</span></FormLabel>
  
                <TextField
                  fullWidth
                  placeholder="Name"
                  size="small"
                  type="text"
                  name="uom_name"
                  value={formik?.values?.uom_name}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  error={formik.touched.uom_name && formik.errors.uom_name}
                  helperText={formik.touched.uom_name && formik.errors.uom_name}
                />
              </Grid>
              <Grid item xs={12} sm={11}>
                <FormLabel>Status</FormLabel>
  
                <TextField
                  fullWidth
                  size="small"
                  select
                  name="status"
                  value={formik?.values?.status}
                  onChange={formik.handleChange}
                  error={formik.touched.status && formik.errors.status}
                  helperText={formik.touched.status && formik.errors.status}
                  id="form-layouts-separator-select"
                  labelId="form-layouts-separator-select-label"
                  input={<OutlinedInput id="select-multiple-language" />}
                >
                  <MenuItem value={true}>Yes</MenuItem>
                  <MenuItem value={"false"}>No</MenuItem>
                </TextField>
              </Grid>
  
              <Grid item xs={12} sm={6}>
                <Button
                  className="btn"
                  key="submit"
                  type="primary"
                  onClick={() => formik.handleSubmit()}
                >
                  Submit
                </Button>
              </Grid>
            </Grid>
          </form>
          <div className="slick-container">
            <SlickCarousel />
          </div>
        </div>
      </Modal>
    );
  };
  export default CreateUoms;
  