import { <PERSON><PERSON>, But<PERSON> } from "antd";
import {
  FormLabel,
  Grid,
  TextField
} from "@mui/material";
import SlickCarousel from "../.././SlickCarousel";


const createChallengeLevel = ({handleCancel, isLoading,  isModalOpen, setTarget, initialTargetData, target, handleSubmit, handleInputChange}) => {
    return(
        <Modal
            width={600}
            open={isModalOpen}
            onCancel={() => {
              handleCancel();
              setTarget(initialTargetData);
            }}
            footer={null}
          >
            <div className="headingCont paddingBot">
              <span className="heading">{target?.id ? "Edit " : "Create"}</span>
              <span className="orange heading"> Challenge Target</span>
              <p className="grey">Set up a new target for fitness challenge</p>
            </div>
            <div className="parentCont">
              <form className="form1" onSubmit={handleSubmit}>
                <Grid container spacing={1}>
                  <Grid container spacing={3} className="marbot">
                    <Grid item xs={12} sm={12}>
                      <FormLabel>
                        Name<span className="text-[red]">*</span>
                      </FormLabel>
                      <TextField
                        fullWidth
                        placeholder="Target Name"
                        size="small"
                        type="text"
                        name="name"
                        value={target?.name}
                        onChange={handleInputChange}
                      />
                    </Grid>
                  </Grid>
                  <Grid container spacing={3} className="marbot">
                    <Grid item xs={12} sm={5.5} className="spcl">
                      <Button className="btn" htmlType="submit" loading={isLoading} style={{
                            boxShadow: "0px 4px 10px rgba(0, 0, 0, 0.25)",
                        }}>
                      <span style={{ color: '#fff' }}>Submit</span>
                      </Button>
                    </Grid>
                  </Grid>
                </Grid>
              </form>
              <div className="slick-container">
                <SlickCarousel />
              </div>
            </div>
          </Modal>
    )
}

export default createChallengeLevel;