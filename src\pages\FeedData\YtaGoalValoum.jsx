import { useEffect, useState, use<PERSON>emo, useCallback } from "react";
import { <PERSON><PERSON> } from "../../components/ui/button";
import { Input } from "../../components/ui/input";
import { Card, CardContent, CardHeader } from "../../components/ui/card";
import {
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableHeader,
	TableRow,
} from "../../components/ui/table";
import { Edit, Trash2, ToggleLeft, ToggleRight, Plus } from "lucide-react";
import { YtaGoalVolumeDialog } from "../../components/admin/yta-goal-volume-dialog";
import { YtaGoalDialog } from "../../components/admin/yta-goal-dialog";
import { DeleteConfirmDialog } from "../../components/admin/delete-confirm-dialog";
import Header from "../../components/Header";
import {
	getAllYTAVolumeData,
	getAllYTAData,
	deleteYTAVolumeData,
	deleteYTAData,
} from "../../API/api-endpoint";
import Swal from "sweetalert2";

const YtaGoalValoum = () => {
	const [ytaData, setYtaData] = useState([]);
	const [isLoading, setIsLoading] = useState(true);
	const [showDialog, setShowDialog] = useState(false);
	const [editingItem, setEditingItem] = useState(null);
	const [deleteId, setDeleteId] = useState(null);
	const [searchTerm, setSearchTerm] = useState("");
	const [currentPage, setCurrentPage] = useState(1);
	const [isVolumeView, setIsVolumeView] = useState(true);
	const pageSize = 10;
	const [goalLookup, setGoalLookup] = useState({});

	const fetchData = useCallback(async () => {
		try {
			setIsLoading(true);
			const response = isVolumeView
				? await getAllYTAVolumeData()
				: await getAllYTAData();
			if (isVolumeView && response) {
				try {
					const goalsResponse = await getAllYTAData();
					const lookup = {};
					if (goalsResponse && Array.isArray(goalsResponse)) {
						goalsResponse.forEach((goal) => {
							lookup[goal.id] = goal.goalname;
						});
					}
					setGoalLookup(lookup);
				} catch (goalError) {
					console.error(
						"Error fetching goals for lookup:",
						goalError
					);
				}
			}

			if (response) {
				setYtaData(response || []);
			} else {
				console.log("Response status is false or missing");
				setYtaData([]);
			}
		} catch (error) {
			console.error("Error fetching YTA data:", error);
			Swal.fire({
				title: "Error",
				text: "Failed to fetch data. Please try again.",
				icon: "error",
				timer: 3000,
				showConfirmButton: false,
			});
			setYtaData([]);
		} finally {
			setIsLoading(false);
		}
	}, [isVolumeView]);

	const filteredData = useMemo(() => {
		if (!searchTerm.trim()) return ytaData;

		return ytaData.filter((item) => {
			if (isVolumeView) {
				const goalName =
					goalLookup[item?.goalname_id] ||
					goalLookup[item?.ytagoal_id] ||
					"";
				return (
					goalName
						?.toLowerCase()
						.includes(searchTerm.toLowerCase()) ||
					item?.running
						?.toString()
						.toLowerCase()
						.includes(searchTerm.toLowerCase()) ||
					item?.cycling
						?.toString()
						.toLowerCase()
						.includes(searchTerm.toLowerCase()) ||
					item?.swimming
						?.toString()
						.toLowerCase()
						.includes(searchTerm.toLowerCase())
				);
			} else {
				return (
					item?.goalname
						?.toLowerCase()
						.includes(searchTerm.toLowerCase()) ||
					item?.goaltable
						?.toLowerCase()
						.includes(searchTerm.toLowerCase()) ||
					item?.phase
						?.toLowerCase()
						.includes(searchTerm.toLowerCase())
				);
			}
		});
	}, [ytaData, searchTerm, isVolumeView, goalLookup]);

	const paginatedData = useMemo(() => {
		const startIndex = (currentPage - 1) * pageSize;
		const endIndex = startIndex + pageSize;
		return filteredData.slice(startIndex, endIndex);
	}, [filteredData, currentPage, pageSize]);

	const totalPages = Math.ceil(filteredData.length / pageSize);

	const handlePageChange = (page) => {
		setCurrentPage(page);
	};

	const handleViewToggle = () => {
		setIsVolumeView(!isVolumeView);
		setCurrentPage(1);
		setSearchTerm("");
		setEditingItem(null);
	};

	const handleCreate = () => {
		setEditingItem(null);
		setShowDialog(true);
	};

	const handleEdit = (item) => {
		setEditingItem(item);
		setShowDialog(true);
	};

	const handleDelete = async (id) => {
		try {
			const response = isVolumeView
				? await deleteYTAVolumeData(id)
				: await deleteYTAData(id);
			const isSuccess =
				response === true ||
				response?.status === true ||
				response === "Delete Successfully" ||
				(typeof response === "string" &&
					response.toLowerCase().includes("success")) ||
				(typeof response === "object" &&
					response !== null &&
					response.id);

			if (isSuccess) {
				Swal.fire({
					title: "Success",
					text: "Item deleted successfully",
					icon: "success",
					timer: 2000,
					showConfirmButton: false,
				});
				fetchData();
				setCurrentPage(1);
			} else {
				console.log(
					"🗑️ DELETE DEBUG - Delete failed, response status is false"
				);
				throw new Error(response?.message || "Failed to delete item");
			}
		} catch (error) {
			Swal.fire({
				title: "Error",
				text:
					error.message || "Failed to delete item. Please try again.",
				icon: "error",
				timer: 3000,
				showConfirmButton: false,
			});
		}
		setDeleteId(null);
	};

	const handleDialogClose = () => {
		setShowDialog(false);
		setEditingItem(null);
	};

	const handleSuccess = () => {
		fetchData();
		handleDialogClose();
		setCurrentPage(1);
	};

	useEffect(() => {
		fetchData();
	}, [fetchData]);

	useEffect(() => {
		setCurrentPage(1);
	}, [searchTerm]);

	return (
		<div>
			<Header />
			<div className='mx-auto p-6 max-w-[1300px] mt-16'>
				<Card>
					<CardHeader className='bg-orange-50 border-b flex flex-col gap-4'>
						<div>
							<h1 className='text-2xl font-bold text-orange-900'>
								{isVolumeView ? "YTA Goal Volume" : "YTA Goal"}
							</h1>
							<p className='text-orange-700 mt-1 text-sm'>
								{isVolumeView
									? "Manage YTA goal volumes with running, cycling, and swimming data"
									: "Manage YTA goals with goal names, tables, and phases"}
							</p>
						</div>
						<div className='flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4'>
							<div className='flex flex-wrap gap-3'>
								<Button
									onClick={handleViewToggle}
									variant='outline'
									className='flex items-center gap-2 border-orange-300 text-orange-700 hover:bg-orange-100'
								>
									{isVolumeView ? (
										<ToggleLeft className='h-4 w-4' />
									) : (
										<ToggleRight className='h-4 w-4' />
									)}
									{isVolumeView
										? "Switch to Goals"
										: "Switch to Volume"}
								</Button>
								<Button
									onClick={handleCreate}
									className='bg-orange-600 hover:bg-orange-700 text-white'
								>
									<Plus className='h-4 w-4 mr-2' />
									Create {isVolumeView ? "Volume" : "Goal"}
								</Button>
							</div>

							<div className='flex gap-3'>
								<Input
									placeholder={`Search ${
										isVolumeView ? "volumes" : "goals"
									}...`}
									value={searchTerm}
									onChange={(e) =>
										setSearchTerm(e.target.value)
									}
									className='w-full text-sm'
								/>
							</div>
						</div>
					</CardHeader>
					<CardContent className='p-0'>
						<div className='overflow-x-auto'>
							<Table className='min-w-full w-full'>
								<TableHeader>
									<TableRow className='bg-blue-600 hover:bg-blue-600'>
										<TableHead className='text-white font-semibold'>
											Sr No
										</TableHead>
										{isVolumeView ? (
											<>
												<TableHead className='text-white font-semibold'>
													Goal Name
												</TableHead>
												<TableHead className='text-white font-semibold'>
													Running
												</TableHead>
												<TableHead className='text-white font-semibold'>
													Cycling
												</TableHead>
												<TableHead className='text-white font-semibold'>
													Swimming
												</TableHead>
											</>
										) : (
											<>
												<TableHead className='text-white font-semibold'>
													Goal Name
												</TableHead>
												<TableHead className='text-white font-semibold'>
													Goal Table
												</TableHead>
												<TableHead className='text-white font-semibold'>
													Phase
												</TableHead>
											</>
										)}
										<TableHead className='text-white font-semibold'>
											Actions
										</TableHead>
									</TableRow>
								</TableHeader>
								<TableBody>
									{isLoading ? (
										<TableRow>
											<TableCell
												colSpan={isVolumeView ? 6 : 5}
												className='text-center py-8'
											>
												Loading...
											</TableCell>
										</TableRow>
									) : paginatedData.length > 0 ? (
										paginatedData.map((item, index) => (
											<TableRow
												key={item.id}
												className='hover:bg-gray-50'
											>
												<TableCell className='font-medium'>
													{(currentPage - 1) *
														pageSize +
														index +
														1}
												</TableCell>
												{isVolumeView ? (
													<>
														<TableCell>
															{goalLookup[
																item
																	?.goalname_id
															] ||
																goalLookup[
																	item
																		?.ytagoal_id
																] ||
																"N/A"}
														</TableCell>
														<TableCell>
															{item?.running ||
																"N/A"}
														</TableCell>
														<TableCell>
															{item?.cycling ||
																"N/A"}
														</TableCell>
														<TableCell>
															{item?.swimming ||
																"N/A"}
														</TableCell>
													</>
												) : (
													<>
														<TableCell>
															{item?.goalname ||
																"N/A"}
														</TableCell>
														<TableCell>
															{item?.goaltable ||
																"N/A"}
														</TableCell>
														<TableCell>
															{item?.phase ||
																"N/A"}
														</TableCell>
													</>
												)}
												<TableCell>
													<div className='flex gap-2'>
														<Button
															variant='ghost'
															size='sm'
															onClick={() =>
																handleEdit(item)
															}
															className='text-blue-600 hover:text-blue-800'
														>
															<Edit className='h-4 w-4' />
														</Button>
														<Button
															variant='ghost'
															size='sm'
															onClick={() =>
																setDeleteId(
																	item.id
																)
															}
															className='text-red-700 hover:text-red-800'
														>
															<Trash2 className='h-4 w-4' />
														</Button>
													</div>
												</TableCell>
											</TableRow>
										))
									) : (
										<TableRow>
											<TableCell
												colSpan={isVolumeView ? 6 : 5}
												className='text-center py-8 text-gray-500'
											>
												No{" "}
												{isVolumeView
													? "volumes"
													: "goals"}{" "}
												found
											</TableCell>
										</TableRow>
									)}
								</TableBody>
							</Table>
						</div>

						{totalPages > 1 && (
							<div className='flex justify-center gap-2 p-4 border-t'>
								<Button
									variant='outline'
									size='sm'
									onClick={() =>
										handlePageChange(
											Math.max(1, currentPage - 1)
										)
									}
									disabled={currentPage === 1}
								>
									Previous
								</Button>

								{Array.from({ length: 5 }, (_, i) => {
									const startPage =
										Math.floor((currentPage - 1) / 5) * 5 +
										1;
									const page = startPage + i;
									if (page > totalPages) return null;

									return (
										<Button
											key={page}
											variant={
												currentPage === page
													? "default"
													: "outline"
											}
											size='sm'
											onClick={() =>
												handlePageChange(page)
											}
											className={
												currentPage === page
													? "bg-orange-500 hover:bg-orange-600 text-white"
													: ""
											}
										>
											{page}
										</Button>
									);
								})}

								<Button
									variant='outline'
									size='sm'
									onClick={() =>
										handlePageChange(
											Math.min(
												totalPages,
												currentPage + 1
											)
										)
									}
									disabled={currentPage === totalPages}
								>
									Next
								</Button>
							</div>
						)}
					</CardContent>
				</Card>
			</div>

			{isVolumeView ? (
				<YtaGoalVolumeDialog
					open={showDialog}
					onClose={handleDialogClose}
					onSuccess={handleSuccess}
					editingItem={editingItem}
				/>
			) : (
				<YtaGoalDialog
					open={showDialog}
					onClose={handleDialogClose}
					onSuccess={handleSuccess}
					editingItem={editingItem}
				/>
			)}

			<DeleteConfirmDialog
				open={!!deleteId}
				onOpenChange={(open) => !open && setDeleteId(null)}
				onConfirm={() => handleDelete(deleteId)}
				title={`Delete ${isVolumeView ? "Volume" : "Goal"}`}
				description={`Are you sure you want to delete this ${
					isVolumeView ? "volume" : "goal"
				}? This action cannot be undone.`}
			/>
		</div>
	);
};

export default YtaGoalValoum;
