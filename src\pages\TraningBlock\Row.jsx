import React, { useRef } from "react";
import { useDrag } from "react-dnd";
import { ROW } from "./constants";
import DropZone from "./DropZone";
import Column from "./Column";

const style = {};
const Row = ({ data, handleDrop, path ,setCopyWorkoutData,CopyWorkoutData}) => {
  const ref = useRef(null);

  const [{ isDragging }, drag] = useDrag({
    item: {
      type: ROW,
      id: data.id,
      children: data.children,
      path,
    },
    collect: (monitor) => ({
      isDragging: monitor.isDragging(),
    }),
  });

  const opacity = isDragging ? 0 : 1;
  drag(ref);

  const renderColumn = (column, currentPath, index,setCopyWorkoutData,CopyWorkoutData) => {
    return (
      <Column
        key={column.id}
        setCopyWorkoutData={setCopyWorkoutData}
        CopyWorkoutData={CopyWorkoutData}
        data={column}
        handleDrop={handleDrop}
        path={currentPath}
        index={index}
        id={column.id}
      />
    );
  };

  return (
    <div ref={ref} style={{ ...style, opacity }} className="base draggable">
      {/* {data.id} */}
      <div className="columns">
        {data.children.map((column, index) => {
          const currentPath = `${path}-${column.counter}`;

          return (
            <React.Fragment key={column.id}>
              <DropZone
                data={{
                  path: currentPath,
                  childrenCount: data.children.length,
                }}
                onDrop={handleDrop}
                className="horizontalDrag"
                id={column.id}
              ></DropZone>
              {renderColumn(column, currentPath, index,setCopyWorkoutData,CopyWorkoutData)}
            </React.Fragment>
          );
        })}
        {/* <DropZone
          data={{
            path: `${path}-${data.children.length}`,
            childrenCount: data.children.length,
          }}
          onDrop={handleDrop}
          className="horizontalDrag"
          isLast
        /> */}
      </div>
    </div>
  );
};
export default Row;
