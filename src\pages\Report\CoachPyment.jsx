import React, { useCallback, useEffect, useState } from "react";
import Paper from "@mui/material/Paper";
import Table from "@mui/material/Table";
import TableBody from "@mui/material/TableBody";
import TableContainer from "@mui/material/TableContainer";
import TableHead from "@mui/material/TableHead";
import TableRow from "@mui/material/TableRow";
import TableCell, { tableCellClasses } from "@mui/material/TableCell";
import { styled } from "@mui/material/styles";
import { Box } from "@mui/system";
import { Button, FormLabel, Grid, TextField } from "@mui/material";
import { useNavigate } from "react-router";
import { async } from "q";
import Header from "../../components/Header";
import { getCoachPayment, updateCoachPaymnet } from "../../API/api-endpoint";
import { debounce } from 'lodash';
import { Modal } from "antd";
import { showInfo, showSuccess } from "../../components/Messages";
const StyledTableCell = styled(TableCell)(({ theme }) => ({
    [`&.${tableCellClasses.head}`]: {
        backgroundColor: "#1e40af",
        color: theme.palette.common.white,
    },
    [`&.${tableCellClasses.body}`]: {
        fontSize: 14,
    },
}));
const StyledTableRow = styled(TableRow)(({ theme }) => ({
    "&:nth-of-type(odd)": {
        backgroundColor: theme.palette.action.hover,
    },
    // hide last border
    "&:last-child td, &:last-child th": {
        border: 0,
    },
}));
const CoachPyment = () => {
    const [formValue, setFormValue] = useState({})
    const [reportData, setReportData] = useState()
    const [isAction, setisAction] = useState({isOpen:false,id:""})

    const [addComments, setAddComments] = useState("")

    console.log("addComments", isAction);

    const fetchReport = async () => {
        const response = await getCoachPayment(formValue)
        console.log("response", response);
        setReportData(response)
    }
    useEffect(() => {
        fetchReport()
    }, [])
  
    const handleInputChange = (event,id) => {
        setAddComments(event.target.value);
        // updateComment(id); // This will be delayed by the debounce duration
    };
const handleOpen=(id,status)=>{
setisAction({isOpen:true,id:id,status:status})
}
   const updateComment = async(id,status)=>{
    if (addComments) {
        let data = {
            id: id,
            comments: addComments,
            status: status?status:1
          };
          // Your API call logic
          const response = await updateCoachPaymnet(data);
          if (response?.status) {
          setAddComments("")
          setisAction({isOpen:false,id:""})
          fetchReport()  
          showSuccess(response?.message)
          }
          
    }
   else{
    showInfo("Please add comments")
   }
   }
    return (
        <div>
            <Header />
            <div className="grid grid-cols-1 xl:grid-cols-5 items-start gap-x-4"></div>
            <div style={{ marginTop: "100px", padding: "20px" }}>
                
                    <div style={{ fontSize: "18px", background: "#FFEADC", width: "100%", padding: "10px" }}>
                    Coach Payments
                    </div>
                    <TableContainer component={Paper}>
                    <Table sx={{ minWidth: 700, padding: "10px" }} aria-label="customized table">
                        <TableHead>
                            <TableRow>
                                <StyledTableCell align="left">Sr ID</StyledTableCell>
                                <StyledTableCell align="left">Trainees Name</StyledTableCell>
                                <StyledTableCell align="left">Program Name</StyledTableCell>
                                <StyledTableCell align="left">Service Days</StyledTableCell>
                                <StyledTableCell align="left">Payment Received</StyledTableCell>
                                <StyledTableCell align="left">Coach Commission</StyledTableCell>
                                <StyledTableCell align="left">Amount To Be Paid</StyledTableCell>
                                <StyledTableCell align="left">Status</StyledTableCell>
                                <StyledTableCell align="left">Comments</StyledTableCell>
                                <StyledTableCell align="left">Actions</StyledTableCell>
                            </TableRow>
                        </TableHead>
                        <TableBody>
                            {reportData?.length > 0 ? (
                                <>
                                    {reportData?.map((row, index) => (
                                        <StyledTableRow key={index}>
                                            <StyledTableCell align="left">
                                                {index + 1}
                                            </StyledTableCell>
                                            <StyledTableCell align="left">
                                                {row?.trainee?.firstname} {row?.trainee?.lastname}

                                            </StyledTableCell>
                                            <StyledTableCell align="left">
                                                {row?.program_name}

                                            </StyledTableCell>
                                            <StyledTableCell align="left">
                                                {row?.servicedays}

                                            </StyledTableCell>
                                            <StyledTableCell align="left">
                                                {row?.paymentrecieved}

                                            </StyledTableCell>
                                            <StyledTableCell align="left">
                                                {row?.coach_commission}

                                            </StyledTableCell>
                                            <StyledTableCell align="left">
                                                {row?.amounttobepaid}

                                            </StyledTableCell>
                                            <StyledTableCell align="left">
                                                {row?.status==3?"Reject":row?.status==2?"Problem":row?.status==1?"Approved":""}

                                            </StyledTableCell>
                                            <StyledTableCell align="left">
                                                <TextField size="small" disabled={row?.comments} value={row?.comments?row.comments:addComments} placeholder="Type comment.." onChange={(e) => handleInputChange(e, row?.id)} />
                                            </StyledTableCell>
                                            <StyledTableCell align="left">
                                                <Button variant="contained" disabled={!addComments && !row?.comments}  style={{marginBottom:"10px", background:"#28a745"}}
                                                    onClick={()=>{
                                                        
                                                        if (row?.comments) {
                                                            handleOpen(row?.id,1)
                                                            setAddComments(row?.comments)
                                                        }else{
                                                            updateComment(row?.id)}}
                                                        }
                                                >Approve </Button>
                                                &nbsp;
                                                <Button variant="contained" style={{marginBottom:"10px", background:"#ffc107"}} 
                                                
                                                onClick={()=>{
                                                    handleOpen(row?.id,2)
                                                            setAddComments(row?.comments)
                                                }}>Problem</Button> &nbsp;
                                                <Button variant="contained" style={{marginBottom:"10px", background:"#dc3545"}}
                                                onClick={()=>{
                                                    handleOpen(row?.id,3)
                                                            setAddComments(row?.comments)
                                                }}
                                                >Reject</Button>

                                            </StyledTableCell>

                                        </StyledTableRow>
                                    ))}
                                </>
                            ) : (
                                <div className="p-4">No data found</div>
                            )}
                        </TableBody>
                    </Table>
                </TableContainer>
            </div>
            <Modal
                title="Add Comments to update status"
                centered
                open={isAction?.isOpen}
                onOk={() => updateComment(isAction?.id,isAction?.status)}
                onCancel={() => {
                    setAddComments("")
                    setisAction({ isOpen: false, id: "",status:"" })}}
            >
                <div style={{borderBottom:"1px solid gray",paddingBottom:"24px"}}>
                    <FormLabel style={{ textAlign: "center", fontWeight: 600 }} >Comments:</FormLabel>
                    <br />
                    <TextField
                        placeholder="Type comments here"
                        size="small"
                        type="text"
                        name="added_datetime"
                        value={addComments}
                        onChange={(e) => {
                            setAddComments( e.target.value )
                        }}
                        
                    />
                </div>
            </Modal>
        </div>
    )
}
export default CoachPyment
