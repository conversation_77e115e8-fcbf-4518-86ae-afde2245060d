.date-box {
    height: 1rem;
    padding: 7px;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: #efefef;
    border-radius: 10px;
    margin-top: 0.7rem;
    margin-bottom: 0.7rem;
    padding-left: 1rem;
    padding-right: 1rem;
    color: #707070;
  }
  
  .date-box-parent {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 1.7rem;
  }
  
  .ind-chat-date {
    /* font-family: 'Open Sauce Sans'; */
    font-style: normal;
    font-weight: bold !important;
    font-size: 10px !important;
    line-height: 12px;
    text-align: center;
    letter-spacing: 0.05em;
  }
  
  .creator-badge-text {
    /* font-family: 'Open Sauce Sans'; */
    font-style: normal;
    font-weight: 400 !important;
    font-size: 10px !important;
    line-height: 150%;
    text-align: center;
    letter-spacing: 0.05em !important;
  }
  
  .text-msg-box {
    padding: 1rem;
    /* display: inline-block; */
    border-radius: 10px;
    padding-bottom: 1.1rem;
    padding-top: 16px !important;
    font-style: normal !important;
    font-weight: 400 !important;
    font-size: 14px !important;
    line-height: 150% !important;
    /* identical to box height, or 21px */
    letter-spacing: 0.05em !important;
    text-align: left;
    min-width: 100px;
    max-width: fit-content;
    /* max-width: 50%;
      min-width: 3rem; */
    line-break: anywhere;
    position: relative;
  }
  
  .text-msg-timing-parent {
    margin-top: 0;
    padding-bottom: 0.3rem;
    position: absolute;
    bottom: 0;
    right: 0;
    margin-right: 0.5rem;
    margin-bottom: -3.2px;
    text-align: right;
  }
  
  .text-msg-timing {
    font-size: 9px !important;
    font-weight: 300 !important;
  }
  
  .photo-msg-parent {
    width: auto;
    height: auto;
    background-color: #fff388;
    border-radius: 10px;
    margin: 0.5rem 1rem 0.5rem 1rem;
    padding-bottom: 2rem;
    text-align: center;
    padding: 5px;
    display: inline-block;
  }
  
  .photo-msg-img-box {
    border-radius: 10px 10px 0 0 !important;
  }
  
  .photo-msg-view-post-btn {
    height: 24px;
    background-color: #fdfdfd;
    border-radius: 0 0 10px 10px;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 4px;
  }
  
  .photo-img-icon {
    height: 27px;
    width: 27px;
    background-color: #00000026;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: -15px;
  }
  
  .view-post-text {
    /* font-family: 'Open Sauce Sans'; */
    font-style: normal;
    font-weight: 500;
    font-size: 10px !important;
    line-height: 10px;
    letter-spacing: 0.01em;
    color: #056b6b;
  }
  
  /* .photo-img-texts-aligns {} */
  
  .video-msg-box {
    width: 222px;
    height: 255px;
    top: 692px;
    padding: 5px;
    border-radius: 10px 10px 10px 10px;
    display: inline-block;
  }
  
  .video-box {
    height: 224px;
    border-radius: 10px 10px 10px 10px;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: -1000;
  }
  
  .joinplan-msg-box {
    height: auto;
    width: 258px;
    min-height: 152px;
    border-radius: 10px 10px 10px 10px;
    padding: 5px;
    display: inline-block;
  }
  
  .joinplan-box {
    min-height: 110px;
    background-color: white;
    border-radius: 10px 10px 0 0;
  }
  
  .documentss-box {
    background-color: white;
    border-radius: 10px 10px 0 0;
  }
  
  .join-plan-btns {
    width: 50%;
    background-color: #fde723;
    text-align: center;
    font-size: 11px;
    padding: 5px;
    border-radius: 0px 0px 0px 10px;
    border-bottom: 2px solid white;
    border-left: 2px solid white;
  }
  
  .view-post-btns {
    width: 50%;
    background-color: #e4eeee;
    text-align: center;
    font-size: 11px;
    padding: 5px;
    border-radius: 0px 0px 10px 0px;
    border-bottom: 2px solid white;
    border-right: 2px solid white;
  }
  
  .isTravellingTotext {
    font-size: 9px !important;
    padding: 2px;
    padding-left: 5px;
  }
  
  .join-plan-desc {
    padding: 0.5rem;
    padding-left: 1rem;
    font-size: 10px !important;
    text-align: left !important;
  }
  
  .view-post-btns:hover,
  .join-plan-btns:hover,
  .view-post-text:hover {
    cursor: pointer;
  }
  
  .plus-bar-content {
    height: 69.87px;
    background: #ffffff;
    box-shadow: 0px 4px 25px rgba(0, 0, 0, 0.27);
    border-radius: 10px;
    transform: matrix(1, 0, 0, -1, 0, 0);
    position: fixed;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    margin-bottom: 5rem;
    z-index: 1000;
    display: flex;
    padding: 5px 20px 5px 20px;
  }
  
  .plus-bar-icon-div {
    text-align: center;
    flex-direction: column;
    display: flex;
    justify-content: center;
    align-items: center;
    padding-right: 0.7rem;
    padding-left: 0.7rem;
  }
  
  .plus-bar-icon-label {
    text-align: center !important;
    letter-spacing: 0.01em !important;
    color: #707070 !important;
    font-style: normal !important;
    font-weight: 400 !important;
    font-size: 8px !important;
    line-height: 150% !important;
    padding-top: 0.2rem;
  }
  
  .line-plus-icon-menu {
    height: 50%;
    border-left: 1px solid #707070;
    margin: auto;
  }
  
  .senderName {
    color: black;
    font-family: Open Sauce Sans;
    font-size: 10px;
    font-style: normal;
    font-weight: 700;
    line-height: 150%;
    letter-spacing: 0.5px;
    text-align: left;
  }
  
  /* meta card */
  
  .meta_card {
    width: 200px;
    margin-right: auto;
    margin-left: auto;
    border: 1px solid lightgray;
    color: black;
    background-color: #f2f2f2;
    padding: 8px;
    margin-top: 0.8rem;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .meta_img {
    width: 100%;
  }
  
  .meta_desc,
  .meta_title,
  .meta_sitename {
    margin: 0;
    padding: 0;
  }
  
  .meta_desc {
    font-size: 14px;
  }
  
  .meta_sitename {
    font-size: 12px;
  }
  
  .navGroup {
    height: 5%;
  }
  
  .date-box-parent {
    height: 5%;
  }
  
  /* .root_container {
    height: 75%;
    overflow-y: hidden;
  } */
  
  /* @media (min-width: 1463px) and (min-height: 691px) {
    .chatPaper1 {
      height: 390px;
    }
    .chatPaperIndividual {
      height: 412px;
    }
  } */
  
  /* @media (min-width: 1464px) {
    .root_container {
      height: 80%;
      overflow-y: hidden;
    }
    .chatPaper1 {
      height: 630px;
    }
    .chatPaperIndividual {
      height: 630px;
    }
  } */
  
  /* @media (min-width: 1468px) and (min-height: 1534px) {
    .root_container {
      height: 76%;
      overflow-y: hidden;
    }
  } */
  
  /* @media (min-width: 1680px) and (min-height: 1050px) {
    .root_container {
      height: 72%;
      overflow-y: hidden;
    }
  } */
  
  /* @media (min-width: 1920px) and (min-height: 1080px) {
    .root_container {
      height: 71%;
      overflow-y: hidden;
    }
    .chatPaper1 {
      height: 620px;
    }
    .chatPaperIndividual {
      height: 651px;
    }
  }
   */
  /* ---- Chat App Styles  --- */
  
  .chat-container {
    height: 100%;
    max-height: 100%;
    /* border: solid; */
    overflow-y: hidden;
    /* position: absolute; */
  }
  
  .message-list {
    /* width: 600px;
    height: 800px; */ 
    height: 70%;
    max-height: 70%;
    overflow-y: scroll;
    /* border: 1px solid #ccc; */
    background-color: white;
    margin-bottom: 10px;
    padding: 10px;
    /* border-radius: 5px; */
    /* box-shadow: 0 2px 0 rgba(0, 0, 0, 0.1); */
  }
  
  .rce-mbox:hover{
    cursor: pointer;
  }
  
  .message-list .text {
    padding: 5px 10px;
    border-radius: 15px;
    margin: 5px 0;
  }
  
  .message-list .text.right {
    background-color: #0084ff;
    color: white;
    align-self: flex-end;
  }
  
  .message-list .text.left {
    background-color: #f1f1f1;
    color: black;
    align-self: flex-start;
  }
  
  .input-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    padding-left: 2%;
    padding-right: 2%;
  }
  
  input {
    width: calc(100% - 80px);
    padding: 10px;
    border: 1px solid #ccc;
    border-radius: 5px;
    margin-right: 10px;
  }
  
  .chat-btn {
    padding: 10px 15px;
    border: none;
    border-radius: 5px;
    background-color: #E67E22;
    color: white;
    cursor: pointer;
    transition: background-color 0.3s ease;
    width: 10%;
  }
  
  .cs-message-input__content-editor {
    font-size: small;
    display: flex;
    align-items: center;
  }
  
  .cs-message-input__content-editor {
    background-color: #e7e6e5 !important;
  }
  
  .cs-message-input__content-editor-wrapper {
    background-color: #e7e6e5 !important;
  }
  
  .cs-message--outgoing .cs-message__content {
    background-color: #FFDEAD !important;
  }
  
  .cs-message--incoming .cs-message__content {
    background-color: #ebebeb !important;
  }
  
  .cs-main-container {
    border: none !important;
  }
  
  button:hover {
    background-color: #fcda91
  }
  
  .rce-mbox-photo--img img{
    width: 180px !important;
  }
  
  .rce-container-input{
    min-width: unset !important;
    width: 100%;
  }
  
  .file-input-container{
    margin: 10px; /* Adjust the value as needed */
  }
  
  .input-form {
    display: flex;
    justify-content: center;
    max-width: 100%;
  }
  
  
  /* ---- File Input_------ */
  
  .file-preview-overlay {
    position: absolute;
    bottom: 10px;
    right: 10px;
    background: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 10px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    gap: 10px;
  }
  
  .file-preview-overlay img {
    border-radius: 4px;
  }
  
  .file-preview-overlay button {
    background: transparent;
    border: none;
    color: white;
    cursor: pointer;
  }
  /* --- Reply To Styles --- */
  .reply-box {
    display: flex;
    align-items: center;
    background-color: #f1f1f1; /* Light grey background */
    border-radius: 8px;
    padding: 8px 12px;
    margin: 8px 0;
    position: relative;
    color: #333;
    font-size: 14px;
    max-width: 300px; /* Adjust the width of the reply box */
    width: 100%; /* Ensure it scales responsively */
  }
  
  .reply-content {
    display: flex;
    flex-direction: column;
    flex-grow: 1; /* Allow the content to take up remaining space */
  }
  
  .replying-to {
    font-weight: bold;
    color: #666;
    font-size: 15px;
  }
  
  .reply-text {
    font-size: 14px;
    color: #333;
  }
  
  .cancel-reply {
    background: none;
    border: none;
    font-size: 16px;
    color: #888;
    position: absolute;
    top: 5px;  /* Adjust the top distance */
    right: 5px; /* Position at the top-right */
    cursor: pointer;
  }
  
  .cancel-reply:hover {
    color: #333;
  }
  
  /* ------- File Input btn -------- */
  
  .file-input-button {
    position: relative;
    display: inline-block;
    padding: 10px 20px;
    border: none;
    border-radius: 5px;
    background-color: #fff;
    width: 10%;
  }
  
  .file-input-button input[type="file"] {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
  }
  
  /* --- Search and reply ---- */
  .chat-footer {
    position: relative;
  }
  
  .reply-box {
    position: absolute;
    top: -70px; 
    width: 100%;
    left: 0;
  }
  
  .input-form {
    position: relative;
    /* bottom: -25px;
    z-index: 9999999; */
  }
  
  .cs-message--outgoing .cs-message__text-content {
    color: #3B3B3B !important;
  }
  
  .cs-message.cs-message--outgoing.cs-message--single .cs-message__content {
    border-radius: 8px !important;
  }
  
  .cs-message--outgoing .cs-message__content {
    border-radius: 8px !important;
  }
  
  .cs-message.cs-message--incoming.cs-message--single .cs-message__content {
    border-radius: 8px !important;
  }