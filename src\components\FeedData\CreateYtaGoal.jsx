import {
    Chip,
    FormControl,
    FormLabel,
    Grid,
    MenuItem,
    OutlinedInput,
    Select,
    TextField,
  } from "@mui/material";
  import { But<PERSON>, Modal } from "antd";
  import React, { useEffect, useState } from "react";
  import {
    CreatePrograms,
    CreateZonesClasification,
    createGoalNameYTA,
    createGoalsdata,
    createPhaseNamedata,
    createYTAdata,
    getAllActivityData,
    getAllGoalNameForYTA,
    getAllPrograms,
    getAlllevels,
    updateGoalNameYTA,
    updateGoalsdata,
    updatePhaseNamedata,
    updatePrograms,
    updateYTAdata,
    updateZonesClasification,
    weeklyFeedDataPattern,
    weeklyFeedDataProgram,
  } from "../../API/api-endpoint";
  import { useFormik } from "formik";
  import Swal from "sweetalert2";
  import SlickCarousel from "../../pages/SlickCarousel";
  const scoreData = [1, 2, 3, 4, 5];
  const CreateYtaGoal = ({
    fetchReport,
    setShowAssesmentModal,
    showAssesmentModal,
    editData,
    setEditData,
  }) => {
    const formik = useFormik({
      initialValues: {
        goalname: "",
        goaltable: "",
        phase: "",
      },
      validate: (values) => {
        const errors = {};
        if (!values.goalname) {
          errors.goalname = "Goal name is required";
        }
        if (!values.goaltable) {
          errors.goaltable = "Goal table is required";
        }
        if (!values.phase) {
          errors.phase = "Phase name is required";
        }
        return errors;
      },
      // validationSchema: {},
      onSubmit: (values, { resetForm }) => {
        handleSubmitAssesmentForm(values, resetForm);
      },
    });
  
    const handleSubmitAssesmentForm = async (data, resetForm) => {
      let response = "";
      if (editData?.id) {
        response = await updateYTAdata(data);
      } else {
        response = await createYTAdata(data);
        console.log("response", response);
      }
  
      if (response?.status) {
        Swal.fire({
          title: "Success",
          text: response.message,
          icon: "success",
        });
        setShowAssesmentModal(false);
        fetchReport();
        resetForm();
        setEditData({});
        formik?.setValues({
          goalname: "",
          goaltable: "",
          phase: "",
        });
      } else {
        Swal.fire({
          title: "Error",
          text: response.message,
          icon: "error",
        });
      }
      console.log("response", response);
    };
    useEffect(() => {
      if (editData?.id) {
        const { srID, ...data } = editData;
        console.log("data", data);
        formik?.setValues(data);
      }
    }, [editData?.id]);
    return (
      <Modal
        width={1200}
        // title={editData?.id ? "Edit YTA Goal" : "Add YTA Goal"}
        // title={editData ? "Edit Challenge" : "Add Challenges"}
        open={showAssesmentModal}
        onCancel={() => {
          setShowAssesmentModal(false);
          setEditData({});
          formik.resetForm();
          formik?.setValues({
            goalname: "",
            goaltable: "",
            phase: "",
          });
        }}
        footer={
          <div></div>
          //   loading={isLoading}
        }
      >
        <div className="headingCont">
          <span className="heading">{editData?.id ? "Edit " : "Create"}</span>{" "}
          <span className="orange heading">YTA Goal</span>
        </div>
        {/* <h1>{editData ? editData.challengeId : values.challengeId}</h1> */}
        <div className="parentCont">
          <form className="form1" onSubmit={formik.handleSubmit}>
            <Grid container spacing={2}>
              <Grid item xs={12} sm={11}>
                <FormLabel>Goal Name<span className="text-[red]">*</span></FormLabel>
  
                <TextField
                  fullWidth
                  placeholder="goal name"
                  size="small"
                  type="text"
                  name="goalname"
                  value={formik?.values?.goalname}
                  onChange={formik.handleChange}
                  error={formik.touched.goalname && formik.errors.goalname}
                  helperText={formik.touched.goalname && formik.errors.goalname}
                />
              </Grid>
              <Grid item xs={12} sm={11}>
                <FormLabel>Goal Table<span className="text-[red]">*</span></FormLabel>
  
                <TextField
                  fullWidth
                  placeholder="goal table"
                  size="small"
                  type="text"
                  name="goaltable"
                  value={formik?.values?.goaltable}
                  onChange={formik.handleChange}
                  error={formik.touched.goaltable && formik.errors.goaltable}
                  helperText={formik.touched.goaltable && formik.errors.goaltable}
                />
              </Grid>
              <Grid item xs={12} sm={11}>
                <FormLabel>Phase Name<span className="text-[red]">*</span></FormLabel>
  
                <TextField
                  fullWidth
                  placeholder="phase name"
                  size="small"
                  type="text"
                  name="phase"
                  value={formik?.values?.phase}
                  onChange={formik.handleChange}
                  error={formik.touched.phase && formik.errors.phase}
                  helperText={formik.touched.phase && formik.errors.phase}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <Button
                  className="btn"
                  key="submit"
                  type="primary"
                  onClick={() => formik.handleSubmit()}
                >
                  Submit
                </Button>
              </Grid>
            </Grid>
          </form>
          <div className="slick-container">
            <SlickCarousel />
          </div>
        </div>
      </Modal>
    );
  };
  export default CreateYtaGoal;
  