import {
    <PERSON><PERSON>abel,
    <PERSON>rid,
    MenuItem,
    OutlinedInput,
    TextField,
  } from "@mui/material";
  import { <PERSON><PERSON>, Modal, TimePicker } from "antd";
  import React, { useEffect, useState } from "react";
  import {
    creatTagClouddata,
    getAllActivityData,
    updateTagClouddata,
    uploadsaveFile,
  } from "../../API/api-endpoint";
  import { useFormik } from "formik";
  import Swal from "sweetalert2";
  import SlickCarousel from "../../pages/SlickCarousel";
  const CreateTags = ({
    fetchReport,
    setShowAssesmentModal,
    showAssesmentModal,
    editData,
    setEditData,
  }) => {
    console.log("editData", editData);
    const formik = useFormik({
      initialValues: {
        tag_cloud_name: "",
        status: 1,
      },
      validate: (values) => {
        const errors = {};
        if (!values.tag_cloud_name) {
          errors.tag_cloud_name = "Name is required";
        }
  
        return errors;
      },
      // validationSchema: {},
      onSubmit: (values, { resetForm }) => {
        handleSubmitAssesmentForm(values, resetForm);
      },
    });
    console.log("formik", formik?.values, formik?.errors);
  
    const handleSubmitAssesmentForm = async (data, resetForm) => {
      let response = "";
      if (editData["tag-cloud-id"]) {
        response = await updateTagClouddata(data);
      } else {
        response = await creatTagClouddata(data);
      }
      if (response?.status) {
        Swal.fire({
          title: "Success",
          text: response.message,
          icon: "success",
        });
        setEditData({});
        setShowAssesmentModal(false);
        fetchReport();
        formik.resetForm();
        formik?.setValues({
          tag_cloud_name: "",
          status: 1,
        });
      } else {
        Swal.fire({
          title: "Error",
          text: response.message,
          icon: "error",
        });
      }
      console.log("response", response);
    };
    useEffect(() => {
      if (editData && editData["tag-cloud-id"]) {
        const { srID, createdAt, updatedAt, ...data } = editData;
        formik?.setValues(data);
        formik?.setFieldValue("tag_cloud_name", data["tag-cloud-name"]);
      }
    }, [editData]);
  
    return (
      <Modal
        width={1200}
        open={showAssesmentModal}
        onCancel={() => {
          setShowAssesmentModal(false);
          formik.resetForm();
          setEditData({});
          formik?.setValues({
            tag_cloud_name: "",
            status: 1,
          });
        }}
        footer={
          <div></div>
          //   loading={isLoading}
        }
      >
        <div className="headingCont">
        <span className="heading">{editData && editData["tag-cloud-id"] ? "Edit " : "Create"}</span>
          <span className="orange heading">Tags</span>
        </div>
        {/* <h1>{editData ? editData.challengeId : values.challengeId}</h1> */}
        <div className="parentCont">
          <form className="form1" onSubmit={formik.handleSubmit}>
            <Grid container spacing={2}>
              <Grid item xs={12} sm={11}>
                <FormLabel>Name<span className="text-[red]">*</span></FormLabel>
  
                <TextField
                  fullWidth
                  placeholder="Name"
                  size="small"
                  type="text"
                  name="tag_cloud_name"
                  value={formik?.values?.tag_cloud_name}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  error={
                    formik.touched.tag_cloud_name && formik.errors.tag_cloud_name
                  }
                  helperText={
                    formik.touched.tag_cloud_name && formik.errors.tag_cloud_name
                  }
                />
              </Grid>
              <Grid item xs={12} sm={11}>
                <FormLabel>Status</FormLabel>
  
                <TextField
                  fullWidth
                  size="small"
                  select
                  name="status"
                  value={formik?.values?.status}
                  onChange={formik.handleChange}
                  error={formik.touched.status && formik.errors.status}
                  helperText={formik.touched.status && formik.errors.status}
                  id="form-layouts-separator-select"
                  labelId="form-layouts-separator-select-label"
                  input={<OutlinedInput id="select-multiple-language" />}
                >
                  <MenuItem value={1}>Yes</MenuItem>
                  <MenuItem value={0}>No</MenuItem>
                </TextField>
              </Grid>
  
              <Grid item xs={12} sm={6}>
                <Button
                  className="btn"
                  key="submit"
                  type="primary"
                  onClick={() => formik.handleSubmit()}
                >
                  Submit
                </Button>
              </Grid>
            </Grid>
          </form>
  
          <div className="slick-container">
            <SlickCarousel />
          </div>
        </div>
      </Modal>
    );
  };
  export default CreateTags;
  