import React, { useState, useEffect } from "react";
import { But<PERSON> } from "../../components/ui/button";
import {
	<PERSON><PERSON>,
	DialogContent,
	DialogHeader,
	DialogTitle,
} from "../../components/ui/dialog";
import { Input } from "../../components/ui/input";
import { Label } from "../../components/ui/label";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "../../components/ui/select";
import {
	getAllActivityData,
	getAllPhaseNameData,
	createPhaseBloackdata,
	updatePhaseBloackdata,
	createPhaseNamedata,
	updatePhaseNamedata,
} from "../../API/api-endpoint";

export function PhaseBlockDialog({
	open,
	onOpenChange,
	editingItem,
	isPhaseBlock,
	onSuccess,
}) {
	const [formData, setFormData] = useState({
		week: "",
		activities: "",
		phase: "",
	});
	const [activities, setActivities] = useState([]);
	const [phaseNames, setPhaseNames] = useState([]);
	const [isLoading, setIsLoading] = useState(false);
	const [isSubmitting, setIsSubmitting] = useState(false);

	// Fetch data when dialog opens
	useEffect(() => {
		if (open) {
			fetchActivities();
			if (isPhaseBlock) {
				fetchPhaseNames();
			}
		}
	}, [open, isPhaseBlock]);

	// Handle form data when editing
	useEffect(() => {
		if (editingItem) {
			if (isPhaseBlock) {
				setFormData({
					week: editingItem.week?.toString() || "",
					activities: editingItem.activity?.id?.toString() || "",
					phase: editingItem.phasename?.id?.toString() || "",
				});
			} else {
				setFormData({
					week: "",
					activities: "",
					phase: editingItem.phase || "",
				});
			}
		} else {
			setFormData({
				week: "",
				activities: "",
				phase: "",
			});
		}
	}, [editingItem, open, isPhaseBlock]);

	const fetchActivities = async () => {
		try {
			setIsLoading(true);
			const response = await getAllActivityData();
			setActivities(response?.rows || []);
		} catch (error) {
			setActivities([]);
		} finally {
			setIsLoading(false);
		}
	};

	const fetchPhaseNames = async () => {
		try {
			const response = await getAllPhaseNameData();
			setPhaseNames(response || []);
		} catch (error) {
			setPhaseNames([]);
		}
	};

	const handleSubmit = async (e) => {
		e.preventDefault();

		if (isPhaseBlock) {
			if (!formData.week || !formData.activities || !formData.phase) {
				return;
			}
		} else {
			if (!formData.phase) {
				return;
			}
		}

		try {
			setIsSubmitting(true);
			let response;

			if (isPhaseBlock) {
				const phaseBlockData = {
					week: parseInt(formData.week),
					activities: parseInt(formData.activities),
					phase: parseInt(formData.phase),
				};

				if (editingItem?.id) {
					response = await updatePhaseBloackdata({
						...phaseBlockData,
						id: editingItem.id,
					});
				} else {
					response = await createPhaseBloackdata(phaseBlockData);
				}
			} else {
				const phaseNameData = {
					phase: formData.phase,
				};

				if (editingItem?.id) {
					response = await updatePhaseNamedata({
						...phaseNameData,
						id: editingItem.id,
					});
				} else {
					response = await createPhaseNamedata(phaseNameData);
				}
			}

			if (response?.status) {
				onOpenChange(false);
				if (onSuccess) onSuccess();
			} else {
				alert(response?.message || "Operation failed");
			}
		} catch (error) {
			console.error("Error submitting form:", error);
		} finally {
			setIsSubmitting(false);
		}
	};

	return (
		<Dialog open={open} onOpenChange={onOpenChange}>
			<DialogContent className='sm:max-w-[425px] bg-white'>
				<DialogHeader>
					<DialogTitle className='text-orange-950'>
						{editingItem ? "Edit" : "Create"}{" "}
						{isPhaseBlock ? "Phase Block" : "Phase Name"}
					</DialogTitle>
				</DialogHeader>

				<form onSubmit={handleSubmit} className='space-y-4'>
					{isPhaseBlock ? (
						<>
							<div className='space-y-2'>
								<Label htmlFor='week'>Week</Label>
								<Input
									id='week'
									type='number'
									value={formData.week}
									onChange={(e) =>
										setFormData({
											...formData,
											week: e.target.value,
										})
									}
									placeholder='Enter week number'
									required
									className='text-sm w-full'
								/>
							</div>

							<div className='space-y-2'>
								<Label htmlFor='activities'>Activity</Label>
								<Select
									value={formData.activities}
									onValueChange={(value) =>
										setFormData({
											...formData,
											activities: value,
										})
									}
									disabled={isLoading}
								>
									<SelectTrigger className='bg-white border border-gray-300 text-gray-900'>
										<SelectValue
											placeholder={
												isLoading
													? "Loading activities..."
													: "Select activity"
											}
											className='text-gray-900'
										/>
									</SelectTrigger>
									<SelectContent className='bg-white border border-gray-200 shadow-lg'>
										{activities.length > 0 ? (
											activities.map((activity) => (
												<SelectItem
													key={activity.id}
													value={String(activity.id)}
													className='text-gray-900 hover:bg-blue-50 hover:text-blue-900 cursor-pointer pr-3 py-2 pl-8 relative'
												>
													{activity.activity ||
														activity.activity_name}
												</SelectItem>
											))
										) : (
											<SelectItem
												value='no-activities'
												disabled
											>
												No activities available
											</SelectItem>
										)}
									</SelectContent>
								</Select>
							</div>

							<div className='space-y-2'>
								<Label htmlFor='phase'>Phase Name</Label>
								<Select
									value={formData.phase}
									onValueChange={(value) =>
										setFormData({
											...formData,
											phase: value,
										})
									}
								>
									<SelectTrigger className='bg-white border border-gray-300 text-gray-900'>
										<SelectValue
											placeholder='Select phase name'
											className='text-gray-900'
										/>
									</SelectTrigger>
									<SelectContent className='bg-white border border-gray-200 shadow-lg'>
										{phaseNames.length > 0 ? (
											phaseNames.map((phaseName) => (
												<SelectItem
													key={phaseName.id}
													value={String(phaseName.id)}
													className='text-gray-900 hover:bg-blue-50 hover:text-blue-900 cursor-pointer pr-3 py-2 pl-8 relative'
												>
													{phaseName.phase}
												</SelectItem>
											))
										) : (
											<SelectItem
												value='no-phases'
												disabled
											>
												No phase names available
											</SelectItem>
										)}
									</SelectContent>
								</Select>
							</div>
						</>
					) : (
						<div className='space-y-2'>
							<Label htmlFor='phase'>Phase Name</Label>
							<Input
								id='phase'
								value={formData.phase}
								onChange={(e) =>
									setFormData({
										...formData,
										phase: e.target.value,
									})
								}
								placeholder='Enter phase name'
								required
							/>
						</div>
					)}

					<div className='flex justify-end gap-2 pt-4'>
						<Button
							type='button'
							variant='outline'
							onClick={() => onOpenChange(false)}
							disabled={isSubmitting}
						>
							Cancel
						</Button>
						<Button
							type='submit'
							className='bg-orange-600 hover:bg-orange-700 text-white'
							disabled={
								isSubmitting ||
								(isPhaseBlock
									? !formData.week ||
									  !formData.activities ||
									  !formData.phase
									: !formData.phase)
							}
						>
							{isSubmitting
								? "Saving..."
								: editingItem
								? "Update"
								: "Create"}
						</Button>
					</div>
				</form>
			</DialogContent>
		</Dialog>
	);
}
