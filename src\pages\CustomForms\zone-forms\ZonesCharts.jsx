"use client";

import { useState, useEffect } from "react";
import { ChevronUp, ChevronDown, AlertCircle } from "lucide-react";
import {
	BarChart,
	Bar,
	XAxis,
	YAxis,
	CartesianGrid,
	Tooltip,
	Legend,
	ResponsiveContainer,
	RadarChart,
	PolarGrid,
	PolarAngleAxis,
	PolarRadiusAxis,
	Radar,
	LineChart,
	Line,
	ReferenceLine,
} from "recharts";

import { Button } from "../../../components/ui/button";
import {
	Alert,
	AlertTitle,
	AlertDescription,
} from "../../../components/ui/alert";
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from "../../../components/ui/card";
import {
	Tabs,
	TabsContent,
	TabsList,
	TabsTrigger,
} from "../../../components/ui/tabs";
import {
	Collapsible,
	CollapsibleContent,
	CollapsibleTrigger,
} from "../../../components/ui/collapsible";

// Import mock data or fetch from API
const mockZones = {
	cycling: {
		heartRate: [
			{ zone: "1", name: "Recovery", start: 100, end: 120 },
			{ zone: "2", name: "Endurance", start: 121, end: 140 },
			{ zone: "3", name: "Tempo", start: 141, end: 160 },
			{ zone: "4", name: "Threshold", start: 161, end: 180 },
			{ zone: "5", name: "VO2 Max", start: 181, end: 200 },
		],
		power: [
			{ zone: "1", name: "Recovery", start: 100, end: 150 },
			{ zone: "2", name: "Endurance", start: 151, end: 200 },
			{ zone: "3", name: "Tempo", start: 201, end: 250 },
			{ zone: "4", name: "Threshold", start: 251, end: 300 },
			{ zone: "5", name: "VO2 Max", start: 301, end: 350 },
		],
		speed: [
			{ zone: "1", name: "Recovery", start: 15, end: 20 },
			{ zone: "2", name: "Endurance", start: 21, end: 25 },
			{ zone: "3", name: "Tempo", start: 26, end: 30 },
			{ zone: "4", name: "Threshold", start: 31, end: 35 },
			{ zone: "5", name: "VO2 Max", start: 36, end: 40 },
		],
	},
	running: {
		heartRate: [
			{ zone: "1", name: "Recovery", start: 110, end: 130 },
			{ zone: "2", name: "Endurance", start: 131, end: 150 },
			{ zone: "3", name: "Tempo", start: 151, end: 170 },
			{ zone: "4", name: "Threshold", start: 171, end: 185 },
			{ zone: "5", name: "VO2 Max", start: 186, end: 200 },
		],
		pace: [
			{ zone: "1", name: "Recovery", start: 7.0, end: 6.0 },
			{ zone: "2", name: "Endurance", start: 6.0, end: 5.0 },
			{ zone: "3", name: "Tempo", start: 5.0, end: 4.5 },
			{ zone: "4", name: "Threshold", start: 4.5, end: 4.0 },
			{ zone: "5", name: "VO2 Max", start: 4.0, end: 3.5 },
		],
	},
	swimming: {
		heartRate: [
			{ zone: "1", name: "Recovery", start: 100, end: 120 },
			{ zone: "2", name: "Endurance", start: 121, end: 140 },
			{ zone: "3", name: "Tempo", start: 141, end: 160 },
			{ zone: "4", name: "Threshold", start: 161, end: 180 },
			{ zone: "5", name: "VO2 Max", start: 181, end: 200 },
		],
		pace: [
			{ zone: "1", name: "Recovery", start: 2.5, end: 2.2 },
			{ zone: "2", name: "Endurance", start: 2.2, end: 2.0 },
			{ zone: "3", name: "Tempo", start: 2.0, end: 1.8 },
			{ zone: "4", name: "Threshold", start: 1.8, end: 1.6 },
			{ zone: "5", name: "VO2 Max", start: 1.6, end: 1.4 },
		],
	},
	core: {
		results: [
			{ workout: "Push-ups", level: "Intermediate", points: 15 },
			{ workout: "Sit-ups", level: "Advanced", points: 20 },
			{ workout: "Planks", level: "Intermediate", points: 15 },
			{ workout: "Pull-ups", level: "Beginner", points: 10 },
		],
	},
};

// Prepare chart data functions
const prepareHeartRateChartData = (zones) => {
	return zones.map((zone) => ({
		name: `Zone ${zone.zone}: ${zone.name}`,
		min: zone.start,
		max: zone.end,
		range: zone.end - zone.start,
		midpoint: (zone.start + zone.end) / 2,
	}));
};

const preparePowerChartData = (zones) => {
	return zones.map((zone) => ({
		name: `Zone ${zone.zone}: ${zone.name}`,
		min: zone.start,
		max: zone.end,
		range: zone.end - zone.start,
		midpoint: (zone.start + zone.end) / 2,
	}));
};

const prepareSpeedChartData = (zones) => {
	return zones.map((zone) => ({
		name: `Zone ${zone.zone}: ${zone.name}`,
		min: zone.start,
		max: zone.end,
		range: zone.end - zone.start,
		midpoint: (zone.start + zone.end) / 2,
	}));
};

const preparePaceChartData = (zones) => {
	return zones.map((zone) => ({
		name: `Zone ${zone.zone}: ${zone.name}`,
		min: zone.start,
		max: zone.end,
		range: Math.abs(zone.end - zone.start),
		midpoint: (zone.start + zone.end) / 2,
	}));
};

const prepareRadarChartData = (zones) => {
	return zones.map((zone) => ({
		subject: `Zone ${zone.zone}`,
		intensity: Number.parseInt(zone.zone) * 20, // Scale to 0-100
		fullMark: 100,
	}));
};

const prepareCoreChartData = (results) => {
	const levelMap = {
		Beginner: 1,
		Intermediate: 2,
		Advanced: 3,
		Elite: 4,
	};

	return results.map((result) => ({
		name: result.workout,
		points: result.points,
		level: levelMap[result.level] || 0,
		fullLevel: 4,
		value:
			result.unit === "time"
				? convertTimeToSeconds(result.value)
				: Number(result.value),
	}));
};

// Helper function for time conversion
const convertTimeToSeconds = (timeStr) => {
	if (!timeStr) return 0;
	const parts = timeStr.split(":");
	if (parts.length === 2) {
		return parseInt(parts[0]) * 60 + parseInt(parts[1]);
	}
	return parseInt(timeStr);
};

export default function ZoneCharts() {
	const [activeTab, setActiveTab] = useState("cycling");
	const [loading, setLoading] = useState(true);
	const [showCharts, setShowCharts] = useState(true);
	const [coreResults, setCoreResults] = useState([]);
	const [coreTotalPoints, setCoreTotalPoints] = useState(0);
	const [isZoneVisualizationOpen, setIsZoneVisualizationOpen] =
		useState(true);

	// Chart data states
	const [cyclingHeartRateChartData, setCyclingHeartRateChartData] = useState(
		[]
	);
	const [cyclingPowerChartData, setCyclingPowerChartData] = useState([]);
	const [cyclingSpeedChartData, setCyclingSpeedChartData] = useState([]);
	const [runningHeartRateChartData, setRunningHeartRateChartData] = useState(
		[]
	);
	const [runningPaceChartData, setRunningPaceChartData] = useState([]);
	const [swimmingHeartRateChartData, setSwimmingHeartRateChartData] =
		useState([]);
	const [swimmingPaceChartData, setSwimmingPaceChartData] = useState([]);
	const [coreChartData, setCoreChartData] = useState([]);
	const [radarChartData, setRadarChartData] = useState([]);

	// Load chart data
	useEffect(() => {
		const timer = setTimeout(() => {
			setLoading(false);

			// Prepare chart data
			setCyclingHeartRateChartData(
				prepareHeartRateChartData(mockZones.cycling.heartRate)
			);
			setCyclingPowerChartData(
				preparePowerChartData(mockZones.cycling.power)
			);
			setCyclingSpeedChartData(
				prepareSpeedChartData(mockZones.cycling.speed)
			);
			setRunningHeartRateChartData(
				prepareHeartRateChartData(mockZones.running.heartRate)
			);
			setRunningPaceChartData(
				preparePaceChartData(mockZones.running.pace)
			);
			setSwimmingHeartRateChartData(
				prepareHeartRateChartData(mockZones.swimming.heartRate)
			);
			setSwimmingPaceChartData(
				preparePaceChartData(mockZones.swimming.pace)
			);
			setCoreChartData(prepareCoreChartData(mockZones.core.results));
			setRadarChartData(
				prepareRadarChartData(mockZones.cycling.heartRate)
			);
		}, 1000);

		return () => clearTimeout(timer);
	}, []);

	// Custom tooltip for charts
	const CustomTooltip = ({ active, payload, label }) => {
		if (active && payload && payload.length) {
			return (
				<div className='bg-white p-3 border border-orange-200 rounded-md shadow-sm'>
					<p className='font-medium text-orange-800'>{label}</p>
					{payload.map((entry, index) => (
						<p key={index} className='text-sm'>
							{entry.name}: {entry.value}
						</p>
					))}
				</div>
			);
		}
		return null;
	};

	return (
		<div className='container mx-auto px-4 py-8'>
			<Collapsible
				open={isZoneVisualizationOpen}
				onOpenChange={setIsZoneVisualizationOpen}
				className='w-full mb-6'
			>
				<div className='flex items-center justify-between border-b pb-2 mb-4'>
					<CollapsibleTrigger className='flex items-center w-full justify-between'>
						<h2 className='text-xl font-medium text-orange-900'>
							Zone Visualizations
						</h2>
						{isZoneVisualizationOpen ? (
							<ChevronUp className='h-5 w-5 text-orange-500' />
						) : (
							<ChevronDown className='h-5 w-5 text-orange-500' />
						)}
					</CollapsibleTrigger>
				</div>
				<CollapsibleContent>
					<div className='p-4 bg-orange-50/50 rounded-md'>
						{activeTab === "cycling" && !loading && (
							<div className='space-y-6'>
								<div className='grid grid-cols-1 lg:grid-cols-2 gap-6'>
									<Card>
										<CardHeader>
											<CardTitle className='text-orange-800'>
												Heart Rate Zones
											</CardTitle>
											<CardDescription>
												Visualization of your heart rate
												training zones
											</CardDescription>
										</CardHeader>
										<CardContent>
											<div className='h-[300px]'>
												<ResponsiveContainer
													width='100%'
													height='100%'
												>
													<BarChart
														data={
															cyclingHeartRateChartData
														}
														margin={{
															top: 20,
															right: 30,
															left: 20,
															bottom: 70,
														}}
													>
														<CartesianGrid strokeDasharray='3 3' />
														<XAxis
															dataKey='name'
															angle={-45}
															textAnchor='end'
															height={70}
														/>
														<YAxis
															label={{
																value: "BPM",
																angle: -90,
																position:
																	"insideLeft",
															}}
														/>
														<Tooltip
															content={
																<CustomTooltip />
															}
														/>
														<Legend />
														<Bar
															dataKey='range'
															name='Range'
															fill='#f97316'
														/>
														<Bar
															dataKey='midpoint'
															name='Midpoint'
															fill='#ea580c'
														/>
													</BarChart>
												</ResponsiveContainer>
											</div>
										</CardContent>
									</Card>

									<Card>
										<CardHeader>
											<CardTitle className='text-orange-800'>
												Power Zones
											</CardTitle>
											<CardDescription>
												Visualization of your power
												training zones
											</CardDescription>
										</CardHeader>
										<CardContent>
											<div className='h-[300px]'>
												<ResponsiveContainer
													width='100%'
													height='100%'
												>
													<BarChart
														data={
															cyclingPowerChartData
														}
														margin={{
															top: 20,
															right: 30,
															left: 20,
															bottom: 70,
														}}
													>
														<CartesianGrid strokeDasharray='3 3' />
														<XAxis
															dataKey='name'
															angle={-45}
															textAnchor='end'
															height={70}
														/>
														<YAxis
															label={{
																value: "Watts",
																angle: -90,
																position:
																	"insideLeft",
															}}
														/>
														<Tooltip
															content={
																<CustomTooltip />
															}
														/>
														<Legend />
														<Bar
															dataKey='range'
															name='Range'
															fill='#f97316'
														/>
														<Bar
															dataKey='midpoint'
															name='Midpoint'
															fill='#ea580c'
														/>
													</BarChart>
												</ResponsiveContainer>
											</div>
										</CardContent>
									</Card>
								</div>

								<Card>
									<CardHeader>
										<CardTitle className='text-orange-800'>
											Training Zone Intensity
										</CardTitle>
										<CardDescription>
											Radar chart showing relative
											intensity of each zone
										</CardDescription>
									</CardHeader>
									<CardContent>
										<div className='h-[400px]'>
											<ResponsiveContainer
												width='100%'
												height='100%'
											>
												<RadarChart
													cx='50%'
													cy='50%'
													outerRadius='80%'
													data={radarChartData}
												>
													<PolarGrid />
													<PolarAngleAxis dataKey='subject' />
													<PolarRadiusAxis
														angle={30}
														domain={[0, 100]}
													/>
													<Radar
														name='Intensity'
														dataKey='intensity'
														stroke='#f97316'
														fill='#f97316'
														fillOpacity={0.6}
													/>
													<Legend />
												</RadarChart>
											</ResponsiveContainer>
										</div>
									</CardContent>
								</Card>
							</div>
						)}

						{activeTab === "running" && !loading && (
							<div className='space-y-6'>
								<div className='grid grid-cols-1 lg:grid-cols-2 gap-6'>
									<Card>
										<CardHeader>
											<CardTitle className='text-orange-800'>
												Heart Rate Zones
											</CardTitle>
											<CardDescription>
												Visualization of your heart rate
												training zones
											</CardDescription>
										</CardHeader>
										<CardContent>
											<div className='h-[300px]'>
												<ResponsiveContainer
													width='100%'
													height='100%'
												>
													<BarChart
														data={
															runningHeartRateChartData
														}
														margin={{
															top: 20,
															right: 30,
															left: 20,
															bottom: 70,
														}}
													>
														<CartesianGrid strokeDasharray='3 3' />
														<XAxis
															dataKey='name'
															angle={-45}
															textAnchor='end'
															height={70}
														/>
														<YAxis
															label={{
																value: "BPM",
																angle: -90,
																position:
																	"insideLeft",
															}}
														/>
														<Tooltip
															content={
																<CustomTooltip />
															}
														/>
														<Legend />
														<Bar
															dataKey='range'
															name='Range'
															fill='#f97316'
														/>
														<Bar
															dataKey='midpoint'
															name='Midpoint'
															fill='#ea580c'
														/>
													</BarChart>
												</ResponsiveContainer>
											</div>
										</CardContent>
									</Card>

									<Card>
										<CardHeader>
											<CardTitle className='text-orange-800'>
												Recent Pace vs Month
											</CardTitle>
											<CardDescription>
												Recent pace calculations by
												month (min/km)
											</CardDescription>
										</CardHeader>
										<CardContent>
											<div className='overflow-x-auto'>
												<table className='w-full border-collapse'>
													<thead>
														<tr className='bg-orange-50'>
															<th className='p-3 text-left border-b border-orange-200'>
																Month
															</th>
															<th className='p-3 text-left border-b border-orange-200'>
																Average Pace
															</th>
															<th className='p-3 text-left border-b border-orange-200'>
																Zone
															</th>
														</tr>
													</thead>
													<tbody>
														{runningPaceChartData.map(
															(data, index) => (
																<tr
																	key={index}
																	className='hover:bg-orange-50'
																>
																	<td className='p-3 border-b border-orange-200'>
																		{
																			data.name
																		}
																	</td>
																	<td className='p-3 border-b border-orange-200'>
																		{data.midpoint.toFixed(
																			2
																		)}{" "}
																		min/km
																	</td>
																	<td className='p-3 border-b border-orange-200'>
																		{data.name
																			.split(
																				":"
																			)[0]
																			.trim()}
																	</td>
																</tr>
															)
														)}
													</tbody>
												</table>
											</div>
										</CardContent>
									</Card>
								</div>
							</div>
						)}

						{activeTab === "swimming" && !loading && (
							<div className='space-y-6'>
								<div className='grid grid-cols-1 lg:grid-cols-2 gap-6'>
									<Card>
										<CardHeader>
											<CardTitle className='text-orange-800'>
												Heart Rate Zones
											</CardTitle>
											<CardDescription>
												Visualization of your heart rate
												training zones
											</CardDescription>
										</CardHeader>
										<CardContent>
											<div className='h-[300px]'>
												<ResponsiveContainer
													width='100%'
													height='100%'
												>
													<BarChart
														data={
															swimmingHeartRateChartData
														}
														margin={{
															top: 20,
															right: 30,
															left: 20,
															bottom: 70,
														}}
													>
														<CartesianGrid strokeDasharray='3 3' />
														<XAxis
															dataKey='name'
															angle={-45}
															textAnchor='end'
															height={70}
														/>
														<YAxis
															label={{
																value: "BPM",
																angle: -90,
																position:
																	"insideLeft",
															}}
														/>
														<Tooltip
															content={
																<CustomTooltip />
															}
														/>
														<Legend />
														<Bar
															dataKey='range'
															name='Range'
															fill='#f97316'
														/>
														<Bar
															dataKey='midpoint'
															name='Midpoint'
															fill='#ea580c'
														/>
													</BarChart>
												</ResponsiveContainer>
											</div>
										</CardContent>
									</Card>

									<Card>
										<CardHeader>
											<CardTitle className='text-orange-800'>
												Pace Zones
											</CardTitle>
											<CardDescription>
												Visualization of your pace
												training zones (min/100m)
											</CardDescription>
										</CardHeader>
										<CardContent>
											<div className='h-[300px]'>
												<ResponsiveContainer
													width='100%'
													height='100%'
												>
													<LineChart
														data={
															swimmingPaceChartData
														}
														margin={{
															top: 20,
															right: 30,
															left: 20,
															bottom: 70,
														}}
													>
														<CartesianGrid strokeDasharray='3 3' />
														<XAxis
															dataKey='name'
															angle={-45}
															textAnchor='end'
															height={70}
														/>
														<YAxis
															label={{
																value: "min/100m",
																angle: -90,
																position:
																	"insideLeft",
															}}
															domain={[
																"dataMin - 0.2",
																"dataMax + 0.2",
															]}
														/>
														<Tooltip
															content={
																<CustomTooltip />
															}
														/>
														<Legend />
														<Line
															type='monotone'
															dataKey='min'
															name='Faster Pace'
															stroke='#f97316'
															strokeWidth={2}
															dot={{ r: 6 }}
														/>
														<Line
															type='monotone'
															dataKey='max'
															name='Slower Pace'
															stroke='#ea580c'
															strokeWidth={2}
															dot={{ r: 6 }}
														/>
													</LineChart>
												</ResponsiveContainer>
											</div>
										</CardContent>
									</Card>
								</div>
							</div>
						)}

						{activeTab === "core" && !loading && (
							<div className='space-y-6'>
								<Card>
									<CardHeader>
										<CardTitle className='text-orange-800'>
											Core Exercise Performance
										</CardTitle>
										<CardDescription>
											Visualization of your core exercise
											results
										</CardDescription>
									</CardHeader>
									<CardContent>
										<div className='h-[300px]'>
											<ResponsiveContainer
												width='100%'
												height='100%'
											>
												<BarChart
													data={
														coreResults.length > 0
															? coreChartData
															: prepareCoreChartData(
																	mockZones
																		.core
																		.results
															  )
													}
													margin={{
														top: 20,
														right: 30,
														left: 20,
														bottom: 70,
													}}
												>
													<CartesianGrid strokeDasharray='3 3' />
													<XAxis dataKey='name' />
													<YAxis
														yAxisId='left'
														orientation='left'
														label={{
															value: "Points",
															angle: -90,
															position:
																"insideLeft",
														}}
													/>
													<YAxis
														yAxisId='right'
														orientation='right'
														label={{
															value: "Level",
															angle: 90,
															position:
																"insideRight",
														}}
														domain={[0, 4]}
													/>
													<Tooltip
														content={
															<CustomTooltip />
														}
													/>
													<Legend />
													<Bar
														yAxisId='left'
														dataKey='points'
														name='Points'
														fill='#f97316'
													/>
													<Bar
														yAxisId='right'
														dataKey='level'
														name='Fitness Level'
														fill='#ea580c'
													/>
													<ReferenceLine
														yAxisId='right'
														y={1}
														stroke='#94a3b8'
														strokeDasharray='3 3'
														label='Beginner'
													/>
													<ReferenceLine
														yAxisId='right'
														y={2}
														stroke='#94a3b8'
														strokeDasharray='3 3'
														label='Intermediate'
													/>
													<ReferenceLine
														yAxisId='right'
														y={3}
														stroke='#94a3b8'
														strokeDasharray='3 3'
														label='Advanced'
													/>
													<ReferenceLine
														yAxisId='right'
														y={4}
														stroke='#94a3b8'
														strokeDasharray='3 3'
														label='Elite'
													/>
												</BarChart>
											</ResponsiveContainer>
										</div>
									</CardContent>
								</Card>

								{coreResults.length > 0 && (
									<Card>
										<CardHeader>
											<CardTitle className='text-orange-800'>
												Performance Metrics
											</CardTitle>
											<CardDescription>
												Raw performance values for each
												exercise
											</CardDescription>
										</CardHeader>
										<CardContent>
											<div className='h-[300px]'>
												<ResponsiveContainer
													width='100%'
													height='100%'
												>
													<BarChart
														data={coreChartData}
														margin={{
															top: 20,
															right: 30,
															left: 20,
															bottom: 70,
														}}
													>
														<CartesianGrid strokeDasharray='3 3' />
														<XAxis dataKey='name' />
														<YAxis
															label={{
																value: "Value",
																angle: -90,
																position:
																	"insideLeft",
															}}
														/>
														<Tooltip
															content={
																<CustomTooltip />
															}
														/>
														<Legend />
														<Bar
															dataKey='value'
															name='Performance'
															fill='#f97316'
														/>
													</BarChart>
												</ResponsiveContainer>
											</div>
										</CardContent>
									</Card>
								)}
							</div>
						)}
					</div>
				</CollapsibleContent>
			</Collapsible>
		</div>
	);
}
