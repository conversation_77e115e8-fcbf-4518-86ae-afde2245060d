import { useState, useEffect } from "react";
import { But<PERSON> } from "../ui/button";
import { Input } from "../ui/input";
import { Label } from "../ui/label";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "../ui/select";
import { <PERSON>alog, DialogContent, DialogHeader, DialogTitle } from "../ui/dialog";
import {
	createsubspackagedata,
	updatesubspackagedata,
	getAllPrograms,
} from "../../API/api-endpoint";
import Swal from "sweetalert2";

export const SubscriptionDialog = ({
	open,
	onClose,
	onSuccess,
	editingItem,
}) => {
	const [formData, setFormData] = useState({
		program_id: "",
		name: "",
		duration: "",
		price: "",
		iosIapProduct: "",
		status: true,
	});
	const [isLoading, setIsLoading] = useState(false);
	const [programs, setPrograms] = useState([]);

	useEffect(() => {
		const fetchPrograms = async () => {
			try {
				const response = await getAllPrograms();
				setPrograms(response?.rows || []);
			} catch (error) {
				console.error("Error fetching programs:", error);
				setPrograms([]);
			}
		};

		if (open) {
			fetchPrograms();
		}
	}, [open]);

	useEffect(() => {
		if (open) {
			if (editingItem?.id) {
				const editData = {
					program_id: editingItem.program_id || "",
					name: editingItem.name || "",
					duration: editingItem.duration || "",
					price: editingItem.price || "",
					iosIapProduct: editingItem.iosIapProduct || "",
					status:
						editingItem.status !== undefined
							? editingItem.status
							: true,
				};
				setFormData(editData);
			} else {
				const newData = {
					program_id: "",
					name: "",
					duration: "",
					price: "",
					iosIapProduct: "",
					status: true,
				};
				setFormData(newData);
			}
		}
	}, [open, editingItem]);

	const handleInputChange = (field, value) => {
		setFormData((prev) => ({
			...prev,
			[field]: value,
		}));
	};

	const handleSubmit = async (e) => {
		e.preventDefault();

		if (!formData.program_id) {
			Swal.fire({
				title: "Error",
				text: "Program is required",
				icon: "error",
				timer: 1800,
				showConfirmButton: false,
			});
			return;
		}

		if (!formData.name.trim()) {
			Swal.fire({
				title: "Error",
				text: "Name is required",
				icon: "error",
				timer: 1800,
				showConfirmButton: false,
			});
			return;
		}

		if (!formData.duration.trim()) {
			Swal.fire({
				title: "Error",
				text: "Duration is required",
				icon: "error",
				timer: 1800,
				showConfirmButton: false,
			});
			return;
		}

		if (!formData.price.trim()) {
			Swal.fire({
				title: "Error",
				text: "Price is required",
				icon: "error",
				timer: 1800,
				showConfirmButton: false,
			});
			return;
		}

		try {
			setIsLoading(true);

			const apiData = {
				program_id: formData.program_id,
				name: formData.name.trim(),
				duration: formData.duration.trim(),
				price: formData.price.trim(),
				iosIapProduct: formData.iosIapProduct.trim(),
				status: formData.status,
			};

			let response;
			if (editingItem?.id) {
				apiData.id = editingItem.id;
				response = await updatesubspackagedata(apiData);
			} else {
				response = await createsubspackagedata(apiData);
			}

			if (response?.status) {
				Swal.fire({
					title: "Success",
					text:
						response.message ||
						`Subscription ${
							editingItem?.id ? "updated" : "created"
						} successfully`,
					icon: "success",
					timer: 2000,
					showConfirmButton: false,
				});
				onSuccess();
			} else {
				Swal.fire({
					title: "Error",
					text: response?.message || "Failed to save subscription",
					icon: "error",
					timer: 3000,
					showConfirmButton: false,
				});
			}
		} catch (error) {
			console.error("Error saving subscription:", error);
			Swal.fire({
				title: "Error",
				text: "An error occurred while saving the subscription",
				icon: "error",
				timer: 3000,
				showConfirmButton: false,
			});
		} finally {
			setIsLoading(false);
		}
	};

	return (
		<Dialog open={open} onOpenChange={onClose}>
			<DialogContent className='sm:max-w-md bg-white max-h-[90vh] overflow-y-auto'>
				<DialogHeader>
					<DialogTitle className='text-lg font-semibold text-gray-900'>
						{editingItem?.id
							? "Edit Subscription"
							: "Create Subscription"}
					</DialogTitle>
				</DialogHeader>

				<form onSubmit={handleSubmit} className='space-y-4'>
					<div className='grid gap-4'>
						<div className='space-y-2'>
							<Label
								htmlFor='program_id'
								className='text-sm font-semibold'
							>
								Program <span className='text-red-500'>*</span>
							</Label>
							<Select
								value={formData.program_id}
								onValueChange={(value) =>
									handleInputChange("program_id", value)
								}
								disabled={isLoading}
							>
								<SelectTrigger className='w-full text-sm'>
									<SelectValue placeholder='Select Program' />
								</SelectTrigger>
								<SelectContent className='bg-white'>
									{Array.isArray(programs) &&
										programs.map((program) => (
											<SelectItem
												key={program.program_id}
												value={program.program_id}
											>
												{program.program_name}
											</SelectItem>
										))}
								</SelectContent>
							</Select>
						</div>

						<div className='space-y-2'>
							<Label
								htmlFor='name'
								className='text-sm font-semibold'
							>
								Name <span className='text-red-500'>*</span>
							</Label>
							<Input
								id='name'
								className='w-full text-sm'
								value={formData.name}
								onChange={(e) =>
									handleInputChange("name", e.target.value)
								}
								placeholder='Enter subscription name'
								disabled={isLoading}
							/>
						</div>

						<div className='space-y-2'>
							<Label
								htmlFor='duration'
								className='text-sm font-semibold'
							>
								Duration <span className='text-red-500'>*</span>
							</Label>
							<Input
								id='duration'
								className='w-full text-sm'
								value={formData.duration}
								onChange={(e) =>
									handleInputChange(
										"duration",
										e.target.value
									)
								}
								placeholder='Enter duration'
								disabled={isLoading}
							/>
						</div>

						<div className='space-y-2'>
							<Label
								htmlFor='price'
								className='text-sm font-semibold'
							>
								Price <span className='text-red-500'>*</span>
							</Label>
							<Input
								id='price'
								className='w-full text-sm'
								value={formData.price}
								onChange={(e) =>
									handleInputChange("price", e.target.value)
								}
								placeholder='Enter price'
								disabled={isLoading}
							/>
						</div>

						<div className='space-y-2'>
							<Label
								htmlFor='iosIapProduct'
								className='text-sm font-semibold'
							>
								iOS IAP Product Identifier
							</Label>
							<Input
								id='iosIapProduct'
								className='w-full text-sm'
								value={formData.iosIapProduct}
								onChange={(e) =>
									handleInputChange(
										"iosIapProduct",
										e.target.value
									)
								}
								placeholder='com.yoska.<academy>.<subscription-duration>'
								disabled={isLoading}
							/>
						</div>

						<div className='space-y-2'>
							<Label
								htmlFor='status'
								className='text-sm font-semibold'
							>
								Status
							</Label>
							<Select
								value={formData.status.toString()}
								onValueChange={(value) =>
									handleInputChange(
										"status",
										value === "true"
									)
								}
								disabled={isLoading}
							>
								<SelectTrigger className='w-full text-sm'>
									<SelectValue placeholder='Select Status' />
								</SelectTrigger>
								<SelectContent className='bg-white'>
									<SelectItem value='true'>Yes</SelectItem>
									<SelectItem value='false'>No</SelectItem>
								</SelectContent>
							</Select>
						</div>
					</div>

					<div className='flex justify-end gap-3 pt-4'>
						<Button
							type='button'
							variant='outline'
							onClick={onClose}
							disabled={isLoading}
						>
							Cancel
						</Button>
						<Button
							type='submit'
							className='bg-orange-600 hover:bg-orange-700 text-white'
							disabled={isLoading}
						>
							{isLoading
								? "Saving..."
								: editingItem?.id
								? "Update Subscription"
								: "Create Subscription"}
						</Button>
					</div>
				</form>
			</DialogContent>
		</Dialog>
	);
};
