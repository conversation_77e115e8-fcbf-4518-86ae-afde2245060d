import { Box, Toolbar } from '@mui/material'
import React, { useState } from 'react'
import KeyboardBackspaceIcon from '@mui/icons-material/KeyboardBackspace';
import '../../styles/navbar.css'
import { useNavigate } from 'react-router-dom';

export default function NavGroupInfo({  groupName,setOpenPageOne,openPageOne }) {
    const [checked] = useState(false)
    const navigate = useNavigate()

    const handleGoBack = () => setOpenPageOne("home")

    return <>
        <Toolbar className="appBar" style={{
            // backgroundColor:"#F5F5F5", 
            // backgroundColor: "#E67E22"
            backgroundColor: "rgb(255, 222, 173)"
            }}>
        {openPageOne === "create-group" ?
    <Box sx={{ display: 'flex', cursor: "pointer" }}>
        <KeyboardBackspaceIcon onClick={handleGoBack} />
    </Box>
    : null
}

            <Box className="readUnreadButton" sx={{margin: "0 auto"}}>
                <span style={!checked ? { color: "black", fontSize: 15 } : {}}>
                    {groupName}
                </span>
            </Box>
            <Box >
            </Box>
        </Toolbar >
    </>
}