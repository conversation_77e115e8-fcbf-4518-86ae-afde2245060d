import React, { useState, useEffect } from 'react';
import { Card, CardActionArea, CardMedia, CardContent, Typography } from '@mui/material';

function UrlPreview() {

    const url = 'meet.google.com/dnm-adpy-fir'

    const [previewData, setPreviewData] = useState(null);

    useEffect(() => {
        const fetchUrlPreview = async () => {
            try {
                const response = await fetch(url);
                const data = await response.text();

                let previewData = null;

                if (url.includes('google.com/meet')) {
                    previewData = extractGoogleMeetPreviewData(data);
                } else if (url.includes('docs.google.com')) {
                    previewData = extractGoogleDocsPreviewData(data);
                } else if (url.endsWith('.pdf')) {
                    previewData = extractPdfPreviewData(data);
                } else {
                    previewData = extractWebsitePreviewData(data);
                }

                setPreviewData(previewData);
            } catch (error) {
                console.error('Error fetching URL preview:', error);
            }
        };

        fetchUrlPreview();
    }, [url]);

    const extractGoogleMeetPreviewData = (data) => {
        const titleRegex = /<title>(.*?)<\/title>/i;
        const match = data.match(titleRegex);

        const title = match ? match[1] : 'Google Meet';
        const description = 'Join a Google Meet meeting';
        const image = '/path/to/google-meet-thumbnail.jpg';

        return { title, description, image };
    };

    const extractGoogleDocsPreviewData = (data) => {
        const titleRegex = /<title>(.*?)<\/title>/i;
        const descriptionRegex = /<meta name="description" content="(.*?)"\s?\/?>/i;

        const titleMatch = data.match(titleRegex);
        const descriptionMatch = data.match(descriptionRegex);

        const title = titleMatch ? titleMatch[1] : 'Google Docs';
        const description = descriptionMatch ? descriptionMatch[1] : '';
        const image = '/path/to/google-docs-thumbnail.jpg';

        return { title, description, image };
    };

    const extractPdfPreviewData = (data) => {
        const titleRegex = /<title>(.*?)<\/title>/i;
        const titleMatch = data.match(titleRegex);

        const title = titleMatch ? titleMatch[1] : 'PDF Document';
        const description = 'View a PDF document';
        const image = '/path/to/pdf-thumbnail.jpg';

        return { title, description, image };
    };

    const extractWebsitePreviewData = (data) => {
        const titleRegex = /<title>(.*?)<\/title>/i;
        const descriptionRegex = /<meta name="description" content="(.*?)"\s?\/?>/i;

        const titleMatch = data.match(titleRegex);
        const descriptionMatch = data.match(descriptionRegex);

        const title = titleMatch ? titleMatch[1] : 'Website';
        const description = descriptionMatch ? descriptionMatch[1] : '';
        const image = '/path/to/default-thumbnail.jpg';

        return { title, description, image };
    };

    if (!previewData) {
        return null;
    }

    return (
        <Card>
            <CardActionArea href={url} target="_blank">
                {previewData.image && (
                    <CardMedia
                        component="img"
                        height="140"
                        image={previewData.image}
                        alt="Link Preview"
                    />
                )}
                <CardContent>
                    <Typography gutterBottom variant="h5" component="div">
                        {previewData.title}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                        {previewData.description}
                    </Typography>
                </CardContent>
            </CardActionArea>
        </Card>
    );
}

export default UrlPreview;

