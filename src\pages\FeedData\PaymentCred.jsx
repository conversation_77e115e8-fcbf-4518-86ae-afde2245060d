import React, { useEffect, useMemo, useState } from "react";
import Paper from "@mui/material/Paper";
import Table from "@mui/material/Table";
import TableBody from "@mui/material/TableBody";
import TableContainer from "@mui/material/TableContainer";
import TableHead from "@mui/material/TableHead";
import TableRow from "@mui/material/TableRow";
import TableCell, { tableCellClasses } from "@mui/material/TableCell";
import { styled } from "@mui/material/styles";
import { Button, CircularProgress, FormLabel, Grid, Pagination, TextField } from "@mui/material";
import "../../components/AssignCoach/Assign.css";
import { ExclamationCircleFilled } from '@ant-design/icons';
import Header from "../../components/Header";
import { URL, deletePaymentCredData, getPaymentCredData } from "../../API/api-endpoint";
import { IconEdit, IconTrash } from "@tabler/icons";
import { Modal } from "antd";
import CreatePromotors from "../../components/FeedData/CreatePromotors";
import moment from "moment";
import PaymentCreds from "../../components/FeedData/PaymentCreds";

let PageSize = 15;
const StyledTableCell = styled(TableCell)(({ theme }) => ({
  [`&.${tableCellClasses.head}`]: {
    backgroundColor: "#1e40af",
    color: theme.palette.common.white,
  },
  [`&.${tableCellClasses.body}`]: {
    fontSize: 14,
  },
}));
const StyledTableRow = styled(TableRow)(({ theme }) => ({
  "&:nth-of-type(odd)": {
    backgroundColor: theme.palette.action.hover,
  },
  // hide last border
  "&:last-child td, &:last-child th": {
    border: 0,
  },
}));
const PaymentCred = () => {
  const { confirm } = Modal;
  const [reportData, setReportData] = useState()
  const [editData, setEditData] = useState()
  const [isLoading, setIsLoading] = useState(true)
  const [showAssesmentModal, setShowAssesmentModal] = useState(false);

  const fetchReport = async () => {
    const response = await getPaymentCredData()
    setIsLoading(false)
    console.log("response", response);
    setReportData(response)
  }
  useEffect(() => {
    fetchReport()
  }, [])
  const [currentPage, setCurrentPage] = useState(1);
  const checkLastPage = useMemo(() => {
    let frstPgae = (currentPage - 1) * PageSize;
    let lastPage = frstPgae + PageSize;
    return reportData?.slice(frstPgae, lastPage)?.map((row, index) => ({
      ...row,
      srID: index + 1 + (currentPage > 1 ? frstPgae : 0),
    }));
  }, [currentPage, reportData]);
  const handlePageChange = (event, page) => {
    setCurrentPage(page);
  };

  const DeleteZones = async (id) => {

    const response = await deletePaymentCredData(id)
    setCurrentPage(1)
    fetchReport()
    console.log("res", response);
  }
  const showDeleteConfirm = (id) => {
    confirm({
      title: 'Are you sure delete this payment creds?',
      icon: <ExclamationCircleFilled />,
      okText: 'Yes',
      okType: 'danger',
      cancelText: 'No',
      onOk() {
        DeleteZones(id)
      },
      onCancel() {
        console.log('Cancel');
      },
    });
  };
  return (
    <div>
      <Header />
      <div className="grid grid-cols-1 xl:grid-cols-5 items-start gap-x-4"></div>
      <div style={{ marginTop: "100px", padding: "20px" }}>
        <TableContainer component={Paper}>
          <div style={{ fontSize: "18px", background: "#FFEADC", width: "100%", padding: "10px" }}>
            <h1 className='mb-2' style={{ fontWeight: "700", fontSize: "20px" }}>Payment Creds</h1>
            <Grid container spacing={2}>
              {/*
            
             <Grid item xs={12} sm={12} sx={{ marginTop: "30px" }}>
                <Button variant="contained" onClick={() => {
                  setShowAssesmentModal(true)
                  setEditData({})
                }}>Create Payment Creds</Button>
              </Grid>
            */}
            </Grid>
          </div>
          <TableContainer style={{ maxHeight: 550, scrollbarWidth: "none" }}>
            <Table stickyHeader sx={{ minWidth: 700 }} aria-label="customized table">
              <TableHead>
                <TableRow>
                  <StyledTableCell align="left">Sr ID</StyledTableCell>
                  <StyledTableCell align="left">Key </StyledTableCell>
                  <StyledTableCell align="left">Created On</StyledTableCell>
                  <StyledTableCell align="left">Updated On</StyledTableCell>
                  <StyledTableCell align="left">Action</StyledTableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {isLoading ? (
                  <CircularProgress className="m-6" />) : (
                  <>
                    {checkLastPage?.length > 0 ? (
                      <>
                        {checkLastPage?.map((row, index) => (
                          <StyledTableRow key={index}>
                            <StyledTableCell align="left">
                              {row?.srID}
                            </StyledTableCell>

                            <StyledTableCell align="left">
                              {row?.key}
                            </StyledTableCell>

                            <StyledTableCell align="left">
                              {moment(row?.createdAt).format('Do MMM YYYY')}
                            </StyledTableCell>
                            <StyledTableCell align="left">
                              {moment(row?.updatedAt).format('Do MMM YYYY')}

                            </StyledTableCell>
                            <StyledTableCell align="left">
                              <div className="flex ">
                                <span className="px-2 cursor-pointer">
                                  <IconEdit
                                    color="blue"
                                    onClick={() => {
                                      setShowAssesmentModal(true);
                                      setEditData(row);
                                    }}
                                  />
                                </span>
                                <span className="px-2 cursor-pointer">
                                  <IconTrash
                                    color="red"
                                    onClick={() => showDeleteConfirm(row?.id)}
                                  />
                                </span>
                              </div>
                            </StyledTableCell>
                          </StyledTableRow>
                        ))}
                      </>
                    ) : (
                      <div className="p-4">No data found</div>
                    )}
                  </>
                )}
              </TableBody>
            </Table>
          </TableContainer>
        </TableContainer>
        &nbsp;
        <div className="flex justify-end">
          <Pagination
            count={Math.ceil(reportData?.length / PageSize)} // Calculate total number of pages
            color="primary"
            page={currentPage}
            onChange={handlePageChange}
          />
        </div>
      </div>
      {
        <PaymentCreds setEditData={setEditData} editData={editData} fetchReport={fetchReport} showAssesmentModal={showAssesmentModal} setShowAssesmentModal={setShowAssesmentModal} />}
    </div>
  )
}
export default PaymentCred
