import { Box, Typography, CircularProgress } from "@mui/material";
import React, { useEffect, useState } from "react";
import "./../../../styles/individualChat.css";
import NavGroupChat from "../../../components/navbar/NavGroupChat";
import { useDispatch, useSelector } from "react-redux";
import "./../../../styles/footerFunctions.css";
import {
  get,
  getDatabase,
  ref as rtdbRef,
} from "firebase/database";
import { getGroupChatMessagesAction } from "../../../redux/action/groupAction";
import GroupMsg from "./GroupMsg";
import { useRef } from "react";
import Header from "../../../../../components/Header";
import PubNub from 'pubnub';
import axios from 'axios';
import { URL } from "../../../../../API/api-endpoint";
import { storeOpenedGroupInfoAction } from "../../../redux/action/groupAction";
import styles from "@chatscope/chat-ui-kit-styles/dist/default/styles.min.css";
import {
  MainContainer,
  ChatContainer,
  MessageList,
  Message,
  MessageInput,
} from "@chatscope/chat-ui-kit-react";
import { set } from "lodash";

export default function GroupChatCreator({setOpenPage}) {
  const dispatch = useDispatch();
  const { openedGroupInfo, openedGroupMessages } = useSelector(
    (state) => state.group
  );
  const { currentUser } = useSelector((state) => state.auth);
  const { openedGroupData } = useSelector((state) => state.group);
  const [getMessages, setGetMessages] = useState(false);
  const [isReplyMsg, setIsReplyMsg] = useState(false);
  const [membersPallate, setMembersPallate] = useState("");
  const [isSearchActive, setIsSearchActive] = useState(false);
  const [searchKeyword, setSearchKeyword] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const rootContainerRef = useRef(null);
  const [messages, setMessages] = useState([]);
  const [input, setInput] = useState('');
  const [replyTo, setReplyTo] = useState(null);
  const [file, setFile] = useState(null);
  const [fileName, setFileName] = useState('');
  const [allMessages, setAllMessages] = useState([]);
  const [isChatActive, setIsChatActive] = useState(true);

  // ----- Set the opened group details in global state ------
  const setGroupDetailsGlabally = async (groupName, groupId) => {
    try {
      const authToken = localStorage.getItem("token");
      const groupID = parseInt(groupId);

      const response = await axios.get(`${URL}/group-chat/${groupID}`, {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `${authToken}`
        },
      });

      const groups = response.data.data;
      if (groups.length === 0) {
        const response = await axios.get(`${URL}/subscribe-group`, {
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `${authToken}`
          },
        });
        if (response.status === 200) {
          const allGroups = response.data.data;
          const filteredGroup = allGroups.find(group => group.communitygroup.id === groupId);

          if (filteredGroup) {
            const foundGroup = filteredGroup.communitygroup;
            dispatch(storeOpenedGroupInfoAction({ "groupDetail": foundGroup }));
          } else {
            console.log('Group not found with the specified groupId.');
            return null;
          }
        } else {
          console.error('Failed to fetch groups:', response.statusText);
          return null;
        }
      } else {
        const foundGroup = groups.find(group => group.displayName === groupName);

        if (foundGroup) {
          dispatch(storeOpenedGroupInfoAction({ "groupDetail": foundGroup }));
        } else {
          console.log("Group not found");
        }
      }
    } catch (error) {
      console.error('Error fetching user chats:', error);
    }
  };

  const handleGetTwoMembers = async () => {
    const { groupMembers } = openedGroupInfo;
    let str = "";
    let groupName = null;
    const { groupId } = openedGroupInfo;
    const db = getDatabase();
    const groupRef = rtdbRef(db, `groups/${groupId}`);
    const snapshot = await get(groupRef);

    if (snapshot.exists()) {
      groupName = snapshot.val().groupName;
      setGroupDetailsGlabally(groupName, groupId);
    } else {
      throw new Error("Group data not found.");
    }

    try {
      if (Array.isArray(groupMembers)) {
        const promises = groupMembers.map(async (item) => {
          const userRef = rtdbRef(db, `users/${item}`);
          const snapshot = await get(userRef);

          if (snapshot.exists()) {
            return snapshot.val().displayName;
          } else {
            throw new Error("User data not found.");
          }
        });

        const userDataArray = await Promise.all(promises);
        str = userDataArray.join(", ");
        setMembersPallate(str);
      } else {
        console.error("groupMembers is not an array.");
      }
    } catch (error) {
      console.error("Error getting user data:", error);
      throw error;
    }
  };

  useEffect(() => {
    handleGetTwoMembers();
    dispatch(getGroupChatMessagesAction(openedGroupInfo.groupId));
    return () => {
      dispatch(getGroupChatMessagesAction(openedGroupInfo.groupId));
    };
  }, [getMessages, openedGroupInfo?.groupMembers]);

  const pubnub = new PubNub({
    publishKey: process.env.REACT_APP_CHAT_PUBLISH_KEY,
    subscribeKey: process.env.REACT_APP_CHAT_SUBSCRIBE_KEY,
    // uuid: 'user-' + Math.random().toString(36).substring(2, 9),
    uuid: currentUser.uid,
  });
  const [userId] = useState(pubnub.getUserId());
  const generateId = () => Math.random().toString(36).substring(2, 9);

  // ----- Set the total message count in a state ------ 
  // const getTotalMessageCount = async () => {
  //   try {
  //     const authToken = localStorage.getItem("token");
  //     const groupId = openedGroupData.groupDetail?.id;

  //     if (!groupId) {
  //       console.error("No group ID found.");
  //       return;
  //     }

  //     const response = await axios.get(`${URL}/group-message/${groupId}`, {
  //       params: {
  //         limit: 999,
  //       },
  //       headers: {
  //         'Content-Type': 'application/json',
  //         'Authorization': `${authToken}`
  //       },
  //     });

  //     if (response.status === 200) {
  //       setTotalNoOfMessage(response.data.data.length);
  //     }
  //   } catch (error) {
  //     console.error("Error fetching total message count:", error);
  //   }
  // }

  // getTotalMessageCount();

  // --- Fetch chat-messages from the API ---
  const fetchMessagesFromApi = async (page) => {
    try {
      const groupId = openedGroupData?.groupDetail?.id;
      console.log("APIgroupId", groupId);
      
      const response = await axios.get(`${URL}/group-message/${groupId}`, {
        params: {
          limit: 999,
        },
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `${localStorage.getItem("token")}`,
        },
      });
      console.log("API response", response.data.data);
      if (response.status === 200) {
        if (response.data.data.length > 0) {
          return response.data.data
            .map((msg) => ({
              id: msg.id,
              text: msg.message,
              type: "received",
              replyTo: null,
              fileUrl: null,
              userId: msg.user.id,
              timestamp: new Date(msg.msgTimestamp).getTime(),
              userName: `${msg.user.firstname} ${msg.user.lastname}`,
            }))
            .reverse();
        } else {
          console.log("No more messages in API.");
          return [];
        }
      } else {
        console.error("Failed to fetch message history:", response.status);
        return [];
      }
    } catch (error) {
      console.error("Error fetching messages from API:", error);
      return [];
    }
  };

  // ---- Handle messages from PubNub and API ----
  // const handleGetMessages = async () => {
  //   pubnub.fetchMessages(
  //     {
  //       channels: [openedGroupData?.groupDetail?.channelName],
  //       count: 100,
  //       reverse: false,
  //     },
  //     async (status, response) => {
  //       let historyMessages = [];
  //       if (!status.error) {
  //         historyMessages =
  //           response.channels[openedGroupData.groupDetail.channelName]?.map((msg) => (
  //           {
  //             id: generateId(),
  //             text: msg.message.description || msg.message.message,
  //             type: 'received',
  //             replyTo: msg.message.replyTo || null,
  //             fileUrl: msg.message.fileUrl || null,
  //             userId: msg.message.userId || msg.message.senderId,
  //             timestamp: msg.message.timestamp || msg.message.msg.message.timeStamp,
  //             userName: msg.message.userName || msg.message.senderName
  //           })).reverse() || [];
  //       }

  //       if (historyMessages.length > 0) {
  //         console.log("PubNub messages found.");
  //         setMessages(historyMessages.reverse());
  //       } else if (historyMessages.length === 0) {
  //         console.log("No PubNub messages found. Calling Api");
  //         const apiMessages = await fetchMessagesFromApi();
  //         console.log("apiMessages length", apiMessages.length);
          
  //         if (apiMessages.length > 0) {
  //           setMessages(apiMessages.reverse());
  //         } else {
  //           console.log("No messages found.");
  //           setMessages([]);
  //         }
  //       }
  //     }
  //   );
  // };
  
  const handleGetMessages = async () => {
    pubnub.fetchMessages(
      {
        channels: [openedGroupData?.groupDetail?.channelName],
        count: 100,
        reverse: false,
      },
      async (status, response) => {
        let historyMessages = [];
        
        if (!status.error) {
          historyMessages = response.channels[openedGroupData.groupDetail.channelName]?.map((msg) => ({
            id: generateId(),
            text: msg.message.description || msg.message.message,
            type: 'received',
            replyTo: msg.message.replyTo || null,
            fileUrl: msg.message.fileUrl || null,
            userId: msg.message.userId || msg.message.senderId,
            timestamp: msg.message.timestamp || msg.message.msg.message.timeStamp,
            userName: msg.message.userName || msg.message.senderName
          })) || [];
        }
  
        if (historyMessages.length > 0) {
          console.log("PubNub messages found.");
          setMessages(historyMessages);
        } else {
          console.log("No PubNub messages found. Fetching from API...");
          const apiMessages = await fetchMessagesFromApi();
          
          if (apiMessages.length > 0) {
            setMessages(apiMessages);
          } else {
            console.log("No messages found.");
            setMessages([]);
          }
        }
      }
    );
  };

  // useEffect(() => {
  //   handleGetMessages();
  // }, [openedGroupData]);

  useEffect(() => {
    if (openedGroupData?.groupDetail?.channelName) {
      handleGetMessages();
    }
  }, [openedGroupData?.groupDetail?.channelName]);

  // useEffect(() => {
  //   handleGetMessages();
  // }, [openedGroupData?.groupDetail?.id]);

  const publishMessage = async (message, replyToMessage = null, fileUrl = null) => {
    try {
      const openedGroup = openedGroupData.groupDetail;
      const authToken = localStorage.getItem("token");
      const loggedInUserId = parseInt(localStorage.getItem("userId"));
      const msgTimestamp = new Date().toISOString();
      const loggedInUser = JSON.parse(localStorage.getItem("currentUser"));

      let punnubResp = await pubnub.publish({
        channel: openedGroupData.groupDetail.channelName,
        message: {
          title: openedGroupData.groupDetail.displayName,
          description: message,
          replyTo: replyToMessage ? { text: replyToMessage.text, id: replyToMessage.id } : null,
          fileUrl,
          userId: loggedInUserId,
          timestamp: msgTimestamp,
          userName: loggedInUser.displayName
        },
      });
      
      if (!punnubResp.error) {
        await axios.post(
          `${URL}/group-message`,
          {
            groupId: openedGroup.id,
            message,
            msgTimestamp,
            userId: loggedInUserId,
          },
          {
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `${authToken}`,
            },
          }
        ); 
      }
    } catch (error) {
      console.error('Error sending message:', error);
    }
  };

  const handleSend = (e) => {
    console.log(input, replyTo, file, "input, replyTo, file");
  
    e.preventDefault();    
    if (input.trim() || file) {
      publishMessage(input, replyTo, file ? URL.createObjectURL(file) : null).then(() => {
        setInput('');
        setReplyTo(null);
        setFile(null);
        setFileName('');
      });
    }
  };

  let loggedInUserId = localStorage.getItem("userId");
  const channelName = openedGroupData?.groupDetail?.channelName;

  useEffect(() => {
    setIsChatActive(true);
    if (channelName) {
      pubnub.subscribe({ channels: [channelName] });
      seenAllMessages();
    }

    // Listener to handle incoming messages
    const messageListener = {
      message: async (messageEvent) => {
        const { message } = messageEvent;
        // Build new message object
        const newMessage = {
          id: Date.now(),
          text: message.description || message.message,
          type: "received",
          replyTo: message.replyTo || null,
          fileUrl: message.fileUrl || null,
          userId: message.userId || message.senderId,
          timestamp:
            new Date(message.timestamp).getTime() ||
            new Date(message.timeStamp).getTime(),
          timeToken: messageEvent.timetoken,
          userName: message.userName || message.senderName,
        };

        // Update messages state
        setMessages((prev) => [...prev, newMessage]);

        // Only mark as seen if this chat is active and the message is not from the logged-in user
        if (isChatActive && newMessage.userId.toString() !== loggedInUserId) {
          try {
            await pubnub.addMessageAction({
              channel: channelName,
              messageTimetoken: messageEvent.timetoken,
              action: {
                type: "seen",
                value: loggedInUserId,
              },
            });
          } catch (error) {
            console.error("Error marking message as seen:", error);
          }
        }
      },
    };

    // Add the listener
    pubnub.addListener(messageListener);

    // Cleanup when the component unmounts: mark chat as inactive and unsubscribe
    return () => {
      setIsChatActive(false);
      pubnub.removeListener(messageListener);
      if (channelName) {
        pubnub.unsubscribe({ channels: [channelName] });
      }
    };
  }, [channelName, isChatActive, loggedInUserId]);

  const seenAllMessages = async () => {
    try {
      const channelName = openedGroupData?.groupDetail?.channelName;
      const loggedInUserId = localStorage.getItem("userId");
  
      const { channels } = await pubnub.fetchMessages({
        channels: [channelName],
        count: 100,
        reverse: false,
      });
  
      const messagesWithTimetokens = channels[channelName]?.map(msg => ({
        timetoken: msg.timetoken,
        userId: msg.message.userId || msg.message.senderId
      })) || [];
  
      const unseenMessages = messagesWithTimetokens.filter(msg => 
        msg.userId.toString() !== loggedInUserId
      );
  
      const seenPromises = unseenMessages.map(msg =>
        pubnub.addMessageAction({
          channel: channelName,
          messageTimetoken: msg.timetoken,
          action: {
            type: 'seen',
            value: loggedInUserId
          }
        })
      );
  
      await Promise.all(seenPromises);
      
    } catch (error) {
      console.error('Error marking messages as seen:', error);
    }
  };

  seenAllMessages();

  // useEffect(() => {
  //   listenMessage();
  // }, [openedGroupData]);

  const handleReply = (message) => {
    setReplyTo(message);
  };

  const handleFileChange = (e) => {
    const selectedFile = e.target.files[0];
    if (selectedFile) {
      setFile(selectedFile);
      setFileName(selectedFile.name);
    }
  };

  return (
    <>
    <div className="navGroup">
      <NavGroupChat
        membersPallate={membersPallate}
        backButtonPath={"/chat"}
        profilePicture={openedGroupInfo.groupProfileURL}
        groupName={openedGroupInfo.groupName}
        isSearchActive={isSearchActive}
        setIsSearchActive={setIsSearchActive}
        searchKeyword={searchKeyword}
        setSearchKeyword={setSearchKeyword}
        currentUser={currentUser}
        setOpenPage={setOpenPage}
        groupId={openedGroupInfo.groupId}
      />
      </div>
      {isLoading ? (
        <div className="loading-container" style={{ height: "80%", display: "flex", justifyContent: "center", alignItems: "center" }}>
          <CircularProgress color="primary" />
        </div>
      ) : (
        <>
          {openedGroupInfo.createdById === currentUser.uid ? (
            <Box className="date-box-parent">
              <Box
                className="date-box"
                style={{ backgroundColor: "#FFFFFF" }}
              >
                <Typography className="creator-badge-text">
                  You’ve created this group
                </Typography>
              </Box>
            </Box>
          ) : (
            <Box className="date-box-parent">
              <Box
                className="date-box"
                style={{ backgroundColor: "#FFFFFF"}}
              >
                <Typography className="creator-badge-text">
                  {openedGroupInfo.createdByName} created this group & added you
                </Typography>
              </Box>
            </Box>
          )}
          <div className="root_container" ref={rootContainerRef}>
              <MainContainer>
              <ChatContainer>
                <MessageList>
                {messages.map((msg) => (
                  <GroupMsg
                    onReply={handleReply}
                    isLoading={isLoading}
                    key={msg.timestamp}
                    currentUser={currentUser}
                    groupId={openedGroupInfo?.groupId}
                    setIsReplyMsg={setIsReplyMsg}
                    msg={msg}
                    searchKeyword={searchKeyword}
                    isSearchActive={isSearchActive}
                  />
                ))}
                </MessageList>
                </ChatContainer>
                </MainContainer>
          </div>
          <div className="chat-footer">
          {replyTo && (
            <div className="reply-box">
              <div className="reply-content">
                <span className="replying-to">Replying to:</span>
                <span className="reply-text">{replyTo.text}</span>
              </div>
              <button onClick={() => setReplyTo(null)} className="cancel-reply">
                ✖
              </button>
            </div>
          )}
          <div>
            <form
              onSubmit={(e) => {
                e.preventDefault(); // Prevent default form submission
                handleSend(e); // Trigger the send handler
                setInput(""); // Clear the input field
              }}
              className="input-form"
            >
              <div className="input-container">
                {/* Replace Input with MessageInput */}
                <MessageInput
                  style={{ height: "35px", width: "80%" }}
                  placeholder="Type a message ...."
                  value={input}
                  onChange={(value) => setInput(value)} // Chatscope passes value directly
                  attachButton={false} // Remove default attach button if not needed
                  sendButton={false} // Remove the send button
                  onKeyDown={(e) => {
                    if (e.key === "Enter" && !e.shiftKey) {
                      e.preventDefault(); // Prevent newline creation
                      handleSend(e); // Call the send handler
                      setInput(""); // Clear the input field
                    }
                  }}
                />

                <label className="file-input-button">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 20 20"
                    id="attachment"
                    width="24"
                    height="24"
                    fill="currentColor"
                  >
                    <path d="M5.602 19.8c-1.293 0-2.504-.555-3.378-1.44-1.695-1.716-2.167-4.711.209-7.116l9.748-9.87c.988-1 2.245-1.387 3.448-1.06 1.183.32 2.151 1.301 2.468 2.498.322 1.22-.059 2.493-1.046 3.493l-9.323 9.44c-.532.539-1.134.858-1.738.922-.599.064-1.17-.13-1.57-.535-.724-.736-.828-2.117.378-3.337l6.548-6.63c.269-.272.705-.272.974 0s.269.714 0 .986l-6.549 6.631c-.566.572-.618 1.119-.377 1.364.106.106.266.155.451.134.283-.029.606-.216.909-.521l9.323-9.439c.64-.648.885-1.41.69-2.145a2.162 2.162 0 0 0-1.493-1.513c-.726-.197-1.48.052-2.12.7l-9.748 9.87c-1.816 1.839-1.381 3.956-.209 5.143 1.173 1.187 3.262 1.629 5.079-.212l9.748-9.87a.683.683 0 0 1 .974 0 .704.704 0 0 1 0 .987L9.25 18.15c-1.149 1.162-2.436 1.65-3.648 1.65z"></path>
                  </svg>
                  <input type="file" onChange={handleFileChange} />
                </label>

                <button className="chat-btn" type="submit">
                  Send
                </button>
              </div>
            </form>
          </div>
        </div>
          {fileName && (
            <div className="file-preview-overlay">
              <span>File: {fileName}</span>
              {file && file.type.startsWith('image/') && (
                <img src={URL.createObjectURL(file)} alt="Preview" width="50" height="50" />
              )}
              <button className="chat-btn" onClick={() => { setFile(null); setFileName(''); }}>Remove</button>
            </div>
          )}
        </>
      )}
    </>
  );
}
