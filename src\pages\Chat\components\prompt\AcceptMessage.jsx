import { Box, Button, Typography } from '@mui/material'
import React, { useEffect, useState } from 'react'
import "../../styles/prompts/acceptMessage.css"
import { getDatabase, ref, set,get } from 'firebase/database'

export default function AcceptMessage({ currentUserId, combinedId, openedUserId }) {
    
    const [senderDisplayName, setSenderDisplayName] = useState(null);

    useEffect(() => {
        const fetchSenderDisplayName = async () => {
            const db = getDatabase();
            const userRef = ref(db, `users/${openedUserId}`);

            try {
                const snapshot = await get(userRef);
                if (snapshot.exists()) {
                    setSenderDisplayName(snapshot.val().displayName);
                }
            } catch (error) {
                console.error("Error fetching sender display name:", error);
            }
        };

        fetchSenderDisplayName();
    }, [openedUserId]);

    
    
    const handleAcceptRequest = async () => {
        const db = getDatabase()

        await set(ref(db, `userChats/${currentUserId}/${combinedId}/isMsgReqAccepted`), true)

    }
    const handleDeclineRequest = async () => {
        const db = getDatabase()

        await set(ref(db, `userChats/${currentUserId}/${combinedId}/isMsgReqDeclined`), true)
        await set(ref(db, `userChats/${openedUserId}/${combinedId}/isMsgReqDeclined`), true)

    }

    return <div  style={{ display: 'grid', placeItems: 'center' }}>
        <Box className="sticky-acceptmsg-prompt" >
            <Typography className='accept-msg-heading'>Accept message request from {senderDisplayName || "User"}</Typography>
            <Typography className='accept-msg-desc'>If you accept, they will  be able to see when you have read messages.</Typography>

            <Box>
                <Button className='decline-btn'
                    onClick={handleDeclineRequest}
                >Decline</Button>
                <Button className='accept-btn'
                    onClick={handleAcceptRequest}
                >Accept</Button>
            </Box>
        </Box>
    </div>
}