// import { createStore, combineReducers, applyMiddleware } from "redux"
// import { composeWithDevTools } from 'redux-devtools-extension'
// import thunk from 'redux-thunk'
// import { authReducer } from "./reducer/authReducer"
// import { usersReducer } from "./reducer/usersReducer"
// import { chatsReducer } from "./reducer/chatsReduces"
// import { getCurrentUserChatsReducer } from "./reducer/userChats"
// import { groupReducer } from "./reducer/groupReducer"

// const rootreducer = combineReducers({
//     auth: authReducer,
//     users: usersReducer,
//     chats: chatsReducer,
//     userChats: getCurrentUserChatsReducer,
//     group: groupReducer
// })

// const userlocalStorageData = localStorage.getItem("currentUser")
//     ? JSON.parse(localStorage.getItem("currentUser"))
//     : null
// console.log(userlocalStorageData)

// const intialValue = {
//     auth: {
//         currentUser: userlocalStorageData
//     }
// }

// const store = createStore(
//     rootreducer,
//     intialValue,
//     composeWithDevTools(applyMiddleware(thunk))
// )

// export default store