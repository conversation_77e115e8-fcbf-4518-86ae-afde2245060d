import {
	Chip,
	FormControl,
	FormLabel,
	Grid,
	MenuItem,
	OutlinedInput,
	Select,
	TextField,
} from "@mui/material";
import { <PERSON><PERSON>, Modal } from "antd";
import React, { useEffect, useState } from "react";
import {
	CreateFeedWeeklyDataPattern,
	createAssesmentform,
	fetchSelefAssesmnetGetData,
	getAllActivityData,
	updateFeedWeeklyDataPattern,
	weeklyFeedDataPattern,
	weeklyFeedDataProgram,
} from "../../API/api-endpoint";
import { useFormik } from "formik";
import Swal from "sweetalert2";
import SlickCarousel from "../../pages/SlickCarousel";
const scoreData = [1, 2, 3, 4, 5, 6, 7];
const CreateFeedWeeklyData = ({
	fetchReport,
	setShowAssesmentModal,
	showAssesmentModal,
	editData,
	setEditData,
}) => {
	const [programList, setProgramList] = useState([]);
	const [weeklyDay, setWeeklyDay] = useState([]);

	console.log("editData", editData);
	const formik = useFormik({
		initialValues: {
			program: "",
			option: "",
			daysaweek: "",
			monday: "",
			tuesday: "",
			wednesday: "",
			thursday: "",
			friday: "",
			saturday: "",
			sunday: "",
		},
		validate: (values) => {
			const errors = {};
			if (!values.thursday) {
				errors.thursday = "Thursday is required";
			}
			if (!values.friday) {
				errors.friday = "Friday is required";
			}
			if (!values.saturday) {
				errors.saturday = "Saturday is required";
			}
			if (!values.sunday) {
				errors.sunday = "Sunday is required";
			}
			if (!values.program) {
				errors.program = "Activity Name is required";
			}
			if (!values.option) {
				errors.option = "Option is required";
			}
			if (!values.daysaweek) {
				errors.daysaweek = "Days in a week is required";
			}
			if (!values.monday) {
				errors.monday = "Monday is required";
			}
			if (!values.tuesday) {
				errors.tuesday = "Tuesday is required";
			}
			if (!values.wednesday) {
				errors.wednesday = "Wednesday is required";
			}

			return errors;
		},
		// validationSchema: {},
		onSubmit: (values, { resetForm }) => {
			handleSubmitAssesmentForm(values, resetForm);
		},
	});
	console.log("formik", formik?.values);
	const getSelfAssesmentGetData = async () => {
		const response = await getAllActivityData();
		console.log("response", response);
		setProgramList(response?.rows);
	};
	const getWeekDatapattern = async () => {
		const response = await weeklyFeedDataPattern(formik?.values?.program);
		console.log("response", response);
		setWeeklyDay(response);
		// setProgramList(response?.programs)
	};
	useEffect(() => {
		getSelfAssesmentGetData();
	}, []);
	useEffect(() => {
		if (formik?.values?.program) {
			getWeekDatapattern();
		}
	}, [formik?.values?.program]);
	useEffect(() => {
		if (editData?.id) {
			let data = {
				program: editData?.activity?.id,
				friday: editData?.friday?.id,
				monday: editData?.monday?.id,
				saturday: editData?.saturday?.id,
				sunday: editData?.sunday?.id,
				thursday: editData?.thursday?.id,
				tuesday: editData?.tuesday?.id,
				wednesday: editData?.wednesday?.id,
				friday: editData?.friday?.id,
				friday: editData?.friday?.id,
				activities: editData?.activities,
				id: editData?.id,
				option: editData?.options,
				daysaweek: editData?.run_days_per_week,
			};
			console.log("data", data);
			// const { srID, ...data } = editData
			formik?.setValues(data);
		}
	}, [editData?.id]);
	const handleSubmitAssesmentForm = async (data, resetForm) => {
		let response = "";
		if (editData?.id) {
			response = await updateFeedWeeklyDataPattern(data);
		} else {
			console.log("Old DIALOG PAYLOAD:", data);

			response = await CreateFeedWeeklyDataPattern(data);
		}
		if (response?.status) {
			Swal.fire({
				title: "Success",
				text: response.message,
				icon: "success",
			});
			setEditData({});
			setShowAssesmentModal(false);
			fetchReport();
			formik.resetForm();
			formik?.setValues({ option: "" });
		} else {
			Swal.fire({
				title: "Error",
				text: response.message,
				icon: "error",
			});
		}
		console.log("response", response);
	};
	return (
		<Modal
			width={1200}
			open={showAssesmentModal}
			onCancel={() => {
				setEditData({});
				setShowAssesmentModal(false);
				formik.resetForm();
				formik?.setValues({ option: "" });
			}}
			footer={<div></div>}
		>
			<div className='headingCont'>
				<span className='heading'>
					{editData?.id ? "Edit " : "Create"}
				</span>{" "}
				<span className='orange heading'>Days</span>
			</div>
			{/* <h1>{editData ? editData.challengeId : values.challengeId}</h1> */}
			<div className='parentCont'>
				<form className='form1' onSubmit={formik.handleSubmit}>
					<Grid container>
						<Grid className='mbtm' item xs={12} sm={10.8}>
							<FormLabel>
								Activity Name
								<span className='text-[red]'>*</span>
							</FormLabel>

							<TextField
								fullWidth
								size='small'
								select
								SelectProps={{
									MenuProps: {
										PaperProps: {
											style: {
												scrollbarColor: "#E67E22 white",
												scrollbarWidth: "thin",
											},
										},
									},
								}}
								name='program'
								value={formik?.values?.program}
								onChange={formik.handleChange}
								error={
									formik.touched.program &&
									formik.errors.program
								}
								helperText={
									formik.touched.program &&
									formik.errors.program
								}
								id='form-layouts-separator-select'
								labelId='form-layouts-separator-select-label'
								input={
									<OutlinedInput id='select-multiple-language' />
								}
							>
								<MenuItem value={""} disabled>
									Select Activity
								</MenuItem>
								{programList?.map((value, index) => {
									return (
										<MenuItem value={value?.id}>
											{value?.activity_name}
										</MenuItem>
									);
								})}
							</TextField>
						</Grid>
						<Grid className='mbtm' item xs={12} sm={10.8}>
							<FormLabel>
								Option<span className='text-[red]'>*</span>
							</FormLabel>

							<TextField
								fullWidth
								placeholder='option'
								size='small'
								type='number'
								name='option'
								value={formik?.values?.option}
								onChange={formik.handleChange}
								error={
									formik.touched.option &&
									formik.errors.option
								}
								helperText={
									formik.touched.option &&
									formik.errors.option
								}
							/>
						</Grid>

						<Grid className='marbot' container spacing={3}>
							<Grid item xs={12} sm={5.5}>
								<FormLabel>
									Days a week
									<span className='text-[red]'>*</span>
								</FormLabel>

								<TextField
									fullWidth
									size='small'
									select
									SelectProps={{
										MenuProps: {
											PaperProps: {
												style: {
													scrollbarColor:
														"#E67E22 white",
													scrollbarWidth: "thin",
												},
											},
										},
									}}
									name='daysaweek'
									value={formik?.values?.daysaweek}
									onChange={formik.handleChange}
									error={
										formik.touched.daysaweek &&
										formik.errors.daysaweek
									}
									helperText={
										formik.touched.daysaweek &&
										formik.errors.daysaweek
									}
									id='form-layouts-separator-select'
									labelId='form-layouts-separator-select-label'
									input={
										<OutlinedInput id='select-multiple-language' />
									}
								>
									<MenuItem value={""} disabled>
										Select Day
									</MenuItem>
									{scoreData?.map((value, index) => {
										return (
											<MenuItem value={value}>
												{value}
											</MenuItem>
										);
									})}
								</TextField>
							</Grid>
							<Grid item xs={12} sm={5.5}>
								<FormLabel>
									Monday<span className='text-[red]'>*</span>
								</FormLabel>

								<TextField
									fullWidth
									size='small'
									select
									name='monday'
									value={formik?.values?.monday}
									onChange={formik.handleChange}
									error={
										formik.touched.monday &&
										formik.errors.monday
									}
									helperText={
										formik.touched.monday &&
										formik.errors.monday
									}
									id='form-layouts-separator-select'
									labelId='form-layouts-separator-select-label'
									input={
										<OutlinedInput id='select-multiple-language' />
									}
								>
									<MenuItem value={""} disabled>
										Select Day
									</MenuItem>
									{weeklyDay?.map((value, index) => {
										return (
											<MenuItem value={value?.id}>
												{value?.workout}
											</MenuItem>
										);
									})}
								</TextField>
							</Grid>
						</Grid>

						<Grid className='marbot' container spacing={3}>
							<Grid item xs={12} sm={5.5}>
								<FormLabel>
									Tuesday<span className='text-[red]'>*</span>
								</FormLabel>

								<TextField
									fullWidth
									size='small'
									select
									name='tuesday'
									value={formik?.values?.tuesday}
									onChange={formik.handleChange}
									error={
										formik.touched.tuesday &&
										formik.errors.tuesday
									}
									helperText={
										formik.touched.tuesday &&
										formik.errors.tuesday
									}
									id='form-layouts-separator-select'
									labelId='form-layouts-separator-select-label'
									input={
										<OutlinedInput id='select-multiple-language' />
									}
								>
									<MenuItem value={""} disabled>
										Select Day
									</MenuItem>
									{weeklyDay?.map((value, index) => {
										return (
											<MenuItem value={value?.id}>
												{value?.workout}
											</MenuItem>
										);
									})}
								</TextField>
							</Grid>
							<Grid item xs={12} sm={5.5}>
								<FormLabel>
									Wednesday
									<span className='text-[red]'>*</span>
								</FormLabel>

								<TextField
									fullWidth
									size='small'
									select
									name='wednesday'
									value={formik?.values?.wednesday}
									onChange={formik.handleChange}
									error={
										formik.touched.wednesday &&
										formik.errors.wednesday
									}
									helperText={
										formik.touched.wednesday &&
										formik.errors.wednesday
									}
									id='form-layouts-separator-select'
									labelId='form-layouts-separator-select-label'
									input={
										<OutlinedInput id='select-multiple-language' />
									}
								>
									<MenuItem value={""} disabled>
										Select Day
									</MenuItem>
									{weeklyDay?.map((value, index) => {
										return (
											<MenuItem value={value?.id}>
												{value?.workout}
											</MenuItem>
										);
									})}
								</TextField>
							</Grid>
						</Grid>

						<Grid className='marbot' container spacing={3}>
							<Grid item xs={12} sm={5.5}>
								<FormLabel>
									Thursday
									<span className='text-[red]'>*</span>
								</FormLabel>

								<TextField
									fullWidth
									size='small'
									select
									name='thursday'
									value={formik?.values?.thursday}
									onChange={formik.handleChange}
									error={
										formik.touched.thursday &&
										formik.errors.thursday
									}
									helperText={
										formik.touched.thursday &&
										formik.errors.thursday
									}
									id='form-layouts-separator-select'
									labelId='form-layouts-separator-select-label'
									input={
										<OutlinedInput id='select-multiple-language' />
									}
								>
									<MenuItem value={""} disabled>
										Select Day
									</MenuItem>
									{weeklyDay?.map((value, index) => {
										return (
											<MenuItem value={value?.id}>
												{value?.workout}
											</MenuItem>
										);
									})}
								</TextField>
							</Grid>
							<Grid item xs={12} sm={5.5}>
								<FormLabel>
									Friday<span className='text-[red]'>*</span>
								</FormLabel>

								<TextField
									fullWidth
									size='small'
									select
									name='friday'
									value={formik?.values?.friday}
									onChange={formik.handleChange}
									error={
										formik.touched.friday &&
										formik.errors.friday
									}
									helperText={
										formik.touched.friday &&
										formik.errors.friday
									}
									id='form-layouts-separator-select'
									labelId='form-layouts-separator-select-label'
									input={
										<OutlinedInput id='select-multiple-language' />
									}
								>
									<MenuItem value={""} disabled>
										Select Day
									</MenuItem>
									{weeklyDay?.map((value, index) => {
										return (
											<MenuItem value={value?.id}>
												{value?.workout}
											</MenuItem>
										);
									})}
								</TextField>
							</Grid>
						</Grid>

						<Grid className='marbot' container spacing={3}>
							<Grid item xs={12} sm={5.5}>
								<FormLabel>
									Saturday
									<span className='text-[red]'>*</span>
								</FormLabel>

								<TextField
									fullWidth
									size='small'
									select
									name='saturday'
									value={formik?.values?.saturday}
									onChange={formik.handleChange}
									error={
										formik.touched.saturday &&
										formik.errors.saturday
									}
									helperText={
										formik.touched.saturday &&
										formik.errors.saturday
									}
									id='form-layouts-separator-select'
									labelId='form-layouts-separator-select-label'
									input={
										<OutlinedInput id='select-multiple-language' />
									}
								>
									<MenuItem value={""} disabled>
										Select Day
									</MenuItem>
									{weeklyDay?.map((value, index) => {
										return (
											<MenuItem value={value?.id}>
												{value?.workout}
											</MenuItem>
										);
									})}
								</TextField>
							</Grid>
							<Grid item xs={12} sm={5.5}>
								<FormLabel>
									Sunday<span className='text-[red]'>*</span>
								</FormLabel>

								<TextField
									fullWidth
									size='small'
									select
									name='sunday'
									value={formik?.values?.sunday}
									onChange={formik.handleChange}
									error={
										formik.touched.sunday &&
										formik.errors.sunday
									}
									helperText={
										formik.touched.sunday &&
										formik.errors.sunday
									}
									id='form-layouts-separator-select'
									labelId='form-layouts-separator-select-label'
									input={
										<OutlinedInput id='select-multiple-language' />
									}
								>
									<MenuItem value={""} disabled>
										Select Day
									</MenuItem>
									{weeklyDay?.map((value, index) => {
										return (
											<MenuItem value={value?.id}>
												{value?.workout}
											</MenuItem>
										);
									})}
								</TextField>
							</Grid>
						</Grid>

						<Grid item xs={12} sm={5.5}>
							<Button
								className='btn'
								key='submit'
								type='primary'
								onClick={() => formik.handleSubmit()}
							>
								Submit
							</Button>
						</Grid>
					</Grid>
				</form>

				<div className='slick-container'>
					<SlickCarousel />
				</div>
			</div>
		</Modal>
	);
};

export default CreateFeedWeeklyData;
