import {
    Chip,
    FormControl,
    FormLabel,
    Grid,
    MenuItem,
    OutlinedInput,
    Select,
    TextField,
  } from "@mui/material";
  import { <PERSON><PERSON>, Modal } from "antd";
  import React, { useEffect, useState } from "react";
  import {
    CreateLevels,
    CreatePrograms,
    CreateZonesClasification,
    createAssesmentdata,
    createScoreReadnessdata,
    createSegmentReadnessdata,
    createSugmentdata,
    getAllAssesmentData,
    getAllPrograms,
    getAlllevels,
    updateAssesmentdata,
    updateLevel,
    updatePrograms,
    updateScoreReadnessdata,
    updateSegmentReadnessdata,
    updateSugmentdata,
    updateZonesClasification,
    weeklyFeedDataPattern,
    weeklyFeedDataProgram,
  } from "../../API/api-endpoint";
  import { useFormik } from "formik";
  import Swal from "sweetalert2";
  import SlickCarousel from "../../pages/SlickCarousel";
  const scoreData = [1, 2, 3, 4, 5];
  const CreateScoreData = ({
    fetchReport,
    setShowAssesmentModal,
    showAssesmentModal,
    editData,
    setEditData,
  }) => {
    const [assesmentList, setAssesmentList] = useState([]);
    console.log("editData", assesmentList);
    const formik = useFormik({
      initialValues: {
        ironman_description: "",
        olympic_description: "",
        seventy_point_three_description: "",
        sprint_description: "",
        points_range_from: "",
        points_range_to: "",
      },
      validate: (values) => {
        const errors = {};
        if (!values.ironman_description) {
          errors.ironman_description = "Ironman description is required";
        }
        if (!values.olympic_description) {
          errors.olympic_description = "Olympic description is required";
        }
        if (!values.seventy_point_three_description) {
          errors.seventy_point_three_description =
            "Seventy point three description is required";
        }
        if (!values.sprint_description) {
          errors.sprint_description = "Sprint description is required";
        }
        if (!values.points_range_from) {
          errors.points_range_from = "Start range is required";
        }
        if (!values.points_range_to) {
          errors.points_range_to = "End range is required";
        }
        return errors;
      },
      // validationSchema: {},
      onSubmit: (values, { resetForm }) => {
        handleSubmitAssesmentForm(values, resetForm);
      },
    });
    console.log("formik", formik?.values);
    const assementdata = async () => {
      const response = await getAllAssesmentData();
      console.log("response", response);
      setAssesmentList(response);
    };
    useEffect(() => {
      assementdata();
    }, []);
  
    const handleSubmitAssesmentForm = async (data, resetForm) => {
      let response = "";
      if (editData?.id) {
        response = await updateScoreReadnessdata(data);
      } else {
        response = await createScoreReadnessdata(data);
      }
      if (response?.status) {
        Swal.fire({
          title: "Success",
          text: response.message,
          icon: "success",
        });
        setShowAssesmentModal(false);
        setEditData({});
        fetchReport();
        resetForm();
        formik?.setValues({
          ironman_description: "",
          olympic_description: "",
          seventy_point_three_description: "",
          sprint_description: "",
          points_range_from: "",
          points_range_to: "",
        });
      } else {
        Swal.fire({
          title: "Error",
          text: response.message,
          icon: "error",
        });
      }
      console.log("response", response);
    };
    useEffect(() => {
      if (editData?.id) {
        const { srID, ...data } = editData;
        console.log("data", data);
        formik?.setValues(data);
      } else {
        setEditData({});
      }
    }, [editData?.id]);
  
    return (
      <Modal
        width={1200}
        open={showAssesmentModal}
        onCancel={() => {
          setShowAssesmentModal(false);
          setEditData({});
          formik.resetForm();
          formik?.setValues({
            ironman_description: "",
            olympic_description: "",
            seventy_point_three_description: "",
            sprint_description: "",
            points_range_from: "",
            points_range_to: "",
          });
        }}
        footer={
          <div></div>
          //   loading={isLoading}
        }
      >
        <div className="headingCont">
          <span className="heading">{editData?.id ? "Edit " : "Create"}</span>{" "}
          <span className="orange heading">Score Data</span>
        </div>
        {/* <h1>{editData ? editData.challengeId : values.challengeId}</h1> */}
        <div className="parentCont">
          <form className="form1" onSubmit={formik.handleSubmit}>
            <Grid container spacing={2}>
              <Grid item xs={12} sm={11}>
                <FormLabel>Ironman Description<span className="text-[red]">*</span></FormLabel>
  
                <TextField
                  fullWidth
                  placeholder="Ironman description"
                  size="small"
                  type="text"
                  name="ironman_description"
                  value={formik?.values?.ironman_description}
                  onChange={formik.handleChange}
                  error={
                    formik.touched.ironman_description &&
                    formik.errors.ironman_description
                  }
                  helperText={
                    formik.touched.ironman_description &&
                    formik.errors.ironman_description
                  }
                />
              </Grid>
              <Grid item xs={12} sm={11}>
                <FormLabel>Olympic Description<span className="text-[red]">*</span></FormLabel>
  
                <TextField
                  fullWidth
                  placeholder="Olympic description"
                  size="small"
                  type="text"
                  name="olympic_description"
                  value={formik?.values?.olympic_description}
                  onChange={formik.handleChange}
                  error={
                    formik.touched.olympic_description &&
                    formik.errors.olympic_description
                  }
                  helperText={
                    formik.touched.olympic_description &&
                    formik.errors.olympic_description
                  }
                />
              </Grid>
              <Grid item xs={12} sm={11}>
                <FormLabel>Sprint Description<span className="text-[red]">*</span></FormLabel>
  
                <TextField
                  fullWidth
                  placeholder="Sprint description"
                  size="small"
                  type="text"
                  name="sprint_description"
                  value={formik?.values?.sprint_description}
                  onChange={formik.handleChange}
                  error={
                    formik.touched.sprint_description &&
                    formik.errors.sprint_description
                  }
                  helperText={
                    formik.touched.sprint_description &&
                    formik.errors.sprint_description
                  }
                />
              </Grid>
              <Grid item xs={12} sm={11}>
                <FormLabel>Seventy Point Three Description<span className="text-[red]">*</span></FormLabel>
  
                <TextField
                  fullWidth
                  placeholder="Seventy point three description"
                  size="small"
                  type="text"
                  name="seventy_point_three_description"
                  value={formik?.values?.seventy_point_three_description}
                  onChange={formik.handleChange}
                  error={
                    formik.touched.seventy_point_three_description &&
                    formik.errors.seventy_point_three_description
                  }
                  helperText={
                    formik.touched.seventy_point_three_description &&
                    formik.errors.seventy_point_three_description
                  }
                />
              </Grid>
              <Grid item xs={12} sm={11}>
                <FormLabel>Points Start Range<span className="text-[red]">*</span></FormLabel>
  
                <TextField
                  fullWidth
                  placeholder="Points start range"
                  size="small"
                  type="number"
                  name="points_range_from"
                  value={formik?.values?.points_range_from}
                  onChange={formik.handleChange}
                  error={
                    formik.touched.points_range_from &&
                    formik.errors.points_range_from
                  }
                  helperText={
                    formik.touched.points_range_from &&
                    formik.errors.points_range_from
                  }
                />
              </Grid>
              <Grid item xs={12} sm={11}>
                <FormLabel>Points Start To<span className="text-[red]">*</span></FormLabel>
  
                <TextField
                  fullWidth
                  placeholder="Points start to"
                  size="small"
                  type="number"
                  name="points_range_to"
                  value={formik?.values?.points_range_to}
                  onChange={formik.handleChange}
                  error={
                    formik.touched.points_range_to &&
                    formik.errors.points_range_to
                  }
                  helperText={
                    formik.touched.points_range_to &&
                    formik.errors.points_range_to
                  }
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <Button
                  className="btn"
                  key="submit"
                  type="primary"
                  onClick={() => formik.handleSubmit()}
                >
                  Submit
                </Button>
              </Grid>
            </Grid>
          </form>
          <div className="slick-container">
            <SlickCarousel />
          </div>
        </div>
      </Modal>
    );
  };
  export default CreateScoreData;
  