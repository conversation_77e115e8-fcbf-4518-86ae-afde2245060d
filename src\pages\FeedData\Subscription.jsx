import { useEffect, useState, use<PERSON>emo, useCallback } from "react";
import { <PERSON><PERSON> } from "../../components/ui/button";
import { Input } from "../../components/ui/input";
import { Card, CardContent, CardHeader } from "../../components/ui/card";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "../../components/ui/select";
import {
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableHeader,
	TableRow,
} from "../../components/ui/table";
import { Edit, Trash2, Plus } from "lucide-react";
import { Badge } from "../../components/ui/badge";
import { SubscriptionDialog } from "../../components/admin/subscription-dialog";
import { DeleteConfirmDialog } from "../../components/admin/delete-confirm-dialog";
import Header from "../../components/Header";
import {
	getsubspackageData,
	deletesubspackageData,
} from "../../API/api-endpoint";
import Swal from "sweetalert2";

const Subscription = () => {
	const [subscriptionData, setSubscriptionData] = useState([]);
	const [isLoading, setIsLoading] = useState(true);
	const [searchTerm, setSearchTerm] = useState("");
	const [selectedProgramFilter, setSelectedProgramFilter] = useState("All");
	const [currentPage, setCurrentPage] = useState(1);
	const [showDialog, setShowDialog] = useState(false);
	const [editingItem, setEditingItem] = useState(null);
	const [deleteId, setDeleteId] = useState(null);
	const pageSize = 10;

	const programOptions = ["All", "YRA", "YCA", "YSA", "YFA", "YTA", "YNA"];

	const getProgramBadgeColor = (programName) => {
		const colors = {
			YRA: "bg-blue-100 text-blue-800 border-blue-200",
			YCA: "bg-green-100 text-green-800 border-green-200",
			YSA: "bg-purple-100 text-purple-800 border-purple-200",
			YFA: "bg-orange-100 text-orange-800 border-orange-200",
			YTA: "bg-red-100 text-red-800 border-red-200",
			YNA: "bg-indigo-100 text-indigo-800 border-indigo-200",
		};
		return (
			colors[programName] || "bg-gray-100 text-gray-800 border-gray-200"
		);
	};

	const fetchData = useCallback(async () => {
		try {
			setIsLoading(true);
			const response = await getsubspackageData();

			if (response) {
				const dataWithSrID = response.map((item, index) => ({
					...item,
					srID: index + 1,
				}));
				setSubscriptionData(dataWithSrID || []);
			} else {
				setSubscriptionData([]);
			}
		} catch (error) {
			console.error("Error fetching subscription data:", error);
			Swal.fire({
				title: "Error",
				text: "Failed to fetch subscription data. Please try again.",
				icon: "error",
				timer: 3000,
				showConfirmButton: false,
			});
			setSubscriptionData([]);
		} finally {
			setIsLoading(false);
		}
	}, []);

	useEffect(() => {
		fetchData();
	}, [fetchData]);

	const filteredData = useMemo(() => {
		return subscriptionData.filter((item) => {
			const matchesSearch =
				!searchTerm.trim() ||
				item?.name?.toLowerCase().includes(searchTerm.toLowerCase());

			const matchesProgram =
				selectedProgramFilter === "All" ||
				item?.program?.program_name === selectedProgramFilter;

			return matchesSearch && matchesProgram;
		});
	}, [subscriptionData, searchTerm, selectedProgramFilter]);

	const paginatedData = useMemo(() => {
		const startIndex = (currentPage - 1) * pageSize;
		const endIndex = startIndex + pageSize;
		return filteredData.slice(startIndex, endIndex);
	}, [filteredData, currentPage, pageSize]);

	const totalPages = Math.ceil(filteredData.length / pageSize);

	const handleSearch = (value) => {
		setSearchTerm(value);
		setCurrentPage(1);
	};

	const handleProgramFilterChange = (value) => {
		setSelectedProgramFilter(value);
		setCurrentPage(1);
	};

	const handleCreate = () => {
		setEditingItem(null);
		setShowDialog(true);
	};

	const handleEdit = (item) => {
		setEditingItem(item);
		setShowDialog(true);
	};

	const handleDelete = async (id) => {
		try {
			await deletesubspackageData(id);

			Swal.fire({
				title: "Success",
				text: "Subscription deleted successfully",
				icon: "success",
				timer: 2000,
				showConfirmButton: false,
			});

			setCurrentPage(1);
			fetchData();
		} catch (error) {
			console.error("Error deleting subscription:", error);
			Swal.fire({
				title: "Error",
				text: "Failed to delete subscription. Please try again.",
				icon: "error",
				timer: 3000,
				showConfirmButton: false,
			});
		}
	};

	const handleDialogSuccess = () => {
		setShowDialog(false);
		setEditingItem(null);
		fetchData();
	};

	return (
		<div>
			<Header />
			<div className='mx-auto p-6 max-w-[1400px] mt-16'>
				<Card>
					<CardHeader className='bg-orange-50 border-b flex flex-col gap-4'>
						<div className='flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4'>
							<div>
								<h1 className='text-2xl font-bold text-orange-900'>
									Subscription Packages
								</h1>
								<p className='text-orange-700 mt-1 text-sm'>
									Manage subscription plans and pricing
									packages
								</p>
							</div>
						</div>
						<div className='flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4'>
							<div className='flex gap-3'>
								<Button
									onClick={handleCreate}
									className='bg-orange-600 hover:bg-orange-700 text-white'
								>
									<Plus className='h-4 w-4 mr-2' />
									Create Subscription
								</Button>
							</div>

							<div className='flex gap-3'>
								<Input
									placeholder='Search by name...'
									value={searchTerm}
									onChange={(e) =>
										handleSearch(e.target.value)
									}
									className='w-full text-sm'
								/>
								<Select
									value={selectedProgramFilter}
									onValueChange={handleProgramFilterChange}
								>
									<SelectTrigger className='w-[180px] text-sm'>
										<SelectValue placeholder='Filter by Program' />
									</SelectTrigger>
									<SelectContent className='bg-white'>
										{programOptions.map((program) => (
											<SelectItem
												key={program}
												value={program}
											>
												{program}
											</SelectItem>
										))}
									</SelectContent>
								</Select>
							</div>
						</div>
					</CardHeader>
					<CardContent className='p-0'>
						<div className='overflow-x-auto'>
							<Table className='min-w-full w-full'>
								<TableHeader>
									<TableRow className='bg-blue-600 hover:bg-blue-600'>
										<TableHead className='text-white font-semibold'>
											Sr No
										</TableHead>
										<TableHead className='text-white font-semibold'>
											Name
										</TableHead>
										<TableHead className='text-white font-semibold'>
											Program Name
										</TableHead>
										<TableHead className='text-white font-semibold'>
											Price
										</TableHead>
										<TableHead className='text-white font-semibold'>
											Duration
										</TableHead>
										<TableHead className='text-white font-semibold'>
											iOS IAP Product ID
										</TableHead>
										<TableHead className='text-white font-semibold'>
											Actions
										</TableHead>
									</TableRow>
								</TableHeader>
								<TableBody>
									{isLoading ? (
										<TableRow>
											<TableCell
												colSpan={7}
												className='text-center py-8'
											>
												Loading...
											</TableCell>
										</TableRow>
									) : paginatedData.length > 0 ? (
										paginatedData.map((item, index) => (
											<TableRow
												key={item.id || index}
												className='hover:bg-gray-50'
											>
												<TableCell className='font-medium'>
													{(currentPage - 1) *
														pageSize +
														index +
														1}
												</TableCell>
												<TableCell>
													{item?.name || "N/A"}
												</TableCell>
												<TableCell>
													{item?.program
														?.program_name ? (
														<Badge
															className={`${getProgramBadgeColor(
																item.program
																	.program_name
															)} font-medium px-2 py-1 text-xs rounded-full border`}
														>
															{
																item.program
																	.program_name
															}
														</Badge>
													) : (
														<span className='text-gray-500'>
															N/A
														</span>
													)}
												</TableCell>
												<TableCell>
													${item?.price || "0"}
												</TableCell>
												<TableCell>
													{item?.duration || "N/A"}
												</TableCell>
												<TableCell className='text-gray-500'>
													{item?.iosIapProduct
														? item.iosIapProduct
																.length > 30
															? `${item.iosIapProduct.substring(
																	0,
																	30
															  )}...`
															: item.iosIapProduct
														: "N/A"}
												</TableCell>
												<TableCell>
													<div className='flex gap-2'>
														<Button
															variant='ghost'
															size='sm'
															onClick={() =>
																handleEdit(item)
															}
															className='text-blue-600 hover:text-blue-800'
														>
															<Edit className='h-4 w-4' />
														</Button>
														<Button
															variant='ghost'
															size='sm'
															onClick={() =>
																setDeleteId(
																	item.id
																)
															}
															className='text-red-600 hover:text-red-800'
														>
															<Trash2 className='h-4 w-4' />
														</Button>
													</div>
												</TableCell>
											</TableRow>
										))
									) : (
										<TableRow>
											<TableCell
												colSpan={7}
												className='text-center py-8 text-gray-500'
											>
												No subscriptions found
											</TableCell>
										</TableRow>
									)}
								</TableBody>
							</Table>
						</div>

						{totalPages > 1 && (
							<div className='flex justify-center gap-2 p-4 border-t'>
								<Button
									variant='outline'
									size='sm'
									onClick={() =>
										setCurrentPage(
											Math.max(1, currentPage - 1)
										)
									}
									disabled={currentPage === 1}
								>
									Previous
								</Button>

								{Array.from({ length: 5 }, (_, i) => {
									const startPage =
										Math.floor((currentPage - 1) / 5) * 5 +
										1;
									const page = startPage + i;
									if (page > totalPages) return null;

									return (
										<Button
											key={page}
											variant={
												currentPage === page
													? "default"
													: "outline"
											}
											size='sm'
											onClick={() => setCurrentPage(page)}
											className={
												currentPage === page
													? "bg-orange-500 hover:bg-orange-600 text-white"
													: ""
											}
										>
											{page}
										</Button>
									);
								})}

								<Button
									variant='outline'
									size='sm'
									onClick={() =>
										setCurrentPage(
											Math.min(
												totalPages,
												currentPage + 1
											)
										)
									}
									disabled={currentPage === totalPages}
								>
									Next
								</Button>
							</div>
						)}
					</CardContent>
				</Card>
			</div>

			<SubscriptionDialog
				open={showDialog}
				onClose={() => setShowDialog(false)}
				onSuccess={handleDialogSuccess}
				editingItem={editingItem}
			/>

			<DeleteConfirmDialog
				open={!!deleteId}
				onOpenChange={(open) => !open && setDeleteId(null)}
				onConfirm={() => {
					handleDelete(deleteId);
					setDeleteId(null);
				}}
				title='Delete Subscription'
				description='Are you sure you want to delete this subscription? This action cannot be undone.'
			/>
		</div>
	);
};

export default Subscription;
