import React from 'react'
import Header from '../components/Header'
import { Menu, Tabs } from '@mantine/core'
import {
  IconAccessible,
  IconActivity,
  IconArrowUpRightCircle,
  IconCalendarEvent,
  IconDevices2,
  IconFileCertificate,
  IconReport,
  IconScale,
  IconSettings,
  IconTarget,
  IconVaccine,
  IconYoga,
  IconBell,
  IconUserCircle,
  IconEdit,
  IconTrash,
  IconSearch
} from '@tabler/icons'

const FitProfilePage = () => {
  const actions = [
    {
      id: 1,
      title: 'Plan',
      icon: <IconCalendarEvent size={22} />
    },
    {
      id: 2,
      title: 'Goals',
      icon: <IconTarget size={22} />
    },
    {
      id: 3,
      title: 'Devices',
      icon: <IconDevices2 size={22} />
    },
    {
      id: 4,
      title: 'Injuries',
      icon: <IconVaccine size={22} />
    },
    {
      id: 5,
      title: 'Fitness history',
      icon: <IconActivity size={22} />
    },
    {
      id: 6,
      title: 'Performance summary',
      icon: <IconAccessible size={22} />
    },
    {
      id: 7,
      title: 'Weight tracker',
      icon: <IconScale size={22} />
    },
    {
      id: 8,
      title: 'Reces',
      icon: <IconYoga size={22} />
    },
    {
      id: 9,
      title: 'Progress report',
      icon: <IconReport size={22} />
    },
    {
      id: 10,
      title: 'Challenges',
      icon: <IconArrowUpRightCircle size={22} />
    },
    {
      id: 11,
      title: 'Leader board',
      icon: <IconFileCertificate size={22} />
    },
    {
      id: 12,
      title: 'Settings',
      icon: <IconSettings size={22} />
    },
    {
      id: 13,
      title: 'Notifications',
      icon: <IconBell size={22} />
    },
    {
      id: 14,
      title: 'Training profile',
      icon: <IconUserCircle size={22} />
    }
  ]

  const performanceSummary = [
    {
      id: 1,
      category: 'Triathlon - Half IronMan',
      totalRaces: 1,
      segment: 'Swimming | Running | Cycling',
      bestPerformance: '00:00:00 | 00:00:00 | 00:00:00',
      lastPerformance: ''
    },
    {
      id: 2,
      category: 'Running - Half Marathon',
      totalRaces: 1,
      segment: 'Running',
      bestPerformance: '01:55:00',
      lastPerformance: ''
    },
    {
      id: 3,
      category: 'Triathlon - Half IronMan',
      totalRaces: 1,
      segment: 'Running',
      bestPerformance: '01:30:00',
      lastPerformance: ''
    }
  ]

  const challenges = [
    {
      id: 1,
      img: 'https://www.yoska.in/kona-coach/images/app/subMenuIcons/challenges.png',
      title: 'Run your best 5 K',
      date: '27-Aug-2020 - 05-Jan-2022'
    },
    {
      id: 2,
      img: 'https://www.yoska.in/wp-content/uploads/2022/01/Daily-Walk_30mins.png',
      title: 'Walk for at least 30 min daily for a week',
      date: '20-Dec-2021 - 31-Dec-2027'
    },
    {
      id: 3,
      img: 'https://www.yoska.in/wp-content/uploads/2022/01/Weekly-Walk_150mins.png',
      title: 'Walk a total of 150 minutes in a week',
      date: '20-Dec-2021 - 31-Dec-2027'
    },
    {
      id: 4,
      img: 'https://www.yoska.in/wp-content/uploads/2022/01/20mins_Core.png',
      title: 'Get 20 minutes of Core Conditioning  Workout for 5 times a week',
      date: '20-Dec-2021 - 31-Dec-2027'
    },
    {
      id: 5,
      img: 'https://www.yoska.in/wp-content/uploads/2022/01/Weekly-Walk_200mins.png',
      title: 'Walk a total of 200 minutes in a week',
      date: '20-Dec-2021 - 31-Dec-2027'
    },
    {
      id: 6,
      img: 'https://www.yoska.in/wp-content/uploads/2022/01/7Hr_Sleep.png',
      title: 'Walk a total of 200 minutes in a week',
      date: '20-Dec-2021 - 31-Dec-2027'
    }
  ]

  return (
    <>
      <Header />
      <div className='w-full p-4 h-full'>
        {/* Top header */}
        <div className='flex flex-col md:flex-row items-center justify-between gap-6 my-6'>
          <div className='flex items-end gap-x-2'>
            <h1 className='font-medium text-xl'>Fitness Profile</h1>
            <span className='text-xs uppercase py-1 px-2 bg-orange-500 text-slate-50 rounded-sm'>
              Public
            </span>
          </div>
          <div className='flex gap-x-4 items-center'>
            <div className='flex gap-x-2 items-center'>
              <div>
                <IconSearch size={20} color='#334155' />
              </div>
              <div>
                <input
                  type='text'
                  name='searcxh'
                  id='search'
                  placeholder='Search users'
                  className='px-2 py-1 focus:outline-none text-sm'
                />
              </div>
            </div>
            <div>
              <Menu shadow='md' width={250} position='bottom-end'>
                <Menu.Target className='cursor-pointer'>
                  <svg
                    xmlns='http://www.w3.org/2000/svg'
                    className='icon icon-tabler icon-tabler-dots-vertical'
                    width='24'
                    height='24'
                    viewBox='0 0 24 24'
                    strokeWidth='1.5'
                    stroke='#2c3e50'
                    fill='none'
                    strokeLinecap='round'
                    strokeLinejoin='round'
                  >
                    <path stroke='none' d='M0 0h24v24H0z' fill='none' />
                    <circle cx='12' cy='12' r='1' />
                    <circle cx='12' cy='19' r='1' />
                    <circle cx='12' cy='5' r='1' />
                  </svg>
                </Menu.Target>

                <Menu.Dropdown>
                  {actions.map((item) => (
                    <Menu.Item color={'gray'} icon={item.icon} key={item.id}>
                      {item.title}
                    </Menu.Item>
                  ))}
                </Menu.Dropdown>
              </Menu>
            </div>
          </div>
        </div>
        {/* Middle header */}
        <div className='w-full mb-6'>
          <h1 className='font-medium text-xl'>About me</h1>
          <p className='text-sm py-1 text-slate-600'>
            Neil, a 25 year old triathlete is driven by passion to make a
            successful career in competitive short course racing and triathlon
            coaching.
          </p>
        </div>
        {/* Bottom header */}
        <div className='w-full mb-6'>
          <h1 className='font-medium text-xl'>Achievements</h1>
          <p className='text-sm py-1 text-slate-600'>
            Finished several 10K and HM races, 4 Ironman 70.3 distance races and
            5 Olympic distance races
          </p>
          <span className='underline underline-offset-2 font-medium text-sm'>
            Personal Bests
          </span>
        </div>
        <br />
        {/* Races */}
        <div className='w-full mb-6'>
          <h1 className='font-medium text-xl'>Races</h1>
          <Tabs defaultValue='past'>
            <Tabs.List className='flex gap-x-1 my-3'>
              <Tabs.Tab
                value='past'
                className='bg-slate-200 hover:bg-slate-200'
              >
                Past races
              </Tabs.Tab>
              <Tabs.Tab
                value='future'
                className='bg-slate-200 hover:bg-slate-200'
              >
                Future races
              </Tabs.Tab>
            </Tabs.List>
            <p className='my-4 text-sm text-slate-500'>
              These are the past races enrolled by athlete
            </p>
            <Tabs.Panel value='past'>
              <div className='grid grid-cols-1 md:grid-cols-5 w-full items-center h-full bg-slate-200 rounded-md'>
                <div className='flex flex-col p-6 text-center'>
                  <p className='text-sm'>Sunday</p>
                  <p className='text-4xl font-semibold'>28</p>
                  <p className='text-sm'>July</p>
                </div>
                <div className='flex flex-col justify-between items-start md:col-span-3 p-6 bg-slate-100'>
                  <div>
                    <h2 className='text-xl font-medium'>
                      Kanakia Monsoon Marathon Challenge 2018 - 2019
                    </h2>
                    <p className='text-sm text-slate-600'>
                      Mumbai, Maharashtra, India
                    </p>
                  </div>
                  <div className='mt-6'>
                    <p className='text-sm'>
                      Category : Running - Half Marathon
                    </p>
                  </div>
                </div>
                <div className='flex gap-x-6 p-6 justify-between items-center'>
                  <div className='flex flex-col gap-y-2 text-center'>
                    <p className='text-4xl font-semibold'>1171</p>
                    <p className='text-sm'>Days to go</p>
                  </div>
                  <div className='flex flex-col gap-y-4'>
                    <IconEdit size={24} color='grey' />
                    <IconTrash size={24} color='grey' />
                  </div>
                </div>
              </div>
            </Tabs.Panel>
            <Tabs.Panel value='future'>
              <div className='grid grid-cols-5 w-full items-center h-full bg-slate-200 rounded-md'>
                <div className='flex flex-col p-6 text-center'>
                  <p className='text-sm'>Sunday</p>
                  <p className='text-4xl font-semibold'>13</p>
                  <p className='text-sm'>November</p>
                </div>
                <div className='flex flex-col justify-between items-start col-span-3 p-6 bg-slate-100'>
                  <div>
                    <h2 className='text-xl font-medium'>
                      IRONMAN 70.3 MELBOURNE - 2022
                    </h2>
                    <p className='text-sm text-slate-600'>
                      St.Kilda VIctoria Australia
                    </p>
                  </div>
                  <div className='mt-6'>
                    <p className='text-sm'>
                      Category : Triathlon - Half IronMan
                    </p>
                  </div>
                </div>
                <div className='flex gap-x-6 p-6 justify-between items-center'>
                  <div className='flex flex-col gap-y-2 text-center'>
                    <p className='text-4xl font-semibold'>34</p>
                    <p className='text-sm'>Days to go</p>
                  </div>
                  <div className='flex flex-col gap-y-4'>
                    <IconEdit size={24} />
                    <IconTrash size={24} />
                  </div>
                </div>
              </div>
            </Tabs.Panel>
          </Tabs>
        </div>
        <br />
        {/* Performance summary */}
        <div className='w-full mb-6'>
          <h1 className='font-medium text-xl'>Performance summary</h1>
          <div className='my-6'>
            <div className='flex flex-col'>
              <div className='overflow-x-auto shadow-md'>
                <div className='inline-block min-w-full align-middle'>
                  <div className='overflow-hidden '>
                    <table className='min-w-full divide-y divide-gray-200 table-fixed dark:divide-gray-700'>
                      <thead className='bg-gray-100 '>
                        <tr>
                          <th
                            scope='col'
                            className='py-3 px-6 text-xs font-medium tracking-wider text-left text-gray-700 uppercase dark:text-gray-400'
                          >
                            Category
                          </th>
                          <th
                            scope='col'
                            className='py-3 px-6 text-xs font-medium tracking-wider text-left text-gray-700 uppercase dark:text-gray-400'
                          >
                            Total Races
                          </th>
                          <th
                            scope='col'
                            className='py-3 px-6 text-xs font-medium tracking-wider text-left text-gray-700 uppercase dark:text-gray-400'
                          >
                            Segment
                          </th>
                          <th
                            scope='col'
                            className='py-3 px-6 text-xs font-medium tracking-wider text-left text-gray-700 uppercase dark:text-gray-400'
                          >
                            Best Performance
                          </th>
                          <th
                            scope='col'
                            className='py-3 px-6 text-xs font-medium tracking-wider text-left text-gray-700 uppercase dark:text-gray-400'
                          >
                            Last Race Performance
                          </th>
                          <th
                            scope='col'
                            className='py-3 px-6 text-xs font-medium tracking-wider text-left text-gray-700 uppercase dark:text-gray-400'
                          >
                            Actions
                          </th>
                        </tr>
                      </thead>
                      <tbody className='bg-white divide-y divide-gray-200 '>
                        {performanceSummary.map((item, index) => (
                          <tr
                            className='hover:bg-gray-100 text-slate-600'
                            key={index}
                          >
                            <td className='py-4 px-6 text-sm font-medium text-gray-900 whitespace-nowrap'>
                              <div className='text-sm font-medium'>
                                {item.category}
                              </div>
                            </td>
                            <td className='py-4 px-6 text-sm font-medium text-gray-500 whitespace-nowrap'>
                              {item.totalRaces}
                            </td>
                            <td className='py-4 px-6 text-sm font-medium text-gray-500 whitespace-nowrap'>
                              {item.segment}
                            </td>
                            <td className='py-4 px-6 text-sm font-medium text-gray-500 whitespace-nowrap'>
                              {item.bestPerformance}
                            </td>
                            <td className='py-4 px-6 text-sm font-medium text-green-500 whitespace-nowrap'>
                              {item.lastPerformance}
                            </td>
                            <td className='py-4 px-6 text-sm font-medium text-left whitespace-nowrap flex gap-4 items-center'>
                              <div className='flex gap-x-6'>
                                <IconEdit size={18} color='grey' />
                                <IconTrash size={18} color='grey' />
                              </div>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <br />
        {/* Chanllenges */}
        <div className='w-full mb-6'>
          <h1 className='font-medium text-xl'>Challenges</h1>
          <div className='my-6'>
            <div className='flex flex-col'>
              <div className='overflow-x-auto shadow-md'>
                <div className='inline-block min-w-full align-middle'>
                  <div className='overflow-hidden '>
                    <table className='min-w-full divide-y divide-gray-200 table-fixed dark:divide-gray-700'>
                      <thead className='bg-gray-100 '>
                        <tr>
                          <th
                            scope='col'
                            className='py-3 px-6 text-xs font-medium tracking-wider text-left text-gray-700 uppercase dark:text-gray-400'
                          >
                            Name
                          </th>
                          <th
                            scope='col'
                            className='py-3 px-6 text-xs font-medium tracking-wider text-left text-gray-700 uppercase dark:text-gray-400'
                          >
                            Date
                          </th>
                        </tr>
                      </thead>
                      <tbody className='bg-white divide-y divide-gray-200'>
                        {challenges.map((item, index) => (
                          <tr
                            className='hover:bg-gray-100 text-slate-600'
                            key={index}
                          >
                            <td className='py-4 px-6 text-sm font-medium text-gray-900 whitespace-nowrap flex gap-x-4'>
                              <div>
                                <img
                                  src={`${item.img}`}
                                  alt='...'
                                  className='w-14 rounded-md'
                                />
                              </div>
                              <div className='text-sm font-medium'>
                                {item.title}
                              </div>
                            </td>
                            <td className='py-4 px-6 text-sm font-medium text-gray-500 whitespace-nowrap'>
                              {item.date}
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  )
}

export default FitProfilePage
