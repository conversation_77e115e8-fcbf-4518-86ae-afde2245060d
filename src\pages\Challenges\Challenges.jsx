import React, { useState, useEffect, useRef } from "react";
import { <PERSON><PERSON>, <PERSON>dal as AntModal } from "antd";
import {
	TrophyOutlined,
	CalendarOutlined,
	AimOutlined,
	InfoCircleOutlined,
	BarChartOutlined,
	ClockCircleOutlined,
} from "@ant-design/icons";
import { useNavigate } from "react-router-dom";
import Header from "../../components/Header";
import axios from "axios";
import {
	URL,
	getProgress,
	getUserChalnnges,
	getUserOngoingChalenges,
	subsribeToChallenge,
	listEnrolledChallengesAthlete,
} from "../../API/api-endpoint";
import { showError, showSuccess } from "../../components/Messages";
import { capitalizeFirstLetter } from "../../utils/Resubale";
import {
	Tabs,
	TabsContent,
	TabsList,
	TabsTrigger,
} from "../../components/ui/tabs";
import {
	Dialog,
	DialogContent,
	DialogHeader,
	DialogTitle,
} from "../../components/ui/dialog";
import { Loader } from "lucide-react";


const Challenges = ({ isEnrolledChallengesOpen }) => {
	const navigate = useNavigate();
	const [availableChallenges, setAvailableChallenges] = useState([]);
	const [enrolledChallenges, setEnrolledChallenges] = useState([]);
	const [loading, setLoading] = useState(false);
	const [isCheckedProgress, setCheckProgress] = useState(false);
	const [progressMap, setProgressMap] = useState({});
	const [roleId, setRoleId] = useState(null);
	const [assidnedChallengesProress, setassidnedChallengesProress] =
		useState();
	const scrollPositionRef = useRef(0);

	useEffect(() => {
		setRoleId(localStorage.getItem("roleID"));
	}, []);

	const handleCheckProgress = async (challengeId) => {
		try {
			setLoading(true);
			const data = { id: challengeId };
			const response = await getProgress(data);
			setLoading(false);

			if (!response.status && response.message) {
				setassidnedChallengesProress({
					error: true,
					message: response.message,
				});
			} else {
				setassidnedChallengesProress(response);
			}
		} catch (error) {
			setLoading(false);
			setassidnedChallengesProress({
				error: true,
				message: "Error fetching progress data",
			});
		}
	};

	// useEffect(() => {
	// 	if (isEnrolledChallengesOpen?.assignCoachId) {
	// 		fetchAssignedChallenges();
	// 	}
	// 	if (!isEnrolledChallengesOpen?.assignCoachId) {
	// 		fetchAssignedChallenges();
	// 	}
	// }, [isEnrolledChallengesOpen?.assignCoachId]);

	const fetchAssignedChallenges = async () => {
		// const onBoardingState = userDetail?.onboardingState;

		// if (onBoardingState == "community") {
		// 	const response = await listEnrolledChallengesAthlete();
		// } else {
		const response = await getUserChalnnges(
			isEnrolledChallengesOpen?.assignCoachId
		);
		// }
	};

	// Add state for the description modal
	const [descriptionModal, setDescriptionModal] = useState({
		visible: false,
		title: "",
		description: "",
	});

	// Get user onboarding state from localStorage
	const storedUser = localStorage.getItem("user");
	let user = null;
	let onboardingState = null;

	if (storedUser) {
		user = JSON.parse(storedUser);
		onboardingState = user.onboardingState;
	}

	useEffect(() => {
		setLoading(true);
		if (onboardingState === "community") {
			fetchAthleteEnrollments().then((enrolledList) => {
				fetchCommunityChallenges(enrolledList);
			});
		} else if (onboardingState === "yoska_academy") {
			fetchAssignedChallenge();
			fetchAssignedChallenges();
		}
	}, [onboardingState]);

	// API call for yoska_academy users
	const fetchAssignedChallenge = async () => {
		try {
			const response = await getUserOngoingChalenges();
			if (response?.challenges) {
				const { available, enrolled } = response.challenges.reduce(
					(acc, challenge) => {
						if (challenge.is_user_enrolled) {
							acc.enrolled.push(challenge);
						} else {
							acc.available.push(challenge);
						}
						return acc;
					},
					{ available: [], enrolled: [] }
				);

				setAvailableChallenges(available);
				setEnrolledChallenges(enrolled);
			}
			setLoading(false);
		} catch (error) {
			console.error("Error fetching assigned challenges:", error);
			setLoading(false);
		}
	};

	// API call for community users to fetch available challenges
	const fetchCommunityChallenges = async (enrolledChallengesList = []) => {
		try {
			setLoading(true);
			const groupDetailString = localStorage.getItem("groupDetail");
			const groupDetail = groupDetailString
				? JSON.parse(groupDetailString)
				: null;
			const athleteCommunityGroupId = groupDetail?.id;

			if (!athleteCommunityGroupId) {
				showError("Athlete Community Group ID is missing.");
				setLoading(false);
				return;
			}

			const token = localStorage.getItem("token");
			const response = await axios.get(
				`${URL}/athlete-community/group-challenge/group/${athleteCommunityGroupId}`,
				{
					headers: {
						"Content-Type": "application/json",
						Authorization: token,
					},
				}
			);
			
			if (response.data.status) {
				// --- Old logic: Show enrolled challenges in both tabs (commented for reference) ---
				// const activeChallenges = response.data.data.filter(
				// 	(challenge) =>
				// 		new Date(
				// 			challenge.athletecommunitygroupchallenge.challengeEndDate
				// 		) > new Date()
				// );

				// setAvailableChallenges(activeChallenges);

				// --- New logic: Exclude enrolled challenges from available tab ---
				const enrolledIds = enrolledChallengesList.map(
					(enrollment) => enrollment.athletecommunitygroupchallenge.id
				);

				const availableChallenges = response.data.data.filter(
					(challenge) => {
						const challengeId =
							challenge.athletecommunitygroupchallenge.id;
						const endDate = new Date(
							challenge.athletecommunitygroupchallenge.challengeEndDate
						);
						return (
							endDate > new Date() &&
							!enrolledIds.includes(challengeId)
						);
					}
				);

				setAvailableChallenges(availableChallenges);
			} else {
				showError("Failed to fetch community challenges.");
			}
			setLoading(false);
		} catch (error) {
			console.error("Error fetching community challenges:", error);
			showError("Error fetching community challenges.");
			setLoading(false);
		}
	};

	// Refactored fetchAthleteEnrollments to return the enrolled list
	const fetchAthleteEnrollments = async () => {
		try {
			setLoading(true);
			const token = localStorage.getItem("token");
			const athleteId = localStorage.getItem("userId");

			if (!athleteId) {
				showError("Athlete ID is missing.");
				return [];
			}

			const response = await axios.get(
				`${URL}/athlete-community/group-challenge-enrolment/athlete/${athleteId}`,
				{
					headers: {
						"Content-Type": "application/json",
						Authorization: token,
					},
				}
			);

			if (response.data.status) {
				const enrolledData = response.data.data;
				setEnrolledChallenges(enrolledData);
				return enrolledData;
			} else {
				showError("Failed to fetch athlete enrollments.");
				return [];
			}
		} catch (error) {
			showError("Error fetching athlete enrollments.");
			return [];
		} finally {
			setLoading(false);
		}
	};

	// For enrolling in challenges
	const handleEnrollChallenge = async (challengeId) => {
		if (onboardingState === "yoska_academy") {
			await handleSubscribeToChannel(challengeId);
		} else if (onboardingState === "community") {
			await enrollCommunityChallenge(challengeId);
		}
	};

	// For yoska_academy subscriptions
	const handleSubscribeToChannel = async (id) => {
		const data = { challengeId: id };
		const response = await subsribeToChallenge(data);

		if (response?.status) {
			showSuccess("You are Subscribed to this Challenge");
			fetchAssignedChallenge();
			fetchAssignedChallenges();
		} else {
			showError(response?.message);
		}
	};

	// For community challenge enrollments
	const enrollCommunityChallenge = async (challengeId) => {
		try {
			const token = localStorage.getItem("token");
			const userId = localStorage.getItem("userId");

			const payload = {
				athleteCommunityGroupChallengeId: challengeId,
				userId: userId,
			};

			const response = await axios.post(
				`${URL}/athlete-community/group-challenge-enrolment`,
				payload,
				{
					headers: {
						"Content-Type": "application/json",
						Authorization: token,
					},
				}
			);

			if (response.data.status) {
				showSuccess("Successfully enrolled in community challenge.");
				// First fetch enrollments to update the enrolled list
				await fetchAthleteEnrollments();
				// Then fetch available challenges to update the available list
				await fetchCommunityChallenges();
			} else {
				showError(
					"Enrollment failed: " +
					(response.data.message || "Unknown error")
				);
			}
		} catch (error) {
			console.error("Error enrolling in community challenge:", error);
			showError("Error enrolling in community challenge.");
		}
	};

	const handleViewLeaderboard = (challengeId) => {
		navigate(`/leaderboard?challengeId=${challengeId}`);
	};

	// Add a function to handle opening the details modal
	const handleViewDetails = (challengeName, description) => {
		// Check if description contains HTML tags
		const sanitizedDescription = description
			? // If it contains HTML tags but they're visible as text, remove them
			description.includes("<p>") && !description.includes("&lt;")
				? description.replace(/<\/?p>/g, "")
				: description
			: "No description available";

		setDescriptionModal({
			visible: true,
			title: capitalizeFirstLetter(challengeName),
			description: sanitizedDescription,
		});
	};

	// Add a function to close the modal
	const closeDescriptionModal = () => {
		setDescriptionModal({
			...descriptionModal,
			visible: false,
		});
	};

	const renderEmptyState = (title, message1, message2) => (
		<div className='flex flex-col items-center justify-center py-15 px-5 text-center h-[50vh]'>
			<TrophyOutlined className='text-5xl text-[#ffa940] mb-5' />
			<h3 className='text-xl font-medium text-gray-800 mb-3'>{title}</h3>
			<p className='text-base text-gray-600 m-0 mb-2'>{message1}</p>
			<p className='text-base text-gray-600 m-0 mb-2'>{message2}</p>
		</div>
	);

	useEffect(() => {
		if (onboardingState !== "yoska_academy") return;
		const fetchProgressForAllChallenges = async () => {
			if (!enrolledChallenges?.length) return;

			const progressResults = await Promise.all(
				enrolledChallenges.map(async (challenge) => {
					try {
						const response = await getProgress({
							id: challenge.id,
						});
						return {
							id: challenge.id,
							data: response.status
								? response
								: {
									error: true,
									message:
										response.message ||
										"No progress data",
								},
						};
					} catch (err) {
						return {
							id: challenge.id,
							data: {
								error: true,
								message: "Error fetching progress",
							},
						};
					}
				})
			);

			setProgressMap(
				progressResults.reduce((acc, { id, data }) => {
					acc[id] = data;
					return acc;
				}, {})
			);
		};

		fetchProgressForAllChallenges();
	}, [enrolledChallenges, onboardingState]);

	// Add this sorting function before rendering the challenges
	const sortChallengesByDate = (challenges) => {
		return [...challenges].sort((a, b) => {
			// For yoska_academy challenges
			// Case 1: Direct challenge objects (available challenges)
			if (a.challengeStartDate && b.challengeStartDate) {
				return (
					new Date(b.challengeStartDate) -
					new Date(a.challengeStartDate)
				);
			}

			// Case 2: Nested challenge objects (enrolled challenges)
			if (
				a.challenge?.challengeStartDate &&
				b.challenge?.challengeStartDate
			) {
				return (
					new Date(b.challenge.challengeStartDate) -
					new Date(a.challenge.challengeStartDate)
				);
			}

			// For community challenges
			// Case 1: Direct athletecommunitygroupchallenge objects
			if (
				a.athletecommunitygroupchallenge?.challengeStartDate &&
				b.athletecommunitygroupchallenge?.challengeStartDate
			) {
				return (
					new Date(
						b.athletecommunitygroupchallenge.challengeStartDate
					) -
					new Date(
						a.athletecommunitygroupchallenge.challengeStartDate
					)
				);
			}

			// Case 2: Nested athletecommunitygroupchallenge objects
			if (
				a.challenge?.athletecommunitygroupchallenge
					?.challengeStartDate &&
				b.challenge?.athletecommunitygroupchallenge?.challengeStartDate
			) {
				return (
					new Date(
						b.challenge.athletecommunitygroupchallenge.challengeStartDate
					) -
					new Date(
						a.challenge.athletecommunitygroupchallenge.challengeStartDate
					)
				);
			}

			return 0;
		});
	};

	const renderYoskaAcademyCard = (
		challengeData,
		isEnrolled,
		progressData
	) => {
		const challenge = challengeData.challenge || challengeData;

		if (!challenge) return null;

		const activityBadge =
			challengeData?.targetchallanges?.[0]?.activity?.badge ||
			challengeData?.userTargets?.[0]?.activity?.badge;

		const activityName =
			challengeData?.targetchallanges?.[0]?.activity?.activity_name ||
			challengeData?.userTargets?.[0]?.activity?.activity_name;

		return (
			<div
				className='bg-white rounded-lg overflow-hidden shadow-md border-l-4 border-l-[#16a34a]'
				key={challenge.id}
			>
				<div className='bg-[#fff2e8]  py-4 px-4 flex justify-between items-center'>
					<div className='text-xs text-semibold text-orange-800'>
						<CalendarOutlined />{" "}
						{new Date(
							challenge.challengeStartDate
						).toLocaleDateString("en-GB")}{" "}
						-{" "}
						{new Date(
							challenge.challengeEndDate
						).toLocaleDateString("en-GB")}
					</div>
					<div className='text-xs text-orange-800 font-semibold border border-pink-200 !bg-white py-0.5 px-2 rounded-full'>
						{challenge.activitylevel?.level}
					</div>
				</div>
				<div className='py-4 px-4 pb-2 text-lg font-semibold text-[#8b0000] bg-[#fff2e8]'>
					{capitalizeFirstLetter(challenge.challengeName)}
				</div>
				<div className='px-4 pb-4 flex justify-between items-center text-orange-800 font-semibold bg-[#fff2e8]'>
					<span className='text-base'>
						{challenge.activityGroup?.activity_group}
					</span>
					<span className='flex items-center justify-center w-8 h-8 rounded-full bg-gradient-to-br from-orange-50 to-white border-2 border-[#e67e22] text-orange-800'>
						<img
							src={`${URL}/static/public/assets/${activityBadge}`}
							alt={activityName || "Activity"}
							className='w-5 h-5'
						/>
					</span>
				</div>
				<div className='px-4 my-4'>
					<div className='flex justify-between py-2 border-b border-gray-100'>
						<div className='text-gray-600 flex items-center gap-2 text-sm'>
							<ClockCircleOutlined className='text-orange-600' />{" "}
							Duration
						</div>
						<div className='font-semibold  text-base'>
							{challenge.challengeDuration}{" "}
							{challenge.durationunit || "Days"}
						</div>
					</div>
					<div className='flex justify-between py-2 border-b border-gray-100'>
						<div className='text-gray-600 flex items-center gap-2 text-sm'>
							<AimOutlined className='text-orange-600' /> Target
						</div>
						<div className='font-semibold text-base'>
							{challenge?.targetchallanges?.length > 0 ? (
								<>
									{challenge?.targetchallanges?.map(
										(target, index) => (
											<div key={index}>
												{target?.quota} Km
											</div>
										)
									)}
								</>
							) : (
								"NA"
							)}{" "}
						</div>
					</div>
					<div className='flex justify-between py-2'>
						<div className='text-gray-600 flex items-center gap-2 text-sm'>
							<TrophyOutlined className='text-orange-600' />{" "}
							Points
						</div>
						<div className='font-semibold text-base'>
							{challenge.challengePoints} Points
						</div>
					</div>
				</div>

				{isEnrolled && (
					<>
						{progressData && !progressData.error ? (
							<div className='px-4'>
								<div className='text-xs text-gray-600 flex justify-between mb-1'>
									<span>
										Progress:{" "}
										{(
											progressData?.totalprogress ?? 0
										).toFixed(2)}
										%
									</span>
									<span>
										Covered:{" "}
										{progressData?.progress?.[0]
											?.completed_quota ?? "-"}{" "}
										/{" "}
										{progressData?.progress?.[0]?.quota ??
											"-"}
									</span>
								</div>
								<div className='w-full bg-gray-200 rounded-full h-2 mb-2 items-center'>
									<div
										className='bg-orange-600 h-2 rounded-full transition-all duration-300 '
										style={{
											width: `${(
												progressData?.totalprogress ?? 0
											).toFixed(2)}%`,
										}}
									></div>
								</div>
							</div>
						) : (
							<div className='text-sm text-gray-400 mt-2'>
								Progress not available
							</div>
						)}

						<div className='p-4 pt-0'>
							<button
								className='w-full flex items-center justify-center text-base gap-2 py-2 px-4 bg-[#e67e22] text-white rounded hover:bg-[#15803d] transition-colors mb-2'
								onClick={() =>
									handleViewLeaderboard(challengeData.id)
								}
							>
								<TrophyOutlined /> View Leaderboard
							</button>

							<div className='flex gap-2 my-2'>
								<Button
									type='default'
									icon={<InfoCircleOutlined />}
									className='flex-1 !border-[#e67e22] !text-orange-800 font-semibold hover:!text-orange-800'
									onClick={() =>
										handleViewDetails(
											challenge.challengeName,
											challenge.challengeDescription
										)
									}
								>
									Details
								</Button>

								<Button
									type='default'
									icon={<BarChartOutlined />}
									className='flex-1 !border-[#e67e22] !text-orange-800 font-semibold hover:!text-orange-800'
									onClick={() => {
										scrollPositionRef.current =
											window.scrollY; // Save scroll position BEFORE opening
										setCheckProgress(true);
										handleCheckProgress(challengeData?.id);
									}}
								>
									Progress
								</Button>
							</div>
						</div>
					</>
				)}

				{!isEnrolled && (
					<div className='px-4 py-2 pb-4 text-center border-t border-gray-100'>
						<div className='flex gap-2'>
							<Button
								type='primary'
								className={`flex-1 ${challenge.is_user_enrolled
									? "!bg-gray-400 !border-gray-400"
									: "!bg-[#e67e22] !border-[#e67e22]"
									}`}
								disabled={challenge.is_user_enrolled}
								onClick={() =>
									handleEnrollChallenge(challenge.id)
								}
							>
								{challenge.is_user_enrolled
									? "Enrolled"
									: "Enroll"}
							</Button>
							<Button
								type='default'
								icon={<InfoCircleOutlined />}
								className='flex-1 !border-[#e67e22] !text-orange-800 font-semibold hover:!text-orange-800'
								onClick={() =>
									handleViewDetails(
										challenge.challengeName,
										challenge.challengeDescription
									)
								}
							>
								View Description
							</Button>
						</div>
					</div>
				)}
			</div>
		);
	};

	const renderCommunityCard = (challenge, isEnrolled) => {
		const challengeData = challenge.athletecommunitygroupchallenge;
		// Check if the current community challenge is already enrolled
		const currentChallengeName =
			challengeData?.challengeName?.toLowerCase();
		const isAlreadyEnrolled = enrolledChallenges.some((enrollment) => {
			const enrolledName =
				enrollment?.athletecommunitygroupchallenge?.challengeName?.toLowerCase();
			return enrolledName === currentChallengeName;
		});

		return (
			<div
				className='bg-white rounded-lg overflow-hidden shadow-md border-l-4 border-l-[#10b981]'
				key={challengeData.id}
			>
				<div className='bg-[#fff2e8] py-4 px-4 flex justify-between items-center'>
					<div className='text-xs text-semibold text-orange-800'>
						<CalendarOutlined />{" "}
						{new Date(
							challengeData.challengeStartDate
						).toLocaleDateString("en-GB")}{" "}
						-{" "}
						{new Date(
							challengeData.challengeEndDate
						).toLocaleDateString("en-GB")}
					</div>
					<div className='text-xs text-orange-800 font-semibold border border-pink-200 !bg-white py-0.5 px-2 rounded-full'>
						{challenge.activitylevel?.level}
					</div>
				</div>
				<div className='py-4 px-4 pb-2 text-lg font-semibold text-[#8b0000] bg-[#fff2e8]'>
					{capitalizeFirstLetter(challengeData.challengeName)}
				</div>
				<div className='px-4 pb-4 flex justify-between items-center text-orange-800 font-semibold bg-[#fff2e8]'>
					<span className='text-base'>
						{challenge.activity?.activity_name}
					</span>
					
					{/* <span className='flex items-center justify-center w-6 h-6 rounded-full bg-white border border-[#e67e22] text-orange-800 font-bold'>
						<img
							src={`${URL}/static/public/assets/${challenge.activity?.badge}`}
							alt={challenge.activity?.activity_name}
							className='w-5 h-5'
						/>
					</span> */}
				</div>
				<div className='px-4 my-4'>
					<div className='flex justify-between py-2 border-b border-gray-100'>
						<div className='text-gray-600 flex items-center gap-2 text-sm'>
							<ClockCircleOutlined className='text-orange-600' />{" "}
							Duration
						</div>
						<div className='font-semibold  text-base'>
							{challengeData.challengeDuration}{" "}
							{challengeData.challengeDurationUnit || "Days"}
						</div>
					</div>
					<div className='flex justify-between py-2 border-b border-gray-100'>
						<div className='text-gray-600 flex items-center gap-2 text-sm'>
							<AimOutlined className='text-orange-600' /> Target
						</div>
						<div className='font-semibold text-base'>
							{challengeData?.target?.name || "NA"}
						</div>
					</div>
					<div className='flex justify-between py-2'>
						<div className='text-gray-600 flex items-center gap-2 text-sm'>
							<TrophyOutlined className='text-orange-600' />{" "}
							Points
						</div>
						<div className='font-semibold text-base'>
							{challengeData.challengePoints} Points
						</div>
					</div>
				</div>

				{isEnrolled && (
					<div className='p-4 flex flex-col gap-2'>
						<Button
							type='primary'
							icon={<TrophyOutlined />}
							className='w-full !bg-[#10b981] !border-[#10b981] hover:!bg-[#059669] hover:!border-[#059669]'
							onClick={() =>
								handleViewLeaderboard(challengeData.id)
							}
						>
							View Leaderboard
						</Button>
						<Button
							type='default'
							icon={<InfoCircleOutlined />}
							className='flex-1 my-2 !border-[#e67e22] !text-orange-800 font-semibold hover:!text-orange-800'
							onClick={() =>
								handleViewDetails(
									challengeData.challengeName,
									challengeData.challengeDescription
								)
							}
						>
							Details
						</Button>
					</div>
				)}

				{/* {!isEnrolled && (
					<div className='px-4 py-2 pb-4 text-center border-t border-gray-100'>
						<div className='flex gap-2'>
							<Button
								type='primary'
								className={`flex-1 ${
									isAlreadyEnrolled
										? "!bg-gray-400 !border-gray-400"
										: "!bg-[#e67e22] !border-[#e67e22]"
								}`}
								disabled={isAlreadyEnrolled}
								onClick={() =>
									handleEnrollChallenge(challengeData.id)
								}
							>
								{isAlreadyEnrolled ? "Enrolled" : "Enroll"}
							</Button>
							<Button
								type='default'
								icon={<InfoCircleOutlined />}
								className='flex-1 !border-[#e67e22] !text-orange-800 font-semibold hover:!text-orange-800'
								onClick={() =>
									handleViewDetails(
										challengeData.challengeName,
										challengeData.challengeDescription
									)
								}
							>
								View Description
							</Button>
						</div>
					</div>
				)} */}
				{!isEnrolled && (
					<div className='px-4 py-2 pb-4 text-center border-t border-gray-100'>
						<div className='flex gap-2'>
							<button
								type='button'
								style={{
									backgroundColor: isAlreadyEnrolled
										? "green"
										: "#e67e22",
									color: "white",
									borderColor: isAlreadyEnrolled
										? "green"
										: "#e67e22",
									padding: "3% 5% 3% 5%",
									borderRadius: "5px",
									cursor: isAlreadyEnrolled
										? "not-allowed"
										: roleId == 6
										? "not-allowed"
										: "pointer",
									opacity: isAlreadyEnrolled ? 0.6 : 1,
									fontWeight: "bold",
								}}
								disabled={isAlreadyEnrolled || roleId == 6}
								onClick={() =>
									handleEnrollChallenge(challengeData.id)
								}
							>
								{isAlreadyEnrolled ? "Enrolled" : "Enroll"}
							</button>
							<Button
								type='default'
								icon={<InfoCircleOutlined />}
								className='flex-1 !border-[#e67e22] !text-orange-800 font-semibold hover:!text-orange-800'
								onClick={() =>
									handleViewDetails(
										challengeData.challengeName,
										challengeData.challengeDescription
									)
								}
							>
								View Description
							</Button>
						</div>
					</div>
				)}
			</div>
		);
	};

	return (
		<div className='w-full min-h-screen '>
			<Header />
			<div className='max-w-[1200px] mx-auto px-5 pt-[100px] pb-10'>
				<div className='mb-8'>
					<h1 className='text-3xl font-bold text-orange-950 mb-2'>
						Challenges
					</h1>
					<p className='text-base text-gray-600'>
						Discover and enroll in challenges to improve your
						fitness
					</p>
				</div>

				<div className='mt-4'>
					<Tabs defaultValue='available' className='w-full'>
						<TabsList className='bg-orange-100/50 border border-orange-200 mb-8'>
							<TabsTrigger
								value='available'
								className='text-gray-600 font-bold'
								activeClassName='bg-white rounded-full text-orange-800'
							>
								Available Challenges
							</TabsTrigger>
							<TabsTrigger
								value='enrolled'
								className='text-gray-600 font-bold'
								activeClassName='bg-white rounded-full text-orange-800'
							>
								My Enrolled Challenges
							</TabsTrigger>
						</TabsList>

						<TabsContent value='available'>
							<div>
								{loading ? (
									<div className='flex flex-col items-center justify-center py-8'>
										<Loader className='animate-spin text-orange-500 w-10 h-10 mb-3' />
										<p className='text-gray-600 text-center'>
											Loading progress data...
										</p>
									</div>
								) : availableChallenges.length > 0 ? (
									<div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-5 mt-5'>
										{onboardingState === "yoska_academy"
											? sortChallengesByDate(
												availableChallenges
											).map((challenge) =>
												renderYoskaAcademyCard(
													challenge,
													false
												)
											)
											: sortChallengesByDate(
												availableChallenges
											).map((challenge) =>
												renderCommunityCard(
													challenge,
													false
												)
											)}
									</div>
								) : (
									renderEmptyState(
										"No challenges available",
										"There are no active challenges available at the moment.",
										"Check back later for new challenges."
									)
								)}
							</div>
						</TabsContent>

						<TabsContent value='enrolled'>
							<div>
								{loading ? (
									<div className='flex flex-col items-center justify-center py-8'>
										<Loader className='animate-spin text-orange-500 w-10 h-10 mb-3' />
										<p className='text-gray-600 text-center'>
											Loading progress data...
										</p>
									</div>
								) : enrolledChallenges?.length > 0 ? (
									<div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-5 mt-5'>
										{sortChallengesByDate(
											enrolledChallenges
										).map((challengeData) =>
											onboardingState === "yoska_academy"
												? renderYoskaAcademyCard(
													challengeData,
													true,
													progressMap[
													challengeData.id
													]
												)
												: renderCommunityCard(
													challengeData,
													true
												)
										)}
									</div>
								) : (
									renderEmptyState(
										"No enrolled challenges",
										"You haven't enrolled in any challenges yet.",
										"Check out available challenges to get started."
									)
								)}
							</div>
						</TabsContent>
					</Tabs>
				</div>
			</div>

			{/* Description Modal */}
			<AntModal
				title='Challenge Description'
				open={descriptionModal.visible}
				onCancel={closeDescriptionModal}
				footer={[
					<Button key='close' onClick={closeDescriptionModal}>
						Close
					</Button>,
				]}
			>
				<h3 className='text-lg font-medium mb-2'>
					{descriptionModal.title}
				</h3>
				<div>{descriptionModal.description}</div>
			</AntModal>

			{isCheckedProgress && (
				<Dialog
					open={isCheckedProgress}
					onOpenChange={(open) => {
						if (!open) {
							setCheckProgress(false);
							requestAnimationFrame(() => {
								window.scrollTo(0, scrollPositionRef.current);
							});
						}
					}}
					aria-describedby='progress-dialog-description'
				>
					<DialogContent className='sm:max-w-[450px] bg-white'>
						<DialogHeader className='flex justify-between '>
							<DialogTitle className='text-lg font-semibold'>
								Progress Details
								<div className='text-gray-500 mb-4 text-sm'>
									Your current progress for this challenge
								</div>
							</DialogTitle>
						</DialogHeader>

						<div className='py-2' id='progress-dialog-description'>
							{loading ? (
								<div className='flex flex-col items-center justify-center py-8'>
									<Loader className='animate-spin text-orange-500 w-10 h-10 mb-3' />
									<p className='text-gray-600 text-center'>
										Loading progress data...
									</p>
								</div>
							) : assidnedChallengesProress?.error ? (
								<div className='flex flex-col items-center justify-center py-8'>
									<InfoCircleOutlined className='text-3xl text-orange-400 mb-3' />
									<p className='text-orange-600 text-center font-medium'>
										{assidnedChallengesProress.message ||
											"No progress data available"}
									</p>
									<p className='text-orange-500 text-center text-sm mt-2'>
										This could be because you haven't
										started the challenge yet or there was
										an error retrieving your data.
									</p>
								</div>
							) : !assidnedChallengesProress ||
								!assidnedChallengesProress.progress ? (
								<div className='flex flex-col items-center justify-center py-8'>
									<InfoCircleOutlined className='text-3xl text-gray-400 mb-3' />
									<p className='text-orange-600 text-center'>
										No progress data available or there was
										an error fetching the data.
									</p>
								</div>
							) : assidnedChallengesProress.progress.length ===
								0 ? (
								<div className='flex flex-col items-center justify-center py-8'>
									<BarChartOutlined className='text-3xl text-gray-400 mb-3' />
									<p className='text-orange-600 text-center'>
										No progress recorded yet for this
										challenge.
									</p>
								</div>
							) : (
								<>
									<div className='text-base font-semibold mb-3'>
										Progress Data
									</div>
									<div className='flex justify-between items-center mb-2'>
										<div className='text-gray-500 text-base'>
											Activity Name:
										</div>
										<div className='font-semibold text-base'>
											{
												assidnedChallengesProress
													?.progress?.[0]
													?.activity_name
											}
										</div>
									</div>

									<div className='flex justify-between items-center mb-2'>
										<div className='text-gray-500 text-base'>
											Distance:
										</div>
										<div className='font-semibold text-base'>
											{
												assidnedChallengesProress
													?.progress?.[0]?.quota
											}
											{assidnedChallengesProress
												?.progress?.[0]?.unit || " Km"}
										</div>
									</div>

									<div className='flex justify-between items-center mb-2'>
										<div className='text-gray-500 text-base'>
											Complete Distance:
										</div>
										<div className='font-semibold text-base'>
											{assidnedChallengesProress
												?.progress?.[0]
												?.completed_quota || "0"}
											{assidnedChallengesProress
												?.progress?.[0]?.unit || " Km"}
										</div>
									</div>

									<div className='flex justify-between items-center mb-1'>
										<div className='text-gray-500 text-base'>
											Progress:
										</div>
										<div className='font-semibold text-base'>
											{assidnedChallengesProress?.totalprogress?.toFixed(
												2
											) || "0.00"}
											%
										</div>
									</div>

									<div className='w-full bg-gray-200 rounded-full h-4 overflow-hidden'>
										<div
											className='h-full bg-orange-500 transition-all duration-500'
											style={{
												width: `${assidnedChallengesProress?.totalprogress?.toFixed(
													2
												) || 0
													}%`,
											}}
										></div>
									</div>

									<div className='border rounded-lg overflow-hidden shadow-sm'>
										<div className='grid grid-cols-3 bg-[#fff8f1] p-3 font-semibold text-base text-orange-800 border-b'>
											<div>Activity Name</div>
											<div className='text-center'>
												Distance
											</div>
											<div className='text-right'>
												Completed Distance
											</div>
										</div>

										{assidnedChallengesProress?.progress.map(
											(item, index) => (
												<div
													key={index}
													className='grid grid-cols-3 p-3 text-base font-semibold border-b last:border-b-0'
												>
													<div>
														{item.activity_name}
													</div>
													<div className='text-center'>
														{item.quota}{" "}
														{item.unit || "Km"}
													</div>
													<div className='text-right'>
														{item.completed_quota}{" "}
														{item.unit || "Km"}
													</div>
												</div>
											)
										)}
									</div>
								</>
							)}
						</div>
					</DialogContent>
				</Dialog>
			)}
		</div>
	);
};

export default Challenges;
