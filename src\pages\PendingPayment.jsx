import { IconMail } from '@tabler/icons'
import React from 'react'
import Header from '../components/Header'

const PendingPayment = () => {
  return (
    <>
      <Header />
      <section>
        <div className='w-full md:w-10/12 lg:w-8/12 xl:w-6/12 mx-auto'>
          <div className='flex items-center justify-center p-4 w-full lg:col-span-4'>
            <div className='md:absolute md:top-32 md:right-2/2 px-4 py-6 bg-slate-50 drop-shadow-sm rounded-md border-2 border-slate-300/75 w-full md:w-8/12 lg:w-6/12 xl:w-4/12'>
              <h1 className='text-xl font-medium m-0 pb-2'>Payment Status</h1>
              <hr />
              <div className='flex flex-col gap-y-4 items-start mt-4'>
                <div className='flex items-center gap-2'>
                  <IconMail size={20} color='#3b82f6' />
                  <small className='text-blue-500'>Email reminder</small>
                </div>
                <div className='flex flex-col gap-6 items-start'>
                  <div className='flex flex-col gap-2'>
                    <p>Payment status</p>
                    <div className='flex gap-3 items-start flex-wrap'>
                      <select
                        name='status'
                        id='status'
                        className='py-1 px-3 border text-sm rounded-sm focus:outline-none'
                      >
                        <option value='Confirm'>Confirm</option>
                        <option value='Onhold'>Onhold</option>
                        <option value='Paid'>Paid</option>
                        <option value='Pending'>Pending</option>
                      </select>
                      <select
                        name='status'
                        id='status'
                        className='py-1 px-3 border text-sm rounded-sm focus:outline-none'
                      >
                        <option value='January'>January</option>
                        <option value='February'>February</option>
                        <option value='March'>March</option>
                        <option value='April'>April</option>
                        <option value='May'>May</option>
                        <option value='June'>June</option>
                        <option value='July'>July</option>
                        <option value='August'>August</option>
                        <option value='September'>September</option>
                        <option value='October'>October</option>
                        <option value='November'>November</option>
                        <option value='December'>December</option>
                      </select>
                      <input
                        type='text'
                        defaultValue='2022'
                        className='py-1 px-3 border text-sm rounded-sm focus:outline-none'
                      />
                    </div>
                  </div>
                  <div className='w-full flex flex-col gap-2'>
                    <small>0 are yet to pay in the selected month</small>
                    <input
                      type='text'
                      placeholder='Search by firstname or secondname'
                      className='py-2 px-3 border text-sm rounded-sm focus:outline-none'
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </>
  )
}

export default PendingPayment
