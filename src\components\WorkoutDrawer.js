import React, { useEffect, useState } from "react";
import DeleteForeverIcon from "@mui/icons-material/DeleteForever";
import CloseIcon from "@mui/icons-material/Close";
import {
  Button,
  Select,
  FormLabel,
  Drawer,
  Typography,
  TextField,
  InputLabel,
  FormControl,
  OutlinedInput,
  MenuItem,
  CardHeader,
  Grid,
  Divider,
  CardContent,
  Checkbox,
} from "@mui/material";
import "react-datepicker/dist/react-datepicker.css";
import AddIcon from "@mui/icons-material/Add";
import DatePicker from "react-datepicker";
import moment from "moment";
import { useSelector } from "react-redux";
import { useDispatch } from "react-redux";
import { handleWorkoutFormState } from "../store/slices/MultiTrainingBlocksSlice";

const WorkoutDrawer = (props) => {
  let dispatch = useDispatch();
  const {
    workoutformdrawerState,
    setworkoutformdrawerState,
    formData,
    setmasterData,
    masterData,
    setFilteredData,
  } = props;

  const [formValue, setFormValue] = useState({});
  let isOpen = useSelector(
    (state) => state.MultiTrainingBlocksSlice.isWorkoutFormOpen
  );

  useEffect(() => {
    setFormValue(formData === undefined ? {} : formData);
  }, [formData]);

  const handleChange = (event, name) => {
    if (name === "start" || name === "end") {
      const newName = name;
      const value = event;
      setFormValue({ ...formValue, [newName]: value });
    } else {
      const newName = name;
      const value = event.target.value;
      // let newObject = { ...formValue };
      // console.log(newObject);
      setFormValue({ ...formValue, [newName]: value });
    }
  };

  const drawerStateChange = (state) => {
    let newObject = {
      id: null,
      bool: state,
    };
    dispatch(handleWorkoutFormState(newObject));
  };

  const DrawerHeader = () => (
    <div style={{ display: "flex", justifyContent: "space-between" }}>
      <div>
        <CardHeader
          title={formValue.isEditForm ? "Edit Workout" : "Add Workout"}
          titleTypographyProps={{ variant: "h4" }}
        />
      </div>
      <div
        style={{ display: "flex", justifyContent: "right", marginTop: "12px" }}
      >
        <span>
          {formValue?.isEditForm ? (
            <Button style={{ color: "black" }}>
              {" "}
              {/*onClick={() => onDelete()} */}
              <DeleteForeverIcon />
            </Button>
          ) : (
            ""
          )}
        </span>
        <span>
          <Button
            onClick={() => drawerStateChange(false)}
            style={{ color: "black" }}
          >
            <CloseIcon />
          </Button>
        </span>
      </div>
    </div>
  );

  const handleSubmit = (e) => {
    e.preventDefault();
    console.log(formValue);
    setFormValue({});
    drawerStateChange(false);
  };

  return (
    <div style={{ padding: "5%" }}>
      {/* <div style={{ display: "flex", justifyContent: "center" }}>
        <Button
          startIcon={<AddIcon />}
          onClick={() => drawerStateChange(true)}
          style={{ color: "white", backgroundColor: "rgb(145, 85, 253)" }}
        >
          {" "}
          {formValue.isEditForm ? "Edit Workout" : "Add Workout"}
        </Button>
      </div> */}

      <Drawer
        anchor={"right"}
        open={isOpen}
        onClose={() => drawerStateChange(false)}
        PaperProps={{
          sx: { width: "50%" },
        }}
      >
        <DrawerHeader />
        <Divider sx={{ margin: 0 }} />
        <form onSubmit={(e) => handleSubmit(e)}>
          <CardContent>
            <Grid container>
              <Grid container spacing={2}>
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    label="Activity Name"
                    //placeholder="Walking"
                    defaultValue={formValue.title}
                    onChange={(e) => handleChange(e, "title")}
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    label="Sub-Activity"
                    placeholder="Easy"
                    onChange={(e) => handleChange(e, "sub-activity")}
                  />
                </Grid>
                <Grid item xs={12} sm={12}>
                  <TextField
                    fullWidth
                    multiline
                    minRows={3}
                    label="Description"
                    placeholder="Easy Run"
                    onChange={(e) => handleChange(e, "description")}
                  />
                </Grid>
                <Grid item xs={12} sm={4}>
                  <DatePicker
                    name="start"
                    //selected={new Date()}
                    showYearDropdown
                    showMonthDropdown
                    placeholderText="DD-MM-YYYY"
                    customInput={
                      <TextField
                        fullWidth
                        label="Start Date"
                        autoComplete="off"
                        name="start"
                      />
                    }
                    onChange={(e) => handleChange(e, "start")}
                    value={
                      moment(formValue.start).format("YYYY-MM-DD") ||
                      moment(new Date()).format("YYYY-MM-DD")
                    }
                  />
                </Grid>
                <Grid item xs={12} sm={4}>
                  <DatePicker
                    name="end"
                    // selected={new Date()}
                    showYearDropdown
                    showMonthDropdown
                    placeholderText="DD-MM-YYYY"
                    customInput={
                      <TextField
                        fullWidth
                        label="End Date"
                        autoComplete="off"
                        name="end"
                      />
                    }
                    onChange={(e) => handleChange(e, "end")}
                    value={
                      moment(formValue.end).format("YYYY-MM-DD") ||
                      moment(new Date()).format("YYYY-MM-DD")
                    }
                  />
                </Grid>
              </Grid>
              <Grid
                container
                xs={12}
                sm={12}
                spacing={1}
                style={{
                  margin: "2% 0% 0% 0%",
                  border: "2px solid black",
                  padding: "2%",
                  // margin: "2%",
                }}
              >
                <Grid item xs={4} sm={2}></Grid>
                <Grid item xs={4} sm={4}>
                  <Typography variant="h6">Planned</Typography>
                </Grid>
                <Grid item xs={4} sm={6}>
                  <Typography variant="h6">Actuals</Typography>
                </Grid>
                <Grid item xs={4} sm={2}>
                  <FormLabel variant="h6">Distance</FormLabel>
                </Grid>
                <Grid item xs={4} sm={2}>
                  <TextField
                    fullWidth
                    label="Distance"
                    placeholder="0"
                    onChange={(e) => handleChange(e, "planned_distance")}
                  />
                </Grid>
                <Grid item xs={4} sm={2}>
                  <FormControl fullWidth>
                    <InputLabel id="form-layouts-separator-select-label">
                      Distance
                    </InputLabel>
                    <Select
                      label="Distance"
                      placeholder="Distance"
                      defaultValue="km"
                      onChange={(e) => handleChange(e, "planned_distance_unit")}
                      id="form-layouts-separator-select"
                      labelId="form-layouts-separator-select-label"
                      input={
                        <OutlinedInput
                          label="Language"
                          id="select-multiple-language"
                        />
                      }
                    >
                      <MenuItem value={"km"}>km</MenuItem>
                      <MenuItem value={"mtr"}>mtr</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>
                <Grid item xs={4} sm={2}>
                  <TextField
                    fullWidth
                    label="Distance"
                    placeholder="0"
                    onChange={(e) => handleChange(e, "actual_distance")}
                  />
                </Grid>
                <Grid item xs={4} sm={2}>
                  <FormControl fullWidth>
                    <InputLabel id="form-layouts-separator-select-label">
                      Distance
                    </InputLabel>
                    <Select
                      label="Distance"
                      placeholder="Distance"
                      defaultValue="km"
                      onChange={(e) => handleChange(e, "actual_distance_unit")}
                      id="form-layouts-separator-select"
                      labelId="form-layouts-separator-select-label"
                      input={
                        <OutlinedInput
                          label="Language"
                          id="select-multiple-language"
                        />
                      }
                    >
                      <MenuItem value={"km"}>km</MenuItem>
                      <MenuItem value={"mtr"}>mtr</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>
                <Grid item xs={4} sm={1}></Grid>
                <Grid item xs={4} sm={2}>
                  <FormLabel variant="h6">Duration</FormLabel>
                </Grid>
                <Grid item xs={4} sm={4}>
                  <TextField
                    fullWidth
                    label="Duration"
                    placeholder="1 : 0 : 0"
                    onChange={(e) => handleChange(e, "planned_duration")}
                  />
                </Grid>
                <Grid item xs={4} sm={4}>
                  <TextField
                    fullWidth
                    label="Duration"
                    placeholder="1 : 0 : 0"
                    onChange={(e) => handleChange(e, "actual_duration")}
                  />
                </Grid>
                <Grid item xs={4} sm={1}></Grid>
                <Grid item xs={12} sm={12}>
                  <Divider sx={{ margin: "10px" }} />
                </Grid>
                <Grid item xs={4} sm={2}>
                  <FormLabel variant="h6">Zone</FormLabel>
                </Grid>
                <Grid item xs={4} sm={4}>
                  <FormControl fullWidth>
                    <InputLabel id="form-layouts-separator-select-label">
                      Zone
                    </InputLabel>
                    <Select
                      label="Zone"
                      placeholder="Zone"
                      defaultValue="zone1"
                      onChange={(e) => handleChange(e, "zone")}
                      id="form-layouts-separator-select"
                      labelId="form-layouts-separator-select-label"
                      input={
                        <OutlinedInput
                          label="Language"
                          id="select-multiple-language"
                        />
                      }
                    >
                      <MenuItem value={"zone1"}>Zone 1</MenuItem>
                      <MenuItem value={"zone2"}>Zone 2</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>
                <Grid item xs={1} sm={2}></Grid>
                <Grid item xs={1} sm={2}></Grid>
                <Grid item xs={1} sm={1}></Grid>
                <Grid item xs={4} sm={2}>
                  <FormLabel variant="h6">Heart Rate</FormLabel>
                </Grid>
                <Grid item xs={4} sm={2}>
                  <TextField
                    fullWidth
                    label="Heart Rate"
                    placeholder="0"
                    onChange={(e) => handleChange(e, "planned_heartrate")}
                  />
                </Grid>
                <Grid item xs={4} sm={2}>
                  <FormControl fullWidth>
                    <InputLabel id="form-layouts-separator-select-label">
                      HR
                    </InputLabel>
                    <Select
                      label="Heart Rate"
                      placeholder="123"
                      defaultValue="bpm"
                      onChange={(e) =>
                        handleChange(e, "planned_heartrate_unit")
                      }
                      id="form-layouts-separator-select"
                      labelId="form-layouts-separator-select-label"
                      input={
                        <OutlinedInput
                          label="Language"
                          id="select-multiple-language"
                        />
                      }
                    >
                      <MenuItem value={"bpm"}>BPM</MenuItem>
                      <MenuItem value={"bpm"}>BPM</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>
                <Grid item xs={4} sm={2}>
                  <TextField
                    fullWidth
                    label="Heart Rate"
                    placeholder="0"
                    onChange={(e) => handleChange(e, "actual_heartrate")}
                  />
                </Grid>
                <Grid item xs={4} sm={2}>
                  <FormControl fullWidth>
                    <InputLabel id="form-layouts-separator-select-label">
                      HR
                    </InputLabel>
                    <Select
                      label="HeartRate"
                      placeholder="123"
                      defaultValue="bpm"
                      onChange={(e) => handleChange(e, "actual_heartrate_unit")}
                      id="form-layouts-separator-select"
                      labelId="form-layouts-separator-select-label"
                      input={
                        <OutlinedInput
                          label="Language"
                          id="select-multiple-language"
                        />
                      }
                    >
                      <MenuItem value={"bpm"}>BPM</MenuItem>
                      <MenuItem value={"bpm"}>BPM</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>
                <Grid item xs={4} sm={1}></Grid>
                <Grid item xs={4} sm={2}>
                  <FormLabel variant="h6">Pace</FormLabel>
                </Grid>
                <Grid item xs={4} sm={4}>
                  <FormControl fullWidth>
                    <InputLabel id="form-layouts-separator-select-label">
                      Pace
                    </InputLabel>
                    <Select
                      label="Pace"
                      placeholder="Pace"
                      defaultValue="5:46"
                      onChange={(e) => handleChange(e, "planned_pace")}
                      id="form-layouts-separator-select"
                      labelId="form-layouts-separator-select-label"
                      input={
                        <OutlinedInput
                          label="Language"
                          id="select-multiple-language"
                        />
                      }
                    >
                      <MenuItem value={"5:46"}>5:46</MenuItem>
                      <MenuItem value={"5:47"}>5:47</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>
                <Grid item xs={4} sm={4}>
                  <FormControl fullWidth>
                    <InputLabel id="form-layouts-separator-select-label">
                      Pace
                    </InputLabel>
                    <Select
                      label="Pace"
                      placeholder="Pace"
                      defaultValue="5:46"
                      onChange={(e) => handleChange(e, "actual_pace")}
                      id="form-layouts-separator-select"
                      labelId="form-layouts-separator-select-label"
                      input={
                        <OutlinedInput
                          label="Language"
                          id="select-multiple-language"
                        />
                      }
                    >
                      <MenuItem value={"5:46"}>5:46</MenuItem>
                      <MenuItem value={"5:47"}>5:47</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>
                <Grid item xs={0} sm={1}></Grid>
                <Grid item xs={4} sm={2}>
                  <FormLabel variant="h6">
                    Rate of Perceived Exertion(RPE)
                  </FormLabel>
                </Grid>
                <Grid item xs={4} sm={4}>
                  <FormControl fullWidth>
                    <InputLabel id="form-layouts-separator-select-label">
                      RPE
                    </InputLabel>
                    <Select
                      label="RPE"
                      placeholder="RPE"
                      defaultValue={4}
                      onChange={(e) => handleChange(e, "planned_rpe")}
                      id="form-layouts-separator-select"
                      labelId="form-layouts-separator-select-label"
                      input={
                        <OutlinedInput
                          label="Language"
                          id="select-multiple-language"
                        />
                      }
                    >
                      <MenuItem value={4}>4</MenuItem>
                      <MenuItem value={9}>9</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>
                <Grid item xs={4} sm={4}>
                  <FormControl fullWidth>
                    <InputLabel id="form-layouts-separator-select-label">
                      RPE
                    </InputLabel>
                    <Select
                      label="RPE"
                      placeholder="RPE"
                      defaultValue={4}
                      onChange={(e) => handleChange(e, "actual_rpe")}
                      id="form-layouts-separator-select"
                      labelId="form-layouts-separator-select-label"
                      input={
                        <OutlinedInput
                          label="Language"
                          id="select-multiple-language"
                        />
                      }
                    >
                      <MenuItem value={4}>4</MenuItem>
                      <MenuItem value={9}>9</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>{" "}
                <Grid item xs={4} sm={1}></Grid>
                <Grid item xs={5} sm={2}>
                  <FormLabel variant="h6">Power</FormLabel>
                </Grid>
                <Grid item xs={4} sm={2}>
                  <TextField
                    fullWidth
                    label="Power"
                    placeholder="123"
                    onChange={(e) => handleChange(e, "planned_power")}
                  />
                </Grid>
                <Grid item xs={4} sm={2}>
                  <FormControl fullWidth>
                    <InputLabel id="form-layouts-separator-select-label">
                      Power
                    </InputLabel>
                    <Select
                      label="Power"
                      placeholder="123"
                      defaultValue="spm"
                      onChange={(e) => handleChange(e, "planned_power_unit")}
                      id="form-layouts-separator-select"
                      labelId="form-layouts-separator-select-label"
                      input={
                        <OutlinedInput
                          label="Language"
                          id="select-multiple-language"
                        />
                      }
                    >
                      <MenuItem value={"spm"}>SPM</MenuItem>
                      <MenuItem value={"spm"}>SPM</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>
                <Grid item xs={4} sm={2}>
                  <TextField
                    fullWidth
                    label="Power"
                    placeholder="123"
                    onChange={(e) => handleChange(e, "actual_power")}
                  />
                </Grid>
                <Grid item xs={4} sm={2}>
                  <FormControl fullWidth>
                    <InputLabel id="form-layouts-separator-select-label">
                      Power
                    </InputLabel>
                    <Select
                      label="Power"
                      placeholder="123"
                      defaultValue="spm"
                      onChange={(e) => handleChange(e, "actual_power_unit")}
                      id="form-layouts-separator-select"
                      labelId="form-layouts-separator-select-label"
                      input={
                        <OutlinedInput
                          label="Language"
                          id="select-multiple-language"
                        />
                      }
                    >
                      <MenuItem value={"spm"}>SPM</MenuItem>
                      <MenuItem value={"spm"}>SPM</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>
                <Grid item xs={4} sm={1}></Grid>
                <Grid item xs={5} sm={2}>
                  <FormLabel variant="h6">Cadence</FormLabel>
                </Grid>
                <Grid item xs={4} sm={2}>
                  <TextField
                    fullWidth
                    label="Cadence"
                    placeholder="123"
                    onChange={(e) => handleChange(e, "planned_cadence")}
                  />
                </Grid>
                <Grid item xs={4} sm={2}>
                  <FormControl fullWidth>
                    <InputLabel id="form-layouts-separator-select-label">
                      Cadence
                    </InputLabel>
                    <Select
                      label="Cadence"
                      placeholder="123"
                      defaultValue="spm"
                      onChange={(e) => handleChange(e, "planned_cadence_unit")}
                      id="form-layouts-separator-select"
                      labelId="form-layouts-separator-select-label"
                      input={
                        <OutlinedInput
                          label="Language"
                          id="select-multiple-language"
                        />
                      }
                    >
                      <MenuItem value={"spm"}>SPM</MenuItem>
                      <MenuItem value={"spm"}>SPM</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>
                <Grid item xs={4} sm={2}>
                  <TextField
                    fullWidth
                    label="Cadence"
                    placeholder="123"
                    onChange={(e) => handleChange(e, "actual_cadence")}
                  />
                </Grid>
                <Grid item xs={4} sm={2}>
                  <FormControl fullWidth>
                    <InputLabel id="form-layouts-separator-select-label">
                      Cadence
                    </InputLabel>
                    <Select
                      label="Cadence"
                      placeholder="123"
                      defaultValue="spm"
                      onChange={(e) => handleChange(e, "actual_cadence_unit")}
                      id="form-layouts-separator-select"
                      labelId="form-layouts-separator-select-label"
                      input={
                        <OutlinedInput
                          label="Language"
                          id="select-multiple-language"
                        />
                      }
                    >
                      <MenuItem value={"spm"}>SPM</MenuItem>
                      <MenuItem value={"spm"}>SPM</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>
                <Grid item xs={12} sm={11}>
                  <Typography variant="h6">
                    How did you Post workout feel after the activity
                  </Typography>
                </Grid>
                <Grid container xs={12} sm={12}>
                  <Grid item xs={2} sm={1}>
                    😊
                  </Grid>
                  <Grid item xs={2} sm={1}>
                    😃
                  </Grid>
                  <Grid item xs={2} sm={1}>
                    👍
                  </Grid>
                  <Grid item xs={2} sm={1}>
                    👏
                  </Grid>
                  <Grid item xs={2} sm={1}>
                    🥳
                  </Grid>
                </Grid>
                <Grid item xs={6} sm={6}>
                  <Checkbox defaultChecked name="group_workout" />
                  Group Workout
                </Grid>
                <Grid item xs={6} sm={6}>
                  <Button
                    type="submit"
                    variant="contained"
                    size="large"
                    style={{ backgroundColor: "#2563EB" }}
                    fullWidth
                  >
                    Save
                  </Button>
                </Grid>
              </Grid>

              <Grid
                container
                xs={12}
                sm={12}
                spacing={1}
                style={{
                  margin: "2% 0% 0% 0%",
                  border: "2px solid black",
                  padding: "2%",
                }}
              >
                <Grid item xs={12} sm={12}>
                  <TextField
                    fullWidth
                    multiline
                    minRows={5}
                    label="Comments"
                    placeholder="Write Here"
                    onChange={(e) => handleChange(e, "comment")}
                  />
                </Grid>
                <Grid item xs={12} sm={12}>
                  How important is this to you?
                </Grid>
                <Grid container xs={12} sm={12}>
                  <Grid item xs={4} sm={4}>
                    27
                  </Grid>
                  <Grid item xs={4} sm={4}>
                    27
                  </Grid>
                  <Grid item xs={4} sm={4}>
                    27
                  </Grid>
                </Grid>
                <Grid item xs={12} sm={12}>
                  <Button
                    type="submit"
                    variant="contained"
                    size="large"
                    style={{ backgroundColor: "#2563EB" }}
                    fullWidth
                  >
                    Submit
                  </Button>
                </Grid>
              </Grid>
            </Grid>
          </CardContent>
        </form>
      </Drawer>
    </div>
  );
};

export default WorkoutDrawer;
