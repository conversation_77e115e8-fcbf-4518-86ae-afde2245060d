import { useState, useEffect } from "react";
import { But<PERSON> } from "../ui/button";
import { Input } from "../ui/input";
import { Label } from "../ui/label";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "../ui/select";
import { <PERSON>alog, DialogContent, DialogHeader, DialogTitle } from "../ui/dialog";
import {
	createPowerdata,
	updatePowerdata,
	getAllActivityData,
	getZonesDataById,
} from "../../API/api-endpoint";
import Swal from "sweetalert2";

export const ZonesPowerDialog = ({ open, onClose, onSuccess, editingItem }) => {
	const [formData, setFormData] = useState({
		activity_id: "",
		name: "",
		zone: "",
		offset_start: "",
		offset_end: "",
	});
	const [isLoading, setIsLoading] = useState(false);
	const [activityList, setActivityList] = useState([]);
	const [zoneList, setZoneList] = useState([]);

	useEffect(() => {
		const fetchDropdownData = async () => {
			try {
				const activityResponse = await getAllActivityData();
				setActivityList(activityResponse?.rows || []);
			} catch (error) {
				console.error("Error fetching dropdown data:", error);
			}
		};

		if (open) {
			fetchDropdownData();
		}
	}, [open]);

	useEffect(() => {
		const fetchZones = async () => {
			if (formData.activity_id) {
				try {
					const response = await getZonesDataById(
						formData.activity_id
					);
					setZoneList(response?.uniquesnamesonly || []);
				} catch (error) {
					console.error("Error fetching zones:", error);
					setZoneList([]);
				}
			} else {
				setZoneList([]);
			}
		};

		fetchZones();
	}, [formData.activity_id]);

	useEffect(() => {
		if (open) {
			if (editingItem?.id) {
				const editData = {
					activity_id: editingItem.activity_id?.toString() || "",
					name: editingItem.name || "",
					zone: editingItem.zone || "",
					offset_start: editingItem.offset_start?.toString() || "",
					offset_end: editingItem.offset_end?.toString() || "",
				};
				setFormData(editData);

				if (editingItem.activity_id) {
					const fetchZonesForEdit = async () => {
						try {
							const response = await getZonesDataById(
								editingItem.activity_id
							);
							setZoneList(response?.uniquesnamesonly || []);
						} catch (error) {
							console.error(
								"Error fetching zones for edit:",
								error
							);
							setZoneList([]);
						}
					};
					fetchZonesForEdit();
				}
			} else {
				const newData = {
					activity_id: "",
					name: "",
					zone: "",
					offset_start: "",
					offset_end: "",
				};
				setFormData(newData);
				setZoneList([]);
			}
		}
	}, [open, editingItem]);

	const handleInputChange = (field, value) => {
		setFormData((prev) => ({
			...prev,
			[field]: value,
		}));
	};

	const handleSubmit = async (e) => {
		e.preventDefault();

		if (!formData.activity_id) {
			Swal.fire({
				title: "Error",
				text: "Activity is required",
				icon: "error",
				timer: 1800,
				showConfirmButton: false,
			});
			return;
		}

		if (!formData.name.trim()) {
			Swal.fire({
				title: "Error",
				text: "Name is required",
				icon: "error",
				timer: 1800,
				showConfirmButton: false,
			});
			return;
		}

		if (!formData.zone.trim()) {
			Swal.fire({
				title: "Error",
				text: "Zone is required",
				icon: "error",
				timer: 1800,
				showConfirmButton: false,
			});
			return;
		}

		if (!formData.offset_start.trim()) {
			Swal.fire({
				title: "Error",
				text: "Offset start is required",
				icon: "error",
				timer: 1800,
				showConfirmButton: false,
			});
			return;
		}

		if (!formData.offset_end.trim()) {
			Swal.fire({
				title: "Error",
				text: "Offset end is required",
				icon: "error",
				timer: 1800,
				showConfirmButton: false,
			});
			return;
		}

		try {
			setIsLoading(true);

			const apiData = {
				activity_id: parseInt(formData.activity_id),
				name: formData.name.trim(),
				zone: formData.zone.trim(),
				offset_start: parseFloat(formData.offset_start),
				offset_end: parseFloat(formData.offset_end),
			};

			let response;
			if (editingItem?.id) {
				apiData.id = editingItem.id;
				response = await updatePowerdata(apiData);
			} else {
				response = await createPowerdata(apiData);
			}

			if (response?.status) {
				Swal.fire({
					title: "Success",
					text:
						response.message ||
						`Zones power ${
							editingItem?.id ? "updated" : "created"
						} successfully`,
					icon: "success",
					timer: 2000,
					showConfirmButton: false,
				});
				onSuccess();
			} else {
				Swal.fire({
					title: "Error",
					text: response?.message || "Failed to save zones power",
					icon: "error",
					timer: 3000,
					showConfirmButton: false,
				});
			}
		} catch (error) {
			console.error("Error saving zones power:", error);
			Swal.fire({
				title: "Error",
				text: "An error occurred while saving the zones power",
				icon: "error",
				timer: 3000,
				showConfirmButton: false,
			});
		} finally {
			setIsLoading(false);
		}
	};

	return (
		<Dialog open={open} onOpenChange={onClose}>
			<DialogContent className='sm:max-w-2xl bg-white max-h-[90vh] overflow-y-auto'>
				<DialogHeader>
					<DialogTitle className='text-lg font-semibold text-gray-900'>
						{editingItem?.id
							? "Edit Zones Power"
							: "Create Zones Power"}
					</DialogTitle>
				</DialogHeader>

				<form onSubmit={handleSubmit} className='space-y-4'>
					<div className='grid gap-4'>
						<div className='grid grid-cols-1 sm:grid-cols-2 gap-4'>
							<div className='space-y-2'>
								<Label
									htmlFor='activity_id'
									className='text-sm font-semibold'
								>
									Activity{" "}
									<span className='text-red-500'>*</span>
								</Label>
								<Select
									value={formData.activity_id}
									onValueChange={(value) =>
										handleInputChange("activity_id", value)
									}
									disabled={isLoading}
								>
									<SelectTrigger className='w-full text-sm'>
										<SelectValue placeholder='Select Activity' />
									</SelectTrigger>
									<SelectContent className='bg-white'>
										{activityList.map((activity) => (
											<SelectItem
												key={activity.id}
												value={activity.id.toString()}
											>
												{activity.activity_name}
											</SelectItem>
										))}
									</SelectContent>
								</Select>
							</div>

							<div className='space-y-2'>
								<Label
									htmlFor='name'
									className='text-sm font-semibold'
								>
									Name <span className='text-red-500'>*</span>
								</Label>
								<Input
									id='name'
									className='w-full text-sm'
									value={formData.name}
									onChange={(e) =>
										handleInputChange(
											"name",
											e.target.value
										)
									}
									placeholder='Enter name'
									disabled={isLoading}
									required
								/>
							</div>
						</div>

						<div className='space-y-2'>
							<Label
								htmlFor='zone'
								className='text-sm font-semibold'
							>
								Zone <span className='text-red-500'>*</span>
							</Label>
							<Select
								value={formData.zone}
								onValueChange={(value) =>
									handleInputChange("zone", value)
								}
								disabled={isLoading || !formData.activity_id}
							>
								<SelectTrigger className='w-full text-sm'>
									<SelectValue
										placeholder={
											formData.activity_id
												? "Select Zone"
												: "Select Activity First"
										}
									/>
								</SelectTrigger>
								<SelectContent className='bg-white'>
									{zoneList.map((zone, index) => (
										<SelectItem key={index} value={zone}>
											{zone}
										</SelectItem>
									))}
								</SelectContent>
							</Select>
						</div>

						<div className='grid grid-cols-1 sm:grid-cols-2 gap-4'>
							<div className='space-y-2'>
								<Label
									htmlFor='offset_start'
									className='text-sm font-semibold'
								>
									Offset Start{" "}
									<span className='text-red-500'>*</span>
								</Label>
								<Input
									id='offset_start'
									type='number'
									step='0.01'
									className='w-full text-sm'
									value={formData.offset_start}
									onChange={(e) =>
										handleInputChange(
											"offset_start",
											e.target.value
										)
									}
									placeholder='Enter offset start'
									disabled={isLoading}
									required
								/>
							</div>

							<div className='space-y-2'>
								<Label
									htmlFor='offset_end'
									className='text-sm font-semibold'
								>
									Offset End{" "}
									<span className='text-red-500'>*</span>
								</Label>
								<Input
									id='offset_end'
									type='number'
									step='0.01'
									className='w-full text-sm'
									value={formData.offset_end}
									onChange={(e) =>
										handleInputChange(
											"offset_end",
											e.target.value
										)
									}
									placeholder='Enter offset end'
									disabled={isLoading}
									required
								/>
							</div>
						</div>
					</div>

					<div className='flex justify-end gap-3 pt-4'>
						<Button
							type='button'
							variant='outline'
							onClick={onClose}
							disabled={isLoading}
						>
							Cancel
						</Button>
						<Button
							type='submit'
							className='bg-orange-600 hover:bg-orange-700 text-white'
							disabled={isLoading}
						>
							{isLoading
								? "Saving..."
								: editingItem?.id
								? "Update Zone"
								: "Create Zone"}
						</Button>
					</div>
				</form>
			</DialogContent>
		</Dialog>
	);
};
