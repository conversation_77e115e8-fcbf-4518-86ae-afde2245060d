import { useState, useEffect } from "react";
import { But<PERSON> } from "../ui/button";
import { Input } from "../ui/input";
import { Label } from "../ui/label";
import {
	Dialog,
	DialogContent,
	DialogHeader,
	DialogTitle,
	DialogFooter,
} from "../ui/dialog";
import {
	createMultipleRaceCalculation,
	updateMultipleRaceCalculation,
} from "../../API/api-endpoint";
import Swal from "sweetalert2";

export function MultipleRaceCalculatorDialog({
	open,
	onClose,
	onSave,
	editingRaceCalculation,
}) {
	const [formData, setFormData] = useState({
		goal_name: "",
		running_quota: "",
		running_time: "",
		cycling_quota: "",
		cycling_time: "",
		swimming_quota: "",
		swimming_time: "",
	});
	const [isLoading, setIsLoading] = useState(false);

	// Reset form when dialog opens/closes or when editing different race calculation
	useEffect(() => {
		console.log("Dialog opened:", open, "Editing:", editingRaceCalculation);
		if (open) {
			if (editingRaceCalculation) {
				// Editing existing race calculation
				const editData = {
					goal_name: editingRaceCalculation.goal_name || "",
					running_quota: editingRaceCalculation.running_quota || "",
					running_time: editingRaceCalculation.running_time || "",
					cycling_quota: editingRaceCalculation.cycling_quota || "",
					cycling_time: editingRaceCalculation.cycling_time || "",
					swimming_quota: editingRaceCalculation.swimming_quota || "",
					swimming_time: editingRaceCalculation.swimming_time || "",
				};
				console.log("Setting edit data:", editData);
				setFormData(editData);
			} else {
				// Creating new race calculation
				const newData = {
					goal_name: "",
					running_quota: "",
					running_time: "",
					cycling_quota: "",
					cycling_time: "",
					swimming_quota: "",
					swimming_time: "",
				};
				console.log("Setting new data:", newData);
				setFormData(newData);
			}
		}
	}, [open, editingRaceCalculation]);

	// Handle input changes
	const handleInputChange = (field, value) => {
		console.log(`Updating ${field} with value:`, value);
		setFormData((prev) => {
			const newData = {
				...prev,
				[field]: value,
			};
			console.log("New form data:", newData);
			return newData;
		});
	};

	// Handle form submission
	const handleSubmit = async (e) => {
		e.preventDefault();

		// Validation
		if (!formData.goal_name.trim()) {
			Swal.fire({
				title: "Error",
				text: "Goal name is required",
				icon: "error",
				timer: 1800,
				showConfirmButton: false,
			});
			return;
		}

		if (!formData.running_quota || formData.running_quota === "") {
			Swal.fire({
				title: "Error",
				text: "Running distance is required",
				icon: "error",
				timer: 1800,
				showConfirmButton: false,
			});
			return;
		}

		if (!formData.running_time || formData.running_time === "") {
			Swal.fire({
				title: "Error",
				text: "Running time is required",
				icon: "error",
				timer: 1800,
				showConfirmButton: false,
			});
			return;
		}

		if (!formData.cycling_quota || formData.cycling_quota === "") {
			Swal.fire({
				title: "Error",
				text: "Cycling distance is required",
				icon: "error",
				timer: 1800,
				showConfirmButton: false,
			});
			return;
		}

		if (!formData.cycling_time || formData.cycling_time === "") {
			Swal.fire({
				title: "Error",
				text: "Cycling time is required",
				icon: "error",
				timer: 1800,
				showConfirmButton: false,
			});
			return;
		}

		if (!formData.swimming_quota || formData.swimming_quota === "") {
			Swal.fire({
				title: "Error",
				text: "Swimming distance is required",
				icon: "error",
				timer: 1800,
				showConfirmButton: false,
			});
			return;
		}

		if (!formData.swimming_time || formData.swimming_time === "") {
			Swal.fire({
				title: "Error",
				text: "Swimming time is required",
				icon: "error",
				timer: 1800,
				showConfirmButton: false,
			});
			return;
		}

		setIsLoading(true);

		try {
			let response;
			const submitData = { ...formData };

			if (editingRaceCalculation?.id) {
				// Update existing race calculation
				submitData.id = editingRaceCalculation.id;
				response = await updateMultipleRaceCalculation(submitData);
			} else {
				// Create new race calculation
				response = await createMultipleRaceCalculation(submitData);
			}

			if (response?.status) {
				Swal.fire({
					title: "Success",
					text:
						response.message ||
						`Multiple race calculation ${
							editingRaceCalculation?.id ? "updated" : "created"
						} successfully`,
					icon: "success",
					timer: 1800,
					showConfirmButton: false,
				});
				onSave();
				onClose();
			} else {
				Swal.fire({
					title: "Error",
					text:
						response?.message ||
						"Failed to save multiple race calculation",
					icon: "error",
				});
			}
		} catch (error) {
			console.error("Error saving multiple race calculation:", error);
			Swal.fire({
				title: "Error",
				text: "Failed to save multiple race calculation",
				icon: "error",
			});
		} finally {
			setIsLoading(false);
		}
	};

	const handleClose = () => {
		if (!isLoading) {
			onClose();
		}
	};

	return (
		<Dialog open={open} onOpenChange={handleClose}>
			<DialogContent className='sm:max-w-[600px] bg-white max-h-[90vh] overflow-y-auto'>
				<DialogHeader>
					<DialogTitle className='text-xl font-semibold text-gray-900'>
						{editingRaceCalculation?.id ? "Edit" : "Create"}{" "}
						Multiple Race Calculation
					</DialogTitle>
				</DialogHeader>

				<form onSubmit={handleSubmit} className='space-y-6'>
					<div className='space-y-1'>
						<Label
							htmlFor='goal_name'
							className='text-sm font-semibold'
						>
							Goal Name
						</Label>
						<Input
							id='goal_name'
							type='text'
							className='w-full text-sm'
							value={formData.goal_name}
							onChange={(e) =>
								handleInputChange("goal_name", e.target.value)
							}
							placeholder='Enter goal name'
							disabled={isLoading}
							required
						/>
					</div>

					<div className='space-y-2'>
						<h3 className='text-lg font-semibold text-gray-800 border-b pb-1'>
							Running
						</h3>
						<div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
							<div className='space-y-1'>
								<Label
									htmlFor='running_quota'
									className='text-sm font-semibold'
								>
									Distance{" "}
								</Label>
								<Input
									id='running_quota'
									type='number'
									className='w-full text-sm'
									value={formData.running_quota || ""}
									onChange={(e) =>
										handleInputChange(
											"running_quota",
											e.target.value
										)
									}
									placeholder='Enter running distance'
									disabled={isLoading}
									required
								/>
							</div>
							<div className='space-y-2'>
								<Label
									htmlFor='running_time'
									className='text-sm font-semibold'
								>
									Time (minutes){" "}
								</Label>
								<Input
									id='running_time'
									type='number'
									className='w-full text-sm'
									value={formData.running_time || ""}
									onChange={(e) =>
										handleInputChange(
											"running_time",
											e.target.value
										)
									}
									placeholder='Enter running time'
									disabled={isLoading}
									required
								/>
							</div>
						</div>
					</div>

					{/* Cycling Section */}
					<div className='space-y-2'>
						<h3 className='text-lg font-semibold text-gray-800 border-b pb-1'>
							Cycling
						</h3>
						<div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
							<div className='space-y-1'>
								<Label
									htmlFor='cycling_quota'
									className='text-sm font-semibold'
								>
									Distance{" "}
								</Label>
								<Input
									id='cycling_quota'
									type='number'
									className='w-full text-sm'
									value={formData.cycling_quota || ""}
									onChange={(e) =>
										handleInputChange(
											"cycling_quota",
											e.target.value
										)
									}
									placeholder='Enter cycling distance'
									disabled={isLoading}
									required
								/>
							</div>
							<div className='space-y-1'>
								<Label
									htmlFor='cycling_time'
									className='text-sm font-semibold'
								>
									Time (minutes){" "}
								</Label>
								<Input
									id='cycling_time'
									type='number'
									className='w-full text-sm'
									value={formData.cycling_time || ""}
									onChange={(e) =>
										handleInputChange(
											"cycling_time",
											e.target.value
										)
									}
									placeholder='Enter cycling time'
									disabled={isLoading}
									required
								/>
							</div>
						</div>
					</div>

					{/* Swimming Section */}
					<div className='space-y-2'>
						<h3 className='text-lg font-semibold text-gray-800 border-b pb-1'>
							Swimming
						</h3>
						<div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
							<div className='space-y-1'>
								<Label
									htmlFor='swimming_quota'
									className='text-sm font-semibold'
								>
									Distance{" "}
								</Label>
								<Input
									id='swimming_quota'
									type='number'
									className='w-full text-sm'
									value={formData.swimming_quota || ""}
									onChange={(e) =>
										handleInputChange(
											"swimming_quota",
											e.target.value
										)
									}
									placeholder='Enter swimming distance'
									disabled={isLoading}
									required
								/>
							</div>
							<div className='space-y-1'>
								<Label
									htmlFor='swimming_time'
									className='text-sm font-semibold'
								>
									Time (minutes){" "}
								</Label>
								<Input
									id='swimming_time'
									type='number'
									className='w-full text-sm'
									value={formData.swimming_time || ""}
									onChange={(e) =>
										handleInputChange(
											"swimming_time",
											e.target.value
										)
									}
									placeholder='Enter swimming time'
									disabled={isLoading}
									required
								/>
							</div>
						</div>
					</div>

					<DialogFooter className='flex gap-3'>
						<Button
							type='button'
							variant='outline'
							onClick={handleClose}
							disabled={isLoading}
						>
							Cancel
						</Button>
						<Button
							type='submit'
							className='bg-orange-600 hover:bg-orange-700 text-white'
							disabled={isLoading}
						>
							{isLoading
								? "Saving..."
								: editingRaceCalculation?.id
								? "Update"
								: "Create"}
						</Button>
					</DialogFooter>
				</form>
			</DialogContent>
		</Dialog>
	);
}
