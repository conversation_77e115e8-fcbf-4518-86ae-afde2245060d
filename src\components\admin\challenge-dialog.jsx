import { useState, useEffect } from "react";
import { But<PERSON> } from "../ui/button";
import { Input } from "../ui/input";
import { Label } from "../ui/label";
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from "../ui/dialog";
import {
	createChallengedata,
	updateChallengedata,
} from "../../API/api-endpoint";
import Swal from "sweetalert2";

export const ChallengeDialog = ({ open, onClose, onSuccess, editingItem }) => {
	const [formData, setFormData] = useState({
		level: "",
	});
	const [isLoading, setIsLoading] = useState(false);

	// Reset form when dialog opens/closes or when editing different item
	useEffect(() => {
		if (open) {
			if (editingItem?.id) {
				// Editing existing challenge
				const editData = {
					level: editingItem.level || "",
				};
				setFormData(editData);
			} else {
				// Creating new challenge
				const newData = {
					level: "",
				};
				setFormData(newData);
			}
		}
	}, [open, editingItem]);

	const handleInputChange = (field, value) => {
		setFormData((prev) => ({
			...prev,
			[field]: value,
		}));
	};

	const handleSubmit = async (e) => {
		e.preventDefault();

		// Validation
		if (!formData.level.trim()) {
			Swal.fire({
				title: "Error",
				text: "Level name is required",
				icon: "error",
				timer: 1800,
				showConfirmButton: false,
			});
			return;
		}

		try {
			setIsLoading(true);

			// Prepare data for API
			const apiData = {
				level: formData.level.trim(),
			};

			let response;
			if (editingItem?.id) {
				// Add ID for update
				apiData.id = editingItem.id;
				response = await updateChallengedata(apiData);
			} else {
				response = await createChallengedata(apiData);
			}

			console.log("API response:", response);

			if (response?.status) {
				Swal.fire({
					title: "Success",
					text: response.message || `Challenge ${editingItem?.id ? 'updated' : 'created'} successfully`,
					icon: "success",
					timer: 2000,
					showConfirmButton: false,
				});
				onSuccess();
			} else {
				Swal.fire({
					title: "Error",
					text: response?.message || "Failed to save challenge",
					icon: "error",
					timer: 3000,
					showConfirmButton: false,
				});
			}
		} catch (error) {
			console.error("Error saving challenge:", error);
			Swal.fire({
				title: "Error",
				text: "An error occurred while saving the challenge",
				icon: "error",
				timer: 3000,
				showConfirmButton: false,
			});
		} finally {
			setIsLoading(false);
		}
	};

	return (
		<Dialog open={open} onOpenChange={onClose}>
			<DialogContent className='sm:max-w-md bg-white'>
				<DialogHeader>
					<DialogTitle className='text-lg font-semibold text-gray-900'>
						{editingItem?.id ? "Edit Challenge" : "Create Challenge"}
					</DialogTitle>
				</DialogHeader>

				<form onSubmit={handleSubmit} className='space-y-4'>
					<div className='grid gap-4'>
						<div className='space-y-2'>
							<Label
								htmlFor='level'
								className='text-sm font-semibold'
							>
								Level Name <span className='text-red-500'>*</span>
							</Label>
							<Input
								id='level'
								type='text'
								className='w-full text-sm'
								value={formData.level}
								onChange={(e) =>
									handleInputChange("level", e.target.value)
								}
								placeholder='Enter level name'
								disabled={isLoading}
								required
							/>
						</div>
					</div>

					<div className='flex justify-end gap-3 pt-4'>
						<Button
							type='button'
							variant='outline'
							onClick={onClose}
							disabled={isLoading}
						>
							Cancel
						</Button>
						<Button
							type='submit'
							className='bg-orange-600 hover:bg-orange-700 text-white'
							disabled={isLoading}
						>
							{isLoading
								? "Saving..."
								: editingItem?.id
								? "Update Challenge"
								: "Create Challenge"}
						</Button>
					</div>
				</form>
			</DialogContent>
		</Dialog>
	);
};
