import { useState, useEffect, useRef } from "react";
import { Button } from "../ui/button";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "../ui/dialog";
import {
	createChallengeActivity,
	updateChallengeActivity,
	getAllActivities,
	getAllTrainingBlock,
	getAllChallenges,
	getAllRenderChallengesData,
} from "../../API/api-endpoint";
import {
	TextField,
	FormLabel,
	Grid,
	MenuItem,
	Checkbox,
	FormControlLabel,
} from "@mui/material";
import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";
import ReactSelect from "react-select";
import RichTextEditor from "../EditorFile";
import Swal from "sweetalert2";

export const ChallengeDialog = ({ open, onClose, onSuccess, editingItem }) => {
	const [formData, setFormData] = useState({
		challengeName: "",
		challengeDescription: "",
		challengeActivity: "",
		challengeActivityGroup: "",
		challengeActivityTrack: "",
		challengeDuration: "",
		durationunit: "days",
		challengeStartDate: null,
		challengeEndDate: null,
		challengeLevel: "",
		challengePoints: "",
		trainingBlock: "",
		challengePrerequisites: [],
		challangeTarget: [],
		badge: null,
	});
	const [isLoading, setIsLoading] = useState(false);
	const [activities, setActivities] = useState([]);
	const [trainingBlocks, setTrainingBlocks] = useState([]);
	const [challenges, setChallenges] = useState([]);
	const [allChallengesData, setAllChallengesData] = useState(null);
	const [fetchChallengeTarget, setFetchChallengeTarget] = useState([]);
	const [initial, setInitial] = useState("");
	const fileInputRef = useRef(null);

	useEffect(() => {
		const fetchData = async () => {
			try {
				const [
					activitiesRes,
					trainingBlocksRes,
					challengesRes,
					allChallengesDataRes,
				] = await Promise.all([
					getAllActivities(),
					getAllTrainingBlock(),
					getAllChallenges(),
					getAllRenderChallengesData(),
				]);
				setActivities(activitiesRes || []);
				setTrainingBlocks(trainingBlocksRes || []);
				setChallenges(challengesRes || []);
				setAllChallengesData(allChallengesDataRes || null);
			} catch (error) {
				console.error("Error fetching data:", error);
			}
		};

		if (open) {
			fetchData();
		}
	}, [open]);

	useEffect(() => {
		if (open) {
			if (editingItem?.id) {
				const editData = {
					challengeName: editingItem.challengeName || "",
					challengeDescription:
						editingItem.challengeDescription || "",
					challengeActivity: editingItem.challengeActivity
						? String(editingItem.challengeActivity)
						: "",
					challengeActivityGroup:
						editingItem.challengeActivityGroup || "",
					challengeActivityTrack:
						editingItem.challengeActivityTrack || "",
					challengeDuration: String(
						editingItem.challengeDuration || ""
					),
					durationunit: editingItem.durationunit || "days",
					challengeStartDate: editingItem.challengeStartDate
						? new Date(editingItem.challengeStartDate)
						: null,
					challengeEndDate: editingItem.challengeEndDate
						? new Date(editingItem.challengeEndDate)
						: null,
					challengeLevel: editingItem.challengeLevel || "",
					challengePoints: String(editingItem.challengePoints || ""),
					trainingBlock: editingItem.trainingBlock
						? String(editingItem.trainingBlock)
						: "",
					challengePrerequisites:
						editingItem.challengePrerequisites || [],
					challangeTarget: editingItem.challangeTarget || [],
					badge: null,
				};
				setFormData(editData);
			} else {
				const newData = {
					challengeName: "",
					challengeDescription: "",
					challengeActivity: "",
					challengeActivityGroup: "",
					challengeActivityTrack: "",
					challengeDuration: "",
					durationunit: "days",
					challengeStartDate: "",
					challengeEndDate: "",
					challengeLevel: "",
					challengePoints: "",
					trainingBlock: "",
					challengePrerequisites: [],
					challangeTarget: [],
					badge: null,
				};
				setFormData(newData);
			}
		}
	}, [open, editingItem]);

	const handleInputChange = (field, value) => {
		setFormData((prev) => ({
			...prev,
			[field]: value,
		}));
	};

	const handleFileUpload = (event) => {
		const file = event.target.files[0];
		if (!file) return;

		const acceptedFileTypes = [
			".jpg",
			".JPG",
			".jpeg",
			".JPEG",
			".png",
			".PNG",
			".gif",
			".GIF",
		];
		const currentFileType = "." + file.type.split("/")[1];
		const maxSizeInBytes = 5 * 1024 * 1024;

		if (file.size > maxSizeInBytes) {
			Swal.fire({
				title: "Error",
				text: "File size exceeds the limit of 5MB.",
				icon: "error",
				timer: 2000,
				showConfirmButton: false,
			});
			event.target.value = null;
		} else if (!acceptedFileTypes.includes(currentFileType)) {
			Swal.fire({
				title: "Error",
				text: "Accepted file types are .jpg, .jpeg, .png, .gif",
				icon: "error",
				timer: 2000,
				showConfirmButton: false,
			});
			event.target.value = null;
		} else {
			setFormData((prev) => ({ ...prev, badge: file }));
		}
	};

	const handleSubmit = async (e) => {
		e.preventDefault();

		if (!formData.challengeName.trim()) {
			Swal.fire({
				title: "Error",
				text: "Challenge name is required",
				icon: "error",
				timer: 1800,
				showConfirmButton: false,
			});
			return;
		}

		if (!formData.challengeActivity) {
			Swal.fire({
				title: "Error",
				text: "Challenge activity is required",
				icon: "error",
				timer: 1800,
				showConfirmButton: false,
			});
			return;
		}

		if (!formData.challengeDuration.trim()) {
			Swal.fire({
				title: "Error",
				text: "Challenge duration is required",
				icon: "error",
				timer: 1800,
				showConfirmButton: false,
			});
			return;
		}

		try {
			setIsLoading(true);

			const apiData = new FormData();

			if (formData.badge) {
				apiData.append("badge", formData.badge);
			}

			if (
				formData.challangeTarget &&
				formData.challangeTarget.length > 0
			) {
				formData.challangeTarget.forEach(
					({ activity_id, quota }, index) => {
						apiData.append(
							`challangeTarget[${index}][activity_id]`,
							activity_id
						);
						apiData.append(
							`challangeTarget[${index}][quota]`,
							quota
						);
					}
				);
			}

			if (
				formData.challengePrerequisites &&
				formData.challengePrerequisites.length > 0
			) {
				formData.challengePrerequisites.forEach((id, index) => {
					apiData.append(`challengePrerequisites[${index}]`, id);
				});
			} else {
				apiData.append("challengePrerequisites", "null");
			}

			apiData.append("challengeName", formData.challengeName.trim());
			apiData.append("challengeActivity", formData.challengeActivity);
			apiData.append(
				"challengeActivityGroup",
				formData.challengeActivityGroup
			);
			apiData.append(
				"challengeActivityTrack",
				formData.challengeActivityTrack
			);
			apiData.append("durationunit", formData.durationunit);
			apiData.append(
				"challengeDescription",
				formData.challengeDescription.trim()
			);
			apiData.append(
				"challengeDuration",
				formData.challengeDuration.trim()
			);
			apiData.append(
				"challengeStartDate",
				formData.challengeStartDate
					? formData.challengeStartDate.toISOString().split("T")[0]
					: ""
			);
			apiData.append(
				"challengeEndDate",
				formData.challengeEndDate
					? formData.challengeEndDate.toISOString().split("T")[0]
					: ""
			);
			apiData.append("challengeLevel", formData.challengeLevel);
			apiData.append("challengePoints", formData.challengePoints.trim());
			apiData.append("trainingBlock", formData.trainingBlock);

			if (editingItem?.id) {
				apiData.append("id", editingItem.id);
			}

			let response;
			if (editingItem?.id) {
				response = await updateChallengeActivity(apiData);
			} else {
				response = await createChallengeActivity(apiData);
			}

			if (response?.status) {
				Swal.fire({
					title: "Success",
					text:
						response.message ||
						`Challenge ${
							editingItem?.id ? "updated" : "created"
						} successfully`,
					icon: "success",
					timer: 2000,
					showConfirmButton: false,
				});
				onSuccess();
			} else {
				Swal.fire({
					title: "Error",
					text: response?.message || "Failed to save challenge",
					icon: "error",
					timer: 3000,
					showConfirmButton: false,
				});
			}
		} catch (error) {
			console.error("Error saving challenge:", error);
			Swal.fire({
				title: "Error",
				text: "An error occurred while saving the challenge",
				icon: "error",
				timer: 3000,
				showConfirmButton: false,
			});
		} finally {
			setIsLoading(false);
		}
	};

	return (
		<Dialog open={open} onOpenChange={onClose}>
			<DialogContent className='sm:max-w-6xl bg-white max-h-[90vh] overflow-y-auto'>
				<DialogHeader>
					<DialogTitle className='text-lg font-semibold text-gray-900'>
						{editingItem?.id
							? "Edit Challenge"
							: "Create Challenge"}
					</DialogTitle>
				</DialogHeader>

				<form onSubmit={handleSubmit} className='space-y-4'>
					<Grid container spacing={2}>
						{/* Challenge Name */}
						<Grid item xs={12} sm={6}>
							<FormLabel>
								Challenge Name{" "}
								<span className='text-red-500'>*</span>
							</FormLabel>
							<TextField
								fullWidth
								placeholder='Challenge Name'
								size='small'
								type='text'
								name='challengeName'
								value={formData.challengeName}
								onChange={(e) =>
									handleInputChange(
										"challengeName",
										e.target.value
									)
								}
								disabled={isLoading}
							/>
						</Grid>

						{/* Challenge Badge */}
						<Grid item xs={12} sm={6}>
							<FormLabel>Challenge Badge:</FormLabel>
							<input
								ref={fileInputRef}
								style={{
									width: "100%",
									border: "1px solid #c2bbbb",
									borderRadius: "5px",
									minHeight: "40px",
									padding: "5px",
								}}
								type='file'
								accept='.jpg,.JPG,.jpeg,.JPEG,.png,.PNG,.gif,.GIF,.gfif'
								onChange={handleFileUpload}
								disabled={isLoading}
							/>
							<p
								style={{
									fontSize: "0.8rem",
									color: "gray",
									marginTop: "5px",
								}}
							>
								File size should be less than 5MB
							</p>
						</Grid>

						{/* Challenge Activity */}
						<Grid item xs={12} sm={6}>
							<FormLabel>
								Challenge Activity{" "}
								<span className='text-red-500'>*</span>
							</FormLabel>
							<TextField
								fullWidth
								size='small'
								select
								name='challengeActivity'
								value={formData.challengeActivity}
								onChange={(event) => {
									const value = event.target.value;
									handleInputChange(
										"challengeActivity",
										value
									);

									// Handle special activity logic (Triathlon/Duathlon)
									let updatedChallengeTarget = [
										{ activity_id: value, quota: "" },
									];

									if (value === "6") {
										// Triathlon
										const triathlonActivities = [1, 2, 3]; // Running, Cycling, Swimming
										updatedChallengeTarget =
											triathlonActivities.map((id) => ({
												activity_id: id,
												quota: "",
											}));
										setFetchChallengeTarget(
											activities.filter((act) =>
												[1, 2, 3].includes(act.id)
											)
										);
									} else if (value === "5") {
										// Duathlon
										const duathlonActivities = [1, 2]; // Running, Cycling
										updatedChallengeTarget =
											duathlonActivities.map((id) => ({
												activity_id: id,
												quota: "",
											}));
										setFetchChallengeTarget(
											activities.filter((act) =>
												[1, 2].includes(act.id)
											)
										);
									} else {
										setFetchChallengeTarget([]);
									}

									handleInputChange(
										"challangeTarget",
										updatedChallengeTarget
									);
								}}
								disabled={isLoading}
							>
								<MenuItem value='' disabled>
									Select Activity
								</MenuItem>
								{Array.isArray(activities) &&
									activities.map((activity) => (
										<MenuItem
											key={activity.id}
											value={String(activity.id)}
										>
											{activity.activity_name}
										</MenuItem>
									))}
							</TextField>
						</Grid>

						{/* Challenge Activity Group */}
						<Grid item xs={12} sm={6}>
							<FormLabel>Challenge Activity Group:</FormLabel>
							<TextField
								fullWidth
								size='small'
								select
								name='challengeActivityGroup'
								value={formData.challengeActivityGroup}
								onChange={(e) =>
									handleInputChange(
										"challengeActivityGroup",
										e.target.value
									)
								}
								disabled={isLoading}
							>
								<MenuItem value='' disabled>
									Select Activity Group
								</MenuItem>
								{allChallengesData?.activityGroups?.map(
									(group) => (
										<MenuItem
											key={group.id}
											value={String(group.id)}
										>
											{group.activity_group}
										</MenuItem>
									)
								)}
							</TextField>
						</Grid>

						{/* Challenge Activity Track */}
						<Grid item xs={12} sm={6}>
							<FormLabel>Challenge Activity Track:</FormLabel>
							<TextField
								fullWidth
								size='small'
								select
								name='challengeActivityTrack'
								value={formData.challengeActivityTrack}
								onChange={(e) =>
									handleInputChange(
										"challengeActivityTrack",
										e.target.value
									)
								}
								disabled={isLoading}
							>
								<MenuItem value='' disabled>
									Select Activity Track
								</MenuItem>
								{allChallengesData?.activityTracks?.map(
									(track) => (
										<MenuItem
											key={track.id}
											value={String(track.id)}
										>
											{track.activity_track}
										</MenuItem>
									)
								)}
							</TextField>
						</Grid>

						{/* Challenge Duration */}
						<Grid item xs={12} sm={6}>
							<FormLabel>
								Challenge Duration{" "}
								<span className='text-red-500'>*</span>
							</FormLabel>
							<TextField
								fullWidth
								placeholder='Challenge Duration'
								size='small'
								type='number'
								name='challengeDuration'
								value={formData.challengeDuration}
								onChange={(e) =>
									handleInputChange(
										"challengeDuration",
										e.target.value
									)
								}
								disabled={isLoading}
							/>
						</Grid>

						{/* Duration Unit */}
						<Grid item xs={12} sm={6}>
							<FormLabel>Duration Unit:</FormLabel>
							<TextField
								fullWidth
								size='small'
								select
								name='durationunit'
								value={formData.durationunit}
								onChange={(e) =>
									handleInputChange(
										"durationunit",
										e.target.value
									)
								}
								disabled={isLoading}
							>
								<MenuItem value='days'>Day</MenuItem>
								<MenuItem value='weeks'>Week</MenuItem>
								<MenuItem value='months'>Month</MenuItem>
								<MenuItem value='years'>Year</MenuItem>
							</TextField>
						</Grid>

						{/* Challenge Start Date */}
						<Grid item xs={12} sm={6}>
							<FormLabel>
								Challenge Start Date{" "}
								<span className='text-red-500'>*</span>
							</FormLabel>
							<DatePicker
								selected={formData.challengeStartDate}
								onChange={(date) => {
									handleInputChange(
										"challengeStartDate",
										date
									);
									// Auto-calculate duration if end date exists
									if (formData.challengeEndDate && date) {
										const diffTime = Math.abs(
											formData.challengeEndDate - date
										);
										const diffDays = Math.ceil(
											diffTime / (1000 * 60 * 60 * 24)
										);
										handleInputChange(
											"challengeDuration",
											String(diffDays)
										);
									}
								}}
								dateFormat='dd-MM-yyyy'
								customInput={
									<TextField
										fullWidth
										placeholder='dd-mm-yyyy'
										size='small'
										type='text'
										name='challengeStartDate'
										disabled={isLoading}
									/>
								}
							/>
						</Grid>

						{/* Challenge End Date */}
						<Grid item xs={12} sm={6}>
							<FormLabel>
								Challenge End Date{" "}
								<span className='text-red-500'>*</span>
							</FormLabel>
							<DatePicker
								selected={formData.challengeEndDate}
								onChange={(date) => {
									handleInputChange("challengeEndDate", date);
									// Auto-calculate duration if start date exists
									if (formData.challengeStartDate && date) {
										const diffTime = Math.abs(
											date - formData.challengeStartDate
										);
										const diffDays = Math.ceil(
											diffTime / (1000 * 60 * 60 * 24)
										);
										handleInputChange(
											"challengeDuration",
											String(diffDays)
										);
									}
								}}
								minDate={formData.challengeStartDate}
								dateFormat='dd-MM-yyyy'
								customInput={
									<TextField
										fullWidth
										placeholder='dd-mm-yyyy'
										size='small'
										type='text'
										name='challengeEndDate'
										disabled={isLoading}
									/>
								}
							/>
						</Grid>

						{/* Training Block */}
						<Grid item xs={12} sm={6}>
							<FormLabel>Training Block:</FormLabel>
							<ReactSelect
								value={
									formData.trainingBlock
										? trainingBlocks.find(
												(block) =>
													String(block.id) ===
													formData.trainingBlock
										  )
										: null
								}
								onChange={(selectedOption) => {
									handleInputChange(
										"trainingBlock",
										selectedOption
											? String(selectedOption.id)
											: ""
									);
								}}
								options={trainingBlocks.map((block) => ({
									value: String(block.id),
									label: block.name,
									id: block.id,
								}))}
								placeholder='Select Training Block'
								isClearable
								isDisabled={isLoading}
							/>
						</Grid>

						{/* Challenge Level */}
						<Grid item xs={12} sm={6}>
							<FormLabel>
								Challenge Level{" "}
								<span className='text-red-500'>*</span>
							</FormLabel>
							<TextField
								fullWidth
								size='small'
								select
								name='challengeLevel'
								value={formData.challengeLevel}
								onChange={(e) =>
									handleInputChange(
										"challengeLevel",
										e.target.value
									)
								}
								disabled={isLoading}
							>
								<MenuItem value='' disabled>
									Select Level
								</MenuItem>
								{allChallengesData?.levels?.map((level) => (
									<MenuItem
										key={level.id}
										value={String(level.id)}
									>
										{level.level}
									</MenuItem>
								))}
							</TextField>
						</Grid>

						{/* Challenge Points */}
						<Grid item xs={12} sm={6}>
							<FormLabel>
								Challenge Points{" "}
								<span className='text-red-500'>*</span>
							</FormLabel>
							<TextField
								fullWidth
								placeholder='Challenge Points'
								size='small'
								type='number'
								name='challengePoints'
								value={formData.challengePoints}
								onChange={(e) =>
									handleInputChange(
										"challengePoints",
										e.target.value
									)
								}
								disabled={isLoading}
							/>
						</Grid>

						{/* Challenge Prerequisites */}
						<Grid item xs={12} sm={6}>
							<FormLabel>Challenge Pre-requisites:</FormLabel>
							<ReactSelect
								isMulti
								value={formData.challengePrerequisites
									.map((id) => {
										const challenge = challenges.find(
											(c) => String(c.id) === String(id)
										);
										return challenge
											? {
													value: String(challenge.id),
													label: challenge.challengeName,
											  }
											: null;
									})
									.filter(Boolean)}
								onChange={(selectedOptions) => {
									const ids = selectedOptions
										? selectedOptions.map(
												(option) => option.value
										  )
										: [];
									handleInputChange(
										"challengePrerequisites",
										ids
									);
								}}
								options={challenges.map((challenge) => ({
									value: String(challenge.id),
									label: challenge.challengeName,
								}))}
								placeholder='Select Prerequisites'
								isClearable
								isDisabled={isLoading}
							/>
						</Grid>
					</Grid>

					{/* Challenge Description */}
					<Grid container spacing={2}>
						<Grid item xs={12}>
							<FormLabel>
								Challenge Description{" "}
								<span className='text-red-500'>*</span>
							</FormLabel>
							<RichTextEditor
								setInitial={setInitial}
								initial={initial}
								initialContent={formData.challengeDescription}
								onContentChange={(newContent) =>
									handleInputChange(
										"challengeDescription",
										newContent
									)
								}
								placeholder='Type your content here...'
							/>
						</Grid>
					</Grid>

					{/* Challenge Target Section */}
					{fetchChallengeTarget.length > 0 && (
						<Grid container spacing={2}>
							<Grid item xs={12}>
								<FormLabel>Challenge Target:</FormLabel>
								{fetchChallengeTarget.map((activity, index) => (
									<div
										key={activity.id}
										style={{ marginBottom: "10px" }}
									>
										<FormControlLabel
											control={
												<Checkbox
													checked={formData.challangeTarget.some(
														(target) =>
															target.activity_id ===
															activity.id
													)}
													onChange={(e) => {
														const { checked } =
															e.target;
														let updatedTargets = [
															...formData.challangeTarget,
														];

														if (checked) {
															// Add target if not exists
															if (
																!updatedTargets.some(
																	(t) =>
																		t.activity_id ===
																		activity.id
																)
															) {
																updatedTargets.push(
																	{
																		activity_id:
																			activity.id,
																		quota: "",
																	}
																);
															}
														} else {
															// Remove target
															updatedTargets =
																updatedTargets.filter(
																	(t) =>
																		t.activity_id !==
																		activity.id
																);
														}

														handleInputChange(
															"challangeTarget",
															updatedTargets
														);
													}}
													disabled={isLoading}
												/>
											}
											label={activity.activity_name}
										/>
										{formData.challangeTarget.some(
											(target) =>
												target.activity_id ===
												activity.id
										) && (
											<TextField
												size='small'
												type='number'
												placeholder='Enter quota (KM)'
												value={
													formData.challangeTarget.find(
														(target) =>
															target.activity_id ===
															activity.id
													)?.quota || ""
												}
												onChange={(e) => {
													const updatedTargets =
														formData.challangeTarget.map(
															(target) =>
																target.activity_id ===
																activity.id
																	? {
																			...target,
																			quota: e
																				.target
																				.value,
																	  }
																	: target
														);
													handleInputChange(
														"challangeTarget",
														updatedTargets
													);
												}}
												style={{
													marginLeft: "20px",
													width: "200px",
												}}
												disabled={isLoading}
											/>
										)}
									</div>
								))}
							</Grid>
						</Grid>
					)}

					{/* Submit Buttons */}
					<Grid container spacing={2} style={{ marginTop: "20px" }}>
						<Grid item xs={12}>
							<div className='flex justify-end gap-3'>
								<Button
									type='button'
									variant='outline'
									onClick={onClose}
									disabled={isLoading}
								>
									Cancel
								</Button>
								<Button
									type='submit'
									className='bg-orange-600 hover:bg-orange-700 text-white'
									disabled={isLoading}
								>
									{isLoading
										? "Saving..."
										: editingItem?.id
										? "Update Challenge"
										: "Create Challenge"}
								</Button>
							</div>
						</Grid>
					</Grid>
				</form>
			</DialogContent>
		</Dialog>
	);
};
