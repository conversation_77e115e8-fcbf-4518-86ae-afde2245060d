import { useState, useEffect, useRef } from "react";
import { But<PERSON> } from "../ui/button";
import { Input } from "../ui/input";
import { Label } from "../ui/label";
import { Textarea } from "../ui/textarea";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "../ui/select";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "../ui/dialog";
import {
	createChallengeActivity,
	updateChallengeActivity,
	getAllActivities,
	getAllTrainingBlock,
	getAllChallenges,
} from "../../API/api-endpoint";
import Swal from "sweetalert2";

export const ChallengeDialog = ({ open, onClose, onSuccess, editingItem }) => {
	const [formData, setFormData] = useState({
		challengeName: "",
		challengeDescription: "",
		challengeActivity: "",
		challengeActivityGroup: "",
		challengeActivityTrack: "",
		challengeDuration: "",
		durationunit: "days",
		challengeStartDate: "",
		challengeEndDate: "",
		challengeLevel: "",
		challengePoints: "",
		trainingBlock: "",
		challengePrerequisites: [],
		challangeTarget: [],
		badge: null,
	});
	const [isLoading, setIsLoading] = useState(false);
	const [activities, setActivities] = useState([]);
	const [trainingBlocks, setTrainingBlocks] = useState([]);
	const [challenges, setChallenges] = useState([]);
	const fileInputRef = useRef(null);

	const durationUnits = [
		{ value: "days", label: "Days" },
		{ value: "weeks", label: "Weeks" },
		{ value: "months", label: "Months" },
	];

	const challengeLevels = [
		{ value: "Beginner", label: "Beginner" },
		{ value: "Intermediate", label: "Intermediate" },
		{ value: "Advanced", label: "Advanced" },
		{ value: "Expert", label: "Expert" },
	];

	useEffect(() => {
		const fetchData = async () => {
			try {
				const [activitiesRes, trainingBlocksRes, challengesRes] =
					await Promise.all([
						getAllActivities(),
						getAllTrainingBlock(),
						getAllChallenges(),
					]);
				setActivities(activitiesRes || []);
				setTrainingBlocks(trainingBlocksRes || []);
				setChallenges(challengesRes || []);
			} catch (error) {
				console.error("Error fetching data:", error);
			}
		};

		if (open) {
			fetchData();
		}
	}, [open]);

	useEffect(() => {
		if (open) {
			if (editingItem?.id) {
				const editData = {
					challengeName: editingItem.challengeName || "",
					challengeDescription:
						editingItem.challengeDescription || "",
					challengeActivity: editingItem.challengeActivity
						? String(editingItem.challengeActivity)
						: "",
					challengeActivityGroup:
						editingItem.challengeActivityGroup || "",
					challengeActivityTrack:
						editingItem.challengeActivityTrack || "",
					challengeDuration: String(
						editingItem.challengeDuration || ""
					),
					durationunit: editingItem.durationunit || "days",
					challengeStartDate: editingItem.challengeStartDate || "",
					challengeEndDate: editingItem.challengeEndDate || "",
					challengeLevel: editingItem.challengeLevel || "",
					challengePoints: String(editingItem.challengePoints || ""),
					trainingBlock: editingItem.trainingBlock
						? String(editingItem.trainingBlock)
						: "",
					challengePrerequisites:
						editingItem.challengePrerequisites || [],
					challangeTarget: editingItem.challangeTarget || [],
					badge: null,
				};
				setFormData(editData);
			} else {
				const newData = {
					challengeName: "",
					challengeDescription: "",
					challengeActivity: "",
					challengeActivityGroup: "",
					challengeActivityTrack: "",
					challengeDuration: "",
					durationunit: "days",
					challengeStartDate: "",
					challengeEndDate: "",
					challengeLevel: "",
					challengePoints: "",
					trainingBlock: "",
					challengePrerequisites: [],
					challangeTarget: [],
					badge: null,
				};
				setFormData(newData);
			}
		}
	}, [open, editingItem]);

	const handleInputChange = (field, value) => {
		setFormData((prev) => ({
			...prev,
			[field]: value,
		}));
	};

	const handleFileUpload = (event) => {
		const file = event.target.files[0];
		if (!file) return;

		const acceptedFileTypes = [
			".jpg",
			".JPG",
			".jpeg",
			".JPEG",
			".png",
			".PNG",
			".gif",
			".GIF",
		];
		const currentFileType = "." + file.type.split("/")[1];
		const maxSizeInBytes = 5 * 1024 * 1024;

		if (file.size > maxSizeInBytes) {
			Swal.fire({
				title: "Error",
				text: "File size exceeds the limit of 5MB.",
				icon: "error",
				timer: 2000,
				showConfirmButton: false,
			});
			event.target.value = null;
		} else if (!acceptedFileTypes.includes(currentFileType)) {
			Swal.fire({
				title: "Error",
				text: "Accepted file types are .jpg, .jpeg, .png, .gif",
				icon: "error",
				timer: 2000,
				showConfirmButton: false,
			});
			event.target.value = null;
		} else {
			setFormData((prev) => ({ ...prev, badge: file }));
		}
	};

	const handleSubmit = async (e) => {
		e.preventDefault();

		if (!formData.challengeName.trim()) {
			Swal.fire({
				title: "Error",
				text: "Challenge name is required",
				icon: "error",
				timer: 1800,
				showConfirmButton: false,
			});
			return;
		}

		if (!formData.challengeActivity) {
			Swal.fire({
				title: "Error",
				text: "Challenge activity is required",
				icon: "error",
				timer: 1800,
				showConfirmButton: false,
			});
			return;
		}

		if (!formData.challengeDuration.trim()) {
			Swal.fire({
				title: "Error",
				text: "Challenge duration is required",
				icon: "error",
				timer: 1800,
				showConfirmButton: false,
			});
			return;
		}

		try {
			setIsLoading(true);

			const apiData = new FormData();

			if (formData.badge) {
				apiData.append("badge", formData.badge);
			}

			if (
				formData.challangeTarget &&
				formData.challangeTarget.length > 0
			) {
				formData.challangeTarget.forEach(
					({ activity_id, quota }, index) => {
						apiData.append(
							`challangeTarget[${index}][activity_id]`,
							activity_id
						);
						apiData.append(
							`challangeTarget[${index}][quota]`,
							quota
						);
					}
				);
			}

			if (
				formData.challengePrerequisites &&
				formData.challengePrerequisites.length > 0
			) {
				formData.challengePrerequisites.forEach((id, index) => {
					apiData.append(`challengePrerequisites[${index}]`, id);
				});
			} else {
				apiData.append("challengePrerequisites", "null");
			}

			apiData.append("challengeName", formData.challengeName.trim());
			apiData.append("challengeActivity", formData.challengeActivity);
			apiData.append(
				"challengeActivityGroup",
				formData.challengeActivityGroup
			);
			apiData.append(
				"challengeActivityTrack",
				formData.challengeActivityTrack
			);
			apiData.append("durationunit", formData.durationunit);
			apiData.append(
				"challengeDescription",
				formData.challengeDescription.trim()
			);
			apiData.append(
				"challengeDuration",
				formData.challengeDuration.trim()
			);
			apiData.append("challengeStartDate", formData.challengeStartDate);
			apiData.append("challengeEndDate", formData.challengeEndDate);
			apiData.append("challengeLevel", formData.challengeLevel);
			apiData.append("challengePoints", formData.challengePoints.trim());
			apiData.append("trainingBlock", formData.trainingBlock);

			if (editingItem?.id) {
				apiData.append("id", editingItem.id);
			}

			let response;
			if (editingItem?.id) {
				response = await updateChallengeActivity(apiData);
			} else {
				response = await createChallengeActivity(apiData);
			}

			if (response?.status) {
				Swal.fire({
					title: "Success",
					text:
						response.message ||
						`Challenge ${
							editingItem?.id ? "updated" : "created"
						} successfully`,
					icon: "success",
					timer: 2000,
					showConfirmButton: false,
				});
				onSuccess();
			} else {
				Swal.fire({
					title: "Error",
					text: response?.message || "Failed to save challenge",
					icon: "error",
					timer: 3000,
					showConfirmButton: false,
				});
			}
		} catch (error) {
			console.error("Error saving challenge:", error);
			Swal.fire({
				title: "Error",
				text: "An error occurred while saving the challenge",
				icon: "error",
				timer: 3000,
				showConfirmButton: false,
			});
		} finally {
			setIsLoading(false);
		}
	};

	return (
		<Dialog open={open} onOpenChange={onClose}>
			<DialogContent className='sm:max-w-4xl bg-white max-h-[90vh] overflow-y-auto'>
				<DialogHeader>
					<DialogTitle className='text-lg font-semibold text-gray-900'>
						{editingItem?.id
							? "Edit Challenge"
							: "Create Challenge"}
					</DialogTitle>
				</DialogHeader>

				<form onSubmit={handleSubmit} className='space-y-4'>
					<div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
						{/* Challenge Name */}
						<div className='space-y-2'>
							<Label
								htmlFor='challengeName'
								className='text-sm font-semibold'
							>
								Challenge Name{" "}
								<span className='text-red-500'>*</span>
							</Label>
							<Input
								id='challengeName'
								className='w-full text-sm'
								value={formData.challengeName}
								onChange={(e) =>
									handleInputChange(
										"challengeName",
										e.target.value
									)
								}
								placeholder='Enter challenge name'
								disabled={isLoading}
							/>
						</div>

						{/* Challenge Activity */}
						<div className='space-y-2'>
							<Label
								htmlFor='challengeActivity'
								className='text-sm font-semibold'
							>
								Activity <span className='text-red-500'>*</span>
							</Label>
							<Select
								value={formData.challengeActivity}
								onValueChange={(value) =>
									handleInputChange(
										"challengeActivity",
										value
									)
								}
								disabled={isLoading}
							>
								<SelectTrigger className='w-full text-sm'>
									<SelectValue placeholder='Select Activity' />
								</SelectTrigger>
								<SelectContent className='bg-white'>
									{Array.isArray(activities) &&
										activities.map((activity) => (
											<SelectItem
												key={activity.activity_id}
												value={String(
													activity.activity_id
												)}
											>
												{activity.activity_name}
											</SelectItem>
										))}
								</SelectContent>
							</Select>
						</div>

						{/* Challenge Duration */}
						<div className='space-y-2'>
							<Label
								htmlFor='challengeDuration'
								className='text-sm font-semibold'
							>
								Duration <span className='text-red-500'>*</span>
							</Label>
							<div className='flex gap-2'>
								<Input
									id='challengeDuration'
									type='number'
									className='flex-1 text-sm'
									value={formData.challengeDuration}
									onChange={(e) =>
										handleInputChange(
											"challengeDuration",
											e.target.value
										)
									}
									placeholder='Enter duration'
									disabled={isLoading}
								/>
								<Select
									value={formData.durationunit}
									onValueChange={(value) =>
										handleInputChange("durationunit", value)
									}
									disabled={isLoading}
								>
									<SelectTrigger className='w-24 text-sm'>
										<SelectValue />
									</SelectTrigger>
									<SelectContent className='bg-white'>
										{durationUnits.map((unit) => (
											<SelectItem
												key={unit.value}
												value={unit.value}
											>
												{unit.label}
											</SelectItem>
										))}
									</SelectContent>
								</Select>
							</div>
						</div>

						{/* Challenge Level */}
						<div className='space-y-2'>
							<Label
								htmlFor='challengeLevel'
								className='text-sm font-semibold'
							>
								Challenge Level
							</Label>
							<Select
								value={formData.challengeLevel}
								onValueChange={(value) =>
									handleInputChange("challengeLevel", value)
								}
								disabled={isLoading}
							>
								<SelectTrigger className='w-full text-sm'>
									<SelectValue placeholder='Select Level' />
								</SelectTrigger>
								<SelectContent className='bg-white'>
									{challengeLevels.map((level) => (
										<SelectItem
											key={level.value}
											value={level.value}
										>
											{level.label}
										</SelectItem>
									))}
								</SelectContent>
							</Select>
						</div>

						{/* Challenge Points */}
						<div className='space-y-2'>
							<Label
								htmlFor='challengePoints'
								className='text-sm font-semibold'
							>
								Points
							</Label>
							<Input
								id='challengePoints'
								type='number'
								className='w-full text-sm'
								value={formData.challengePoints}
								onChange={(e) =>
									handleInputChange(
										"challengePoints",
										e.target.value
									)
								}
								placeholder='Enter points'
								disabled={isLoading}
							/>
						</div>

						{/* Training Block */}
						<div className='space-y-2'>
							<Label
								htmlFor='trainingBlock'
								className='text-sm font-semibold'
							>
								Training Block
							</Label>
							<Select
								value={formData.trainingBlock}
								onValueChange={(value) =>
									handleInputChange("trainingBlock", value)
								}
								disabled={isLoading}
							>
								<SelectTrigger className='w-full text-sm'>
									<SelectValue placeholder='Select Training Block' />
								</SelectTrigger>
								<SelectContent className='bg-white'>
									{Array.isArray(trainingBlocks) &&
										trainingBlocks.map((block) => (
											<SelectItem
												key={block.id}
												value={String(block.id)}
											>
												{block.name}
											</SelectItem>
										))}
								</SelectContent>
							</Select>
						</div>

						{/* Start Date */}
						<div className='space-y-2'>
							<Label
								htmlFor='challengeStartDate'
								className='text-sm font-semibold'
							>
								Start Date
							</Label>
							<Input
								id='challengeStartDate'
								type='date'
								className='w-full text-sm'
								value={formData.challengeStartDate}
								onChange={(e) =>
									handleInputChange(
										"challengeStartDate",
										e.target.value
									)
								}
								disabled={isLoading}
							/>
						</div>

						{/* End Date */}
						<div className='space-y-2'>
							<Label
								htmlFor='challengeEndDate'
								className='text-sm font-semibold'
							>
								End Date
							</Label>
							<Input
								id='challengeEndDate'
								type='date'
								className='w-full text-sm'
								value={formData.challengeEndDate}
								onChange={(e) =>
									handleInputChange(
										"challengeEndDate",
										e.target.value
									)
								}
								disabled={isLoading}
							/>
						</div>
					</div>

					{/* Challenge Description */}
					<div className='space-y-2'>
						<Label
							htmlFor='challengeDescription'
							className='text-sm font-semibold'
						>
							Description
						</Label>
						<Textarea
							id='challengeDescription'
							className='w-full text-sm'
							rows={4}
							value={formData.challengeDescription}
							onChange={(e) =>
								handleInputChange(
									"challengeDescription",
									e.target.value
								)
							}
							placeholder='Enter challenge description'
							disabled={isLoading}
						/>
					</div>

					{/* Badge Upload */}
					<div className='space-y-2'>
						<Label
							htmlFor='badge'
							className='text-sm font-semibold'
						>
							Challenge Badge
						</Label>
						<Input
							ref={fileInputRef}
							id='badge'
							type='file'
							accept='.jpg,.jpeg,.png,.gif'
							className='w-full text-sm'
							onChange={handleFileUpload}
							disabled={isLoading}
						/>
						<p className='text-xs text-gray-500'>
							Accepted formats: JPG, JPEG, PNG, GIF (Max size:
							5MB)
						</p>
					</div>

					<div className='flex justify-end gap-3 pt-4'>
						<Button
							type='button'
							variant='outline'
							onClick={onClose}
							disabled={isLoading}
						>
							Cancel
						</Button>
						<Button
							type='submit'
							className='bg-orange-600 hover:bg-orange-700 text-white'
							disabled={isLoading}
						>
							{isLoading
								? "Saving..."
								: editingItem?.id
								? "Update Challenge"
								: "Create Challenge"}
						</Button>
					</div>
				</form>
			</DialogContent>
		</Dialog>
	);
};
