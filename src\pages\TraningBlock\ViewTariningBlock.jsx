import React, { useEffect, useState } from "react";
import DialogTitle from "@mui/material/DialogTitle";
import Dialog from "@mui/material/Dialog";
import { Line } from "react-chartjs-2";
import "chart.js/auto";
import moment from "moment";
import CloseIcon from "@mui/icons-material/Close";
import { BsFillHeartPulseFill } from "react-icons/bs";
import Paper from "@mui/material/Paper";
import Table from "@mui/material/Table";
import TableBody from "@mui/material/TableBody";
import TableContainer from "@mui/material/TableContainer";
import TableHead from "@mui/material/TableHead";
import TableRow from "@mui/material/TableRow";
import Grid from "@mui/material/Grid";
import TableCell, { tableCellClasses } from "@mui/material/TableCell";
import { styled } from "@mui/material/styles";

import VolunteerActivismIcon from "@mui/icons-material/VolunteerActivism";
import { getCadenceData } from "../../API/api-endpoint";
import { CircularProgress, FormControl, MenuItem, OutlinedInput, Select, ToggleButton, ToggleButtonGroup } from "@mui/material";
import { calculatePace, calculateSpeed } from "../../utils/Resubale";
const selectPaceArray = [{ name: "Cadence", value: "cadence" }, { name: "Altitude", value: "altitude" },
{ name: "Pace", value: "pace" },
{ name: "Power", value: "power" },
]
const selectSpeedArray = [{ name: "Cadence", value: "cadence" }, { name: "Altitude", value: "altitude" },
{ name: "Speed", value: "speed" },
{ name: "Power", value: "power" },
]
const StyledTableCell = styled(TableCell)(({ theme }) => ({
  [`&.${tableCellClasses.head}`]: {
    backgroundColor: "white",
    color: theme.palette.common.black,
    border: "1px solid #dbd6d6"
  },
  [`&.${tableCellClasses.body}`]: {
    fontSize: 14,
  },
}));
const StyledTableRow = styled(TableRow)(({ theme }) => ({
  "&:nth-of-type(odd)": {
    backgroundColor: theme.palette.action.hover,
    border: "1px solid #dbd6d6"

  },
  // hide last border
  "&:last-child td, &:last-child th": {
    border: "1px solid #dbd6d6"

  },
}));





const ViewTariningBlock = ({ selectedValue, onClose, open }) => {
  let activityData = JSON.parse(selectedValue?.extendedProps?.activity_json);
  const [graphCadenceData, setGraphCadenceData] = useState({});
  const [changeName, setChangeName] = useState("cadence");
  const [isLoading, setLoading] = useState(false);
  const [graphXLevel, setGraphXlevel] = useState("distance");

let runningCadence =activityData?.average_cadence?activityData?.average_cadence*2:"Not applicable" ;
let allCadence =activityData?.average_cadence
? activityData?.average_cadence
: "Not applicable";

  const [graphaltitudeData, setGraphaltitudeData] = useState({});
  const [graphPowerData, setGraphPowerData] = useState({});
  const [graphPace, setGraphPace] = useState({});
  const [graphSpeed, setGraphSpeed] = useState({});
  const [speedY, setSpeedY] = useState([]);


  let findTyep = selectedValue?.extendedProps?.badge?.split(".")[0]
  useEffect(() => {
    if (selectedValue?.id && selectedValue?.extendedProps?.strava_id && changeName) {

      getCadence(selectedValue?.extendedProps?.strava_id)
    }

  }, [selectedValue?.id, selectedValue?.extendedProps?.process, changeName])

  const getCadence = async (id, alignment, xAccess) => {
    setLoading(true)
    const response = await getCadenceData(id)
    setLoading(false)
    if (response?.status) {
      const timeArray = response?.data?.time?.data || [];
      const distanceArray = response?.data?.distance?.data || [];
      const paceResult = calculatePace(response?.data?.time?.data, response?.data?.distance?.data);
      const speedResultResult = calculateSpeed(response?.data?.time?.data, response?.data?.distance?.data);
      let runningCadenceGraphValue = findTyep == "running" ?  response?.data?.cadence?.data.map(value => value *2) :response?.data?.cadence?.data;

      console.log("speedResultResult",speedResultResult,response?.data?.time?.data,"distance",response?.data?.distance?.data);
      if (xAccess == "altitude") {
        setSpeedY(response?.data?.altitude?.data)
      } else if (xAccess == "power") {
        setSpeedY(response?.data?.watts?.data)
      }
      else if (xAccess == "speed") {
        setSpeedY(speedResultResult)
      } else if (xAccess == "pace") {
        setSpeedY(paceResult)
      } else {
        setSpeedY(runningCadenceGraphValue)

      }
      let defineArray = alignment ? alignment : graphXLevel
      let selectGraph = defineArray === "distance" ? distanceArray.map(value => value / 1000) :timeArray.map(value => value / 60);

      // let speed = (distance(i) - distance(i - 1)) * 18 / 5
      // let pace = (1 / 3600) / (difference in distance / 1000)
      // let swimming = (1 / 3600) / (difference in distance) * 100

      setGraphCadenceData({
        labels: selectGraph,
        datasets: [{
          label: 'Cadence',
          // fill: true,
          data:runningCadenceGraphValue,
          backgroundColor: "rgb(37 99 235 / 70%)",
          borderColor: "rgb(37 99 235 / 70%)",
          borderWidth: 1,
          // tension: 0.1,
          // pointStyle: "none",
          pointRadius: 0,

        }]
      })
      setGraphaltitudeData({
        labels: selectGraph,
        datasets: [{
          label: 'Altitude',
          // fill: true,
          data: response?.data?.altitude?.data,
          backgroundColor: "rgb(37 99 235 / 70%)",
          borderColor: "rgb(37 99 235 / 70%)",
          borderWidth: 1,
          pointRadius: 0,


        }]
      })
      setGraphPowerData({
        labels: selectGraph,
        datasets: [{
          label: 'Power',
          // fill: true,
          data: response?.data?.watts?.data,
          backgroundColor: "rgb(37 99 235 / 70%)",
          borderColor: "rgb(37 99 235 / 70%)",
          borderWidth: 1,
          pointRadius: 0,


        }]
      })
      if (paceResult?.length > 0) {
        setGraphPace({
          labels: selectGraph,
          datasets: [{
            label: 'Pace',
            // fill: true,
            data: paceResult,
            backgroundColor: "rgb(37 99 235 / 70%)",
            borderColor: "rgb(37 99 235 / 70%)",
            borderWidth: 1,
            pointRadius: 0,


          }]
        })
      }
      if (speedResultResult?.length > 0) {
        setGraphSpeed({
          labels: selectGraph,
          datasets: [{
            label: 'Speed',
            // fill: true,
            data: speedResultResult,
            backgroundColor: "rgb(37 99 235 / 70%)",
            borderColor: "rgb(37 99 235 / 70%)",
            borderWidth: 1,
            pointRadius: 0,


          }]
        })
      }


    }

  }
  const options = {
    scales: {
      x: {
        type: 'linear',
        position: 'bottom',
        title: {
          display: true,
          text: graphXLevel === 'distance' ? 'Distance (km)' : 'Time (minutes)',
        },
      },
      y: {
        min: 0,
        max: speedY, // You can adjust the max value as needed
      },
    },
  };
  const handleChange = (event, newAlignment) => {
    console.log("newAlignment:", newAlignment,event.target.value);

    setGraphXlevel(newAlignment)
    getCadence(selectedValue?.extendedProps?.strava_id, newAlignment)
    // setAlignment(newAlignment);
  };
  const control = {
    value: graphXLevel,
    onChange: handleChange,
    exclusive: true,
  };
  return (
    <Dialog
      maxWidth="lg"
      minWidth="lg"
      onClose={() => onClose(false)}
      open={open}
    >
      <div className="flex justify-between">
        <DialogTitle>
          {selectedValue?.extendedProps?.workout} |{" "}
          {moment(selectedValue?.start).format("dddd, DD-MMM-YYYY")}
        </DialogTitle>
        <DialogTitle className=" cursor-pointer" onClick={() => onClose(false)}>
          {" "}
          <CloseIcon />
        </DialogTitle>


      </div>
      {selectedValue?.extendedProps?.strava_id &&
        <div >
          <Grid container>
            <Grid md={4} sm={4} lg={4} xs={12} sx={{ paddingLeft: "10px" }}>
              <FormControl fullWidth>
                <Select
                  placeholder="Distance"
                  name="activity"
                  value={changeName}
                  onChange={(e) => {
                    setChangeName(e.target.value)
                    getCadence(selectedValue?.extendedProps?.strava_id, "", e.target.value)
                  }}


                  id="form-layouts-separator-select"
                  labelId="form-layouts-separator-select-label"
                  input={<OutlinedInput id="select-multiple-language" />}
                >
                  {findTyep != "cycling" ?
                    selectPaceArray?.map((value, index) => {
                      return (
                        <MenuItem value={value?.value}>
                          {value?.name}
                        </MenuItem>
                      );
                    })
                    : selectSpeedArray?.map((value, index) => {
                      return (
                        <MenuItem value={value?.value}>
                          {value?.name}
                        </MenuItem>
                      );
                    })}
                </Select>
              </FormControl>
            </Grid>
            <Grid md={6} sm={6} lg={6} xs={12} sx={{ paddingLeft: "10px" }}>

              <ToggleButtonGroup size="small" {...control} aria-label="Small sizes">
                <ToggleButton
                  style={{
                    backgroundColor: graphXLevel === 'distance' ? '#2563eb' : 'inherit',
                    color: graphXLevel === 'distance' ? 'white' : 'inherit',
                  }}
                  value="distance" key="distance">
                  By Distance
                </ToggleButton>,
                <ToggleButton
                  style={{
                    backgroundColor: graphXLevel === 'time' ? '#2563eb' : 'inherit',
                    color: graphXLevel === 'time' ? 'white' : 'inherit',
                  }}
                  value="time" key="time">
                  By Time
                </ToggleButton>
              </ToggleButtonGroup>
            </Grid>
          </Grid>

          {isLoading ? (<CircularProgress className="m-6" />) : <>
            {changeName === "cadence" &&
              <>
                {graphCadenceData?.labels?.length > 0 ?
                  <div style={{ width: "913px", height: "500px" }}>
                    <Line data={graphCadenceData} options={options} />
                  </div>
                  : <div style={{ "fontSize": "20px", "padding": "10px", "fontWeight": "600" }}>No Data Found {changeName?.charAt(0).toUpperCase() + changeName?.slice(1)} </div>
                }
              </>
            }
            {changeName === "altitude" &&
              <>
                {graphaltitudeData?.labels?.length > 0 ?
                  <div style={{ width: "913px", height: "500px" }}>
                    <Line data={graphaltitudeData} options={options} />
                  </div>
                  : <div style={{ "fontSize": "20px", "padding": "10px", "fontWeight": "600" }}>No Data Found {changeName?.charAt(0).toUpperCase() + changeName?.slice(1)}</div>
                }
              </>
            }
            {changeName === "power" &&
              <>
                {graphPowerData?.labels?.length > 0 ?
                  <div style={{ width: "913px", height: "500px" }}>
                    <Line data={graphPowerData} options={options} />
                  </div>
                  : <div style={{ "fontSize": "20px", "padding": "10px", "fontWeight": "600" }}>No Data Found {changeName?.charAt(0).toUpperCase() + changeName?.slice(1)}</div>
                }
              </>
            }

            {changeName === "pace" &&
              <>
                {graphPace?.labels?.length > 0 ?
                  <div style={{ width: "913px", height: "500px" }}>
                    <Line data={graphPace} options={options} />
                  </div>
                  : <div style={{ "fontSize": "20px", "padding": "10px", "fontWeight": "600" }}>No Data Found {changeName?.charAt(0).toUpperCase() + changeName?.slice(1)}</div>
                }
              </>
            }
            {changeName === "speed" &&
              <>
                {graphSpeed?.labels?.length > 0 ?
                  <div style={{ width: "913px", height: "500px" }}>
                    <Line data={graphSpeed} options={options} />
                  </div>
                  : <div style={{ "fontSize": "20px", "padding": "10px", "fontWeight": "600" }}>No Data Found {changeName?.charAt(0).toUpperCase() + changeName?.slice(1)}</div>
                }
              </>
            }

          </>}



        </div>
      }
      <div className="flex justify-center text-sm">
        <div>Data type </div>
      </div>
      <div className="flex text-sm">
        <p className="flex items-center">
          {" "}
          &nbsp;
          <BsFillHeartPulseFill style={{ color: "red" }} /> Average Heart Rate:{" "}
          {activityData?.average_heartrate
            ? activityData?.average_heartrate
            : "Not applicable"}
        </p>
        <p className="flex items-center">
          &nbsp;
          <BsFillHeartPulseFill style={{ color: "red" }} /> Max. Heart Rate:
          {activityData?.max_heartrate
            ? activityData?.max_heartrate
            : "Not applicable"}
        </p>
        <p>
          &nbsp;
          <VolunteerActivismIcon style={{ color: "green" }} /> Average Cadence:{" "}
          {findTyep == "running" ?runningCadence : allCadence}
        </p>
      </div>
      &nbsp;
      <div className="flex text-sm">
        <div className="w-[24vw]  p-2 border-r-0 border-dotted border-2 border-gray-300">
          Distance:
          <div>Plan</div>
          <b>
            {selectedValue?.extendedProps.workout_planned_distance}
            {selectedValue?.extendedProps?.unit}
          </b>
          <div>Actuals</div>
          <b>
            {selectedValue?.extendedProps.actual_workout}{" "}
            {selectedValue?.extendedProps?.unit}
          </b>
        </div>
        <div className="w-[24vw]  p-2 border-r-0 border-dotted border-2 border-gray-300">
          Duration:
          <div>Plan</div>
          <b>
            {selectedValue?.extendedProps.workout_planned_duration}
            {selectedValue?.extendedProps?.durationunit}
          </b>
          <div>Actuals</div>
          <b>
            {selectedValue?.extendedProps.actual_duration}{" "}
            {selectedValue?.extendedProps?.durationunit}
          </b>
        </div>
        {findTyep != "cycling" &&
          <div className={findTyep != "cycling" ? "w-[24vw]  p-2  border-dotted border-2 border-gray-300" : "w-[24vw]  p-2 border-r-0 border-dotted border-2 border-gray-300"}>
            Pace:
            <div>Plan</div>
            <b>
              {selectedValue?.extendedProps.workout_planned_pace}
              {selectedValue?.extendedProps?.paceunit}
            </b>
            <div>Actuals</div>
            <b>
              {selectedValue?.extendedProps.actualPace}{" "}
              {selectedValue?.extendedProps?.paceunit}
            </b>
          </div>
        }

        {findTyep == "cycling" &&

          <div className="w-[24vw]  p-2  border-dotted border-2 border-gray-300">
            Speed:
            <div>Plan</div>
            <b>
              {selectedValue?.extendedProps.planned_speed ? selectedValue?.extendedProps.planned_speed : 0}
              {selectedValue?.extendedProps?.speedunit}
            </b>
            <div>Actuals</div>
            <b>

              {selectedValue?.extendedProps.actual_speed ? selectedValue?.extendedProps.actual_speed : 0}{" "}
              {selectedValue?.extendedProps?.speedunit}
            </b>
          </div>
        }
      </div>
      <div className="flex p-2 text-sm">
        <TableContainer component={Paper}>
          <div style={{ fontSize: "18px", background: "lightgray", width: "100%" }}>
            <h3 style={{ padding: "10px", margin: "0" }}>{`Laps (< TBL >)`}</h3>
          </div>
          <Table sx={{ minWidth: 400, }} aria-label="customized table">
            <TableHead>
              <TableRow>
                <StyledTableCell align="left">Lap</StyledTableCell>
                <StyledTableCell align="left">Lap distance (km)</StyledTableCell>
                <StyledTableCell align="left">Lap time (hh:mm:ss)</StyledTableCell>
                <StyledTableCell align="left">Lap pace (mm:ss/km) </StyledTableCell>

              </TableRow>
            </TableHead>
            <TableBody>
              {activityData?.laps?.length > 0 ? (
                <>
                  {activityData?.laps?.map((ele) => (
                    <StyledTableRow >
                      <StyledTableCell align="left">
                        {ele?.name}

                      </StyledTableCell>
                      <StyledTableCell align="left">
                        {ele?.distance}

                      </StyledTableCell>
                      <StyledTableCell align="left">
                        {ele?.elapsed_time}

                      </StyledTableCell>
                      <StyledTableCell align="left">
                        {ele?.elapsed_time / ele?.distance}

                      </StyledTableCell>

                    </StyledTableRow>
                  ))}
                </>
              ) : (
                <div className="p-4">No data found</div>
              )}
            </TableBody>
          </Table>
        </TableContainer>
        &nbsp;&nbsp;&nbsp;
        <TableContainer component={Paper}>
          <div style={{ fontSize: "18px", background: "lightgray", width: "100%" }}>
            <h3 style={{ padding: "10px", margin: "0" }}>{`Laps (< TBL >)`}</h3>
          </div>
          <Table sx={{ minWidth: 400, }} aria-label="customized table">
            <TableHead>
              <TableRow>
                <StyledTableCell align="left">Split (/km)</StyledTableCell>
                <StyledTableCell align="left">Split distance (km)</StyledTableCell>
                <StyledTableCell align="left">Split pace (mm:ss/km)</StyledTableCell>
                <StyledTableCell align="left">Elevation (m) </StyledTableCell>
                <StyledTableCell align="left">Avg HR </StyledTableCell>
                <StyledTableCell align="left">Avg Calories</StyledTableCell>


              </TableRow>
            </TableHead>
            <TableBody>
              {activityData?.splits_metric?.length > 0 ? (
                <>
                  {activityData?.splits_metric?.map((ele) => (
                    <StyledTableRow >
                      <StyledTableCell align="left">
                        {ele?.split}

                      </StyledTableCell>
                      <StyledTableCell align="left">
                        {ele?.distance}

                      </StyledTableCell>
                      <StyledTableCell align="left">
                        {ele?.distance / ele?.elapsed_time}


                      </StyledTableCell>
                      <StyledTableCell align="left">
                        {ele?.elevation_difference}

                      </StyledTableCell>
                      <StyledTableCell align="left">
                        {ele?.elapsed_time}

                      </StyledTableCell>
                      <StyledTableCell align="left">
                        {ele?.average_speed}

                      </StyledTableCell>

                    </StyledTableRow>
                  ))}
                </>
              ) : (
                <div className="p-4">No data found</div>
              )}
            </TableBody>
          </Table>
        </TableContainer>
      </div>
    </Dialog>
  );
};

export default ViewTariningBlock
