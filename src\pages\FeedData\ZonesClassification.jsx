import { useState, useMemo } from "react";
import { Plus, Search, Edit, Trash2, Clock, Target } from "lucide-react";

import { <PERSON><PERSON> } from "../../components/ui/button";
import { Input } from "../../components/ui/input";
import {
	Card,
	CardContent,
	CardHeader,
	CardTitle,
} from "../../components/ui/card";
import { Badge } from "../../components/ui/badge";
import {
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableHeader,
	TableRow,
} from "../../components/ui/table";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "../../components/ui/select";
import { ZonesClassificationDialog } from "../../components/admin/zones-classification-dialog";
import { DeleteConfirmDialog } from "../../components/admin/delete-confirm-dialog";
import Header from "../../components/Header";
import {
	getZonesClasification,
	CreateZonesClasification,
	updateZonesClasification,
	deleteZonesClasification,
	getAllActivityData,
	getAlllevels,
} from "../../API/api-endpoint";
import { useEffect } from "react";
import Swal from "sweetalert2";

const getLevelColor = (level) => {
	switch (level.toLowerCase()) {
		case "beginner":
			return "bg-gray-100 text-gray-800 border-gray-200";
		case "novice":
			return "bg-blue-100 text-blue-800 border-blue-200";
		case "intermediate":
			return "bg-yellow-100 text-yellow-800 border-yellow-200";
		case "advanced":
			return "bg-orange-100 text-orange-800 border-orange-200";
		case "elite":
			return "bg-red-100 text-red-800 border-red-200";
		default:
			return "bg-gray-100 text-gray-800 border-gray-200";
	}
};

const getActivityColor = (activity) => {
	switch (activity.toLowerCase()) {
		case "running":
			return "bg-green-100 text-green-800 border-green-200";
		case "cycling":
			return "bg-blue-100 text-blue-800 border-blue-200";
		case "swimming":
			return "bg-cyan-100 text-cyan-800 border-cyan-200";
		case "triathlon":
			return "bg-purple-100 text-purple-800 border-purple-200";
		case "fitness":
			return "bg-orange-100 text-orange-800 border-orange-200";
		default:
			return "bg-gray-100 text-gray-800 border-gray-200";
	}
};

export default function ZonesClassificationPage() {
	const [searchTerm, setSearchTerm] = useState("");
	const [activityFilter, setActivityFilter] = useState("All");
	const [currentPage, setCurrentPage] = useState(1);
	const [isDialogOpen, setIsDialogOpen] = useState(false);
	const [editingZone, setEditingZone] = useState(null);
	const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
	const [zoneToDelete, setZoneToDelete] = useState(null);
	const [zonesData, setZonesData] = useState([]);
	const [activities, setActivities] = useState([]);
	const [activityFilterOptions, setActivityFilterOptions] = useState([]);
	const [levels, setLevels] = useState([]);

	const itemsPerPage = 10;

	// Fetch zones from API
	const fetchZones = async () => {
		const data = await getZonesClasification();
		const mapped = (data || []).map((z) => ({
			id: z.id,
			activity: z.activity?.activity_name || z.activity || "",
			activity_id: z.activity?.id || z.activity_id || "",
			level: z.level?.level || z.level || "",
			level_id: z.level?.id || z.level_id || "",
			distance: z.distance,
			startRange: z.from_range,
			endRange: z.end_range,
		}));
		setZonesData(mapped);
	};

	// Fetch activities and levels for dropdowns and mapping
	const fetchMeta = async () => {
		try {
			const acts = await getAllActivityData();
			const lvls = await getAlllevels();

			const activityObjects = acts?.rows || [];
			const activityNames =
				activityObjects.map(
					(activity) => activity.activity || activity.activity_name
				) || [];

			setActivities(activityObjects);
			setActivityFilterOptions(["All", ...activityNames]);
			setLevels(lvls?.rows || []);
		} catch (error) {
			console.error("Error fetching meta data:", error);
			setActivities([]);
			setActivityFilterOptions(["All"]);
			setLevels([]);
		}
	};

	useEffect(() => {
		fetchZones();
		fetchMeta();
	}, []);

	const filteredZones = useMemo(() => {
		return zonesData.filter((zone) => {
			const matchesSearch =
				zone.activity
					.toLowerCase()
					.includes(searchTerm.toLowerCase()) ||
				zone.level.toLowerCase().includes(searchTerm.toLowerCase());

			const matchesActivity =
				activityFilter.toLowerCase() === "all" ||
				zone.activity.toLowerCase().trim() ===
					activityFilter.toLowerCase().trim();

			return matchesSearch && matchesActivity;
		});
	}, [zonesData, searchTerm, activityFilter]);

	const paginatedZones = useMemo(() => {
		const startIndex = (currentPage - 1) * itemsPerPage;
		return filteredZones.slice(startIndex, startIndex + itemsPerPage);
	}, [filteredZones, currentPage]);

	const totalPages = Math.ceil(filteredZones.length / itemsPerPage);

	const handleEdit = (zone) => {
		setEditingZone(zone);
		setIsDialogOpen(true);
	};

	const handleDelete = (zone) => {
		setZoneToDelete(zone);
		setDeleteDialogOpen(true);
	};

	const confirmDelete = async () => {
		if (zoneToDelete) {
			await deleteZonesClasification(zoneToDelete.id);
			setDeleteDialogOpen(false);
			setZoneToDelete(null);
			fetchZones();
		}
	};

	const handleSave = async (zoneData) => {
		if (!zoneData.activity_id || !zoneData.level_id) {
			console.log("Invalid activity or level selection.");
			return;
		}
		if (editingZone) {
			await updateZonesClasification({
				id: editingZone.id,
				activity_id: zoneData.activity_id,
				level_id: zoneData.level_id,
				distance: zoneData.distance,
				from_range: zoneData.startRange,
				end_range: zoneData.endRange,
			});
		} else {
			await CreateZonesClasification({
				activity_id: zoneData.activity_id,
				level_id: zoneData.level_id,
				distance: zoneData.distance,
				from_range: zoneData.startRange,
				end_range: zoneData.endRange,
			});
		}
		Swal.fire({
			title: "Success",
			text: "Zone saved successfully!",
			icon: "success",
			timer: 1800,
			showConfirmButton: false,
		});
		setIsDialogOpen(false);
		setEditingZone(null);
		fetchZones();
	};

	return (
		<div>
			<Header />
			<div className='max-w-[1300px] mx-auto px-4 py-8 mt-16'>
				<div className='mb-8'>
					<div className='flex items-center gap-3 mb-2'>
						<Target className='h-8 w-8 text-orange-500' />
						<h1 className='text-3xl font-bold text-gray-900'>
							Zones Classification
						</h1>
					</div>
					<p className='text-gray-600 text-base'>
						Manage performance zones and time ranges for different
						activities and skill levels
					</p>
				</div>

				<Card>
					<CardHeader className='bg-orange-50 border-b border-orange-100'>
						<div className='flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4'>
							<CardTitle className='text-xl font-semibold text-orange-900'>
								Zones Classification Management
							</CardTitle>
							<Button
								onClick={() => {
									setEditingZone(null);
									setIsDialogOpen(true);
								}}
								className='bg-orange-500 hover:bg-orange-600 text-white'
							>
								<Plus className='h-4 w-4 mr-2' />
								Create Zone Classification
							</Button>
						</div>
					</CardHeader>

					<CardContent className='p-6 my-6'>
						<div className='flex flex-col sm:flex-row gap-4 mb-6'>
							<div className='flex-1 relative'>
								<Search className='absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4' />
								<Input
									placeholder='Search by activity or level...'
									value={searchTerm}
									onChange={(e) =>
										setSearchTerm(e.target.value)
									}
									className='pl-10 text-sm'
								/>
							</div>

							<div className='w-full sm:w-48'>
								<Select
									value={activityFilter}
									onValueChange={(value) => {
										setActivityFilter(value);
										setCurrentPage(1);
									}}
								>
									<SelectTrigger>
										<SelectValue placeholder='Filter by Activity' />
									</SelectTrigger>
									<SelectContent className='bg-white'>
										{activityFilterOptions.map(
											(activity) => (
												<SelectItem
													key={activity}
													value={activity}
												>
													{activity}
												</SelectItem>
											)
										)}
									</SelectContent>
								</Select>
							</div>
						</div>

						<div className='border rounded-lg overflow-hidden'>
							<Table>
								<TableHeader className='bg-orange-500'>
									<TableRow>
										<TableHead className='text-white font-semibold'>
											Sr ID
										</TableHead>
										<TableHead className='text-white font-semibold'>
											Activity
										</TableHead>
										<TableHead className='text-white font-semibold'>
											Level
										</TableHead>
										<TableHead className='text-white font-semibold'>
											Distance (m)
										</TableHead>
										<TableHead className='text-white font-semibold'>
											Start Range
										</TableHead>
										<TableHead className='text-white font-semibold'>
											End Range
										</TableHead>
										<TableHead className='text-white font-semibold'>
											Actions
										</TableHead>
									</TableRow>
								</TableHeader>
								<TableBody>
									{paginatedZones.length > 0 ? (
										paginatedZones.map((zone, index) => (
											<TableRow
												key={zone.id}
												className='hover:bg-gray-50'
											>
												<TableCell>
													{(currentPage - 1) *
														itemsPerPage +
														index +
														1}
												</TableCell>
												<TableCell>
													<Badge
														variant='outline'
														className={getActivityColor(
															zone.activity
														)}
													>
														{zone.activity}
													</Badge>
												</TableCell>
												<TableCell>
													<Badge
														variant='outline'
														className={getLevelColor(
															zone.level
														)}
													>
														{zone.level}
													</Badge>
												</TableCell>
												<TableCell className='font-mono'>
													{zone.distance.toLocaleString()}
												</TableCell>
												<TableCell>
													<div className='flex items-center gap-1'>
														<Clock className='h-4 w-4 text-gray-400' />
														<span className='font-mono'>
															{zone.startRange}
														</span>
													</div>
												</TableCell>
												<TableCell>
													<div className='flex items-center gap-1'>
														<Clock className='h-4 w-4 text-gray-400' />
														<span className='font-mono'>
															{zone.endRange}
														</span>
													</div>
												</TableCell>
												<TableCell>
													<div className='flex items-center gap-2'>
														<Button
															variant='ghost'
															size='sm'
															onClick={() =>
																handleEdit(zone)
															}
															className='text-blue-600 hover:text-blue-800 hover:bg-blue-50'
														>
															<Edit className='h-4 w-4' />
														</Button>
														<Button
															variant='ghost'
															size='sm'
															onClick={() =>
																handleDelete(
																	zone
																)
															}
															className='text-red-600 hover:text-red-800 hover:bg-red-50'
														>
															<Trash2 className='h-4 w-4' />
														</Button>
													</div>
												</TableCell>
											</TableRow>
										))
									) : (
										<TableRow>
											<TableCell
												colSpan={7}
												className='text-center py-8 text-gray-500'
											>
												No zones found matching your
												criteria
											</TableCell>
										</TableRow>
									)}
								</TableBody>
							</Table>
						</div>

						{totalPages > 1 && (
							<div className='flex justify-center mt-6'>
								<div className='flex items-center gap-2'>
									<Button
										variant='outline'
										size='sm'
										onClick={() =>
											setCurrentPage(
												Math.max(1, currentPage - 1)
											)
										}
										disabled={currentPage === 1}
									>
										Previous
									</Button>
									{Array.from(
										{ length: totalPages },
										(_, i) => i + 1
									).map((page) => (
										<Button
											key={page}
											variant={
												currentPage === page
													? "default"
													: "outline"
											}
											size='sm'
											onClick={() => setCurrentPage(page)}
											className={
												currentPage === page
													? "bg-orange-500 hover:bg-orange-600 text-white"
													: ""
											}
										>
											{page}
										</Button>
									))}
									<Button
										variant='outline'
										size='sm'
										onClick={() =>
											setCurrentPage(
												Math.min(
													totalPages,
													currentPage + 1
												)
											)
										}
										disabled={currentPage === totalPages}
									>
										Next
									</Button>
								</div>
							</div>
						)}
					</CardContent>
				</Card>

				<ZonesClassificationDialog
					open={isDialogOpen}
					onOpenChange={setIsDialogOpen}
					zone={editingZone}
					onSave={handleSave}
					activities={activities}
					levels={levels}
				/>

				<DeleteConfirmDialog
					open={deleteDialogOpen}
					onOpenChange={setDeleteDialogOpen}
					title='Delete Zone Classification'
					description={`Are you sure you want to delete this zone classification for ${zoneToDelete?.activity} - ${zoneToDelete?.level}? This action cannot be undone.`}
					onConfirm={confirmDelete}
				/>
			</div>
		</div>
	);
}
