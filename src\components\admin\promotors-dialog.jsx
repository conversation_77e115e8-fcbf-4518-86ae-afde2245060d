import { useState, useEffect } from "react";
import { But<PERSON> } from "../ui/button";
import { Input } from "../ui/input";
import { Label } from "../ui/label";
import { Textarea } from "../ui/textarea";
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from "../ui/dialog";
import {
	createPromotorsdata,
	updatePromotorsdata,
	uploadsaveFile,
	URL,
} from "../../API/api-endpoint";
import Swal from "sweetalert2";

export const PromotorsDialog = ({ open, onClose, onSuccess, editingItem }) => {
	const [formData, setFormData] = useState({
		title: "",
		url: "",
		description: "",
		image: "",
	});
	const [isLoading, setIsLoading] = useState(false);
	const [previewImage, setPreviewImage] = useState(null);
	const [selectedFile, setSelectedFile] = useState(null);

	// Reset form when dialog opens/closes or when editing different item
	useEffect(() => {
		if (open) {
			if (editingItem?.id) {
				// Editing existing promotor
				const editData = {
					title: editingItem.title || "",
					url: editingItem.url || "",
					description: editingItem.description || "",
					image: editingItem.image || "",
				};
				setFormData(editData);
				setSelectedFile(null);
				
				// Set preview image if editing
				if (editingItem.image) {
					setPreviewImage(
						`${URL}/static/public/userimages/${editingItem.image}`
					);
				} else {
					setPreviewImage(null);
				}
			} else {
				// Creating new promotor
				const newData = {
					title: "",
					url: "",
					description: "",
					image: "",
				};
				setFormData(newData);
				setSelectedFile(null);
				setPreviewImage(null);
			}
		}
	}, [open, editingItem]);

	const handleInputChange = (field, value) => {
		setFormData((prev) => ({
			...prev,
			[field]: value,
		}));
	};

	const handleFileChange = async (e) => {
		const file = e.target.files[0];
		if (file) {
			setSelectedFile(file);
			
			// Create preview URL
			const reader = new FileReader();
			reader.onload = (e) => {
				setPreviewImage(e.target.result);
			};
			reader.readAsDataURL(file);

			// Upload file immediately
			try {
				const formData = new FormData();
				formData.append('file', file);
				
				const response = await uploadsaveFile(formData);
				if (response?.status) {
					setFormData((prev) => ({
						...prev,
						image: response.file,
					}));
				}
			} catch (error) {
				console.error("Error uploading file:", error);
				Swal.fire({
					title: "Error",
					text: "Failed to upload image. Please try again.",
					icon: "error",
					timer: 3000,
					showConfirmButton: false,
				});
			}
		}
	};

	const handleSubmit = async (e) => {
		e.preventDefault();

		// Validation
		if (!formData.title.trim()) {
			Swal.fire({
				title: "Error",
				text: "Title is required",
				icon: "error",
				timer: 1800,
				showConfirmButton: false,
			});
			return;
		}

		if (!formData.url.trim()) {
			Swal.fire({
				title: "Error",
				text: "Website URL is required",
				icon: "error",
				timer: 1800,
				showConfirmButton: false,
			});
			return;
		}

		if (!formData.description.trim()) {
			Swal.fire({
				title: "Error",
				text: "Description is required",
				icon: "error",
				timer: 1800,
				showConfirmButton: false,
			});
			return;
		}

		// For create mode, image is required
		if (!editingItem?.id && !formData.image) {
			Swal.fire({
				title: "Error",
				text: "Image is required",
				icon: "error",
				timer: 1800,
				showConfirmButton: false,
			});
			return;
		}

		try {
			setIsLoading(true);

			// Prepare data for API (JSON format)
			const apiData = {
				title: formData.title.trim(),
				url: formData.url.trim(),
				description: formData.description.trim(),
				image: formData.image,
			};

			let response;
			if (editingItem?.id) {
				// Add ID for update
				apiData.id = editingItem.id;
				response = await updatePromotorsdata(apiData);
			} else {
				response = await createPromotorsdata(apiData);
			}

			if (response?.status) {
				Swal.fire({
					title: "Success",
					text: response.message || `Promotor ${editingItem?.id ? 'updated' : 'created'} successfully`,
					icon: "success",
					timer: 2000,
					showConfirmButton: false,
				});
				onSuccess();
			} else {
				Swal.fire({
					title: "Error",
					text: response?.message || "Failed to save promotor",
					icon: "error",
					timer: 3000,
					showConfirmButton: false,
				});
			}
		} catch (error) {
			console.error("Error saving promotor:", error);
			Swal.fire({
				title: "Error",
				text: "An error occurred while saving the promotor",
				icon: "error",
				timer: 3000,
				showConfirmButton: false,
			});
		} finally {
			setIsLoading(false);
		}
	};

	return (
		<Dialog open={open} onOpenChange={onClose}>
			<DialogContent className='sm:max-w-2xl bg-white max-h-[90vh] overflow-y-auto'>
				<DialogHeader>
					<DialogTitle className='text-lg font-semibold text-gray-900'>
						{editingItem?.id ? "Edit Promotor" : "Create Promotor"}
					</DialogTitle>
				</DialogHeader>

				<form onSubmit={handleSubmit} className='space-y-4'>
					<div className='grid gap-4'>
						<div className='space-y-2'>
							<Label
								htmlFor='title'
								className='text-sm font-semibold'
							>
								Title <span className='text-red-500'>*</span>
							</Label>
							<Input
								id='title'
								className='w-full text-sm'
								value={formData.title}
								onChange={(e) =>
									handleInputChange("title", e.target.value)
								}
								placeholder='Enter title'
								disabled={isLoading}
								required
							/>
						</div>

						<div className='space-y-2'>
							<Label
								htmlFor='url'
								className='text-sm font-semibold'
							>
								Website URL <span className='text-red-500'>*</span>
							</Label>
							<Input
								id='url'
								type='url'
								className='w-full text-sm'
								value={formData.url}
								onChange={(e) =>
									handleInputChange("url", e.target.value)
								}
								placeholder='Enter website URL'
								disabled={isLoading}
								required
							/>
						</div>

						<div className='space-y-2'>
							<Label
								htmlFor='description'
								className='text-sm font-semibold'
							>
								Description <span className='text-red-500'>*</span>
							</Label>
							<Textarea
								id='description'
								className='w-full text-sm'
								value={formData.description}
								onChange={(e) =>
									handleInputChange("description", e.target.value)
								}
								placeholder='Enter description'
								disabled={isLoading}
								rows={4}
								required
							/>
						</div>

						<div className='space-y-2'>
							<Label
								htmlFor='image'
								className='text-sm font-semibold'
							>
								Image {!editingItem?.id && <span className='text-red-500'>*</span>}
							</Label>
							<Input
								id='image'
								type='file'
								accept='image/*'
								className='w-full text-sm'
								onChange={handleFileChange}
								disabled={isLoading}
								required={!editingItem?.id}
							/>
							{previewImage && (
								<div className='mt-2'>
									<img
										src={previewImage}
										alt='Promotor Preview'
										className='w-32 h-32 rounded-lg border-2 object-cover'
									/>
								</div>
							)}
						</div>
					</div>

					<div className='flex justify-end gap-3 pt-4'>
						<Button
							type='button'
							variant='outline'
							onClick={onClose}
							disabled={isLoading}
						>
							Cancel
						</Button>
						<Button
							type='submit'
							className='bg-orange-600 hover:bg-orange-700 text-white'
							disabled={isLoading}
						>
							{isLoading
								? "Saving..."
								: editingItem?.id
								? "Update Promotor"
								: "Create Promotor"}
						</Button>
					</div>
				</form>
			</DialogContent>
		</Dialog>
	);
};
