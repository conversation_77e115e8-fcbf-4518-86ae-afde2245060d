import {
	App<PERSON><PERSON>,
	<PERSON>,
	Fade,
	FormControlLabel,
	IconButton,
	Toolbar,
	Typography,
} from "@mui/material";
import React, { useState } from "react";
import KeyboardBackspaceIcon from "@mui/icons-material/KeyboardBackspace";
import "../../styles/mobile/navbar.css";
import Switch from "@mui/material/Switch";
import SearchIcon from "@mui/icons-material/Search";
import MoreVertIcon from "@mui/icons-material/MoreVert";
import { useNavigate } from "react-router-dom";
import { getGroupInfoAction } from "../../redux/action/groupAction";
import { useDispatch } from "react-redux";

export default function MobileNavbar({
	setIsUnRead,
	isUnRead,
	setSearchKeyword,
	searchKeyword,
	setIsSearchActive,
	isSearchActive,
}) {
	const dispatch = useDispatch();
	const navigate = useNavigate();

	const handleSetIsSearch = async () => {
		if (isSearchActive) {
			// Clear the input value only if isSearchActive is true
			setSearchKeyword("");
		}
		await setIsSearchActive(!isSearchActive ? true : false);
		const inputElement = document.getElementById("search-msg-input");
		if (inputElement) {
			inputElement.focus();
		}
	};

	return (
		<>
			<AppBar position='sticky' className='appBar' elevation={0}>
				<Toolbar>
					<Box
						style={{
							position: "relative",
							textAlign: "center",
							display: "flex",
							alignItems: "center",
							justifyContent: "center", // Center horizontally
							width: "100%",
						}}
					>
						<div
							style={{
								position: "relative",
								width: "100%",
								maxWidth: "100%",
							}}
						>
							<input
								id='search-msg-input'
								value={searchKeyword}
								onChange={(e) =>
									setSearchKeyword(e.target.value)
								}
								placeholder='Search message...'
								type='text'
								className='search-message-input'
								style={{
									width: "100%",
									paddingLeft: "50px", // Space for the icon
									letterSpacing: "1px",
									paddingRight: "unset !important",
									marginLeft: "unset !important",
								}}
							/>
							<img
								onClick={handleSetIsSearch}
								src={
									!isSearchActive
										? "/images/searcher-icon.svg"
										: "/images/close-icon.png"
								}
								alt='search'
								style={{
									position: "absolute",
									left: "15px",
									top: "50%",
									transform: "translateY(-50%)",
									width: "20px",
									height: "20px",
									cursor: "pointer",
								}}
							/>
						</div>
					</Box>
				</Toolbar>
			</AppBar>
		</>
	);
}
