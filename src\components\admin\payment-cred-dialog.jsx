import { useState, useEffect } from "react";
import { But<PERSON> } from "../ui/button";
import { Input } from "../ui/input";
import { Label } from "../ui/label";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "../ui/dialog";
import {
	createPaymentCreddata,
	updatePaymentCreddata,
} from "../../API/api-endpoint";
import Swal from "sweetalert2";

export const PaymentCredDialog = ({
	open,
	onClose,
	onSuccess,
	editingItem,
}) => {
	const [formData, setFormData] = useState({
		key: "",
		secret: "",
	});
	const [isLoading, setIsLoading] = useState(false);

	useEffect(() => {
		if (open) {
			if (editingItem?.id) {
				const editData = {
					key: editingItem.key || "",
					secret: editingItem.secret || "",
				};
				setFormData(editData);
			} else {
				const newData = {
					key: "",
					secret: "",
				};
				setFormData(newData);
			}
		}
	}, [open, editingItem]);

	const handleInputChange = (field, value) => {
		setFormData((prev) => ({
			...prev,
			[field]: value,
		}));
	};

	const handleSubmit = async (e) => {
		e.preventDefault();

		if (!formData.key.trim()) {
			Swal.fire({
				title: "Error",
				text: "Key is required",
				icon: "error",
				timer: 1800,
				showConfirmButton: false,
			});
			return;
		}

		if (!formData.secret.trim()) {
			Swal.fire({
				title: "Error",
				text: "Secret is required",
				icon: "error",
				timer: 1800,
				showConfirmButton: false,
			});
			return;
		}

		try {
			setIsLoading(true);

			const apiData = {
				key: formData.key.trim(),
				secret: formData.secret.trim(),
			};

			let response;
			if (editingItem?.id) {
				apiData.id = editingItem.id;
				response = await updatePaymentCreddata(apiData);
			} else {
				response = await createPaymentCreddata(apiData);
			}

			if (response?.status) {
				Swal.fire({
					title: "Success",
					text:
						response.message ||
						`Payment credential ${
							editingItem?.id ? "updated" : "created"
						} successfully`,
					icon: "success",
					timer: 2000,
					showConfirmButton: false,
				});
				onSuccess();
			} else {
				Swal.fire({
					title: "Error",
					text:
						response?.message ||
						"Failed to save payment credential",
					icon: "error",
					timer: 3000,
					showConfirmButton: false,
				});
			}
		} catch (error) {
			console.error("Error saving payment credential:", error);
			Swal.fire({
				title: "Error",
				text: "An error occurred while saving the payment credential",
				icon: "error",
				timer: 3000,
				showConfirmButton: false,
			});
		} finally {
			setIsLoading(false);
		}
	};

	return (
		<Dialog open={open} onOpenChange={onClose}>
			<DialogContent className='sm:max-w-md bg-white max-h-[90vh] overflow-y-auto'>
				<DialogHeader>
					<DialogTitle className='text-lg font-semibold text-gray-900'>
						{editingItem?.id
							? "Edit Payment Credential"
							: "Create Payment Credential"}
					</DialogTitle>
				</DialogHeader>

				<form onSubmit={handleSubmit} className='space-y-4'>
					<div className='grid gap-4'>
						<div className='space-y-2'>
							<Label
								htmlFor='key'
								className='text-sm font-semibold'
							>
								Key <span className='text-red-500'>*</span>
							</Label>
							<Input
								id='key'
								className='w-full text-sm'
								value={formData.key}
								onChange={(e) =>
									handleInputChange("key", e.target.value)
								}
								placeholder='Enter payment gateway key'
								disabled={isLoading}
								required
							/>
						</div>

						<div className='space-y-2'>
							<Label
								htmlFor='secret'
								className='text-sm font-semibold'
							>
								Secret <span className='text-red-500'>*</span>
							</Label>
							<Input
								id='secret'
								type='text'
								className='w-full text-sm'
								value={formData.secret}
								onChange={(e) =>
									handleInputChange("secret", e.target.value)
								}
								placeholder='Enter payment gateway secret'
								disabled={isLoading}
								required
							/>
						</div>
					</div>

					<div className='flex justify-end gap-3 pt-4'>
						<Button
							type='button'
							variant='outline'
							onClick={onClose}
							disabled={isLoading}
						>
							Cancel
						</Button>
						<Button
							type='submit'
							className='bg-orange-600 hover:bg-orange-700 text-white'
							disabled={isLoading}
						>
							{isLoading
								? "Saving..."
								: editingItem?.id
								? "Update Credential"
								: "Create Credential"}
						</Button>
					</div>
				</form>
			</DialogContent>
		</Dialog>
	);
};
