import { useState, useEffect, useMemo } from "react";
import {
	Calendar,
	MapPin,
	Star,
	Globe,
	ExternalLink,
	Search,
	Trophy,
	Flame,
} from "lucide-react";
import { format } from "date-fns";
import { <PERSON><PERSON> } from "../../components/ui/button";
import { Card } from "../../components/ui/card";
import { Input } from "../../components/ui/input";
import { Badge } from "../../components/ui/badge";
import { Skeleton } from "../../components/ui/skeleton";
import { getAllRacesData } from "../../API/api-endpoint";
import Header from "../../components/Header";
import {
	Ta<PERSON>,
	<PERSON>bs<PERSON>ontent,
	TabsList,
	TabsTrigger,
} from "../../components/ui/tabs";

const AthletesRacesData = ({ isEnrolledChallengesOpen }) => {
	const [racesData, setRacesData] = useState([]);
	const [searchTerm, setSearchTerm] = useState("");
	const [loading, setLoading] = useState(true);

	useEffect(() => {
		fetchAllRaces();
	}, []);

	const fetchAllRaces = async () => {
		try {
			setLoading(true);
			const response = await getAllRacesData();
			setRacesData(response || []);
			setLoading(false);
		} catch (error) {
			console.error("Error fetching races:", error);
			setLoading(false);
		}
	};

	const filteredRaces = useMemo(() => {
		return racesData?.filter((row) => {
			// Implement your search logic here
			// For example, if you want to search by a specific property like 'name':
			const firstnameMatches = row?.race_name
				?.toLowerCase()
				.includes(searchTerm?.toLowerCase());
			const emailMatches = row?.RaceType?.race_type_name
				?.toLowerCase()
				.includes(searchTerm?.toLowerCase());
			const cityMatch = row?.city?.name
				?.toLowerCase()
				.includes(searchTerm?.toLowerCase());

			return firstnameMatches || emailMatches || cityMatch;
		});
	}, [racesData, searchTerm]);

	const renderStarRating = (rating) => {
		return (
			<div className='flex items-center'>
				{[...Array(5)].map((_, i) => (
					<Star
						key={i}
						className={`h-4 w-4 ${
							i < rating
								? "text-orange-500 fill-orange-500"
								: "text-gray-300"
						}`}
					/>
				))}
			</div>
		);
	};
	return (
		<div className='bg-gradient-to-b from-orange-50/30 to-white w-full px-4 py-8'>
			<Header />
			<div className=' px-4 py-8 mt-20 mx-auto max-w-7xl'>
				<div className='mb-8'>
					<h1 className='text-3xl font-bold tracking-tight text-orange-950 mb-2'>
						Upcoming Races
					</h1>
					<p className='text-gray-600 mb-6 text-base'>
						Discover and register for upcoming races in your area
					</p>
				</div>

				<div className='flex flex-col md:flex-row justify-between items-start md:items-center mb-8 gap-4'>
					<div className='relative w-full md:w-auto'>
						<Search className='absolute left-2.5 top-2.5 h-4 w-4 text-gray-600' />
						<Input
							type='text'
							placeholder='Search races by name, type, or city...'
							value={searchTerm}
							onChange={(e) => setSearchTerm(e.target.value)}
							className='text-gray-600 text-sm pl-9 border-orange-200 focus-visible:ring-orange-500 w-full md:w-[300px]'
						/>
					</div>
				</div>

				<Tabs defaultValue='all' className='w-full'>
					<TabsList className='bg-orange-100/50 border border-orange-200 mb-8'>
						<TabsTrigger
							value='all'
							className='text-gray-600 font-bold'
							activeClassName='bg-white rounded-full text-orange-800'
						>
							All Races
						</TabsTrigger>
						<TabsTrigger
							value='triathlon'
							className='text-gray-600 font-bold'
							activeClassName='bg-white rounded-full text-orange-800'
						>
							Triathlon
						</TabsTrigger>
						<TabsTrigger
							value='running'
							className='text-gray-600 font-bold'
							activeClassName='bg-white rounded-full text-orange-800'
						>
							Running
						</TabsTrigger>
						<TabsTrigger
							value='cycling'
							className='text-gray-600 font-bold'
							activeClassName='bg-white rounded-full text-orange-800'
						>
							Cycling
						</TabsTrigger>
					</TabsList>

					<TabsContent value='all' className='mt-0'>
						{loading ? (
							<div className='space-y-6'>
								{[...Array(3)].map((_, i) => (
									<Card
										key={i}
										className='border-orange-200 shadow-sm overflow-hidden'
									>
										<div className='flex flex-col lg:flex-row'>
											<div className='lg:w-2/3 p-0'>
												<Skeleton className='h-[400px] w-full rounded-none' />
											</div>
											<div className='lg:w-1/3 p-6'>
												<div className='space-y-4'>
													<Skeleton className='h-8 w-3/4' />
													<Skeleton className='h-4 w-1/2' />
													<Skeleton className='h-4 w-full' />
													<Skeleton className='h-4 w-full' />
													<Skeleton className='h-4 w-3/4' />
													<Skeleton className='h-10 w-full' />
												</div>
											</div>
										</div>
									</Card>
								))}
							</div>
						) : filteredRaces.length > 0 ? (
							<div className='space-y-6'>
								{filteredRaces.map((race) => (
									<Card
										key={race.id}
										className='border border-orange-200 rounded-lg overflow-hidden'
									>
										<div className='flex flex-col lg:flex-row'>
											<div className='lg:w-2/3 relative'>
												<div className='absolute top-4 left-4 z-10'>
													<Badge className='bg-white/90 px-2 text-xs font-bold text-orange-900 border border-orange-200 rounded-full'>
														{race.RaceType
															?.race_type_name ||
															"Race"}
													</Badge>
												</div>

												<div className='p-6 lg:p-8 h-full flex flex-col'>
													<div className='mb-auto mt-8'>
														<h2 className='text-2xl font-bold text-orange-950 mb-2'>
															{race.race_name}
														</h2>
														<div className='flex items-center gap-2 mb-4'>
															{renderStarRating(
																Number.parseInt(
																	race.race_ratings
																) || 0
															)}
															<span className='text-sm text-muted-foreground'>
																(
																{race.race_ratings ||
																	0}
																/5)
															</span>
														</div>
													</div>

													<div className='relative w-full h-[300px] mt-4 bg-orange-50 rounded-md overflow-hidden'>
														{race.image ? (
															<img
																src={`https://api.fit.yoska.in/static/public/userimages/${race.image}`}
																alt={
																	race.race_name
																}
																className='w-full h-full object-cover text-base'
															/>
														) : (
															<div className='w-full h-full flex items-center justify-center bg-orange-100'>
																<Trophy className='h-16 w-16 text-orange-300' />
															</div>
														)}
													</div>
												</div>
											</div>

											<div className='lg:w-1/3 p-6 border-t lg:border-t-0 lg:border-l border-orange-200'>
												<div className='space-y-4'>
													<div>
														<h3 className='text-lg font-bold text-orange-900 mb-4'>
															Race Details
														</h3>

														<div className='space-y-3'>
															<div className='flex justify-between items-center'>
																<div className='flex items-center gap-2 text-sm'>
																	<Trophy className='h-4 w-4 text-orange-500' />
																	<span className='text-gray-600 text-sm'>
																		Race
																		Type
																	</span>
																</div>
																<span className='font-semibold text-sm'>
																	{race
																		.RaceType
																		?.race_type_name ||
																		"N/A"}
																</span>
															</div>

															<div className='flex items-start gap-2'>
																<div className='flex items-center gap-2 text-sm min-w-[80px] pt-0.5'>
																	<MapPin className='h-4 w-4 text-orange-500 flex-shrink-0' />
																	<span className='text-gray-600 text-sm'>
																		Location
																	</span>
																</div>
																<span className='font-semibold text-sm text-right flex-1'>
																	{
																		race
																			.city
																			?.name
																	}
																	{race.State
																		?.name &&
																		`, ${race.State.name}`}
																	{race
																		.Country
																		?.name &&
																		`, ${race.Country.name}`}
																</span>
															</div>

															<div className='flex justify-between items-center'>
																<div className='flex items-center gap-2 text-sm'>
																	<Calendar className='h-4 w-4 text-orange-500' />
																	<span className='text-gray-600 text-sm'>
																		Date
																	</span>
																</div>
																<span className='font-semibold text-sm'>
																	{format(
																		new Date(
																			race.commencement_date
																		),
																		"MMM dd, yyyy"
																	)}
																</span>
															</div>

															{race.distance_field_1 && (
																<div className='flex justify-between items-center'>
																	<div className='flex items-center gap-2 text-sm'>
																		<Trophy className='h-4 w-4 text-orange-500' />
																		<span className='text-gray-600 text-sm'>
																			Distance
																		</span>
																	</div>
																	<span className='font-semibold text-sm'>
																		{
																			race.distance_field_1
																		}
																	</span>
																</div>
															)}
														</div>
													</div>

													{race.race_comments && (
														<div>
															<h4 className='text-sm font-semibold text-orange-800 mb-1'>
																Description
															</h4>
															<p className='text-gray-600 text-sm'>
																{race.race_comments.replace(
																	/<[^>]*>?/gm,
																	""
																)}
															</p>
														</div>
													)}

													<div className='space-y-2 pt-2'>
														{race.race_website && (
															<a
																href={
																	race.race_website
																}
																target='_blank'
																rel='noopener noreferrer'
																className='flex items-center gap-2 text-sm text-orange-700 hover:text-orange-800'
															>
																<Globe className='h-4 w-4' />
																Race Website
																<ExternalLink className='h-3 w-3' />
															</a>
														)}

														{race.yoska_program_link && (
															<a
																href={
																	race.yoska_program_link
																}
																target='_blank'
																rel='noopener noreferrer'
																className='flex items-center gap-2 text-sm text-orange-700 hover:text-orange-800'
															>
																<Flame className='h-4 w-4' />
																Training Program
																<ExternalLink className='h-3 w-3' />
															</a>
														)}
													</div>

													{race.race_registration_link && (
														<Button
															className='w-full text-white mt-4 bg-orange-700 hover:bg-orange-800'
															onClick={() =>
																window.open(
																	race.race_registration_link,
																	"_blank"
																)
															}
														>
															Register Now
														</Button>
													)}

													{race.user && (
														<div className='mt-4 pt-4 border-t border-orange-100'>
															<Badge
																variant='outline'
																className='bg-white/80 font-semibold text-xs px-2 rounded-full border border-orange-200 text-orange-800 w-fit'
															>
																Created by:{" "}
																{
																	race.user
																		.firstname
																}{" "}
																{
																	race.user
																		.lastname
																}
															</Badge>
														</div>
													)}
												</div>
											</div>
										</div>
									</Card>
								))}
							</div>
						) : (
							<div className='flex flex-col items-center justify-center py-12 text-center'>
								<div className='rounded-full bg-orange-100 p-3 mb-4'>
									<Trophy className='h-6 w-6 text-orange-500' />
								</div>
								<h3 className='text-lg font-semibold text-orange-800'>
									No races found
								</h3>
								<p className='text-orange-600/80 mt-1 max-w-md'>
									{searchTerm
										? "No races match your search criteria. Try adjusting your search terms."
										: "There are no races available at the moment. Check back later for upcoming events."}
								</p>
							</div>
						)}
					</TabsContent>

					{/* These would filter by race type in a real implementation */}
					<TabsContent value='triathlon' className='mt-0'>
						<div className='flex flex-col items-center justify-center py-12 text-center'>
							<div className='rounded-full bg-orange-100 p-3 mb-4'>
								<Trophy className='h-6 w-6 text-orange-500' />
							</div>
							<h3 className='text-lg font-semibold text-orange-800'>
								Triathlon Races
							</h3>
							<p className='text-orange-600/80 mt-1 text-sm max-w-md'>
								Filter implementation would show only triathlon
								races here.
							</p>
						</div>
					</TabsContent>

					<TabsContent value='running' className='mt-0'>
						<div className='flex flex-col items-center justify-center py-12 text-center'>
							<div className='rounded-full bg-orange-100 p-3 mb-4'>
								<Trophy className='h-6 w-6 text-orange-500' />
							</div>
							<h3 className='text-lg font-semibold text-orange-800'>
								Running Races
							</h3>
							<p className='text-orange-600/80 text-sm mt-1 max-w-md'>
								Filter implementation would show only running
								races here.
							</p>
						</div>
					</TabsContent>

					<TabsContent value='cycling' className='mt-0'>
						<div className='flex flex-col items-center justify-center py-12 text-center'>
							<div className='rounded-full bg-orange-100 p-3 mb-4'>
								<Trophy className='h-6 w-6 text-orange-500' />
							</div>
							<h3 className='text-lg font-semibold text-orange-800'>
								Cycling Races
							</h3>
							<p className='text-orange-600/80 text-sm mt-1 max-w-md'>
								Filter implementation would show only cycling
								races here.
							</p>
						</div>
					</TabsContent>
				</Tabs>
			</div>
		</div>
	);
};

export default AthletesRacesData;
