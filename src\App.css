/* Toggle A */
input:checked ~ .dot {
  transform: translateX(100%);
  background-color: white;
}

/* Toggle B */
input:checked ~ .dot {
  transform: translateX(100%);
  background-color: white;
}

.bg {
  background-color: lightgray;
}

input:checked ~ .bg {
  background-color: #22c55e;
}

label {
  font-size: medium;
}

.ant-btn-primary {
  background-color: #e67e22 !important;
}

.tags-wraaper {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  border-bottom: 2px solid lightgray;
  border-radius: 2px;
  /* background: #f0f0f0; */
  width: 100%;
  min-height: 42px;
  margin-top: 10px;
}
.tags-wraaper:hover {
  border-bottom: 2px solid lightgray;
}
.tags-wraaper .tags-container {
  display: flex;
  align-items: center;
  width: 92%;
  height: auto;
  flex-wrap: wrap;
}
.tags-wraaper .remove-tag-btn {
  color: black;
  padding-right: 5px;
}
.tags-wraaper .tag {
  display: flex;
  align-items: center;
  background-color: #f0f0f0;
  border-radius: 15px;
  padding: 5px;
  margin: 5px;
  color: gray;
}
.tags-wraaper .tag span {
  margin-right: 5px;
  margin-bottom: 2px;
  margin-left: 2px;
}
.tags-wraaper .tag button {
  background: none;
  border: none;
  cursor: pointer;
}
.tags-wraaper input {
  padding: 5px;
  border: none;
  background: transparent;
}
.tags-wraaper input:focus,
.tags-wraaper select:focus,
.tags-wraaper textarea:focus,
.tags-wraaper button:focus {
  outline: none;
}
.tags-wraaper .add-btn {
  font-weight: 500;
  cursor: pointer;
}

.fix-search span {
  white-space: normal;
  overflow: visible;
  word-wrap: break-word;
}

.css-11xur9t-MuiPaper-root-MuiTableContainer-root {
  box-shadow: none !important;
}

/* //////////////////////////////////////////////////////////// */

.orange {
  color: #ffa654;
}

.orangeBg {
  background-color: #ffa654;
}

.heading {
  font-size: 46px;
  align-self: center;
}

.paddingBot {
  padding-bottom: 3%;
}

.grey {
  color: #919191;
}

.parentCont {
  display: flex;
}

.slick-container {
  width: 35%;
}

.slickCar {
  margin-bottom: 5%;
  background-color: transparent;
  padding-bottom: 20px !important;
  font-size: medium;
}
.hideunhide {
  .slick-prev {
    display: none;
  }
}

.slick-dots {
  background-color: transparent;
  padding-bottom: 20px !important;
}

.slick-dots li.slick-active button:before {
  color: #e67e22 !important;
}

.css-1n4twyu-MuiInputBase-input-MuiOutlinedInput-input {
  position: relative;
}
/* .form1 input {
  border-bottom: none !important;
} */

.form1 {
  width: 100%;
  align-self: center;
}

.cstm {
  margin-top: -3%;
}

.caro cstm {
  margin-top: 0;
}

.mbtm {
  margin-bottom: 15px !important;
}

.marbot {
  margin-bottom: 15px;
  margin-right: 10px;
}

.spcl button {
  float: inline-end !important;
  margin-top: 12%;
}

.swalbtn {
  margin-right: 10px;

  padding: 10px 10px 10px 20px !important;
  font-size: 20px !important;
  background-color: #e67e22 !important;
  border: none !important;
  font-weight: 600 !important;
  color: white !important;
  border-radius: 5px !important;
  height: fit-content;
  margin-top: 5%;
}
.swalbtnCancel {
  margin-right: 10px;

  padding: 10px 10px 10px 10px !important;
  font-size: 20px !important;
  background-color: gray !important;
  border: none !important;
  font-weight: 600 !important;
  color: white !important;
  border-radius: 5px !important;
  height: fit-content;
  margin-top: 5%;
}
.btn {
  padding: 8px 40px !important;
  font-size: 20px !important;
  background-color: #e67e22 !important;
  border: none !important;
  font-weight: 600 !important;
  color: white !important;
  border-radius: 5px !important;
  height: fit-content;
  margin-top: 5%;
}
.form-btn {
  /* padding: 8px 40px !important; */
  font-size: 16px !important;
  background-color: #e67e22 !important;
  border: none !important;
  font-weight: 600 !important;
  color: white !important;
  border-radius: 5px !important;
  height: fit-content;
  margin-top: 5%;
}
.report-btn {
  /* padding: 8px 40px !important; */
  font-size: 16px !important;
  background-color: #e67e22 !important;
  border: none !important;
  font-weight: 600 !important;
  color: white !important;
  border-radius: 5px !important;
  height: fit-content;
}

.carousel-inner1 {
  overflow: hidden;
}

.fc .fc-timegrid-slot {
  height: 8rem !important;
}

.fc-timegrid-event {
  height: max-content !important;
}

.fc-timegrid-col-events {
  display: flex !important;
  flex-direction: column !important;
}
.css-1aquho2-MuiTabs-indicator {
  background-color: #e67e22;
}
.css-1h9z7r5-MuiButtonBase-root-MuiTab-root.Mui-selected {
  color: #e67e22;
}

.fc-day-today {
  background-color: #e67d2215 !important;
}

.ant-pagination-item-active a {
  color: #e67e22 !important;
  border-color: #e67e22 !important;
}

.ant-pagination-item-active {
  border-color: #e67e22 !important;
}

.muiButton-root.css-sghohy-MuiButtonBase-root-MuiButton-root {
  background-color: #e67e22 !important;
}

.MuiButton-root {
  background-color: #e67e22 !important;
}

.white-bg-btn {
  background-color: #ffeadc !important;
}

.pure-white-bg-btn {
  background-color: white !important;
}

.icon-tabler-edit {
  stroke: #e67e22 !important;
}

.css-1to7aaw-MuiButtonBase-root-MuiPaginationItem-root.Mui-selected {
  background-color: #e67e22 !important;
}

.MuiCircularProgress-colorPrimary {
  color: #e67e22 !important;
}

div:where(.swal2-container) button:where(.swal2-styled).swal2-confirm {
  background-color: #e67e22 !important;
  border-color: #e67e22 !important;
  outline: none !important;
  box-shadow: none !important;
}

.fc-header-toolbar > .fc-toolbar-chunk:nth-child(2) {
  min-width: 5%;
}

.inputWrapper .css-1u3bzj6-MuiFormControl-root-MuiTextField-root {
  width: 100%;
  background-color: white;
  border-color: white;
}

.inputWrapper .css-yu75kd-MuiGrid-root {
  width: 100%;
}

.inputWrapper .css-5sfu98-MuiGrid-root {
  width: 100%;
}

.inputWrapper .react-datepicker-wrapper {
  width: 100%;
}

.ant-table-thead .ant-table-cell {
  background-color: #ffeadc !important;
}

.MuiPaginationItem-icon,
.ant-pagination-item-link {
  color: #e67e22 !important;
}

.MuiRadio-colorPrimary {
  color: #e67e22 !important;
}

.ant-modal-title,
.ant-modal-close-icon {
  color: #e67e22 !important;
}

.ant-modal-title {
  font-size: 25px !important;
}

.ant-btn-default:not(:disabled):hover {
  color: #e67e22 !important;
  border-color: #e67e22 !important;
}

/* .MuiButton-textPrimary{
  color: white !important;
} */

.css-u72sq4-MuiTableCell-root.MuiTableCell-head {
  background-color: #e67e22 !important;
}

.react-datepicker-popper {
  z-index: 10 !important;
}

.dateWrap .react-datepicker-wrapper {
  width: 100%;
}

.modalImage {
  max-width: 265px !important;
  max-height: 216px !important;
  border-top-left-radius: 10px;
  border-top-right-radius: 10px;
}

.hideDropdown {
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  background: transparent;
}

.css-1omp2b7.Mui-selected {
  background-color: #e67e22 !important;
}

.changeGoalModal .mantine-Modal-title {
  color: #e67e22;
  font-size: x-large;
}

.athleteLogin .fc-header-toolbar > .fc-toolbar-chunk:nth-child(2) {
  min-width: 20%;
}

.css-1iq2dqk-MuiPaper-root,
.css-pbqcab-MuiPaper-root {
  box-shadow: none !important;
}
.css-pbqcab-MuiPaper-root {
  border-right: 1px solid lightgray;
}

.antTableManage .ant-table-sticky-scroll {
  display: none;
}

.fc .fc-toolbar.fc-header-toolbar {
  margin-left: 20px;
}

/* Add this CSS to your stylesheet or in a styled component */
.thin-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: #e67e22 white; /* Optional, for custom scrollbar color */
}

/* For WebKit browsers */
.thin-scrollbar::-webkit-scrollbar {
  width: 6px; /* Adjust the width as needed */
}

.thin-scrollbar::-webkit-scrollbar-track {
  background: white; /* Optional, for custom scrollbar track color */
}

.thin-scrollbar::-webkit-scrollbar-thumb {
  background-color: #e67e22; /* Optional, for custom scrollbar thumb color */
  border-radius: 10px; /* Optional, for rounded scrollbar thumb */
  border: 3px solid white; /* Adjust the border as needed */
}

.overflowXNone {
  overflow-x: hidden;
}

.slick-slide div {
  outline: none; /* Ensure no outline appears on focus */
}

.custom-select__option--is-focused {
  background-color: #ffeadc !important;
  color: black !important;
}
.custom-select__option--is-selected {
  background-color: #e67e22 !important;
  color: white !important;
}

/* //////////////////////// */
@media (max-width: 1920px) {
  /* .work-calendar{
    height: 111vh !important;
  } 
  */
}

@media (max-width: 1600px) {
  /* .work-calendar{
      height: 114vh !important;
    }  */
}

@media (min-width: 1450px) {
  /* .work-calendar{
  height: 124vh !important;
} 
   */
  /* .blockWidth{
    width: 150px !important; 
  } */
}
@media (min-width: 1550px) {
  /* .work-calendar{
    height: 122vh !important;
  } 
     */
  /* .blockWidth{
      width: 160px !important; 
    } */
}
@media (min-width: 1650px) {
  /* .weeklySummary{
      margin-top: 76px !important;
    } */
  /* .blockWidth{
      width: 165px !important; 
    } */
}
@media (min-width: 1750px) {
  /* .weeklySummary{
        margin-top: 76px !important;
      } */
  /* .blockWidth{
        width: 175px !important; 
      } */
}
@media (min-width: 1050px) {
  /* .weeklySummary{
          margin-top: 76px !important;
        } */
  /* .blockWidth{
          width: 100% !important; 
        } */
}
@media (max-width: 1200px) {
  .fontSmaller {
    font-size: 14px;
  }
}
@media (max-width: 1152px) {
  .athleteTab .weeklySummary {
    margin-top: 172px !important;
  }
}

@media (max-width: 1050px) {
  /* .headingCont{ */
  /* text-align: center; */
  /* } */
  /* .blockWidth{
    width: 125px !important; 
  } */

  .parentCont {
    flex-direction: column;
  }

  .form1 {
    order: 2;
    margin-top: 5%;
  }

  .cstm {
    margin-top: 10px;
    margin-bottom: 20px;
  }

  .slick-container {
    width: 70%;
    padding-top: 2%;
    margin: 0 auto;
  }

  .athleteTab .weeklySummary {
    margin-top: 272px !important;
  }
  .weeklySummary {
    margin-top: 172px !important;
  }

  .fontSmaller {
    font-size: 12px;
  }

  .widthSmaller {
    width: 78px !important;
  }

  .top36px {
    top: 46px !important;
  }

  /* .work-calendar{
    height: 113vh !important;
  }  */
}

@media (max-width: 950px) {
  /* .work-calendar{
  height: 127vh !important;
} 
   */
}

@media (max-width: 800px) {
  .weeklySummary {
    margin-top: 222px !important;
  }
  .profileImage {
    width: 8rem;
    height: 8rem;
    margin-left: 20px;
  }
}

@media screen and (min-width: 900px) and (max-width: 1024px) {
  .maxWidth50 {
    max-width: 370px !important;
  }
}

@media screen and (min-width: 600px) and (max-width: 900px) {
  .maxWidth50 {
    max-width: 220px !important;
  }
}

@media (min-width: 800px) {
  .css-18sywi9 {
    margin-left: 0 !important;
  }
}
/* @media (max-width: 768px) {

  .heading{
    font-size: 32px;
  }
  .css-18sywi9 {
    margin-left: 8px;
}

.form1{
  margin-top: 10%;
}

.work-calendar{
  height: 116vh !important;
} 
  
} */
.athleteTab .fc .fc-daygrid-body {
  height: unset !important;
  overflow: scroll;
}

.fc .fc-daygrid-body {
  height: 72vh !important;
  overflow: scroll;
}

.fc .fc-scrollgrid-section table {
  width: 100% !important;
}

.MuiAutocomplete-paper {
  scrollbar-color: #e67e22 transparent !important;
}

.groupStepOne {
  scrollbar-color: #eee8e8 transparent !important;
  scrollbar-width: thin;
}

@media (max-width: 1240px) {
  .work-calendar {
    height: 100vh !important;
  }

  .workSidebar {
    height: 100vh !important;
  }

  .blockWidth {
    width: 12vw !important;
  }
}

.athleteTab .fc .fc-daygrid-body {
  height: unset !important;
  overflow: scroll;
}
.checkScrollBar {
  padding: 0px 4px 2px 4px;
  overflow-x: auto;
  max-width: 140px;
  scroll-behavior: smooth;
  scrollbar-color: red;
  /* overflow: scroll; */
  scrollbar-color: auto;
  &::-webkit-scrollbar {
    height: 3px;
    background-color: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background-color: #e67e22;
    border-radius: 3px;
  }
}
.fc-direction-ltr .fc-toolbar > * > :not(:first-child) {
  color: gray !important;
}
.css-mvmxd9-MuiTypography-root-MuiPickersToolbarText-root {
  background-color: white !important;
}
.css-ihhycx-MuiTimePickerToolbar-amPmSelection .MuiTimePickerToolbar-ampmLabel {
  background-color: white !important;
}
.css-hlj6pa-MuiDialogActions-root > :not(:first-of-type) {
  color: white;
}
.css-1e6y48t-MuiButtonBase-root-MuiButton-root {
  color: white !important;
}

.assement-scroll {
  overflow-y: auto;
  max-height: 60vh;
  scroll-behavior: smooth;
  scrollbar-color: red;
  /* overflow: scroll; */
  scrollbar-color: auto;
  &::-webkit-scrollbar {
    height: 3px;
    background-color: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background-color: #e67e22;
    border-radius: 3px;
  }
}
.onboarding-flow-description {
  overflow-y: auto;
  max-height: 42vh;
  scroll-behavior: smooth;
  scrollbar-color: red;
  /* overflow: scroll; */
  scrollbar-color: auto;
  &::-webkit-scrollbar {
    height: 3px;
    background-color: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background-color: #e67e22;
    border-radius: 3px;
  }
}
.css-11lq3yg-MuiGrid-root {
  /* height: 80vh !important; */
  overflow: scroll !important;
}
.css-92xg7j {
  height: 100vh !important;
  overflow: scroll !important;
}
.jodit-toolbar {
  display: none !important;
}

.jodit-status-bar {
  display: none !important;
}
.jodit-container jodit jodit_theme_default {
  display: none;
}
.rdw-editor-main {
  max-height: 200px !important;
  overflow-y: scroll !important;
  padding: 10px;
  scrollbar-color: auto;
  &::-webkit-scrollbar {
    height: 3px;
    background-color: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background-color: #e67e22;
    border-radius: 3px;
  }
}
.draftJsLink {
  color: blue !important;
  text-decoration: underline !important;
}
.rdw-link-decorator-wrapper {
  color: blue !important;
  text-decoration: underline !important;
}
.rdw-editor-wrapper .public-DraftStyleDefault-ul,
.rdw-editor-wrapper .public-DraftStyleDefault-ol {
  margin-left: 1.5em; /* Adjust this value to align bullets */
  padding-left: 1.5em; /* Adjust this value to align bullets */
}

.rdw-editor-wrapper .public-DraftStyleDefault-ol {
  list-style-type: decimal; /* Ensure ordered lists use decimal numbering */
}

.rdw-editor-wrapper .public-DraftStyleDefault-ul {
  list-style-type: disc; /* Ensure unordered lists use disc bullets */
}
