{"name": "yoska-admin", "version": "0.1.0", "private": true, "dependencies": {"@chatscope/chat-ui-kit-react": "^2.0.3", "@emotion/styled": "^11.11.0", "@fullcalendar/daygrid": "^5.11.3", "@fullcalendar/interaction": "^5.11.3", "@fullcalendar/list": "^6.1.9", "@fullcalendar/react": "^5.11.2", "@fullcalendar/timegrid": "^5.11.3", "@mantine/core": "^5.5.4", "@mantine/dates": "^5.5.4", "@mantine/hooks": "^5.5.4", "@mantine/rte": "^5.5.4", "@mui/icons-material": "^5.14.7", "@mui/lab": "^5.0.0-alpha.143", "@mui/material": "^5.14.7", "@mui/x-date-pickers": "^6.20.2", "@radix-ui/react-avatar": "^1.1.9", "@radix-ui/react-collapsible": "^1.1.10", "@radix-ui/react-dialog": "^1.1.13", "@radix-ui/react-select": "^2.1.4", "@radix-ui/react-tabs": "^1.1.11", "@reduxjs/toolkit": "^1.9.5", "@tabler/icons": "^1.101.0", "@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "@tinymce/tinymce-react": "^5.1.1", "antd": "^5.3.2", "aos": "^2.3.4", "axios": "^1.5.0", "chart.js": "^4.4.0", "chartjs-adapter-date-fns": "^3.0.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "dayjs": "^1.11.7", "draft-js": "^0.11.7", "draft-js-code": "^0.3.0", "draftjs-to-html": "^0.9.1", "emoji-picker-react": "^4.5.2", "file-saver": "^2.0.5", "firebase": "^11.6.1", "formik": "^2.4.5", "html-to-draftjs": "^1.5.0", "html-to-text": "^9.0.5", "html2canvas": "^1.4.1", "html2pdf.js": "^0.10.2", "js-cookie": "^3.0.5", "jspdf": "^3.0.1", "lodash": "^4.17.21", "lucide-react": "^0.454.0", "moment": "^2.29.4", "moment-timezone": "^0.5.45", "pubnub": "^8.2.10", "react": "^18.3.1", "react-beautiful-dnd": "^13.1.1", "react-big-calendar": "^1.8.2", "react-chartjs-2": "^5.2.0", "react-datepicker": "^4.16.0", "react-dnd": "^10.0.2", "react-dnd-html5-backend": "^10.0.2", "react-dom": "^18.3.1", "react-draft-wysiwyg": "^1.15.0", "react-icons": "^4.11.0", "react-linkify": "^1.0.0-alpha", "react-modal": "^3.16.1", "react-modal-image": "^2.6.0", "react-phone-input-2": "^2.15.1", "react-phone-number-input": "^3.3.8", "react-player": "^2.13.0", "react-quill": "^2.0.0", "react-redux": "^8.1.2", "react-scripts": "^5.0.1", "react-search-autocomplete": "^8.3.1", "react-select": "^5.8.0", "react-slick": "^0.30.2", "react-toastify": "^9.1.1", "react-webcam": "^7.1.1", "recharts": "^2.15.3", "shortid": "^2.2.16", "slate": "^0.103.0", "slate-react": "^0.108.0", "slick-carousel": "^1.8.1", "sweetalert2": "^11.6.13", "sweetalert2-react-content": "^5.0.7", "tailwind-merge": "^2.6.0", "tinymce": "^7.3.0", "web-vitals": "^2.1.4", "xlsx": "^0.18.5", "yup": "^1.3.2"}, "scripts": {"start": "doppler run -- react-scripts --max_old_space_size=8192 start", "build": "doppler run -- react-scripts --max_old_space_size=4096 build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@tailwindcss/forms": "^0.5.3", "cross-env": "^7.0.3", "react-router-dom": "^6.3.0", "tailwindcss": "^3.2.6"}}