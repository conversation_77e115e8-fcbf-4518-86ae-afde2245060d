import { Box, Toolbar, Typography } from "@mui/material";
import React from "react";
import KeyboardBackspaceIcon from "@mui/icons-material/KeyboardBackspace";
import "../../styles/navbar.css";
import { useNavigate } from "react-router-dom";
import InfoIcon from "@mui/icons-material/Info";
import { clearUsersToCreateGroup } from "../../redux/action/usersAction";
import { useDispatch } from "react-redux";

export default function NewGroupStepTwoNav({ setOpenPageOne }) {
  const dispatch = useDispatch();
  const handleGoTo = async () => {
    await dispatch(clearUsersToCreateGroup());
    setOpenPageOne("stepOne")
  };

  return (
    <>
      <Toolbar className="appBar">
        <Box sx={{ display: "flex",cursor:"pointer" }}>
        <KeyboardBackspaceIcon onClick={handleGoTo} sx={{ margin: "auto",cursor:"pointer" }} />
          <Box>
            <Typography
              className="chatTitle"
              style={{ paddingLeft: "1rem", fontSize: "16px" }}
            >
              Create New Group :{" "}
              <span style={{ color: "#707070" }}>Step 2/2</span>
            </Typography>
            <Typography
              className="select-buddies-text"
              style={{ marginTop: "-1rem" }}
            >
              Add Group Name & Image
            </Typography>
          </Box>
        </Box>
        <Box>
          <InfoIcon />
        </Box>
      </Toolbar>
    </>
  );
}
