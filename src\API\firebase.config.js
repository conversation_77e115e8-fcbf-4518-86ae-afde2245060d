// import firebase from "firebase/app";
// import "firebase/auth";
// import "firebase/firestore";
// import "firebase/storage";
// import "firebase/functions";
// import axios from "axios";
import { initializeApp } from "firebase/app";
import { providerSignin, providerSignup } from "./api-endpoint";
import {
  getAuth,
  RecaptchaVerifier,
  PhoneAuthProvider,
  signInWithPopup,
  GoogleAuthProvider,
  OAuthProvider
} from "firebase/auth";
import { getFirestore } from "firebase/firestore";
import axios from "axios";
import { getAnalytics } from "firebase/analytics";
import { getStorage } from "firebase/storage";
import {
  get,
  push,
  getDatabase,
  set,
  update,
  ref,
  child,
  runTransaction,
  serverTimestamp,
} from "firebase/database";
import { storeUserCreds } from "../pages/Chat/redux/action/authAction";
import { showError } from "../components/Messages";

const firebaseConfig = {
  apiKey: process.env.REACT_APP_FIREBASE_API_KEY,
  authDomain: process.env.REACT_APP_FIREBASE_AUTH_DOMAIN,
  projectId: process.env.REACT_APP_FIREBASE_PROJECT_ID,
  storageBucket: process.env.REACT_APP_FIREBASE_STORAGE_BUCKET,
  messagingSenderId: process.env.REACT_APP_FIREBASE_MESSAGING_SENDER_ID,
  appId: process.env.REACT_APP_FIREBASE_APP_ID
};

const app = initializeApp(firebaseConfig);
export const auth = getAuth(app);
export const firestore = getFirestore(app);

const provider = new GoogleAuthProvider();
provider.setCustomParameters({ prompt: "select_account" });

const appleProvider = new OAuthProvider('apple.com');


const db = getDatabase(app);
const storage = getStorage();

export {
  provider,
  db,
  storage,
  ref,
  get,
  push,
  set,
  update,
  child,
  runTransaction,
  serverTimestamp,
};

export const SignupWithGoogle = async (dispatch) => {
  try {
    // Sign in with Google Popup
    const userCredential = await signInWithPopup(auth, provider);
    const user = userCredential.user;
    const { uid, displayName, email, photoURL } = user;
    WriteUserData(uid, displayName, email, photoURL, dispatch);

    // Get the user's ID token
    const idToken = await user.getIdToken();

    // Set the ID token in localStorage
    localStorage.setItem("token", idToken);


    // Call the Firebase Function to perform signup
    const response = await providerSignup({
      uid: user.uid,
      fullName: user.displayName,
      email: user.email,
    });

    localStorage.setItem("fullname", user.displayName);
    localStorage.setItem("email", user.email);

    console.log(response);
    if (
      response.status
    ) {
      localStorage.setItem("token", `Bearer ${response.token}`);
      localStorage.setItem("userId", response.userId);
      localStorage.setItem("roleID", response?.user?.role_id)

      return { success: true, message: response?.message };
    } else {
      return { success: false, message: response?.message };
    }
  } catch (error) {
    console.error("Error signing in with Google:", error);

    let errorMessage = error.message;

    if (error.code === "auth/cancelled-popup-request") {
      // Handle the case where the user closed the Google popup
      // errorMessage = "Google sign-in was cancelled.";
    }

    return { success: false };
  }
};
export const SigninWithGoogle = async (dispatch) => {
  try {
    // Sign in with Google Popup
    const userCredential = await signInWithPopup(auth, provider);
    const user = userCredential.user;
    console.log("user", user);
    const { uid, displayName, email, photoURL } = user;
    WriteUserData(uid, displayName, email, photoURL, dispatch);

    // Get the user's ID token
    const idToken = await user.getIdToken();

    // Set the ID token in localStorage
    localStorage.setItem("token", idToken);

    // Call the Firebase Function to perform signup
    const response = await providerSignin({
      uid: user.uid,
      fullName: user.displayName,
      email: user.email,
    });

    return response;
  } catch (error) {
    let errorMessage = error.message;
    if (error.code === "auth/cancelled-popup-request") {
      showError("Google signin popup was cancelled");
    }
    showError(errorMessage);
  }
};


export async function WriteUserData(
  uid,
  displayName,
  email,
  photoURL,
  dispatch
) {
  console.log("displayName", uid, displayName, email);
  const db = getDatabase();
  const userRef = ref(db, "users/" + uid);
  try {
    const snapshot = await get(userRef);
    if (snapshot.exists()) {
      // User already signed up
      // Handle the case or dispatch an action if needed
      dispatch(storeUserCreds(uid, displayName, email, photoURL));
    } else {
      await set(userRef, {
        uid,
        displayName,
        email,
        photoURL,
        onlineStatus: {
          isOnline: false,
        },
      });
      // User signed up successfully
      dispatch(storeUserCreds(uid, displayName, email, photoURL));
    }
  } catch (error) {
    console.error("Error:", error);
    // Handle the error, e.g., show an error message
  }
}

export const sendOTPToUser = async (phone, token) => {
  try {
    // Replace with your Firebase API key
    const apiKey = "AIzaSyAtuR3l6wl5ugE102LlxTBIsAk_1fYT3fE"; // Replace with your actual API key

    // URL for sending the verification code
    const url = `https://www.googleapis.com/identitytoolkit/v3/relyingparty/sendVerificationCode?key=${apiKey}`;

    // Data to send in the request
    const requestData = {
      phoneNumber: phone, // Replace with the phone number to send the code to
      recaptchaToken: token, // Additional parameters as needed for your use case
    };

    // Make the POST request using async/await
    const response = await axios.post(url, requestData);

    // Handle the response
    console.log("Verification code sent successfully:", response.data);
    return { success: true, data: response.data.sessionInfo };
  } catch (error) {
    // Handle errors
    console.error("Error sending verification code:", error);
    let errorMessage = error.message;

    if (error.response) {
      // If the response contains an error message, use it
      errorMessage = error.response.data.error.message;
    }

    return { success: false, message: errorMessage };
  }
};

export const verifyOTPAndMerge = async (sessionInfo, otp) => {
  try {
    // Replace with your Firebase API key
    const apiKey = "AIzaSyAtuR3l6wl5ugE102LlxTBIsAk_1fYT3fE"; // Replace with your actual API key

    // URL for verifying the OTP
    const url = `https://www.googleapis.com/identitytoolkit/v3/relyingparty/verifyPhoneNumber?key=${apiKey}`;

    // Data to send in the request
    const requestData = {
      sessionInfo: sessionInfo,
      code: otp, // Replace with the OTP code
    };

    // Make the POST request using async/await
    const response = await axios.post(url, requestData);

    // Handle the response
    console.log("OTP verified successfully:", response.data);
    return { success: true, data: response.data, message: "" };
  } catch (error) {
    // Handle errors
    console.error("Error verifying OTP:", error);
    let errorMessage = error.message;

    if (error.response) {
      // If the response contains an error message, use it
      errorMessage = error.response.data.error.message;
    }

    return { success: false, message: errorMessage };
  }
};

const appleProviderSigninData = async () => {
  try {
    appleProvider.addScope('email');
    appleProvider.addScope('name');
    // sign-in popup
    const result = await signInWithPopup(auth, appleProvider);
    // Apple credential and user info
    const credential = OAuthProvider.credentialFromResult(result);
    const user = result.user;
    return user;
  } catch (e) {
    console.error("Apple Login Error:", e);
  }
}

export const AppleSignup = async (dispatch) => {
  try {
    const user = await appleProviderSigninData();
    const { uid, displayName, email, photoURL } = user;
    WriteUserData(uid, displayName, email, photoURL, dispatch);

    // Get the user's ID token
    const idToken = await user.getIdToken();

    // Set the ID token in localStorage
    localStorage.setItem("token", idToken);

    // Call the Firebase Function to perform signup
    const response = await providerSignup({
      uid: user.uid,
      fullName: user.displayName,
      email: user.email,
    });
    return response;
  } catch (e) {
    let errorMessage = e.message;
    if (e.code === "auth/cancelled-popup-request") {
      showError("Google signin popup was cancelled");
    }
    showError(errorMessage);
  }
}

export const AppleSignin = async (dispatch) => {
  try {
    const user = await appleProviderSigninData();
    const { uid, displayName, email, photoURL } = user;
    WriteUserData(uid, displayName, email, photoURL, dispatch);

    // Get the user's ID token
    const idToken = await user.getIdToken();

    // Set the ID token in localStorage
    localStorage.setItem("token", idToken);

    // Call the Firebase Function to perform signup
    const response = await providerSignin({
      uid: user.uid,
      fullName: user.displayName,
      email: user.email,
    });

    localStorage.setItem("fullname", user.displayName);
    localStorage.setItem("email", user.email);

    return response;

  } catch (e) {
    console.error("Apple Signin Error:", e);
  }
}




