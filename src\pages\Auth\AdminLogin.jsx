// ** React Imports
import React, { useEffect, useState } from "react";

// ** MUI Components
import Box from "@mui/material/Box";
import Checkbox from "@mui/material/Checkbox";
import TextField from "@mui/material/TextField";
import InputLabel from "@mui/material/InputLabel";
import Typography from "@mui/material/Typography";
import IconButton from "@mui/material/IconButton";
import CardContent from "@mui/material/CardContent";
import FormControl from "@mui/material/FormControl";
import OutlinedInput from "@mui/material/OutlinedInput";
import { styled } from "@mui/material/styles";
import MuiCard from "@mui/material/Card";
import InputAdornment from "@mui/material/InputAdornment";
import MuiFormControlLabel from "@mui/material/FormControlLabel";
import LoadingButton from "@mui/lab/LoadingButton";
import GoogleImage from "../../Images/GoogleImage.png";

// ** Icons Imports
import VisibilityIcon from "@mui/icons-material/Visibility";
import VisibilityOffIcon from "@mui/icons-material/VisibilityOff";
import { useNavigate } from "react-router-dom";
import { SigninWithGoogle } from "../../API/firebase.config";
import Swal from "sweetalert2";
import { Login } from "../../API/api-endpoint";
import Background from "../../Images/Background.png";
import { useDispatch } from "react-redux";
import { Modal } from "antd";
import { Divider, FormLabel, Grid } from "@mui/material";
import SlickCarousel from "../SlickCarousel";


// ** Styled Components
const Card = styled(MuiCard)(({ theme }) => ({
  [theme.breakpoints.up("sm")]: { width: "28rem" },
}));

const FormControlLabel = styled(MuiFormControlLabel)(({ theme }) => ({
  "& .MuiFormControlLabel-label": {
    fontSize: "0.875rem",
    color: theme.palette.text.secondary,
  },
}));

const AdminLogin = () => {
  const { confirm } = Modal;
  let isVerifyOTP = localStorage.getItem("isOTP")

  const [values, setValues] = useState({
    email: "",
    password: "",
    showPassword: false,
  });
  useEffect(() => {
    localStorage.removeItem("userDetails")
    localStorage.removeItem("userSubsription")
  })
  const [isLoading, setIsLoading] = useState(false);
  const [isGoogleSigninLoading, setIsGoogleSigninLoading] = useState(false);

  const navigate = useNavigate();
  const dispatch = useDispatch();

  const handleChange = (prop) => (event) => {
    setValues({ ...values, [prop]: event.target.value });
  };

  const handleClickShowPassword = () => {
    setValues({ ...values, showPassword: !values.showPassword });
  };

  const handleGoogleSignin = async () => {
    setIsGoogleSigninLoading(true);
    let result = await SigninWithGoogle(dispatch);
    if (result.success == true) {
      Swal.fire({
        title: "Success",
        text: result.message,
        icon: "success",
      });
      navigate("/workout")
      console.log("result", result);

    } else {
      Swal.fire({
        title: "Error!!",
        text: result.message,
        icon: "error",
      });
    }
    setIsGoogleSigninLoading(false);
  };
  console.log("values", values);
  const onSubmit = async () => {
    if (values.email && values.password) {
      setIsLoading(true);
      const result = await Login({
        email: values.email,
        password: values.password,
      }, dispatch);

      console.log("result", result);
      if (result?.user?.role_id == 1) {
        showSuccess("Login successful", "/workout");
        setIsLoading(false)
      } else {
        showErrorAndRedirect(result.message);
        setIsLoading(false)
      }
    } else {
      Swal.fire({
        title: "Error",
        text: "Please enter all details",
        icon: "error",
      });
    }
  };
  const showErrorAndRedirect = (message, path) => {
    Swal.fire({
      title: "Error",
      text: message,
      icon: "error",
    });
    setIsLoading(false);
    localStorage.setItem("email", values.email);
    navigate(path);
  };

  const showSuccess = (message, path) => {
    Swal.fire({
      title: "Success",
      text: message,
      icon: "success",
    });
    setIsLoading(false);
    navigate(path);
  };

  return (
    <Box
      className="content-center"
      style={{
        width: "100vw",
        padding: "40px",
        height: "100vh", // 100% of the viewport height
      }}
    >
      <Grid container>
        <Grid container spacing={2}>
          <Grid item xs={12} sm={7} style={{
            display: "flex",
            // justifyContent: "center",
            alignItems:"center",
            padding: "20px"
          }}>
            <div style={{ width: "100%" }}>
              <div>
              <div className="headingCont">
              <Typography variant="h4" className="heading">Admin<span style={{color:"orange"}}> Login</span></Typography>{" "}
            </div>
                <form autoComplete="off" onSubmit={onSubmit}>
                  <Grid container spacing={2}>
                    <Grid item xs={12} sm={12}>
                      <FormLabel sx={{ fontSize: "14px", fontWeight: "600", color: "background: rgba(13, 20, 28, 1)", marginBottom: "10px" }} >Email</FormLabel>
                      <TextField
                        autoSave="false" size="small"
                        autoFocus={false}
                        fullWidth
                        id="email"
                        sx={{ marginBottom: 4 }}
                        onChange={handleChange("email")}
                        type="email"
                        required
                        InputProps={{ autoComplete: 'off' }}
                      />
                    </Grid>
                  </Grid>
                  <Grid item xs={12} sm={12}>
                    <FormControl fullWidth>
                      <FormLabel htmlFor="auth-login-password">Password*</FormLabel>
                      <OutlinedInput
                        size="small"
                        value={values.password}
                        id="auth-login-password"
                        onChange={handleChange("password")}
                        required
                        type={values.showPassword ? "text" : "password"}
                        endAdornment={
                          <InputAdornment style={{ marginRight: "15px" }} position="end">
                            <IconButton
                              edge="end"
                              onClick={handleClickShowPassword}
                              aria-label="toggle password visibility"
                            >
                              {values.showPassword ? (
                                <VisibilityIcon />
                              ) : (
                                <VisibilityOffIcon />
                              )}
                            </IconButton>
                          </InputAdornment>
                        }
                      />
                    </FormControl>
                  </Grid>

                  <Box
                    sx={{
                      mb: 2,
                      display: "flex",
                      alignItems: "center",
                      flexWrap: "wrap",
                      justifyContent: "space-between",
                      fontWeight: '600',
                      fontSize: '14px',
                    }}
                  >
                    <a onClick={() => navigate("/forgot-password")} className="cursor-pointer text-[black]">
                      Forgot Password?
                    </a>
                  </Box>
                  <Grid container spacing={2}>
                    <Grid item xs={12} sm={6}>
                      <LoadingButton
                        fullWidth
                        size="small"
                        variant="contained"
                        sx={{
                          fontSize: "15px",
                          marginBottom: 2,
                          // color: "white",
                          // backgroundColor: "rgb(145, 85, 253)",
                          color: "white",
                          backgroundColor: "#FFA654",
                          '&:hover': {
                            backgroundColor: '#FFA654', // Change to your desired hover color
                          },
                        }}
                        onClick={() => onSubmit()}
                        loading={isLoading}
                        loadingPosition="start"
                      >
                        Login
                      </LoadingButton>
                    </Grid>
                    <Grid item xs={12} sm={6}>
                      <LoadingButton
                        fullWidth
                        size="small"
                        variant="contained"
                        sx={{
                          marginBottom: 2,
                          // color: "white",
                          // backgroundColor: "rgb(145, 85, 253)",
                          color: "white",
                          backgroundColor: "#FFA654",
                          '&:hover': {
                            backgroundColor: '#FFA654', // Change to your desired hover color
                          },
                        }}
                        onClick={() => handleGoogleSignin()}
                        loading={isGoogleSigninLoading}
                        loadingPosition="start"
                        startIcon={
                          <span style={{ display: 'flex', alignItems: 'center', fontSize: "14px", justifyContent: "center", textAlign: "center" }}>
                            <img src={GoogleImage} alt="Image" style={{ marginRight: '8px' }} />
                            LOG in with Google
                          </span>
                        }
                      >
                      </LoadingButton>
                    </Grid>
                  </Grid>
                  <Typography  sx={{
                    fontWeight: '500',
                    fontSize: '14px',
                  }} variant="h6" component="div">Don't have an account yet?<span>
                  <a onClick={() => navigate("/sign-up")} className="cursor-pointer text-[#E67E22]"> Sign up</a>
                </span></Typography>
                <Typography  sx={{
                  mb: 2,
                  fontWeight: '500',
                  fontSize: '14px',
                }} variant="h6" component="div"><span>
                <a onClick={() => navigate("/")} className="cursor-pointer text-[#E67E22]">Login as an user</a>
              </span></Typography>
                </form>
              </div>
            </div>
          </Grid>
          <Grid item xs={12} sm={5}  >
            <SlickCarousel />
          </Grid>
        </Grid>
      </Grid>
    </Box>
  )
};

export default AdminLogin;
