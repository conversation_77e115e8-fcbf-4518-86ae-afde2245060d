export const generateGrid = (startingNumber, endingNumber, weekNumber) => {
  let newArray = [];
  const numColumns = 8;
  const numRows = 12;
  const itemsPerPage = numColumns * numRows;
  let gridcounter = startingNumber;

  for (let row = 0; row < numRows; row++) {
    const newColArray = [];
    for (let col = 0; col < numColumns; col++) {
      newColArray.push({
        type: "COLUMN",
        id: `week-${weekNumber}-row-${row + 1}-count-${gridcounter}-col-${col}`,
        children: [],
        counter: gridcounter,
      });

      gridcounter++;
      if (gridcounter > endingNumber) {
        break;
      }
    }
    newArray.push({
      type: "ROW",
      id: `week-${weekNumber}-row-${row + 1}`,
      children: newColArray,
    });
    weekNumber++;
    if (gridcounter > endingNumber) {
      break;
    }
  }

  const initialData = {
    layout: newArray,
    components: {},
  };
  console.log(initialData);
  return initialData;
};
