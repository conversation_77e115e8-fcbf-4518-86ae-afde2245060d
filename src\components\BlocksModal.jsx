import dayjs from "dayjs";
import React, { useContext, useEffect, useState } from "react";
import { useParams } from "react-router-dom";
import { ReactSearchAutocomplete } from "react-search-autocomplete";
import GlobalContext from "../context/GlobalContext";
import {
  MenuItem,
  Select,
  Stack,
  Button,
  Checkbox,
  FormControlLabel,
} from "@mui/material";

const blocksData = [
  {
    id: 1,
    name: "checkbox1",
    description: "Description1",
  },
  { id: 2, name: "checkbox2", description: "Description2" },
  {
    id: 3,
    name: "checkbox3",
    description: "Description3",
  },
  {
    id: 4,
    name: "checkbox4",
    description: "Description4",
  },
  {
    id: 5,
    name: "checkbox5",
    description: "Description5",
  },
];

const BlocksModal = ({ setSelectedBlocks, blocksList }) => {
  const { setSelectedEvent, setShowBlockModal, daySelected } =
    useContext(GlobalContext);

  console.log("blocksList", blocksList);

  const [items, setItem] = useState([]);
  const [formValue, setFormValue] = useState({});

  const { id } = useParams();

  // useEffect(() => {
  //   let apiUrl = `https://app.yoska.in/kona-coach/api/coaches/340/workoutLibrary/?fullDetails=true`;
  //   const bearerToken = "qmmvofrilpsj59rctkn0ivseuil1jji5";

  //   fetch(apiUrl, {
  //     method: "GET",
  //     headers: {
  //       Authorization: `Bearer ${bearerToken}`,
  //     },
  //   })
  //     .then((response) => response.json())
  //     .then((data) => {
  //       data && console.log("Workout library", data);
  //       // eslint-disable-next-line array-callback-return
  //       data.map((item) => {
  //         item.workouts.map((workouts) => {
  //           setItem((current) => [
  //             ...current,
  //             {
  //               id: workouts.id,
  //               name: workouts.name,
  //               description: workouts.description,
  //               distance: workouts.distance,
  //               duration: workouts.duration,
  //             },
  //           ]);
  //           return true;
  //         });
  //       });
  //     })
  //     .catch((error) => {
  //       console.error(error);
  //     });
  // }, []);

  useEffect(() => {
    console.log(items);
  }, [items]);

  const handleOnSearch = (string, results) => {
    // onSearch will have as the first callback parameter
    // the string searched and for the second the results.
    console.log(string, results);
  };

  const handleOnHover = (result) => {
    // the item hovered
    console.log(result);
  };

  const handleOnSelect = (item) => {
    // the item selected
    console.log(item);

    const bearerToken = "qmmvofrilpsj59rctkn0ivseuil1jji5";

    const date = dayjs(daySelected).format("YYYY-MM-DD");

    const calendarEvent = {
      action: "saved",
      activityId: 7,
      actualDistance: 0,
      actualDuration: "0:0:0",
      createdByUserId: 138,
      createdByUserType: "coach",
      description: `${item.description}`,
      hideWorkout: false,
      isreviewed: false,
      moodMessageTemplateId: 0,
      name: item.name,
      plannedDistance: `${item.distance}`,
      plannedDuration: `${item.duration}`,
      plannedUOMId: 8,
      savedBy: "coach",
      skipWorkout: false,
      unitOfMeasureId: 8,
    };

    fetch(
      `https://app.yoska.in/kona-coach/api/athletes/${id}/plan/items?date=${date}`,
      {
        method: "POST",
        headers: {
          Authorization: `Bearer ${bearerToken}`,
          Accept: "application/json",
          "Content-Type": "application/json",
        },
        body: JSON.stringify(calendarEvent),
      }
    )
      .then((response) => {
        if (response.status === 200) {
          window.location.reload();
        } else {
          alert("Please fill up all the fields");
        }
        return response.json();
      })
      .then((data) => {
        console.log("Workout Created", data);
      })
      .catch((error) => {
        console.error(error);
      });
  };

  const handleOnFocus = () => {
    console.log("Focused");
  };

  const formatResult = (item) => {
    return (
      <>
        <span style={{ display: "block", textAlign: "left" }}>{item.name}</span>
        <span style={{ display: "block", textAlign: "left" }}>
          {item.duration} | {item.distance}
        </span>
      </>
    );
  };

  const handleChange = (event) => {
    const newName = event.target.name;
    if (event.target.type === "checkbox") {
      const value = event.target.checked;
      setFormValue({ ...formValue, [newName]: value });
    } else {
      const value = event.target.value;
      setFormValue({ ...formValue, [newName]: value });
    }
  };

  const handleSubmit = () => {
    const trueElements = [];

    for (const key in formValue) {
      if (formValue.hasOwnProperty(key) && formValue[key] === true) {
        const matchingBlock = blocksList.find(
          (block) => block["workout-custom-block-name"] === key
        );
        if (matchingBlock) {
          trueElements.push(matchingBlock);
        }
      }
    }
    setSelectedBlocks(trueElements);
    setShowBlockModal(false);
  };

  return (
    <div className="h-screen w-full fixed left-0 -top-16 flex justify-center items-center overflow-y-scroll">
      <form className="bg-white rounded-lg shadow-2xl w-2/4">
        <div className="bg-gray-100 px-4 py-3 flex justify-between items-center">
          <span className="material-icons-outlined text-gray-400">
            drag_handle
          </span>
          <span>Add Blocks</span>
          <div className="flex items-center gap-3">
            <button
              onClick={() => {
                setShowBlockModal(false);
                setSelectedEvent();
              }}
            >
              <span className="material-icons-outlined text-gray-400">
                close
              </span>
            </button>
          </div>
        </div>
        <div className="bg-gray-100 px-4 py-3 items-center">
          <div style={{ width: "100%" }}>
            <ReactSearchAutocomplete
              items={items}
              onSearch={handleOnSearch}
              onHover={handleOnHover}
              onSelect={handleOnSelect}
              onFocus={handleOnFocus}
              autoFocus
              formatResult={formatResult}
              placeholder="Search workouts"
            />
          </div>
          <div
            style={{
              display: "flex",
              justifyContent: "space-between",
            }}
          >
            <div className="walkjog-form-group mr-1 mt-5">
              <label>Activity</label>
              <Select
                labelId="demo-simple-select-label"
                id="demo-simple-select"
                className="select"
                name="activity"
                label="select"
                value={formValue?.activity}
                onChange={(e) => handleChange(e)}
              >
                <MenuItem value={"Running"}>Running</MenuItem>
                <MenuItem value={"Swimming"}>Swimming</MenuItem>
                <MenuItem value={"Walking"}>Walking</MenuItem>
              </Select>
            </div>
            <div className="walkjog-form-group mt-5">
              <label>Sub Activity</label>
              <Select
                labelId="demo-simple-select-label"
                id="demo-simple-select"
                className="select"
                name="sub_activity"
                value={formValue?.sub_activity}
                onChange={(e) => handleChange(e)}
                label="select"
              >
                <MenuItem value={"Running"}>Running</MenuItem>
                <MenuItem value={"Swimming"}>Swimming</MenuItem>
                <MenuItem value={"Walking"}>Walking</MenuItem>
              </Select>
            </div>
          </div>
        </div>
        <Stack
          gap={1}
          direction="column"
          style={{ marginLeft: "5%", overflowY: "scroll", maxHeight: "300px" }}
        >
          {blocksList &&
            blocksList.map((item) => {
              return (
                <FormControlLabel
                  key={item.id}
                  value="end"
                  control={
                    <Checkbox
                      name={item["workout-custom-block-name"]}
                      value={item["status"] == 1 ? true : false} //item['status'] == 1 ? true : false
                      onChange={(e) => handleChange(e)}
                    />
                  }
                  label={item["workout-custom-block-name"]}
                  labelPlacement="End"
                />
              );
            })}
        </Stack>
        <div
          style={{
            display: "flex",
            justifyContent: "end",
            padding: "2%",
            backgroundColor: "#D4D4D4",
          }}
        >
          <Button variant="contained" onClick={handleSubmit}>
            Save
          </Button>
        </div>
      </form>
    </div>
  );
};

export default BlocksModal;
