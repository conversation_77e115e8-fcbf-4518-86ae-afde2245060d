import { IconPlus, IconTrash } from '@tabler/icons'
import { Input, Modal, Select } from 'antd'
import React, { Component } from 'react'
import Switch from '../components/Switch'
import { DragDropContext, Droppable, Draggable } from 'react-beautiful-dnd'

class DetailsPage extends Component {
  constructor(props) {
    super(props)

    this.state = {
      isModalOpen: false,
      optionList: [{ option: '' }],
      topic: [
        {
          id: 1,
          title: 'Prefix (Mr., Mrs, etc.)'
        },
        {
          id: 2,
          title: 'Suffix'
        },
        {
          id: 3,
          title: 'Gender'
        },
        {
          id: 4,
          title: 'Home phone'
        },
        {
          id: 5,
          title: 'Cell phone'
        },
        {
          id: 6,
          title: 'Home address'
        },
        {
          id: 7,
          title: 'Shipping address'
        },
        {
          id: 8,
          title: 'Website'
        },
        {
          id: 9,
          title: 'Blog'
        },
        {
          id: 10,
          title: 'Job title'
        },
        {
          id: 11,
          title: 'Company / Organization'
        },
        {
          id: 12,
          title: 'Work address'
        },
        {
          id: 13,
          title: 'Work phone'
        }
      ]
    }

    this.showModal = this.showModal.bind(this)
    this.handleOk = this.handleOk.bind(this)
    this.handleCancel = this.handleCancel.bind(this)
    this.handleOptionChange = this.handleOptionChange.bind(this)
    this.handleOptionAdd = this.handleOptionAdd.bind(this)
    this.showhandleOptionRemoveModal = this.handleOptionRemove.bind(this)
    this.showModal = this.showModal.bind(this)
    this.onDragEnd = this.onDragEnd.bind(this)
  }

  showModal() {
    this.setState({
      isModalOpen: true
    })
  }

  handleOk() {
    this.setState({
      isModalOpen: false
    })
  }

  handleCancel() {
    this.setState({
      isModalOpen: false
    })
  }

  handleChange(value) {
    console.log(`selected ${value}`)
  }

  handleOptionChange(e, index) {
    const { name, value } = e.target
    const list = [...this.state.optionList]
    list[index][name] = value
    this.setState({
      optionList: list
    })
  }

  handleOptionAdd() {
    this.setState({
      optionList: [...this.state.optionList, { option: '' }]
    })
  }

  handleOptionRemove(index) {
    const list = [...this.state.optionList]
    list.splice(index, 1)
    console.log(list.splice(index, -1))
    this.setState({
      optionList: list
    })
  }

  onDragEnd(result) {
    if (!result.destination) return

    const items = Array.from(this.state.optionList)
    const [reorderedItem] = items.splice(result.source.index, 1)
    items.splice(result.destination.index, 0, reorderedItem)

    this.setState({
      optionList: items
    })
  }

  render() {
    return (
      <>
        <main>
          <div className='container mx-auto p-5 flex flex-col items-start gap-y-6'>
            {/* heading */}
            <div>
              <h1>
                We collect <span className='font-medium'>first name</span>,{' '}
                <span className='font-medium'>last name</span> and{' '}
                <span className='font-medium'>email</span> by default{' '}
              </h1>
            </div>
            {/* table */}
            <div className='w-full'>
              <div className='overflow-x-auto'>
                <div className='inline-block min-w-full align-middle'>
                  <div className='overflow-hidden '>
                    <table className='min-w-full table-fixed '>
                      <thead>
                        <tr>
                          <th
                            scope='col'
                            className='py-3 text-base font-medium tracking-wider text-left text-gray-700'
                          >
                            Details
                          </th>
                          <th
                            scope='col'
                            className='py-3 text-base font-medium tracking-wider text-left text-gray-700'
                          >
                            Include
                          </th>
                          <th
                            scope='col'
                            className='py-3 text-base font-medium tracking-wider text-left text-gray-700'
                          >
                            Required
                          </th>
                        </tr>
                      </thead>
                      <tbody className='bg-white divide-y divide-gray-200 '>
                        {this.state.topic.map((item, index) => (
                          <tr className='text-slate-600' key={index}>
                            <td className='py-4 text-sm font-medium text-slate-600 whitespace-nowrap'>
                              {item.title}
                            </td>
                            <td>
                              <Switch id={`${index}`} />
                            </td>
                            <td>
                              {item.title === 'Suffix' ? (
                                ''
                              ) : (
                                <Switch id={`${item.title}`} />
                              )}
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>
            </div>
            {/* action button */}
            <div>
              <button
                onClick={this.showModal}
                className='text-blue-600 border border-blue-600 px-6 py-3 text-sm font-medium rounded'
              >
                Add a new questions
              </button>
            </div>
          </div>
        </main>
        <Modal
          title='Add a new question'
          open={this.state.isModalOpen}
          onOk={this.handleOk}
          onCancel={this.handleCancel}
          className='mx-auto modal'
        >
          <div className='w-full flex flex-col items-start gap-y-6 my-6'>
            <div className='w-full flex flex-col items-start gap-y-2'>
              <h1 className='text-lg font-medium'>Questions</h1>
              <div className='w-full grid grid-cols-1 items-center gap-x-4 gap-y-2'>
                <div className='w-full'>
                  <Input
                    type='text'
                    name='prompt'
                    id='propmt'
                    placeholder='Question prompt'
                    className='w-full'
                  />
                </div>
                <div className='w-full'>
                  <Select
                    onChange={this.handleChange}
                    className='w-full'
                    placeholder='Question format'
                    options={[
                      { value: 'Short text', label: 'Short text' },
                      { value: 'Paragraph text', label: 'Paragraph text' },
                      { value: 'Multiple choice', label: 'Multiple choice' },
                      { value: 'Single choice', label: 'Single choice' },
                      {
                        value: 'Select from dropdown',
                        label: 'Select from dropdown'
                      },
                      {
                        value: 'Additional terms & conditions',
                        label: 'Additional terms & conditions'
                      }
                    ]}
                  />
                </div>
              </div>
            </div>
            {/* options */}
            <div className='w-full flex flex-col items-start gap-y-3'>
              {/* heading */}
              <div className='w-full flex flex-col md:flex-row md:justify-between items-center'>
                <h1 className='text-base'>Options available</h1>
                <div className='flex items-center'>
                  <p className='text-xs'>Limit quantity for each option</p>
                  <div className='scale-75'>
                    <Switch id={'options'} />
                  </div>
                </div>
              </div>
              <DragDropContext onDragEnd={this.onDragEnd}>
                <Droppable droppableId='droppable'>
                  {(provided) => (
                    <div
                      {...provided.droppableProps}
                      ref={provided.innerRef}
                      className='w-full h-40'
                    >
                      {this.state.optionList.map((item, index) => (
                        <Draggable
                          key={index}
                          draggableId={`${index}`}
                          index={index}
                        >
                          {(provided) => (
                            <div
                              ref={provided.innerRef}
                              {...provided.draggableProps}
                              {...provided.dragHandleProps}
                              className='w-full flex flex-col items-start gap-y-3 relative cursor-move my-2'
                            >
                              {provided.placeholder}
                              <div className='w-full flex space-x-4 items-center '>
                                <Input
                                  type='text'
                                  name='option'
                                  id='option'
                                  placeholder={`Option ${index}`}
                                  className='w-full ml-2'
                                  value={item.option}
                                  autoComplete='off'
                                  onChange={(e) =>
                                    this.handleOptionChange(e, index)
                                  }
                                />
                                <div
                                  className='pr-2'
                                  onClick={() => this.handleOptionRemove(index)}
                                >
                                  <IconTrash
                                    size={20}
                                    className='cursor-pointer'
                                    color='#9ca3af'
                                  />
                                </div>
                              </div>

                              <img
                                src='https://i.ibb.co/SntQyvc/menu.png'
                                alt='..'
                                width={12}
                                className='-left-2 top-2 absolute'
                              />
                              {this.state.optionList.length - 1 === index &&
                                this.state.optionList.length < 4 && (
                                  <div
                                    className='w-full flex space-x-2 items-center cursor-pointer'
                                    onClick={this.handleOptionAdd}
                                  >
                                    <IconPlus color='#2563eb' size={16} />
                                    <p className='text-blue-600'>
                                      Add another option
                                    </p>
                                  </div>
                                )}
                            </div>
                          )}
                        </Draggable>
                      ))}
                    </div>
                  )}
                </Droppable>
              </DragDropContext>
              <div className='flex items-center space-x-2 mt-2'>
                <input type='checkbox' name='terms' id='terms' />
                <p className='text-sm text-slate-700'>
                  Add a conditional sub-question
                </p>
              </div>
            </div>
            {/* optional settings */}
            <div className='w-full flex flex-col items-start gap-y-2'>
              <h1 className='text-lg font-medium'>Optional settings</h1>
              <div className='flex items-center space-x-2 mt-2'>
                <input type='checkbox' name='terms' id='terms' />
                <p className='text-sm text-slate-700'>
                  Show this questions for specific ticket types
                </p>
              </div>
              <div className='flex items-center space-x-2 mt-2'>
                <input type='checkbox' name='terms' id='terms' />
                <p className='text-sm text-slate-700'>
                  Show the attendee's answer on their order confirmation and
                  ticket
                </p>
              </div>
            </div>
          </div>
        </Modal>
      </>
    )
  }
}

export default DetailsPage
