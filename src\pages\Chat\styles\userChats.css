/* .dropdown {
    position: relative;
    display: inline-block;
  } */
  
  /* .dropdown-btn {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    outline: none;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 5px;
  } */
  
  /* .dropdown-content {
    display: none;
    position: absolute;
    background-color: #f9f9f9;
    min-width: 150px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    z-index: 1;
    border-radius: 5px;
    overflow: hidden;
  } */
  
  /* .dropdown-content a {
    color: black;
    padding: 10px 15px;
    text-decoration: none;
    display: block;
    font-size: 16px;
  } */
  
  /* .dropdown-content a:hover {
    background-color: #ddd;
  } */
  
  /* .dropdown-content.active {
    display: block;
  } */

/* ---------------------- */
.dropdown-content {
  display: none;
  position: absolute;
  background-color: #f9f9f9;
  width: auto;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  z-index: 1;
  border-radius: 5px;
} 

.dropdown-content.active {
  display: block; 
}

.dropdown-btn {
  font-size: large;
}

.deleteIcon {
  border-style: solid;
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

/* --------------------------- */
.userName {
  max-width: 100%; /* Prevents the text from exceeding the container */
  white-space: normal; /* Allows text to wrap */
  word-break: break-word; /* Breaks long words */
  overflow-wrap: break-word; /* Ensures proper breaking */
}