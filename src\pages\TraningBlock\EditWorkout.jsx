import React, { useEffect, useState, useContext } from "react";
import {
  Box,
  Button,
  Grid,
  IconButton,
  MenuItem,
  Select,
  Modal,
  Typography,
  TextField,
  Stack,
  Chip,
  FormControl,
  InputLabel,
  Input,
  Dialog,
  DialogTitle,
} from "@mui/material";
import DeleteIcon from "@mui/icons-material/Delete";
import "../../pages/Workouts/componants/ManageWorkouts/AddWorkout.css";

import PropTypes from "prop-types";
import { useAutocomplete } from "@mui/base/useAutocomplete";
import CheckIcon from "@mui/icons-material/Check";
import CloseIcon from "@mui/icons-material/Close";
import SellIcon from "@mui/icons-material/Sell";
import AddIcon from "@mui/icons-material/Add";
import { styled } from "@mui/material/styles";
import { autocompleteClasses } from "@mui/material/Autocomplete";
import { Editor } from "react-draft-wysiwyg";
import "react-draft-wysiwyg/dist/react-draft-wysiwyg.css";
import { EditorState, convertToRaw, convertFromRaw } from "draft-js";
import OutlinedInput from "@mui/material/OutlinedInput";
import GlobalContext from "../../context/GlobalContext";
import BlockModal from "../../components/BlocksModal";
import { useDrag, useDrop } from "react-dnd";
import {
  createCustomBlocks,
  createWorkoutInsideLibrary,
  getAllPhasesFitnessLevelGoalList,
  updateWorkoutinsideLibraryByWorkoutId,
  getAllBlocks,
  getAllBlocksByWorkoutId,
  getSuActivity,
  getFechAlGoals,
  getAllfitnesLevel,
  fetchactivitylevel,
  getAllActivities,
} from "../../API/api-endpoint";
import { TimePicker } from "antd";
import { handleInputTimeChange } from "../../utils/Resubale";
import { getPartculatWorkouts } from "../../API/api-endpoint";
import moment from "moment";
import { showError, showSuccess } from "../../components/Messages";
import { getTrainingPlanMasterMappingByTrainingId, updateColumnsinTrainingData, updateLastColumnDayInRow } from "../../store/slices/MultiTrainingBlocksSlice";
import { useDispatch } from "react-redux";
import RichTextEditor from "../../components/EditorFile";
const DistanceActivity2 = [{ id: "km", value: "Kilometer" },
{ id: "miles", value: "Miles" }]
const swimmingDistance = [{ id: "yards", value: "Yards" },
{ id: "mtr", value: "Meter" }]
const ITEM_HEIGHT = 48;
const ITEM_PADDING_TOP = 8;
const MenuProps = {
  PaperProps: {
    style: {
      maxHeight: ITEM_HEIGHT * 4.5 + ITEM_PADDING_TOP,
      width: 250,
    },
  },
};

const EditWorkout = ({selectedValue, onClose, open,trainingBlock}) => {
  let dispatch = useDispatch();
 
    const [workoutData, setWorkoutData] = useState({});
console.log("workoutData",workoutData);
  const [phasesOption, setPhasesOption] = useState([]);
  const [editorLoaded, setEditorLoaded] = useState(false);
  const [goalOption, setgoalOption] = useState([]);
  const [fetchAllActivities, setFetchActivies] = useState();

  const [fitnessLevelOption, setfitnessLevelOption] = useState([]);
  const [subActivityList, setsubActivityList] = useState([]);
  const [formValue, setFormValue] = useState({ metric: [], zone: [], blocks: [] ,unit:"km"});
  const [isLoading, setisLoading] = useState(false);
  const [initial, setInitial] = useState(false);

console.log("formValue",formValue);
  const getWorkout = async(id)=>{
    const response = await getPartculatWorkouts(id)
    console.log("response",response );
    if (response.status ==200) {
      setWorkoutData(response?.data[0])
      let foundObject = response?.data[0]
      
        //       // let tags = foundObject["workout-tags"].split(",");
        //       console.log("foundObject", foundObject);
              let value = {
                unit: foundObject?.unit,
                activity_id: foundObject?.activity_id,
                time: foundObject?.workout_planned_duration,
                distance: foundObject?.workout_planned_distance,
              }
              fetchPace(value)
              setFormValue({
        
                workout_name: foundObject?.workout_planned_title,
                description: foundObject?.workout_description,
        
                // blocks: foundObject?.blocks,
        
                unit: foundObject?.unit?foundObject?.unit:"km",
                activity: foundObject?.activity_id,
                sub_activity: foundObject?.sub_activity_id,
                distance: foundObject?.workout_planned_distance,
                duration: foundObject?.workout_planned_duration,
                "pace": foundObject?.workout_planned_pace,
                "paceUnit": "minutes/km",
                "sequence_number": foundObject?.sequence,
                "phase": foundObject?.phase,
                "fitnesslevel": foundObject?.fitnessLevel,
                "goal": foundObject?.goals,
                id: foundObject?.workout_id
              });
              setInitial(true)
        
              fetchDGoals(foundObject?.activity_id)
              fetchSubActivity(foundObject?.activity_id)
              const contentState = convertFromRaw({
                blocks: [
                  {
                    text: foundObject?.workout_description // Assuming 'description' is the key containing the text
                  }
                ],
                entityMap: {}
              });
              const newEditorState = EditorState.createWithContent(contentState);
              console.log("foundObject", newEditorState);
        
              setisLoading(false);
        
      
    }
  }
  useEffect(()=>{
    if (selectedValue) {
      getWorkout(selectedValue)
    }
  },[selectedValue])
 
 
  const getAllActivitiesDetails = async (id) => {
    let result = await getAllActivities();
    setFetchActivies(result);
}
useEffect(() => {
  getAllActivitiesDetails()
}, [])
let fetchPace=async(value)=>{
  const response = await fetchactivitylevel(value)
      console.log("response", response);
      setFormValue((prevFormValue) => ({
        ...prevFormValue, "pace": response?.pace, paceUnit: response.paceunit,speedUnit:response?.speedunit,speed:response?.speed
      }));
    
}
//  
 
  const handleCheckbox = (event, value, name) => {
    const isChecked = event.target.checked;
    setFormValue((prevFormValue) => ({
      ...prevFormValue,
      [name]: isChecked ? value : null, // Set the value if checked, otherwise set to null or another default value
    }));
  };
  const handleChange = async (event, name) => {
    if (name == "activity") {
      const newName = name;
      const value = event.target.value;
      setFormValue({ ...formValue, [newName]: value });
      const response = await getSuActivity(event.target.value)
      fetchDGoals(event.target.value)

      setsubActivityList(response)
    }
    else if (event.target.type === "checkbox") {
      const newName = name;
      const value = event.target.checked;
      setFormValue({ ...formValue, [newName]: value });
      handleCheckbox(event, name, newName);
    } else {
      const newName = name;
      const value = event.target.value;
      setFormValue({ ...formValue, [newName]: value });
    }
  };
  const handleSubmit = async () => {
    if (formValue?.activity) {
      let newObject =
      {
        "workoutName": formValue?.workout_name,
        "activity": formValue?.activity,
        "subactivity": formValue?.sub_activity,
        "duration": formValue?.duration,
        "distance": formValue?.distance,
        "seqNumber": formValue?.sequence_number,
        "fitnessLevelId": formValue?.fitnesslevel,
        "pace": formValue?.pace,
        "unit": formValue?.unit,
        "phase": formValue?.phase,
        "goals": formValue?.goal,
        "description": formValue?.description,
        "workout_library_id": selectedValue,
        // "blocks": formValue?.blocks
      }
      let result;
        let data = { ...newObject, id: formValue?.id }
        result = await updateWorkoutinsideLibraryByWorkoutId(
          data
        );
      if (
        result.message == "workout updated successfully"
        
      ) {
        showSuccess("Workout updated successfully")
        onClose(false)
        dispatch(
          getTrainingPlanMasterMappingByTrainingId(trainingBlock.trainingblock_id)
        );
        setTimeout(() => {
          dispatch(updateColumnsinTrainingData());
          dispatch(updateLastColumnDayInRow());
        }, 300);
      }
    }else{
      showError("Please add activity first")
    }
   
  
    //setIsOpenWorkOut("showTable");
    //add a create workout code here
  };

 
  const fetchDGoals = async (id) => {
    let response = await getFechAlGoals(id);
    console.log("response", response);
    setgoalOption(response?.goals);
  };
  const fetchSubActivity = async (id) => {
    const response = await getSuActivity(id)

    setsubActivityList(response)
  }
  useEffect(() => {
    if (selectedValue) {
      fetchgetAllfitnesLevel()
    }

  }, [selectedValue])
  const fetchgetAllfitnesLevel = async () => {
    const response = await getAllfitnesLevel(selectedValue)
    setfitnessLevelOption(response?.levels)
    setPhasesOption(response?.phasenames)
  }
 

  const hnadleFormikDuration = async (e, timeString, keyname) => {
    console.log("e>>>",e.target.value);

    handleInputTimeChange(e, "duration", setFormValue)
    // formik.setFieldValue(keyname, timeString)
    let value = {
      activity_id: formValue?.activity,
      time: formValue?.duration,
      distance: formValue?.distance,
      unit: formValue?.unit
    }
    if (formValue?.activity && timeString && formValue?.distance && formValue?.unit) {
      const response = await fetchactivitylevel(value)
      console.log("response", response);
      setFormValue((prevFormValue) => ({
        ...prevFormValue, "pace": response?.pace, paceUnit: response.paceunit,speedUnit:response?.speedunit,speed:response?.speed
      }));
     
    }

    // fetchactivitylevel
  }
  const hnadleFormikDistance = async (e, keyname) => {
    if (e.target.value =="") {
      setFormValue((prevFormValue) => ({
        ...prevFormValue, "pace": 0, paceUnit: "",distance:e.target.value
      }));
    }else{
      setFormValue((prevFormValue) => ({
        ...prevFormValue, "distance": e.target.value
      }));
      // formik.setFieldValue(keyname, e.target.value)
      let value = {
        activity_id: formValue?.activity,
        time: formValue.duration,
        distance: e.target.value,
        unit: formValue?.unit
      }
      if (formValue?.activity && e.target.value && formValue?.duration && formValue?.unit) {
        const response = await fetchactivitylevel(value)
        console.log("response", response);
        setFormValue((prevFormValue) => ({
          ...prevFormValue, "pace": response?.pace, paceUnit: response.paceunit,speedUnit:response?.speedunit,speed:response?.speed
        }));
       
      }
    }
   

    // fetchactivitylevel
  }

//   useEffect(() => {
//     setEditorLoaded(true);
//   }, []);
 
  return isLoading ? (
    <div>Loading Data...</div>
  ) : (
    <Dialog
    maxWidth="lg"
    minWidth="lg"
    onClose={() => onClose(false)}
    open={open}
  >
  <div className="flex justify-between ">
  <DialogTitle>
    {workoutData?.workout_planned_title} |{" "}
    {moment(workoutData?.date).format("dddd, DD-MMM-YYYY")}
  </DialogTitle>
  <DialogTitle className=" cursor-pointer" onClick={() => onClose(false)}>
    {" "}
    <CloseIcon />
  </DialogTitle>
</div>
      <div className="walkjog-form-main">
          <div className="walkjog-form-group">
            <label> Workout Name</label>
            <input
              type="text"
              placeholder=""
              value={formValue?.workout_name}
              onChange={(e) => handleChange(e, "workout_name")}
            />
          </div>
        <div className="walkjog-second-row">
          <Grid container sx={{ display: "flex", gap: "30px" }}>
            <Grid md={5} sm={5} lg={5} xs={12}>
              <div className="walkjog-acticity-inputs">
                <div className="walkjog-form-group">
                  <label> Activity <span className="text-red-500">*</span>{" "}</label>
                  <FormControl fullWidth>
                    <Select
                      placeholder="Distance"
                      name="activity"
                      value={formValue.activity?formValue.activity:""}
                      onChange={(e) => handleChange(e, "activity")}


                      id="form-layouts-separator-select"
                      labelId="form-layouts-separator-select-label"
                      input={<OutlinedInput id="select-multiple-language" />}
                    >
                      {fetchAllActivities?.map((value, index) => {
                        return (
                          <MenuItem value={value?.id}>
                            {value?.activity_name}
                          </MenuItem>
                        );
                      })}
                    </Select>
                  </FormControl>
                </div>
              </div>
            </Grid>
            {formValue.activity !=7 &&
              <Grid md={5} sm={5} lg={5} xs={6}>
              <div className="walkjog-form-group">
                <label>Sub Activity</label>
                <FormControl fullWidth>
                  <Select
                    placeholder="Distance"
                    name="sub_activity"
                    value={formValue.sub_activity || (subActivityList?.length > 0 ? subActivityList[1]?.id : '')}
                    onChange={(e) => handleChange(e, "sub_activity")}

                    id="form-layouts-separator-select"
                    labelId="form-layouts-separator-select-label"
                    input={<OutlinedInput id="select-multiple-language" />}
                  >
                    {subActivityList?.map((value, index) => {
                      return (
                        <MenuItem value={value?.id}>
                          {value?.subworkout}
                        </MenuItem>
                      );
                    })}
                  </Select>
                </FormControl>
              </div>
            </Grid>
            }
            
            {formValue.activity !=7 &&
            <Grid md={5} sm={5} lg={5} xs={6}>
              <div className="walkjog-form-group">
                <label> Distance Unit</label>
                <FormControl fullWidth>
                  <Select
                    placeholder="Distance"
                    name="unit"
                    value={formValue.unit ? formValue.unit : "km"}
                    onChange={async (e) => {
                      handleChange(e, "unit")
                      let value = {
                        unit: e.target.value,
                        activity_id: formValue?.activity,
                        time: formValue?.duration,
                        distance: formValue?.distance,
                      }
                      const response = await fetchactivitylevel(value)
                      console.log("response", response);
                      setFormValue((prevFormValue) => ({
                        ...prevFormValue, "pace": response?.pace, paceUnit: response.paceunit,speedUnit:response?.speedunit,speed:response?.speed
                      }));
                     
                    }}


                    id="form-layouts-separator-select"
                    labelId="form-layouts-separator-select-label"
                    input={<OutlinedInput id="select-multiple-language" />}
                  >
                    <MenuItem value={""}>Select </MenuItem>
                    {formValue?.activity == 3 ? swimmingDistance?.map((value, index) => {
                      return (
                        <MenuItem value={value?.id}>
                          {value?.value}
                        </MenuItem>
                      );
                    }) :
                      DistanceActivity2?.map((value, index) => {
                        return (
                          <MenuItem value={value?.id}>
                            {value?.value}
                          </MenuItem>
                        );
                      })
                    }
                  </Select>
                </FormControl>
              </div>
            </Grid>
                  }
                  {formValue.activity !=7 &&
            <Grid md={6} sm={6} lg={6} xs={12}>
              {formValue?.activity === "Running" ||
                formValue?.activity === "Walking" ? (
                <div className="walkjog-zone-section">
                  <div className="title">
                    Zone have been defined for this activity. You can attach
                    zone for a selected matric
                  </div>
                  <div className="walkjog-zone-group">
                    <div className="metric">
                      <div className="walkjog-form-group">
                        <label>Metric</label>
                        <Select
                          labelId="demo-simple-select-label"
                          id="demo-simple-select"
                          className="select "
                          name="metric"
                          value={formValue?.metric}
                          onChange={(e) => handleChange(e, "metric")}
                          label="select"
                          variant="standard"
                        >
                          <MenuItem value={"east"}>East</MenuItem>
                          <MenuItem value={"west"}>West</MenuItem>
                          <MenuItem value={"north"}>North</MenuItem>
                          <MenuItem value={"south"}>South</MenuItem>
                        </Select>
                      </div>
                    </div>
                    <div className="zone">
                      <div className="walkjog-form-group">
                        <label>Zone</label>
                        <Select
                          labelId="demo-simple-select-label"
                          id="demo-simple-select"
                          className="select"
                          name="zone"
                          value={formValue?.zone}
                          onChange={(e) => handleChange(e, "zone")}
                          label="select"
                          variant="standard"
                        >
                          <MenuItem value={"zone1"}>zone 1</MenuItem>
                          <MenuItem value={"zone2"}>zone 2</MenuItem>
                          <MenuItem value={"zone3"}>zone 3</MenuItem>
                        </Select>
                      </div>
                    </div>
                  </div>
                </div>
              ) : (
                ""
              )}
            </Grid>}
          </Grid>
        </div>
        {formValue.activity !=7 &&
        <div className="walkjog-third-row">
          <Grid
            container
            sx={{
              display: "flex",
              gap: { md: "25px", sm: "20px", xs: "20px" },
            }}
          >
            <Grid md={2} sm={2} lg={2} xs={12}>
              <div className="walkjog-form-group">
                <label>Duration</label>
                <input
                  type="text"
                  id="timeInput"
                  value={formValue?.duration}
                  onChange={(e) => hnadleFormikDuration(e, "duration", setFormValue)}
                  placeholder="hh:mm:ss"
                />

              </div>
            </Grid>
            <Grid md={3} sm={3} lg={3} xs={12}>
              <div className="walkjog-form-group">
                <label>Distance</label>
                <input
                  type="text"
                  placeholder="0"
                  value={formValue?.distance}
                  onChange={(e) => hnadleFormikDistance(e, "distance")}
                />
              </div>
            </Grid>
            {formValue?.activity === "Running" ||
              formValue?.activity === "Walking" ? (
              <Grid md={3} sm={3} lg={3} xs={12}>
                {" "}
                <div className="walkjog-form-group">
                  <label>Unit of measure</label>
                  <Select
                    labelId="demo-simple-select-label"
                    id="demo-simple-select"
                    className="select"
                    name="measure_unit"
                    value={formValue?.measure_unit}
                    onChange={(e) => handleChange(e, "measure_unit")}
                    label="select"
                    variant="standard"
                  >
                    <MenuItem value={"km"}>KM</MenuItem>
                    <MenuItem value={"meter"}>Meter</MenuItem>
                    <MenuItem value={"yard"}>yard</MenuItem>
                  </Select>
                </div>
              </Grid>
            ) : (
              ""
            )}

            {formValue?.activity !==2 &&
            <Grid md={3} sm={3} lg={3} xs={12}>
              <div className="walkjog-form-group">
                <label>Pace</label>
                <TextField
                  type="text"
                  placeholder="0"
                  value={`${formValue?.pace}  ${formValue?.paceUnit}`}
                  //onChange={(e) => handleChange(e, "pace")}
                  disabled
                />
              </div>
            </Grid>
          }
          {formValue?.activity ==2 &&
            <Grid md={3} sm={3} lg={3} xs={12}>
              <div className="walkjog-form-group">
                <label>Speed</label>
                <TextField
                  type="text"
                  placeholder="0"
                  value={`${formValue?.speed}  ${formValue?.speedUnit}`}
                  //onChange={(e) => handleChange(e, "pace")}
                  disabled
                />
              </div>
            </Grid>
          }

            <Grid md={12} sm={12} lg={12} xs={12}>
              <div className="walkjog-form-group">
                <label>Sequence Number</label>
                <input
                  type="number"
                  placeholder="0"
                  value={formValue?.sequence_number}
                  onChange={(e) => handleChange(e, "sequence_number")}
                />
              </div>
            </Grid>
          </Grid>
        </div>}
        {formValue.activity !=7 &&
        <div>
          <Grid container>
            <Grid item md={4} sm={4} lg={4} xs={4}>
              <div className="mt-5 ml-2">
                <div>
                  <p className="mb-1 text-slate-600 px-1 font-medium">
                    Fitness Level
                  </p>

                  {fitnessLevelOption?.map((option) => {
                    return (
                      <div className="flex p-1">
                        <input
                          type="radio"
                          value={formValue?.fitnesslevel}
                          checked={option.id === formValue?.fitnesslevel}
                          placeholder="Enter your Sequence Number" //handleCheckbox
                          onChange={(e) => handleCheckbox(e, option?.id, "fitnesslevel")}
                        // checked={(formValue?.fitnesslevel || []).includes(option?.id)}
                        />
                        <lable className="ml-2">
                          {option?.level}
                        </lable>
                      </div>
                    );
                  })}
                </div>
              </div>
            </Grid>
            <Grid item md={4} sm={4} lg={4} xs={4}>
              <div className="mt-5">
                <p className="mb-1 text-slate-600 px-1 font-medium">
                  Phases
                </p>
                {phasesOption?.map((option) => {
                  return (
                    <div className="flex p-1">
                      <input
                        type="radio"
                        placeholder="Enter your Sequence Number"
                        value={formValue?.phase}
                        checked={option.id === formValue?.phase}
                        onChange={(e) => handleCheckbox(e, option.id, "phase")}

                      />
                      <lable className="ml-2">
                        {option?.phase}
                      </lable>
                    </div>
                  );
                })}
              </div>
            </Grid>
            <Grid item md={4} sm={4} lg={4} xs={4}>
              {" "}
              <div className="mt-5">
                <p className="mb-1 text-slate-600 px-1 font-medium">
                  Goal
                </p>
                {goalOption?.map((option) => {
                  return (
                    <div className="flex p-1">
                      <input
                        type="radio"
                        value={formValue?.goal}
                        checked={option.goal === formValue?.goal}
                        placeholder="Enter your Sequence Number" //handleCheckbox
                        onChange={(e) => handleCheckbox(e, option?.goal, "goal")}
                      />

                      <lable className="ml-2">
                        {" "}
                        {option?.goal}
                      </lable>
                    </div>
                  );
                })}
              </div>
            </Grid>
          </Grid>
        </div>
              }
        <div className="walkjog-tag-input">
          <div>
            <FormControl style={{ width: 400 }}>
              <InputLabel id="demo-mutiple-checkbox-label">Tags</InputLabel>
              <Select
                labelId="demo-mutiple-checkbox-label"
                id="demo-mutiple-checkbox"
                multiple
                value={formValue["tags"] || []}
                onChange={(e) => handleChange(e, "tags")}
                input={<Input />}
                // renderValue={(selected) => (
                //   <div>
                //     {selected?.map((value) => (
                //       <Chip
                //         key={value}
                //         label={value}
                //         // className={classes.chip}
                //       />
                //     ))}
                //   </div>
                // )}
                MenuProps={MenuProps}
              //style={{ minWidth: "400px" }}
              >
                {fetchAllActivities?.map((tag) => (
                  <MenuItem
                    key={tag?.id}
                    value={tag?.id}
                  // style={getStyles(name, personName, theme)}
                  >
                    {tag?.activity_name}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </div>
        </div>
       
       
        
        <div className="walkjog-discription-section">
          <div className="title">Description</div>
          <RichTextEditor
                  setInitial={setInitial}
                  initial={initial}
        initialContent={formValue?.description}
        onContentChange={(newContent)=>
          setFormValue({ ...formValue, description: newContent })
          }
        placeholder="Type your content here..."
      />
          
        </div>

        <div className="walkjog-footer-section">
          <Button variant="contained" onClick={() => handleSubmit()}>
            {"Edit"}
          </Button>
        </div>
      </div>
    </Dialog>
  );
};
export default EditWorkout
