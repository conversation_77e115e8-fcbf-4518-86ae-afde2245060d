export const API_ENDPOINTS = {
    googleSignup: `${API_BASE_URL}/sign-up/signUpwithgoogle`,
    googleLogin: `${API_BASE_URL}/user/userLoginWithGoogle1`,
    emailSignup: `${API_BASE_URL}/sign-up`,
    login: `${API_BASE_URL}/user/login`,
    getAccessToken: (id) => `${API_BASE_URL}/getstravaaccesstoken/${id}`,
    fetchCurrentProfile: (id) => `${API_BASE_URL}/program/getcurrentprofile/${id}`,
    verifyOtp: `${API_BASE_URL}/sign-up/verify`,
    forgotPassword: `${API_BASE_URL}/user/forget-password`,
    resetPassword: `${API_BASE_URL}/user/reset-password`,
    resendOtp: `${API_BASE_URL}/sign-up/resend-otp`,
    updatePhoneVerificationStatus: `${API_BASE_URL}/user/updatePhoneVerification`,
    createGoogleId: `${API_BASE_URL}/sign-up/creategoogleids`,
    adminLogin: `${API_BASE_URL}/user/userLoginforadmin`,
    createWorkoutLibrary: `${API_BASE_URL}/workoutLibrary/createLibrary`,
    getAllWorkoutLibrary: `${API_BASE_URL}/workoutLibrary/getLibrary`,
    updateWorkoutLibrary: `${API_BASE_URL}/program/updateworkoutlibrary`,
    deleteWorkoutLibrary: `${API_BASE_URL}/program/deleteworkoutlibrary`,
    createWorkoutInsideLibrary: `${API_BASE_URL}/program/addworkouttoworkoutlibrary`,
    getAllWorkoutsFromLibraryByLibraryId: `${API_BASE_URL}/workout-master`,
    updateWorkoutinsideLibraryByWorkoutId: `${API_BASE_URL}/program/updateworkouttoworkoutlibrary`,
    deleteWorkoutfromLibraryByWorkoutId: `${API_BASE_URL}/program/deleteworkouttoworkoutlibrary`,
    getAllWorkouts: `${API_BASE_URL}/workout-master`,
    hideUnhideStravaWorkouts: `${API_BASE_URL}/program/changestravaactivitystatus`,
    deleteSelectedWorkouts: `${API_BASE_URL}/program/deleteworkouts`,
    getAllPhasesFitnessLevelGoalList: `${API_BASE_URL}/getBlocksList`,
    getAllActivity: `${API_BASE_URL}/activitySubactivity/list-activities`,
    getAllSubActivity: `${API_BASE_URL}/Subactivity/get-all-subactivities`,
    getAllTags: `${API_BASE_URL}/tagCloud`,
    getAllBlocks: `${API_BASE_URL}/workoutCustomBlock`,
    createCustomBlocks: `${API_BASE_URL}/workoutCustomBlock/create`,
    updateCustomBlocks: `${API_BASE_URL}/workoutCustomBlock/update`,
    getAllBlocksByWorkoutId: `${API_BASE_URL}/workoutCustomBlockMapping`,
    copyPasteWorkoutLibrary: `${API_BASE_URL}/workoutLibrary/pasteLibrary`,
    copyPasteWorkoutLibrari: `${API_BASE_URL}/program/copyworkoutlibrary`,
    copyPasteWorkoutLibrariWorkout: `${API_BASE_URL}/program/copyworkoutinworkoutlibrary`,
    createWorkout: `${API_BASE_URL}/workoutMaster/createWorkout`,
    updateWorkout: `${API_BASE_URL}/workoutMaster/updateWorkout`,
    deleteWorkout: `${API_BASE_URL}/workoutMaster/deleteWorkout`,
    getAllworkouts: `${API_BASE_URL}/workoutMaster/getWorkouts`,
    createTrainingBlockLibrary: `${API_BASE_URL}/training-block-library/create`,
    pasteTrainingBlockLibrary: `${API_BASE_URL}/training-block-library/paste`,
    getAllTrainingBlockLibrary: `${API_BASE_URL}/training-block-library`,
    getAllTrainingBlocks: `${API_BASE_URL}/program/getallcratedtrainingplans`,
    updateTrainingBlockLibrary: (id) => `${API_BASE_URL}/training-block-library/update/${id}`,
    deleteTrainingBlockLibrary: (id) => `${API_BASE_URL}/training-block-library/delete/${id}`,
    copyTrainingBlock: `${API_BASE_URL}/trainingBlock/copytrainingblock`,

    createTrainingBlock: `${API_BASE_URL}/trainingBlock/create`,
    getAllTrainingBlockByLibraryID: (id) => `${API_BASE_URL}/trainingBlock/libraryId/${id}`,
    updateTrainingBlock: (id) => `${API_BASE_URL}/trainingBlock/update/${id}`,
    deleteTrainingBlock: (id) => `${API_BASE_URL}/trainingBlock/delete/${id}`,

    createTrainingPlanMasterMapping: `${API_BASE_URL}/training-plan-master-mapping/create`,
    deleteTrainingPlanMasterMapping: (id) => `${API_BASE_URL}/training-plan-master-mapping/delete/${id}`,
    getTrainingPlanMasterMappingByTrainingPlanID: (id) => `${API_BASE_URL}/training-plan-master-mapping/training-plan-id/${id}`,
    deleteWorkoutFromTrainingPlanMasterMapping: (id) => `${API_BASE_URL}/training-plan-master-mapping/update/${id}`,
    updateTrainingPlanMasterMappingShileDND: (trainingBlockId, mappingId) => `${API_BASE_URL}/training-plan-master-mapping/update-plan/${trainingBlockId}?mapping_id=${mappingId}`,
    updateTrainingPlanMasterMappingWorkoutStatusForHide: (trainingBlockId) => `${API_BASE_URL}/training-plan-master-mapping/update-status/${trainingBlockId}`,

    createAssignTrainingPlanToUser: `${API_BASE_URL}/user-mapping-training-plan/assign-create`,
    updateAssignTrainingPlanToUser: `${API_BASE_URL}/user-mapping-training-plan/update-assing-mapping-user`,

    getAllUsers: `${API_BASE_URL}/get-user`,
    getAllGoals: `${API_BASE_URL}/workoutGoal`,
    getAllYoskaActivities: `${API_BASE_URL}/yoska-activity`,
    getAllYoskaActivitiesUser: (userId) => `${API_BASE_URL}/yoska-activity/basedonuser/${userId}`,
    getSubscriptionPlanBYProgramID: (programId) => `${API_BASE_URL}/subscription-plans/program-id/${programId}`,
    manageSubscription: `${API_BASE_URL}/program/manageusersubscriptionsdata`,
    manageAllCoach: `${API_BASE_URL}/program/getallcoaches`,
    createAdminCoachPrivilege: (id) => `${API_BASE_URL}/profile/createoreditadmincoachprivilige/${id}`,
    deleteCoachPrivilege: (id) => `${API_BASE_URL}/profile/deleteadmincoachprivilige/${id}`,
    updateSubscription: `${API_BASE_URL}/program/changesubofuser`,
    updateCoach: `${API_BASE_URL}/program/updateCoachDetails`,
    generateInvoice: `${API_BASE_URL}/payment/generateinvoice`,
    putOnHold: (id) => `${API_BASE_URL}/user-subscription/putonhold/${id}`,
    coachPutOnHold: `${API_BASE_URL}/program/putonholduser`,
    terminateCoach: `${API_BASE_URL}/program/userTermination`,
    terminateAthlete: `${API_BASE_URL}/program/userTermination`,
    notifyCoach: `${API_BASE_URL}/program/sendmailtounpaiduser`,
    notifyAthletes: (id) => `${API_BASE_URL}/profile/notifyuserofsubending/${id}`,
    putUnHold: (id) => `${API_BASE_URL}/user-subscription/putoffhold/${id}`,
    getYoskaProgramByActivityID: `${API_BASE_URL}/yoska-program/get-by-activity`,
    getYoskaProgramFeateryID: (activity_id) => `${API_BASE_URL}/feeddata/getProgramByActivityId/${activity_id}`,
    createUserSubscription: `${API_BASE_URL}/user-subscription/create`,
    updateUserSubscription: (id) => `${API_BASE_URL}/user-subscription/update/${id}`,
    createUserDetailsSubscription: `${API_BASE_URL}/user-subscription/create`,
    validatePromoCode: `${API_BASE_URL}/coupons/checkCouponValidity`,
    getStravaURL: (user_ID) => `${API_BASE_URL}/auth/strava2/${user_ID}`,
    getStravaDetails: (user_ID, code, scopes) => `${API_BASE_URL}/auth/strava/callback2/${user_ID}?code=${code}&scope=${scopes}`,
    getAllActivities: `${API_BASE_URL}/program/getallactivities`,
    getUserBadges: (id) => `${API_BASE_URL}/program/gamification/getallbadgesbyuserid/${id}`,
    getAllActivitiesField: (activityId, assignedCoachId) => `${API_BASE_URL}/program/fetchsubactivityandparams/${activityId}/${assignedCoachId}`,
    getSuActivity: (activityId) => `${API_BASE_URL}/program/getallsubworkouts/${activityId}`,
    createWorkouts: `${API_BASE_URL}/program/manualv2`,
    updateWorkouts: `${BASE_URL}/updatemanualv2`,
    updateWorkoutsByDrag: `${BASE_URL}/draganddropworkout`,
    getAllWorkoutsDetails: `${BASE_URL}/getallworkoutdatav2`,
    getWeeklySummary: `${BASE_URL}/getallworkoutdatainrangeweeklysummary`,
    copyPasteWorkouts: `${BASE_URL}/copyworkouts`,
    copyPasteWeeklyWorkouts: `${BASE_URL}/copyweekworkoutsv2`,
    copyPasteWeeklyTrainingBlocks: `${BASE_URL}/copyweekworkoutintrainingblock`,
    getParticularWorkoutDetails: `${BASE_URL}/getmanualworkoutbyid`,
    createCommentOnWorkout: `${BASE_URL}/comments`,
    getAllComments: `${BASE_URL}/getAllComments`,
    getAllUserZones: `${BASE_URL}/getuserzones`,
    getDistanceForActivity: `${BASE_URL}/getdistanceforactivity`,
    fetchActivityLevel: `${BASE_URL}/fetchactivitylevel`,
    saveUserZones: `${BASE_URL}/userzones`,
    getAllCountry: `${URL}/loc/country`,
    getAllState: `${URL}/loc/states`,
    getAllCities: `${URL}/loc/cities`,
    getAllUsers: `${API_BASE_URL}/get-user`,
    getAllUserSubscriptionByUserId: (userId) => `${API_BASE_URL}/program/getallusersubscriptionbyuserid/${userId}`,
    fetchGoalsByActivityId: (activityId) => `${API_BASE_URL}/program/fetchgoalsbyactivityid/${activityId}`,
    getPersonalizationProfileByProgramId: `${API_BASE_URL}/program/getpresonalizationprofle2`,
    getPersonalizationProfile: `${API_BASE_URL}/program/getpresonalizationprofle`,
    fetchDaysById: (activityId) => `${API_BASE_URL}/program/fetchgoalsbyactivityid/${activityId}`,
    fetchDayOptionValues: (days, activityId) => `${API_BASE_URL}/program/fetchdaysoptionvalueforalloptions/${days}/${activityId}`,
    createPersonalizationProfile: `${API_BASE_URL}/program/createpersonalizationprofile`,
    updatePersonalizationProfile: `${API_BASE_URL}/program/updatepersonalizationprofile`,
    getAssignedCoach: (userId, id) => `${API_BASE_URL}/program/assingedatheletestocoachv2/${userId}/${id}`,
    saveWorkoutLibrary: `${API_BASE_URL}/program/saveworkoutlibrary`,
    renderAllLibraryData: `${API_BASE_URL}/program/renderalllibrarydataapi`,
    renderWorkoutLibraryDataById: (libraryId) => `${API_BASE_URL}/program/renderworkoutlibrarydataapi/${libraryId}`,
    getAllWorkoutsByLibrary: (libraryId) => `${API_BASE_URL}/program/renderlibrarydataapibyid/${libraryId}`,
    createTrainingPlan: `${API_BASE_URL}/program/createtraininplan`,
    createTrainingBlocksWorkout: `${API_BASE_URL}/program/manualv2fortraininplan`,
    getTrainingPlanData: (planId) => `${API_BASE_URL}/program/gettrainingplandata/${planId}`,
    getAllUserActivePrograms: (userId) => `${API_BASE_URL}/program/getalluseractiveprograms/${userId}`,
    changeProfile: (programId, userId) => `${API_BASE_URL}/program/changeprofile/${programId}/${userId}`,
    getUserProfile: (userId, programId) => `${API_BASE_URL}/profile/getprofilev2/${userId}/${programId}`,
    saveProfile: `${API_BASE_URL}/profile/saveprofile`,
    getAllUserSubscriptions: `${API_BASE_URL}/program/getallusersubscriptions`,
    getAllGroups: `${API_BASE_URL}/profile/getallgroups`,
    getUserChallenges: `${API_BASE_URL}/program/gamification/leaderboarddata`,
    getCoachChangeLogDetails: (userId, programId) => `${API_BASE_URL}/program/getassingedcoach/${userId}/${programId}`,
    postLeaderboard: `${API_BASE_URL}/program/gamification/leaderboard`,
    getAllCoaches: `${API_BASE_URL}/program/getallcoaches`,
    getUsersWithoutPaymentForSubscription: `${API_BASE_URL}/program/userwithnopaymentforsubs`,
    getAboutToExpireSubscriptions: `${API_BASE_URL}/profile/getabouttoexpiresubs`,
    getAllAthletes: `${API_BASE_URL}/program/getallateheletes`,
    getCoachSchedule: (coachId) => `${API_BASE_URL}/program/getcoachschedule/${coachId}`,
    getCoachScheduleData: (userId) => `${API_BASE_URL}/program/coachscheduledata/${userId}`,
    bookCoachSlot: `${API_BASE_URL}/program/coachbooking`,
    createSlotBooking: `${API_BASE_URL}/program/createslotbooking`,
    createSlotBooking: `${API_BASE_URL}/program/coachschedule`,
    updateSlotBooking: `${API_BASE_URL}/program/updateschedule`,
    approveBooking: id => `${API_BASE_URL}/program/approvecoachbooking/${id}`,
    cancelBooking: id => `${API_BASE_URL}/program/rejectcoachbooking/${id}`,
    getCoachPayment: userId => `${API_BASE_URL}/payment/coachpaymentatsbycoachidsimdata/${userId}`,
    getAllBookingData: userId => `${API_BASE_URL}/program/getcoachbookingdata/${userId}`,
    updateCoachPayment: `${API_BASE_URL}/payment/updatestatusofcoachpayment`,
    createAssignCoach: `${API_BASE_URL}/program/coachassignment`,
    updateAssignCoach: `${API_BASE_URL}/program/coachchange`,
    importStravaActivity: `${API_BASE_URL}/import_strava_activities`,
    syncStravaActivity: userId => `${API_BASE_URL}/testfetchactivitysync/${userId}`,
    syncStravaActivityCoach: (userId, programID) => `${API_BASE_URL}/program/generateautomaticworkoutdatav3/${userId}/${programID}`,
    getAllChallenges: `${API_BASE_URL}/program/gamification/getallchallenges`,
    getAllRenderChallengesData: `${API_BASE_URL}/program/gamification/rendercreatechallangedata`,
    createChallengeActivity: `${API_BASE_URL}/program/gamification/createchallange`,
    updateChallengeActivity: `${API_BASE_URL}/yofit/gamification/editchallenge`,
    updateRaceData: `${API_BASE_URL}/program/updaterace`,
    updateSwimmingPoolData: `${API_BASE_URL}/program/updateswimmingpool`,
    deleteCreatedChallenges: id => `${API_BASE_URL}/yofit/gamification/deletechallenge/${id}`,
    deleteRaceData: id => `${API_BASE_URL}/program/deleterace/${id}`,
    deleteSwimmingPoolData: id => `${API_BASE_URL}/program/deleteswimmingpool/${id}`,
    assignChallengesToUsers: `${API_BASE_URL}/program/gamification/assignchallangetouser`,
    assignChallengesToGroup: `${API_BASE_URL}/program/gamification/assignchallangetogroup`,
    getAllRacesData: `${API_BASE_URL}/program/getallracesdata`,
    getAllRenderSaveRaceData: `${API_BASE_URL}/program/addracedata`,
    createRace: `${API_BASE_URL}/program/addrace`,
    createSwim: `${API_BASE_URL}/program/addswimmingpool`,
    getAllSwimmingData: `${API_BASE_URL}/program/getallswimmingpoolsdata`,
    getWellnessQuestions: `${API_BASE_URL}/program/renderassessmentdata/wellness`,
    createWellnessData: `${API_BASE_URL}/program/readinessindex/wellness`,
    getWellnessResult: userId => `${API_BASE_URL}/program/readinessindex/wellness/showassessmentresultdata/${userId}`,
    getRaceReadinessQuestions: userId => `${API_BASE_URL}/program/readinessindex/racereadinessdata/${userId}`,
    createRaceReadinessData: `${API_BASE_URL}/program/readinessindex/racereadiness`,
    getRaceReadinessResult: userId => `${API_BASE_URL}/program/readinessindex/racereadiness/showracereadinessresultdata/${userId}`,
    getAssignedChallenges: `${API_BASE_URL}/program/gamification/assingedchallengesdata`,
    getAllTrainingBlock: `${API_BASE_URL}/trainingBlock/getAllTrainingBlocksAll`,
    createChallengeTrack: `${API_BASE_URL}/program/gamification/createchallengetrack`,
    getAllAssignedTracks: `${API_BASE_URL}/program/gamification/getallchallengetracksdata`,
    assignTrainingPlan: `${API_BASE_URL}/program/assigntrainingplantouser`,
    unLinkTrainingPlan: `${API_BASE_URL}/program/unlinkassigntrainingplantouser`,
    mergeWorkout: `${API_BASE_URL}/program/mergestravaactivitywithworkout`,
    unMergeWorkout: `${API_BASE_URL}/program/unmergestravaactivitywithworkout`,
    getCalculatorGoals: `${API_BASE_URL}/program/racecalculatordata`,
    createCalculator: `${API_BASE_URL}/program/racecalculatorv2`,
    getCoachRevenueReport: `${API_BASE_URL}/program/coachrevenuereportbytimerangedatav2`,
    getSubscriptionPaymentData: `${API_BASE_URL}/payment/getSubscriptionsWithPaymentdata`,
    getSubscriptionPaymentFile: `${API_BASE_URL}/payment/getSubscriptionsWithPayment`,
    revenueReportByTimeRange: `${API_BASE_URL}/program/revenuereportbytimerangedata`,
    userGrowthReportData: `${API_BASE_URL}/program/usergrowthnumbersdata`,
    userOverallReports: `${API_BASE_URL}/program/growthoverentirespan`,
    formReviewCoach: `${API_BASE_URL}/program/formreview`,
    createAssessmentForm: `${API_BASE_URL}/program/saveassessmentpost`,
    fetchSelfAssessmentData: (userId, id) => `${API_BASE_URL}/program/selfassessmentformdata/${userId}/${id}`,
    fetchSavedAssessmentData: (userId) => `${API_BASE_URL}/program/getformreviewuserdata/${userId}`,
    fetchSavedAssessmentDataCoach: (userId) => `${API_BASE_URL}/program/getformreviewcoachdata/${userId}`,
    notifyAthletes: (id) => `${API_BASE_URL}/program/notifyathleteofformreviewcompletion/${id}`,
    getUserChallenges: (userId) => `${API_BASE_URL}/program/gamification/getalluserchallengesdata/${userId}`,
    checkProgress: `${API_BASE_URL}/program/gamification/checkprogress`,
    getUserOngoingChallenges: (userId) => `${API_BASE_URL}/program/gamification/getallongoingchallengesdata/${userId}`,
    subscribeToChallenge: `${API_BASE_URL}/program/gamification/subscribetochallenge`,
    syncChallenge: (userId) => `${API_BASE_URL}/program/gamification/nodecrontbyuseridforchallengesync/${userId}`,
    weeklyFeedData: `${API_BASE_URL}/feeddata/getallweekpatterdata`,
    weeklyFeedDataProgram: `${API_BASE_URL}/feeddata/weeklypatterdata`,
    weeklyFeedDataPattern: (id) => `${API_BASE_URL}/feeddata/getheaderssavailible/${id}`,
    createWeeklyDataPattern: `${API_BASE_URL}/feeddata/saveweeklypatter`,
    updateWeeklyDataPattern: `${API_BASE_URL}/feeddata/updateweeklypattern`,
    getZonesClassification: `${API_BASE_URL}/feeddata/zonesactivities`,
    createZonesClassification: `${API_BASE_URL}/feeddata/zonesactivities`,
    // Zones Classification
    updateZonesClassification: (id) => `${API_BASE_URL}/feeddata/zonesactivities/${id}`,
    deleteZonesClassification: (id) => `${API_BASE_URL}/feeddata/zonesactivities/${id}`,

    // Weekly Pattern
    deleteWeeklyPattern: (id) => `${API_BASE_URL}/feeddata/deleteweeklypattern/${id}`,

    // Bounds
    getBounds: `${API_BASE_URL}/program/bounds`,
    createBound: `${API_BASE_URL}/program/bounds`,
    updateBounds: (id) => `${API_BASE_URL}/program/bounds/${id}`,
    deleteBounds: (id) => `${API_BASE_URL}/program/bounds/${id}`,

    // Workout Distribution
    getWorkoutDistribution: `${API_BASE_URL}/feeddata/workoutdistribution`,
    createWorkoutDistribution: `${API_BASE_URL}/feeddata/workoutdistribution`,
    updateWorkoutDistribution: (id) => `${API_BASE_URL}/feeddata/workoutdistribution/${id}`,
    deleteWorkoutDistribution: (id) => `${API_BASE_URL}/feeddata/workoutdistribution/${id}`,

    // Automation Data
    getSaveAutomationData: `${API_BASE_URL}/program/getallautomationtrainingblock`,

    // Programs
    getAllPrograms: `${API_BASE_URL}/feeddata/programs`,
    createPrograms: `${API_BASE_URL}/feeddata/programs`,
    updatePrograms: (programId) => `${API_BASE_URL}/feeddata/programs/${programId}`,
    deletePrograms: (id) => `${API_BASE_URL}/feeddata/programs/${id}`,

    // Automation
    deleteAutomation: (id) => `${API_BASE_URL}/program/deleteautomationtrainingblock/${id}`,

    // Levels
    getAllLevels: `${API_BASE_URL}/feeddata/levels`,
    createLevels: `${API_BASE_URL}/feeddata/levels`,
    updateLevel: (id) => `${API_BASE_URL}/feeddata/levels/${id}`,
    deleteLevel: (id) => `${API_BASE_URL}/feeddata/levels/${id}`,

    // Review Section
    getAllReviewSection: `${API_BASE_URL}/program/formreviewsection`,
    getAllReviewSectionById: (id) => `${API_BASE_URL}/program/formreviewsectionbyactivityid/${id}`,
    createReviewSection: `${API_BASE_URL}/program/formreviewsection`,
    updateReviewSection: (id) => `${API_BASE_URL}/program/formreviewsection/${id}`,
    deleteReviewSection: (id) => `${API_BASE_URL}/program/formreviewsection/${id}`,

    // Review Section Option
    getAllReviewSectionOption: `${API_BASE_URL}/program/formreviewsectionoptions`,
    createReviewSectionOption: `${API_BASE_URL}/program/formreviewsectionoptions`,
    updateReviewSectionOption: (id) => `${API_BASE_URL}/program/formreviewsectionoptions/${id}`,
    deleteReviewSectionOption: (id) => `${API_BASE_URL}/program/formreviewsectionoptions/${id}`,

    // Races
    getAllRaces: `${API_BASE_URL}/program/getracetype`,
    createRaces: `${API_BASE_URL}/program/addracetype`,
    updateRaces: `${API_BASE_URL}/program/updateracetype`,
    deleteRaces: (id) => `${API_BASE_URL}/program/deleteracetype/${id}`,

    // Activity Data
    getAllActivityData: `${API_BASE_URL}/feeddata/activities`,
    activities: `${API_BASE_URL}/feeddata/activities`,
    goals: `${API_BASE_URL}/feeddata/goals`,
    multisport: `${API_BASE_URL}/feeddata/multisport`,
    goalNamesForYTA: `${API_BASE_URL}/feeddata/goalnamesforyta`,
    goalsData: `${API_BASE_URL}/feeddata/goalsdata`,
    phase: `${API_BASE_URL}/feeddata/phase`,
    phaseName: `${API_BASE_URL}/feeddata/phasename`,
    ytaGoalsVolume: `${API_BASE_URL}/feeddata/ytagoalsvolume`,
    userGoals: `${API_BASE_URL}/usergoals`,
    ytaGoals: `${API_BASE_URL}/feeddata/ytagoals`,
    phaseSubActivity: `${API_BASE_URL}/feeddata/phasesubactivity`,
    subWorkout: `${API_BASE_URL}/feeddata/subworkout`,
    workout: `${API_BASE_URL}/feeddata/workout`,
    assessments: `${API_BASE_URL}/feeddata/assessments`,
    segments: `${API_BASE_URL}/feeddata/segments`,
    subSegments: `${API_BASE_URL}/feeddata/subsegments`,
    questions: `${API_BASE_URL}/feeddata/questions`,
    getAllOptionData: `${API_BASE_URL}/feeddata/options`,
    createOptionData: `${API_BASE_URL}/feeddata/options`,
    updateOptionData: (id) => `${API_BASE_URL}/feeddata/options/${id}`,
    deleteOptionData: (id) => `${API_BASE_URL}/feeddata/options/${id}`,
    getAllRenderAssessment: `${API_BASE_URL}/program/renderassessmentdata/wellness`,
    getAllRaceReadinessData: `${API_BASE_URL}/feeddata/race-readiness`,
    getAllSegmentReadinessData: `${API_BASE_URL}/feeddata/segments-readiness`,
    createSegmentReadinessData: `${API_BASE_URL}/feeddata/segments-readiness`,
    updateSegmentReadinessData: (id) => `${API_BASE_URL}/feeddata/segments-readiness/${id}`,
    deleteSegmentReadinessData: (id) => `${API_BASE_URL}/feeddata/segments-readiness/${id}`,
    getAllSubSegmentReadinessData: `${API_BASE_URL}/feeddata/subsegments-readiness`,
    createSubSegmentReadinessData: `${API_BASE_URL}/feeddata/subsegments-readiness`,
    updateSubSegmentReadinessData: (id) => `${API_BASE_URL}/feeddata/subsegments-readiness/${id}`,
    deleteSubSegmentReadinessData: (id) => `${API_BASE_URL}/feeddata/subsegments-readiness/${id}`,
    getAllOptionReadinessData: `${API_BASE_URL}/feeddata/options-readiness`,
    createOptionReadinessData: `${API_BASE_URL}/feeddata/options-readiness`,
    updateOptionReadinessData: (id) => `${API_BASE_URL}/feeddata/options-readiness/${id}`,
    deleteOptionReadinessData: (id) => `${API_BASE_URL}/feeddata/options-readiness/${id}`,
    getAllScoreReadinessData: `${API_BASE_URL}/feeddata/readiness-scores`,
    createScoreReadinessData: `${API_BASE_URL}/feeddata/readiness-scores`,
    updateScoreReadinessData: (id) => `${API_BASE_URL}/feeddata/readiness-scores/${id}`,
    deleteScoreReadinessData: (id) => `${API_BASE_URL}/feeddata/readiness-scores/${id}`,
    getAllActivityGroupData: `${API_BASE_URL}/feeddata/activitygroup`,
    createActivityGroupData: `${API_BASE_URL}/feeddata/activitygroup`,
    updateActivityGroupData: (id) => `${API_BASE_URL}/feeddata/activitygroup/${id}`,
    deleteActivityGroupData: (id) => `${API_BASE_URL}/feeddata/activitygroup/${id}`,
    getAllUsers: `${API_BASE_URL}/get-user`,
    getChallengeData: `${API_BASE_URL}/gamelevel`,
    createChallengeData: `${API_BASE_URL}/gamelevel`,
    updateChallengeData: `${API_BASE_URL}/gamelevel/{id}`,
    deleteChallengeData: `${API_BASE_URL}/gamelevel/{id}`,
    getGoalWithoutVolumeData: `${API_BASE_URL}/GoalsWithoutVolume`,
    createGoalWithoutVolumeData: `${API_BASE_URL}/GoalsWithoutVolume`,
    updateGoalWithoutVolumeData: `${API_BASE_URL}/GoalsWithoutVolume/{id}`,
    deleteGoalWithoutVolumeData: `${API_BASE_URL}/GoalsWithoutVolume/{id}`,
    getPowerData: `${API_BASE_URL}/zonespower`,
    createPowerData: `${API_BASE_URL}/zonespower`,
    updatePowerData: `${API_BASE_URL}/zonespower/{id}`,
    deletePowerData: `${API_BASE_URL}/zonespower/{id}`,
    getZoneHeartData: `${API_BASE_URL}/zonesheartrate`,
    createZoneHeartData: `${API_BASE_URL}/zonesheartrate`,
    updateZoneHeartData: `${API_BASE_URL}/zonesheartrate/{id}`,
    deleteZoneHeartData: `${API_BASE_URL}/zonesheartrate/{id}`,
    uploadSaveFile: `${API_BASE_URL}/savefile`,
    getImageURLData: `${API_BASE_URL}/formimages`,
    createImageURLData: `${API_BASE_URL}/formimages`,
    updateImageURLData: `${API_BASE_URL}/formimages/{id}`,
    deleteImageURLData: `${API_BASE_URL}/formimages/{id}`,
    getActivityTrackData: `${API_BASE_URL}/activitytrack`,
    createActivityTrackData: `${API_BASE_URL}/activitytrack`,
    updateActivityTrackData: `${API_BASE_URL}/activitytrack/{id}`,
    deleteActivityTrackData: `${API_BASE_URL}/activitytrack/{id}`,
    getPromotorsData: `${API_BASE_URL}/promotors`,
    createPromotorsData: `${API_BASE_URL}/promotors`,
    updatePromotorsData: `${API_BASE_URL}/promotors/{id}`,
    deletePromotorsData: `${API_BASE_URL}/promotors/{id}`,
    getPaymentCredData: '/feeddata/paymentcreds',
    createPaymentCreddata: '/feeddata/paymentcreds',
    updatePaymentCreddata: '/feeddata/paymentcreds',
    deletePaymentCredData: '/feeddata/paymentcreds',
    getDiscountCouponData: '/feeddata/discountcoupon',
    createDiscountCouponData: '/feeddata/discountcoupon',
    updateDiscountCouponData: '/feeddata/discountcoupon',
    deleteDiscountCouponData: '/feeddata/discountcoupon',
    getSubspackageData: '/feeddata/subspackage',
    createSubspackageData: '/feeddata/subspackage',
    updateSubspackageData: '/feeddata/subspackage',
    deleteSubspackageData: '/feeddata/subspackage',
    getUomsData: '/feeddata/uoms',
    createUomsData: '/feeddata/uoms',
    updateUomsData: '/feeddata/uoms',
    deleteUomsData: '/feeddata/uoms',
    getTagCloudData: '/feeddata/tag-clouds',
    createTagCloudData: '/feeddata/tag-clouds',
    updateTagCloudData: '/feeddata/tag-clouds',
    deleteTagCloudData: '/feeddata/tag-clouds',
    getSystemConfigurationData: '/feeddata/system-configs',
    createSystemConfigurationData: '/feeddata/system-configs',
    updateSystemConfigurationData: '/feeddata/system-configs',
    deleteSystemConfigurationData: '/feeddata/system-configs',
    getActivityMetricData: '/feeddata/activity-metrics',
    createActivityMetricData: '/feeddata/activity-metrics',
    updateActivityMetricData: '/feeddata/activity-metrics',
    deleteActivityMetricData: '/feeddata/activity-metrics',
    createGroupRegistrationData: '/profile/createusergroup',
    disconnectStrava: '/strava/disconnect',
    getCadenceData: '/streams',
    getHeartZoneData: `${API_BASE_URL}/program/userzonesdata`,
    registerCoach: `${API_BASE_URL}/program/register`,
    updatedusers: `${API_BASE_URL}/profile/getonlycoachesforatheletesv2`,
    getzonesupdation: `${API_BASE_URL}/program/checkifzonesupdationscreenshouldbeshown`,
    getCoachProfile: `${API_BASE_URL}/profile/getcoachprofile`,
    getcoachGrantAccess: `${API_BASE_URL}/profile/getadmincoachprivilige`,
    getUpdateAutomation: `${API_BASE_URL}/program/checkautomationcapturestatusweekbyweek`,
    saveCoachProfile: `${API_BASE_URL}/profile/setcoachprofile`,
    getPartculatWorkouts: `${API_BASE_URL}/workoutmaster/getWorkoutMastersbyid`,
    getAllProgramFeature: `${API_BASE_URL}/feeddata/ProgramFeature`,
    createprogramFeature: `${API_BASE_URL}/feeddata/ProgramFeature`,
    updateProgramFeature: `${API_BASE_URL}/feeddata/ProgramFeature`,
    deleteProgramFeature: `${API_BASE_URL}/feeddata/ProgramFeature`,
    getAllProgramPoints: `${API_BASE_URL}/feeddata/OverallPoints`,
    createprogramOverallPoints: `${API_BASE_URL}/feeddata/OverallPoints`,
    updateProgramOverallPoints: `${API_BASE_URL}/feeddata/OverallPoints`,
    deleteProgramOverallPoints: `${API_BASE_URL}/feeddata/OverallPoints`,
    filterUsersProgram: `${API_BASE_URL}/sign-up/exportsallusersfromprogram`
};
