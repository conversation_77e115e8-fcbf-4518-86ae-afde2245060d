import { useState, useEffect } from "react";
import { But<PERSON> } from "../../components/ui/button";
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogFooter,
	DialogHeader,
	DialogTitle,
} from "../../components/ui/dialog";
import { Input } from "../../components/ui/input";
import { Label } from "../../components/ui/label";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "../../components/ui/select";

export function ZonesClassificationDialog({
	open,
	onOpenChange,
	zone,
	onSave,
	activities = [],
	levels = [],
}) {
	const [formData, setFormData] = useState({
		activity_id: "",
		level_id: "",
		distance: "",
		startRange: "",
		endRange: "",
	});

	useEffect(() => {
		if (zone) {
			setFormData({
				activity_id: zone.activity_id ? String(zone.activity_id) : "",
				level_id: zone.level_id ? String(zone.level_id) : "",
				distance: zone.distance?.toString() || "",
				startRange: zone.startRange || "",
				endRange: zone.endRange || "",
			});
		} else {
			setFormData({
				activity_id: "",
				level_id: "",
				distance: "",
				startRange: "",
				endRange: "",
			});
		}
	}, [zone, open]);

	const handleSubmit = (e) => {
		e.preventDefault();
		onSave({
			...formData,
			activity_id: Number(formData.activity_id),
			level_id: Number(formData.level_id),
			distance: parseInt(formData.distance),
		});
	};

	return (
		<Dialog open={open} onOpenChange={onOpenChange}>
			<DialogContent className='sm:max-w-[500px] bg-white'>
				<DialogHeader>
					<DialogTitle>
						{zone
							? "Edit Zone Classification"
							: "Create Zone Classification"}
					</DialogTitle>
					<DialogDescription className='text-gray-600 text-sm'>
						{zone
							? "Update the zone classification details below."
							: "Create a new zone classification with performance ranges."}
					</DialogDescription>
				</DialogHeader>

				<form onSubmit={handleSubmit} className='space-y-4'>
					<div className='grid grid-cols-2 gap-4'>
						<div className='space-y-2'>
							<Label htmlFor='activity' className='font-semibold'>
								Activity
							</Label>
							<Select
								value={formData.activity_id}
								onValueChange={(value) =>
									setFormData({
										...formData,
										activity_id: value,
									})
								}
							>
								<SelectTrigger>
									<SelectValue placeholder='Select activity' />
								</SelectTrigger>
								<SelectContent className='bg-white'>
									{activities.map((activity) => (
										<SelectItem
											key={activity.id}
											value={String(activity.id)}
										>
											{activity.activity ||
												activity.activity_name}
										</SelectItem>
									))}
								</SelectContent>
							</Select>
						</div>

						<div className='space-y-2'>
							<Label htmlFor='level' className='font-semibold'>
								Level
							</Label>
							<Select
								value={formData.level_id}
								onValueChange={(value) =>
									setFormData({
										...formData,
										level_id: value,
									})
								}
							>
								<SelectTrigger>
									<SelectValue placeholder='Select level' />
								</SelectTrigger>
								<SelectContent className='bg-white'>
									{levels.map((l) => (
										<SelectItem
											key={l.id}
											value={String(l.id)}
										>
											{l.level}
										</SelectItem>
									))}
								</SelectContent>
							</Select>
						</div>
					</div>

					<div className='space-y-2'>
						<Label htmlFor='distance' className='font-semibold'>
							Distance (meters)
						</Label>
						<Input
							className='w-full text-sm'
							id='distance'
							type='number'
							placeholder='e.g., 5000'
							value={formData.distance}
							onChange={(e) =>
								setFormData({
									...formData,
									distance: e.target.value,
								})
							}
							required
						/>
					</div>

					<div className='grid grid-cols-2 gap-4'>
						<div className='space-y-2'>
							<Label
								htmlFor='startRange'
								className='font-semibold'
							>
								Start Range (MM:SS:MS)
							</Label>
							<Input
								id='startRange'
								placeholder='e.g., 00:20:01'
								value={formData.startRange}
								onChange={(e) =>
									setFormData({
										...formData,
										startRange: e.target.value,
									})
								}
								required
								className='w-full text-sm'
							/>
						</div>

						<div className='space-y-2'>
							<Label htmlFor='endRange' className='font-semibold'>
								End Range (MM:SS:MS)
							</Label>
							<Input
								id='endRange'
								placeholder='e.g., 00:25:00'
								value={formData.endRange}
								onChange={(e) =>
									setFormData({
										...formData,
										endRange: e.target.value,
									})
								}
								required
								className='w-full text-sm'
							/>
						</div>
					</div>

					<DialogFooter>
						<Button
							type='button'
							variant='outline'
							onClick={() => onOpenChange(false)}
						>
							Cancel
						</Button>
						<Button
							type='submit'
							className='bg-orange-500 text-white'
						>
							{zone ? "Update Zone" : "Create Zone"}
						</Button>
					</DialogFooter>
				</form>
			</DialogContent>
		</Dialog>
	);
}
