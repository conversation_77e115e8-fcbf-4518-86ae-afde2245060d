import React, { useContext, useEffect, useState } from 'react'
import Header from '../components/Header'
import { IconLiveView, IconMessage, IconTrash, IconX } from '@tabler/icons'
import Sidebar from '../components/Sidebar'
import GlobalContext from '../context/GlobalContext'

const CyclingProgram = () => {
  const [createModal, setCreateModal] = useState(false)
  const [athletes, setAthletes] = useState([])
  // const [userId, setUserId] = useState(null)

  const handleModalReveal = () => {
    setCreateModal(true)
  }

  const { user } = useContext(GlobalContext)

  const actions = [
    {
      id: 1,
      title: 'Quick view',
      icon: <IconLiveView size={18} />
    },
    {
      id: 2,
      title: 'Chat',
      icon: <IconMessage size={18} />
    },
    {
      id: 3,
      title: 'Details',
      icon: <IconTrash size={18} />
    }
  ]

  useEffect(() => {
    console.log('user', user)
    // console.log('Access token', accessToken)

    // eslint-disable-next-line no-unused-expressions
    // user === 'deepak'
    //   ? setUserId(422)
    //   : user === '<EMAIL>'
    //   ? setUserId(340)
    //   : user === '<EMAIL>'
    //   ? setUserId(484)
    //   : setUserId(500)

    const apiUrl = `https://app.yoska.in/kona-coach/api/organizations/45/athletes`
    const bearerToken = 'qmmvofrilpsj59rctkn0ivseuil1jji5'

    fetch(apiUrl, {
      headers: {
        Authorization: `Bearer ${bearerToken}`
      }
    })
      .then((response) => response.json())
      .then((data) => {
        console.log('athletes', data)
        setAthletes(data)
        // Do something with the data
      })
      .catch((error) => {
        console.error(error)
        // Handle the error
      })
  }, [user])

  return (
    <>
      <Header />
      <div className='grid grid-cols-1 xl:grid-cols-5 items-start gap-x-4'>
        <div className='p-4 bg-slate-100 fixed top-24 w-full lg:w-4/12 xl:w-2/12 left-0 overflow-hidden h-full'>
          <Sidebar
            handleModalReveal={handleModalReveal}
            actions={actions}
            athletes={athletes}
          />
        </div>
        <div className='flex items-center justify-center p-4 w-full md:w-10/12 lg:col-span-4 absolute right-0 overflow-y-scroll'>
          {/* Create modal */}
          <div
            className={`${
              createModal ? 'md:fixed' : 'md:absolute'
            } md:top-32 md:right-2/2 px-4 py-6 bg-slate-50 drop-shadow-2xl rounded-md border-2 border-slate-200/75 w-full md:w-8/12 lg:w-6/12 xl:w-4/12 z-50`}
            style={{ display: createModal ? 'block' : 'none' }}
          >
            <div className='flex items-center justify-between'>
              <span className='text-xl font-medium m-0 pb-2'>New Athlete</span>
              <span className='text-slate-600'>
                <IconX
                  size={22}
                  className='mb-2 cursor-pointer'
                  onClick={() => setCreateModal(false)}
                />
              </span>
            </div>
            <hr />
            <br />
            <div className='mb-5 grid grid-cols-1 md:grid-cols-2 items-start gap-4'>
              <div>
                <p className='mb-1 text-slate-600 px-1 font-medium'>
                  Group <span className='text-red-500'>*</span>{' '}
                </p>
                <select
                  name='status'
                  id='status'
                  placeholder='Group'
                  className='w-full border-b-2 py-2 rounded-md focus:outline-none text-slate-600'
                >
                  <option value='#'>--Select--</option>
                  <option value='General Fitness'>General Fitness</option>
                </select>
              </div>
              <div>
                <p className='mb-1 text-slate-600 px-1 font-medium'>
                  Sub-group <span className='text-red-500'>*</span>{' '}
                </p>
                <select
                  name='status'
                  id='status'
                  placeholder='Group'
                  className='w-full border-b-2 py-2 rounded-md focus:outline-none text-slate-600'
                >
                  <option value='#'>--Select--</option>
                  <option value='Fitness habit formation'>
                    Fitness habit formation
                  </option>
                  <option value='General fitness and walking'>
                    General fitness and walking
                  </option>
                  <option value='Weight management - 1'>
                    Weight management - 1
                  </option>
                  <option value='Weight management - 2'>
                    Weight management - 2
                  </option>
                </select>
              </div>
            </div>
            <div className='mb-5'>
              <p className='mb-1 text-slate-600 px-1 font-medium'>
                Status <span className='text-red-500'>*</span>{' '}
              </p>
              <select
                name='status'
                id='status'
                placeholder='Group'
                className='w-full border-b-2 py-2 rounded-md focus:outline-none text-slate-600'
              >
                <option value='#'>--Select--</option>
                <option value='Subscribed'>Subscribed</option>
              </select>
            </div>
            <div className='mb-5 grid grid-cols-1 md:grid-cols-2 items-start gap-4'>
              <div>
                <p className='mb-1 text-slate-600 px-1 font-medium'>
                  Firstname <span className='text-red-500'>*</span>{' '}
                </p>
                <input
                  type='text'
                  placeholder='Enter your firstname'
                  className='w-full border-b-2 py-2 rounded-md focus:outline-none text-slate-600 px-1'
                />
              </div>
              <div>
                <p className='mb-1 text-slate-600 px-1 font-medium'>
                  Lastname <span className='text-red-500'>*</span>{' '}
                </p>
                <input
                  type='text'
                  placeholder='Enter your lastname'
                  className='w-full border-b-2 py-2 rounded-md focus:outline-none text-slate-600 px-1'
                />
              </div>
            </div>
            <div className='mb-5'>
              <p className='mb-1 text-slate-600 px-1 font-medium'>
                Gender <span className='text-red-500'>*</span>{' '}
              </p>
              <select
                name='gender'
                id='gender'
                placeholder='Group'
                className='w-full border-b-2 py-2 rounded-md focus:outline-none text-slate-600'
              >
                <option value='#'>--Select--</option>
                <option value='Male'>Male</option>
                <option value='Female'>Female</option>
              </select>
            </div>
            <div className='mb-5 grid grid-cols-1 md:grid-cols-2 items-start gap-4'>
              <div>
                <p className='mb-1 text-slate-600 px-1 font-medium'>
                  Email <span className='text-red-500'>*</span>{' '}
                </p>
                <input
                  type='text'
                  placeholder='Enter your email address'
                  className='w-full border-b-2 py-2 rounded-md focus:outline-none text-slate-600 px-1'
                />
              </div>
              <div>
                <p className='mb-1 text-slate-600 px-1 font-medium'>
                  Mobile <span className='text-red-500'>*</span>{' '}
                </p>
                <input
                  type='text'
                  placeholder='Enter your mobile number'
                  className='w-full border-b-2 py-2 rounded-md focus:outline-none text-slate-600 px-1'
                />
              </div>
            </div>
            <div className='mb-5'>
              <div>
                <p className='mb-1 text-slate-600 px-1 font-medium'>
                  City <span className='text-red-500'>*</span>{' '}
                </p>
                <input
                  type='text'
                  placeholder='Enter your city'
                  className='w-full border-b-2 py-2 rounded-md focus:outline-none text-slate-600 px-1'
                />
              </div>
            </div>
            <div className='mb-5 flex items-center'>
              <input type='checkbox' name='welcome' id='welcome' />
              <span className='ml-2 text-sm'>Send welcome email</span>
            </div>
            <div className='mb-2'>
              <button className='p-2.5 bg-orange-500 text-slate-50 w-full rounded-sm'>
                Create
              </button>
            </div>
          </div>
        </div>
      </div>
    </>
  )
}

export default CyclingProgram
