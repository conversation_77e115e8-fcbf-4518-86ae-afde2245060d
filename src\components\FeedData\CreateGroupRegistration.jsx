import { Button, CircularProgress, FormLabel, Grid, MenuItem, OutlinedInput, Paper, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, TextField, tableCellClasses } from '@mui/material';
import React, { useEffect, useState } from 'react'
import { CreateGroupRegistrationData, getAllActivityData, getAllPrograms, getSubscriptionPlanBYProgramID, updateImageURLdata, uploadsaveFile } from '../../API/api-endpoint';
import { useFormik } from 'formik';
import Swal from 'sweetalert2';
import { IoCloseSharp } from "react-icons/io5";
import Header from '../Header';
import { styled } from "@mui/material/styles";
import SlickCarousel from "../../pages/SlickCarousel";
import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";
import moment from "moment";
let PageSize = 15;
const StyledTableCell = styled(TableCell)(({ theme,alreadyexists }) => ({
    
    [`&.${tableCellClasses.head}`]: {
        backgroundColor: "#1e40af",
        color: theme.palette.common.white,
    },
    [`&.${tableCellClasses.body}`]: {
        fontSize: 14,
    },
}));
const StyledTableRow = styled(TableRow)(({ theme }) => ({
    "&:nth-of-type(odd)": {
        backgroundColor: theme.palette.action.hover,
    },
    // hide last border
    "&:last-child td, &:last-child th": {
        border: 0,
    },
}));
const CreateGroupRegistration = ({ fetchReport }) => {
    const [isLoading, setIsLoading] = useState(false)
    const [userData, setUserData] = useState();
    const [notExistingUserData, setNotExistingUserData] = useState();

    const [subscriptionList, setSubscriptionList] = useState([]);
    const [programList, setProgramList] = useState([]);
    const [userMessage, setUserMessage] = useState("");
    const [downloadLink, setLink] = useState("");


    console.log("programList", programList, subscriptionList);
    const formik = useFormik({
        initialValues: {
            sub_id: "",
            groupname: "",
            price: "",
            file: "",
            start_date: "",
            end_date: "",
        }, validate: (values) => {
            const errors = {};
            if (!values.sub_id) {
                errors.sub_id = "sub id is required";
            }
            if (!values.groupname) {
                errors.groupname = "Group name is required";
            }
            if (!values.price) {
                errors.price = "Price is required";
            }
            if (!values.file) {
                errors.file = "File is required";
            }
            if (!values.start_date) {
                errors.start_date = "Start date is required";
            }
            if (!values.end_date) {
                errors.end_date = "End date is required";
            }

            return errors;
        },
        // validationSchema: {},
        onSubmit: (values, { resetForm }) => {
            handleSubmitAssesmentForm(values, resetForm)

        },
    });
    console.log("formik", formik?.values, formik?.errors);

    useEffect(() => {
        getAllProgramsData()
        if (formik?.values?.activity_id) {
            getSubscriptionPlan()
        }
    }, [formik?.values?.activity_id])
    const getAllProgramsData = async () => {
        const response = await getAllPrograms()
        setProgramList(response?.rows);
    };
    const getSubscriptionPlan = async () => {
        setSubscriptionList([])
        let response = await getSubscriptionPlanBYProgramID(
            formik?.values?.activity_id
        ); //we have to send program_id here
        setSubscriptionList(response);
    };

    const handleSubmitAssesmentForm = async (data, resetForm) => {
        setIsLoading(true)
        let formData = new FormData();

        // Append existing data from 'data' object to the formData
        Object.keys(data).forEach((key) => {
            formData.append(key, data[key]);
        });
        setLink("")
        setNotExistingUserData([])
        setUserData([])
        let response = await CreateGroupRegistrationData(formData)
        setIsLoading(false)
        setLink(response?.filepathtodownload)
        if (response?.createduser?.length > 0) {
            let existsUser = response?.createduser?.filter((ele)=>!ele.alreadyexists)
            setUserData(existsUser)
            let data = response?.createduser?.filter((ele)=>ele.alreadyexists)
            setNotExistingUserData(data)
            setUserMessage("Created Users List")

            Swal.fire({
                title: "Success",
                text: response.message,
                icon: "success",
            });
            formik.resetForm()
            formik?.setValues({
                activity_id: "",
                groupname: "",
                price: "",
                file: "",
                start_date: "",
                end_date: "",


            })
        } else if (response?.message == "invalid entries found, first fix them then upload again") {
            setUserData(response?.invalidatedRows)
            setUserMessage("Invalid User List")
            Swal.fire({
                title: "Error",
                text: response.message,
                icon: "error",
            });
        } else {
            setUserData([])
            Swal.fire({
                title: "Error",
                text: response.message,
                icon: "error",
            });
        }
        console.log("response", response);
    }


    const handleFileUpload = async (event) => {
        const file = event.target.files[0];
        console.log("file", file);
        if (file) {
            formik.setFieldValue("file", file)
            formik.setFieldValue("fileName", file.name)



        }
    }
    const handleDownload = () => {
        const link = document.createElement('a');
        link.href = downloadLink;
        link.download = 'file.csv';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      };
    return (
        <div >
        <Header />
        <div
          className="grid grid-cols-1 xl:grid-cols-5 items-start gap-x-4 "
          style={{ marginTop: "100px" }}
        ></div>
        <div style={{
          margin: "20px 55px 20px 20px",
          padding: "1% 2% ",
          width: "96%",
          boxShadow: "rgba(99, 99, 99, 0.2) 0px 2px 8px 0px",
        }}>
          <div className="headingCont" >
          <span className="heading">Create</span>{" "}
          <span className="orange heading">Group Registration</span>
        </div>
        <div className="parentCont">
          <form className="form1" onSubmit={formik?.handleSubmit}>
            <Grid
              container
              spacing={2}
              
            >
              <Grid item xs={12} sm={11}>
                <FormLabel>Program Name<span className="text-[red]">*</span></FormLabel>
  
                <TextField
                  fullWidth
                  size="small"
                  select
                  name="activity_id"
                  value={formik?.values?.activity_id}
                  onChange={formik.handleChange}
                  error={formik.touched.activity_id && formik.errors.activity_id}
                  helperText={
                    formik.touched.activity_id && formik.errors.activity_id
                  }
                  id="form-layouts-separator-select"
                  labelId="form-layouts-separator-select-label"
                  input={<OutlinedInput id="select-multiple-language" />}
                >
                  <MenuItem value={""} disabled>
                    Select Activity
                  </MenuItem>
                  {programList?.map((value, index) => {
                    return (
                      <MenuItem value={value?.program_id}>
                        {value?.program_name}
                      </MenuItem>
                    );
                  })}
                </TextField>
              </Grid>
              <Grid item xs={12} sm={11}>
                <FormLabel>Subscription<span className="text-[red]">*</span></FormLabel>
  
                <TextField
                  fullWidth
                  size="small"
                  select
                  name="sub_id"
                  value={formik?.values?.sub_id}
                  onChange={formik.handleChange}
                  error={formik.touched.sub_id && formik.errors.sub_id}
                  helperText={formik.touched.sub_id && formik.errors.sub_id}
                  id="form-layouts-separator-select"
                  labelId="form-layouts-separator-select-label"
                  input={<OutlinedInput id="select-multiple-language" />}
                >
                  <MenuItem value={""} disabled>
                    Select Subscription
                  </MenuItem>
                  {subscriptionList?.map((value, index) => {
                    return (
                      <MenuItem value={value?.id}>
                        {value?.name} {value?.price}
                      </MenuItem>
                    );
                  })}
                </TextField>
              </Grid>
              <Grid item xs={12} sm={11}>
                <FormLabel>Group Name<span className="text-[red]">*</span></FormLabel>
  
                <TextField
                  fullWidth
                  placeholder="Group Name"
                  size="small"
                  type="text"
                  name="groupname"
                  value={formik?.values?.groupname}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  error={formik.touched.groupname && formik.errors.groupname}
                  helperText={formik.touched.groupname && formik.errors.groupname}
                />
              </Grid>
              <Grid item xs={12} sm={11}>
                <FormLabel>Price<span className="text-[red]">*</span></FormLabel>
  
                <TextField
                  fullWidth
                  placeholder="Price"
                  size="small"
                  type="number"
                  name="price"
                  value={formik?.values?.price}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  error={formik.touched.price && formik.errors.price}
                  helperText={formik.touched.price && formik.errors.price}
                />
              </Grid>
              <Grid item xs={12} sm={11} className="relative">
                <FormLabel>Upload File<span className="text-[red]">*</span></FormLabel>
  
                <TextField
                  fullWidth
                  placeholder="Zone"
                  size="small"
                  type={formik?.values?.file ? "text" : "file"}
                  name="file"
                  disabled={formik?.values?.file}
                  value={formik?.values?.fileName}
                  onChange={handleFileUpload}
                  error={formik.touched.file && formik.errors.file}
                  helperText={formik.touched.file && formik.errors.file}
                  inputProps={{
                    accept: ".csv", 
                    style:{bottom:"2px"}
                  }}
                />
                <p style={{ fontSize: "0.8rem", color: "gray", marginTop: "5px" }}>Accepted File Type is .csv</p>
                {formik?.values?.file && (
                  <IoCloseSharp
                    onClick={() => {
                      formik.setFieldValue("file", "");
                      formik.setFieldValue("fileName", "");
                    }}
                    color="darkgray"
                    className="cursor-pointer absolute top-0 right-[-10px]"
                    style={{ fontSize: "24px" }}
                  />
                )}
              </Grid>
              <Grid  className="dateWrap" item xs={12} sm={11}>
                <FormLabel>Start Date<span className="text-[red]">*</span></FormLabel>
  
                <DatePicker
  selected={formik.values.start_date ? new Date(formik.values.start_date) : null}
  onChange={(date) => {
    formik.setFieldValue(
      "start_date",
      moment(date).format("YYYY-MM-DD")
    );
  }}
  placeholderText="DD-MM-YYYY"
  dateFormat="dd-MM-yyyy"
  maxDate={formik.values.end_date ? new Date(formik.values.end_date) : null}
  customInput={
    <TextField
      fullWidth
      placeholder="Start Date"
      size="small"
      type="text"
      name="start_date"
      error={formik.touched.start_date && formik.errors.start_date}
      helperText={formik.touched.start_date && formik.errors.start_date}
    />
  }
/>

              </Grid>
              <Grid  className="dateWrap"  item xs={12} sm={11}>
                <FormLabel>End Date<span className="text-[red]">*</span></FormLabel>
  
                <DatePicker
  selected={formik.values.end_date ? new Date(formik.values.end_date) : null}
  onChange={(date) => {
    formik.setFieldValue(
      "end_date",
      moment(date).format("YYYY-MM-DD")
    );
  }}
  placeholderText="DD-MM-YYYY"
  dateFormat="dd-MM-yyyy"
  minDate={formik.values.start_date ? new Date(formik.values.start_date) : null}
  customInput={
    <TextField
      fullWidth
      placeholder="End Date"
      size="small"
      type="text"
      name="end_date"
      error={formik.touched.end_date && formik.errors.end_date}
      helperText={formik.touched.end_date && formik.errors.end_date}
    />
  }
/>

              </Grid>
              <Grid item xs={12} sm={9}></Grid>
              <Grid item xs={12} sm={6}>
                <Button
                  className="btn"
                  key="submit"
                  type="primary"
                  onClick={() => formik.handleSubmit()}
                >
                  Submit
                </Button>
              </Grid>
              &nbsp;
            </Grid>
          </form>
          <div className="slick-container">
            <SlickCarousel />
          </div>
        </div>
        </div>
        
        &nbsp;
        <TableContainer
          component={Paper}
          style={{
            margin: "20px 55px 20px 20px",
            width: "96%",
            boxShadow: "rgba(99, 99, 99, 0.2) 0px 2px 8px 0px",
          }}
        >
          <div
            style={{
              fontSize: "18px",
              background: "lightgray",
              width: "100%",
              display: "flex",
              justifyContent: "space-between",
            }}
          >
            <h3 style={{ padding: "10px", margin: "0" }}>{userMessage}</h3>
            {downloadLink && (
              <h3 style={{ padding: "10px", margin: "0" }}>
                <Button variant="contained" onClick={() => handleDownload()}>
                  {" "}
                  Download User List
                </Button>
              </h3>
            )}
          </div>
          <Table
            sx={{ minWidth: 700, padding: "10px" }}
            aria-label="customized table"
          >
            <TableHead>
              <TableRow>
                <StyledTableCell align="left">Sr ID</StyledTableCell>
                <StyledTableCell align="left">Name </StyledTableCell>
                <StyledTableCell align="left">Gmail </StyledTableCell>
                <StyledTableCell align="left">
                  Temporary Password{" "}
                </StyledTableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {isLoading ? (
                <CircularProgress className="m-6" />
              ) : (
                <>
                  {userData?.length > 0 ? (
                    <>
                      {userData?.map((row, index) => (
                        <StyledTableRow key={index}>
                          <StyledTableCell
                            align="left"
                            alreadyexists={row?.alreadyexists}
                          >
                            {index + 1}
                          </StyledTableCell>
  
                          <StyledTableCell
                            align="left"
                            alreadyexists={row?.alreadyexists}
                          >
                            {row?.firstname} {row?.lastname}
                          </StyledTableCell>
                          <StyledTableCell
                            align="left"
                            alreadyexists={row?.alreadyexists}
                          >
                            {row?.email}
                          </StyledTableCell>
                          <StyledTableCell
                            align="left"
                            alreadyexists={row?.alreadyexists}
                          >
                            {row?.temppassword ? row?.temppassword : "NA"}
                          </StyledTableCell>
                        </StyledTableRow>
                      ))}
                    </>
                  ) : (
                    <div className="p-4">No data found</div>
                  )}
                </>
              )}
            </TableBody>
          </Table>
        </TableContainer>
        {notExistingUserData?.length > 0 && (
          <TableContainer
            component={Paper}
            style={{
              margin: "20px 55px 20px 20px",
              width: "96%",
              boxShadow: "rgba(99, 99, 99, 0.2) 0px 2px 8px 0px",
            }}
          >
            <div
              style={{
                fontSize: "18px",
                background: "lightgray",
                width: "100%",
                display: "flex",
                justifyContent: "space-between",
              }}
            >
              <h3 style={{ padding: "10px", margin: "0" }}>
                Already Existing Users List
              </h3>
              {downloadLink && (
                <h3 style={{ padding: "10px", margin: "0" }}>
                  <Button variant="contained" onClick={() => handleDownload()}>
                    {" "}
                    Download User List
                  </Button>
                </h3>
              )}
            </div>
            <Table
              sx={{ minWidth: 700, padding: "10px" }}
              aria-label="customized table"
            >
              <TableHead>
                <TableRow>
                  <StyledTableCell align="left">Sr ID</StyledTableCell>
                  <StyledTableCell align="left">Name </StyledTableCell>
                  <StyledTableCell align="left">Gmail </StyledTableCell>
                  <StyledTableCell align="left">
                    Temporary Password{" "}
                  </StyledTableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {isLoading ? (
                  <CircularProgress className="m-6" />
                ) : (
                  <>
                    {notExistingUserData?.length > 0 ? (
                      <>
                        {notExistingUserData?.map((row, index) => (
                          <StyledTableRow key={index}>
                            <StyledTableCell
                              align="left"
                              alreadyexists={row?.alreadyexists}
                            >
                              {index + 1}
                            </StyledTableCell>
  
                            <StyledTableCell
                              align="left"
                              alreadyexists={row?.alreadyexists}
                            >
                              {row?.firstname} {row?.lastname}
                            </StyledTableCell>
                            <StyledTableCell
                              align="left"
                              alreadyexists={row?.alreadyexists}
                            >
                              {row?.email}
                            </StyledTableCell>
                            <StyledTableCell
                              align="left"
                              alreadyexists={row?.alreadyexists}
                            >
                              {row?.temppassword ? row?.temppassword : "NA"}
                            </StyledTableCell>
                          </StyledTableRow>
                        ))}
                      </>
                    ) : (
                      <div className="p-4">No data found</div>
                    )}
                  </>
                )}
              </TableBody>
            </Table>
          </TableContainer>
        )}
      </div>
    );
  };
  export default CreateGroupRegistration;
  