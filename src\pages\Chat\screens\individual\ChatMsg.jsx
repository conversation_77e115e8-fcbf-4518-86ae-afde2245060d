import { Avatar, Box, CircularProgress, Typography } from '@mui/material'
import React, { useEffect, useRef, useState } from 'react'
import PhotoOutlinedIcon from '@mui/icons-material/PhotoOutlined'
import EastOutlinedIcon from '@mui/icons-material/EastOutlined'
import FlightTakeoffOutlinedIcon from '@mui/icons-material/FlightTakeoffOutlined'
import { getDatabase, ref, runTransaction,get,update } from 'firebase/database'
import Popover from '../../components/Popover'
import PlayCircleFilledIcon from '@mui/icons-material/PlayCircleFilled'
import VideoModal from '../../components/preview/VideoModal'
import axios from 'axios'
import ReactLinkify from 'react-linkify'
import ModalImage from "react-modal-image";
import Modal from "react-modal";
import { HiDotsVertical } from "react-icons/hi";
const blurStyle = {
  filter: 'blur(4px)'
}

const ChatMsg = ({
  msg,
  isMessageReqAccepted,
  currentUser,
  openedUser,
  setIsReplyMsg,
  searchKeyword,
  isSearchActive,
  isLoading
}) => {
  const [popoverVisible, setPopoverVisible] = useState(false)
  const [typeOfFile, setTypeOfFile] = useState("Message");
  const [modalOpen, setModalOpen] = useState(false)
  const [post, setPost] = useState(null)
  const [loading, setLoading] = useState(false)
console.log("popoverVisible",popoverVisible);
  const openModal = () => {
    setModalOpen(true)
  }

  useEffect(() => {
    console.log(`msg`, msg)
  }, [msg])

  const closeModal = () => {
    setModalOpen(false)
  }

  const messageRef = useRef(null)

  useEffect(() => {
    if (messageRef.current && isSearchActive && msg.type === 'text') {
      const regex = new RegExp(`(${searchKeyword})`, 'gi');
      const text = messageRef.current.textContent;
      const match = regex.exec(text);

      if (match) {
        messageRef.current.scrollIntoView({ behavior: 'smooth', block: 'center' });
      }
    }
  }, [isSearchActive, msg, searchKeyword]);


  const token =
    'b78ed8c9c33b65a4e85ae9c57634254d12a3c26bda45a562e40e6e38cdd9c7c82b3cb3ff009ecd45e730e7ab23f8669890045d53cc2babbf66f333f07fab912f98aa1e913ceb933ea5d153d7c1c836b78f722f781bc6483008713c11e44052820a2e1b5341681d74ac851fb6cf54194f1f4a3d17932877a9fb6403da22e7ea5394b3c73e703e871f3ff54c15c3afa4db973f09f83111491d50c729c2d66dc72506afa19b069158de44c6492f1304dd5ce69eb80568da0d3652980356d80ec768a8262b11f6404448a59a57389898b34ee5d1f0b403f04abdc65a4580114cb616914b05feb5ab956e98d01a92a33ef6d0'

    const parts = msg.message ? msg.message.split(new RegExp(`(${searchKeyword})`, 'gi')) : [];

  const combinedId =
    currentUser.uid > openedUser.uid
      ? currentUser.uid + openedUser.uid
      : openedUser.uid + currentUser.uid

  let timer
  const handlePressHold = (event) => {
    event.persist() // Preserve the event object for later use

    let timer

    const startTimer = () => {
      timer = setTimeout(() => {
        setPopoverVisible(true)
        handleLongPress()
      }, 1000)
    }

    const cancelTimer = () => {
      clearTimeout(timer)
    }

    document.addEventListener('mouseup', cancelTimer)
    document.addEventListener('touchend', cancelTimer)

    startTimer()
  }

  const handleMouseUp = () => {
    clearTimeout(timer)
    document.removeEventListener('mouseup', handleMouseUp)
  }

  const handleLongPress = () => {
    console.log('Long press detected!')
  }

  function formatTimestamp(timestamp) {
    const date = new Date(parseInt(timestamp))
    const formattedTime = date.toLocaleString('en-US', {
      hour: 'numeric',
      minute: 'numeric',
      hour12: true
    })

    return formattedTime
  }

  

  async function updateIsReadStatus(timeStamp) {
    setTimeout(async () => {
      const db = getDatabase();
      const chatRef = ref(db, `chats/${combinedId}/messages`);
  
      // Fetch the current value of messages
      runTransaction(chatRef, (messages) => {
        if (messages) {
          // Convert messages object to an array
          const messageArray = Object.values(messages);
  
          for (let i = 0; i < messageArray.length; i++) {
            if (messageArray[i].timeStamp === timeStamp) {
              messageArray[i].isSeen = true;
              break;
            }
          }
  
          // Convert the modified array back to an object
          const updatedMessages = messageArray.reduce((acc, curr) => {
            acc[curr.timeStamp] = curr;
            return acc;
          }, {});
  
          return updatedMessages;
        }
  
        return messages;
      })
        .then(() => {
          console.log('Msg seen successfully.');
        })
        .catch((error) => {
          console.log('Msg seen failed: ', error);
        });
  
      const messagesRef = ref(db, `chats/${combinedId}/messages`);
      const messagesSnapshot = await get(messagesRef);
  
      if (messagesSnapshot.exists()) {
        const messages = messagesSnapshot.val();
  
        const updates = {};
        for (const messageId in messages) {
          updates[`${messageId}/isSeen`] = true;
        }
  
        await update(ref(db, `chats/${combinedId}/messages`), updates);
      }
    }, 2000); // Delay of 1 second
  }
  
  

  const openDocumentInNewTab = (downloadUrl) => {
    const newWindow = window.open(downloadUrl, '_blank')
    if (newWindow) {
      newWindow.opener = null
    }
  }

  const handleGetPostDetails = async () => {
    const config = {
      headers: {
        Authorization: `Bearer ${token}`
      }
    }

    const payload = {
      postId: '1088'
    }

    try {
      setLoading(true)
      const { data } = await axios.post(
        '/v5/post/get_post_by_id.php',
        payload,
        config
      )
      console.log(data.object)
      setPost(data.object)
    } catch (error) {
      console.error('Error fetching post:', error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    msg.type === 'post' && handleGetPostDetails()

    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          msg.isSeen === false &&
            msg.senderId === openedUser.uid &&
            updateIsReadStatus(msg.timeStamp)
        }
      },
      {
        root: null,
        rootMargin: '0px',
        threshold: 1.0
      }
    )

    if (messageRef.current) {
      observer.observe(messageRef.current)
    }

    return () => {
      if (messageRef.current) {
        observer.unobserve(messageRef.current)
      }
    }
  }, [])

  return (
    <Box
      onMouseDown={handlePressHold}
      onMouseUp={handleMouseUp}
      onTouchStart={handlePressHold}
      ref={messageRef}
      key={msg.timeStamp}
      sx={{
        // Merge existing styles with conditional style
        ...(
          msg.senderId !== currentUser.uid && msg.isSeen === false
            ? {
                backgroundColor: "#C7F6C7",
                // Apply styles for unseen messages
                // borderTop: '2px solid red', 
                // Add other styles as needed
              }
            : {}
        ),
        ...(
          popoverVisible
            ? { backgroundColor: '#e3e3e3', transition: '0.5s' }
            : { transition: '0.5s' }
        )
      }}
    >
      {isSearchActive && msg.type === 'text' && (
        <Box onClick={()=>{setPopoverVisible(true)
          setTypeOfFile("Message")
        }}
          sx={
            msg.senderId === currentUser.uid
              ? {
                  paddingLeft: '1rem',
                  paddingRight: '1rem',
                  textAlign: 'right',
                  marginBottom: '6px',
                  color: 'white',
                  position: 'relative'
                }
              : {
                  paddingLeft: '1rem',
                  paddingRight: '1rem',
                  textAlign: 'left',
                  marginBottom: '6px',
                  position: 'relative'
                }
          }
        >
          <Box
            className='text-msg-box'
            sx={
              msg.senderId === currentUser.uid
                ? { backgroundColor: '#e67e22', marginLeft: 'auto'  }
                : { backgroundColor: '#eee8e8', marginRight: 'auto' }
            }
          >
            <span>
              {parts.map((part, i) =>
                part.toLowerCase() === searchKeyword.toLowerCase() ? (
                  <span
                    key={i}
                    style={{ backgroundColor: '#FFFFFF', color: 'black' }}
                  >
                    {part}
                  </span>
                ) : (
                  part
                )
              )}
            </span>
            <Box className='text-msg-timing-parent'>
              <Typography
                className='text-msg-timing'
                style={
                  msg.senderId === currentUser.uid
                    ? { textAlign: 'right' }
                    : { textAlign: 'left' }
                }
              >
                {msg.isStarred && (
                  <img
                    style={{ marginRight: '0.5rem' }}
                    src='/images/msgIcons/starred.png'
                    height={8}
                    alt=''
                  />
                )}
                {formatTimestamp(msg.timeStamp)}
              </Typography>
            </Box>
          </Box>
        </Box>
      )}

      {!isSearchActive && msg.type === 'text' && (
        <Box 
          sx={ 
            msg.senderId === currentUser.uid
              ? {
                  paddingLeft: '1rem',
                  paddingRight: '1rem',
                  textAlign: 'right',
                  marginBottom: '6px',
                  color: 'rgb(59, 59, 59)',
                  position: 'relative'
                }
              : {
                  paddingLeft: '1rem',
                  paddingRight: '1rem',
                  textAlign: 'left',
                  marginBottom: '6px',
                  position: 'relative'
                }
          }
        >
          <Box
            className='text-msg-box relative'
            sx={
              msg.senderId === currentUser.uid
                ? { backgroundColor: 'rgb(255, 222, 173)', marginLeft: 'auto' }
                : { backgroundColor: '#eee8e8', marginRight: 'auto' }
            }
          >
            <span >
            {msg.senderId === currentUser.uid &&
            <HiDotsVertical onClick={ ()=>{
              if (msg.senderId === currentUser.uid) {
                setPopoverVisible(true)
                setTypeOfFile("Message")
              }}
              } className='absolute right-[2px] top-[4px] cursor-pointer'/>
            }
              <ReactLinkify
                componentDecorator={(decoratedHref, decoratedText, key) => (
                  <a
                    href={decoratedHref}
                    key={key}
                    target='_blank' // This attribute opens the link in a new tab
                    rel='noopener noreferrer' // Recommended for security reasons
                    style={{ color: 'skyblue' }}
                  >
                    {decoratedText}
                  </a>
                )}
              >
                {' '}
                {msg.message}
              </ReactLinkify>

              {msg.linkData && (
                <div className='meta_card'>
                  {msg?.linkData?.image ? (
                    <img
                      src={msg?.linkData?.image}
                      alt='...'
                      className='meta_img'
                    />
                  ) : (
                    <img
                      src={
                        'https://upload.wikimedia.org/wikipedia/commons/thumb/3/3f/Placeholder_view_vector.svg/681px-Placeholder_view_vector.svg.png'
                      }
                      alt='...'
                      className='meta_img'
                    />
                  )}
                  <p className='meta_desc'>
                    {msg?.linkData?.description?.substring(1, 60)}...
                  </p>
                  <p className='meta_title'>{msg?.linkData?.title}</p>
                  <p className='meta_sitename'>{msg?.linkData?.sitename}</p>
                </div>
              )}
            </span>
            <Box className='text-msg-timing-parent'>
              <Typography
                className='text-msg-timing'
                style={
                  msg.senderId === currentUser.uid
                    ? { textAlign: 'right' }
                    : { textAlign: 'left' }
                }
              >
                {msg.isStarred && (
                  <img
                    style={{ marginRight: '0.5rem' }}
                    src='/images/msgIcons/starred.png'
                    height={8}
                    alt=''
                  />
                )}
                {formatTimestamp(msg.timeStamp)}
              </Typography>
            </Box>
          </Box>
        </Box>
      )}

      {msg.type === 'replied-text' && (
        <Box 
          sx={
            msg.senderId === currentUser.uid
              ? {
                  paddingLeft: '1rem',
                  paddingRight: '1rem',
                  textAlign: 'right',
                  marginBottom: '6px',
                  color: 'white',
                  position: 'relative'
                }
              : {
                  paddingLeft: '1rem',
                  paddingRight: '1rem',
                  textAlign: 'left',
                  marginBottom: '6px',
                  position: 'relative'
                }
          }
        >
          <Box
            className='text-msg-box'
            sx={
              msg.senderId === currentUser.uid
                ? { backgroundColor: '#e67e22', paddingTop: 0.8 }
                : { backgroundColor: '#eee8e8' }
            }
          >
            <span style={{ fontSize: '9.5px' }}>{`${
              currentUser.uid === msg.repliedMsgInfo.senderId
                ? 'You'
                : openedUser.displayName.split(' ')[0]
            } : ${msg.repliedMsgInfo.message}`}</span>
            <hr />
            <span style={isMessageReqAccepted ? {} : { ...blurStyle }}>
              {msg.message}
            </span>
            <Box className='text-msg-timing-parent'>
              <Typography className='text-msg-timing'>
                {msg.isStarred && (
                  <img
                    style={{ marginRight: '0.5rem' }}
                    src='/images/msgIcons/starred.png'
                    height={8}
                    alt=''
                  />
                )}
                {msg.isStarred && (
                  <img
                    style={{ marginRight: '0.5rem' }}
                    src='/images/msgIcons/starred.png'
                    height={8}
                    alt=''
                  />
                )}
                {formatTimestamp(msg.timeStamp)}
              </Typography>
            </Box>
          </Box>
        </Box>
      )}

      <Popover
        setPopoverVisible={setPopoverVisible}
        typeOfFile={typeOfFile}
        isVisible={popoverVisible}
        setTypeOfFile={setTypeOfFile}
        msg={msg}
        combinedId={combinedId}
        currentUserUid={currentUser.uid}
        setIsReplyMsg={setIsReplyMsg}
        isGroup={false}
        currentUserID={currentUser.uid}
      />

      {msg.type === 'photo' && (
        <Box
          sx={
            msg.senderId === currentUser.uid
              ? { textAlign: 'right', marginBottom: '6px', color: 'white' }
              : { textAlign: 'left', marginBottom: '6px' }
          }
        >
          <Box
            className='photo-msg-parent'
            sx={
              msg.senderId === currentUser.uid
                ? { backgroundColor: '#e67e22' }
                : { backgroundColor: '#eee8e8' }
            }
          >
          {isLoading &&
          <CircularProgress/>
          }
            <Box className='photo-msg-img-box relative'>
            <ModalImage
            small={msg.message}
            medium={msg.message}
            large={msg.message}
            alt=""
            className="modalImage"
            style={isMessageReqAccepted ? {} : { filter: 'blur(5px)' }}
          /> 
          {msg.senderId === currentUser.uid &&
            <HiDotsVertical onClick={ ()=>{
              if (msg.senderId === currentUser.uid) {
                setPopoverVisible(true)
                setTypeOfFile("Image")}}
              } className='absolute right-[4px] top-[5px] cursor-pointer'/>
            }
             
            </Box>
           
            <Typography className='text-msg-timing' sx={{ textAlign: 'right',paddingTop:"5px" }}>
              {msg.isStarred && (
                <img
                  style={{ marginRight: '0.5rem' }}
                  src='/images/msgIcons/starred.png'
                  height={8}
                  alt=''
                />
              )}
              {formatTimestamp(msg.timeStamp)}
            </Typography>
          </Box>
        </Box>
      )}

      {msg.type === 'view-post' && (
        <Box
          sx={
            msg.senderId === currentUser.uid
              ? { textAlign: 'right', marginBottom: '6px', color: 'white' }
              : { textAlign: 'left', marginBottom: '6px' }
          }
        >
          <Box className='photo-msg-parent'>
            <Box
              className='photo-msg-img-box'
              style={
                isMessageReqAccepted
                  ? {
                      backgroundImage: `url(${msg.message})`,
                      backgroundSize: 'cover'
                    }
                  : {
                      backgroundImage: `url(${msg.message})`,
                      backgroundSize: 'cover',
                      filter: 'blur(6px)'
                    }
              }
            >
              <Box
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'space-evenly',
                  paddingLeft: '0.5rem',
                  paddingRight: '0.5rem'
                }}
              >
                <Box
                  style={{
                    display: 'flex',
                    justifyContent: 'center',
                    alignItems: 'center'
                  }}
                >
                  <Avatar
                    style={{ marginTop: '-15px' }}
                    alt='Remy Sharp'
                    sx={{ height: 25, width: 25 }}
                    src={
                      msg.senderId === currentUser.uid
                        ? currentUser.photoURL
                        : openedUser.photoURL
                    }
                  />
                  <Box className='photo-img-texts-aligns'>
                    <Typography
                      className='userName'
                      fontWeight='fontWeightBold'
                      sx={{
                        color: 'white',
                        fontSize: '12px',
                        paddingLeft: '0.6rem',
                        lineHeight: 1.3
                      }}
                    >
                      {msg.senderId === currentUser.uid
                        ? currentUser.displayName
                        : openedUser.displayName}
                    </Typography>
                    <Typography
                      className='userName'
                      fontWeight='fontWeightNormal'
                      sx={{ color: 'white', fontSize: '10px', lineHeight: 1.3 }}
                    >
                      {'Rishikesh'}
                    </Typography>
                    <Typography
                      className='userName'
                      fontWeight='fontWeightNormal'
                      sx={{ color: 'white', fontSize: '10px', lineHeight: 1.3 }}
                    >
                      12 days ago
                    </Typography>
                  </Box>
                </Box>
                <Box className='photo-img-icon'>
                  <PhotoOutlinedIcon fontSize='20' />
                </Box>
              </Box>
            </Box>
            <Box
              sx={isMessageReqAccepted ? {} : { filter: 'blur(px)' }}
              className='photo-msg-view-post-btn'
            >
              <Typography className='view-post-text'>View Post</Typography>
              <EastOutlinedIcon
                sx={{
                  fontSize: '14px',
                  paddingLeft: '0.24rem',
                  color: '#e67e22',
                  marginTop: '-2px'
                }}
              />
            </Box>
            <Box className='text-msg-timing-parent'>
              <Typography className='text-msg-timing'>
                {formatTimestamp(msg.timeStamp)}
              </Typography>
            </Box>
          </Box>
        </Box>
      )}
      {msg.type === 'document' && (
        <Box
          
          sx={
            msg.senderId === currentUser.uid
              ? { textAlign: 'right', marginBottom: '6px', color: 'white' }
              : { textAlign: 'left', marginBottom: '6px' }
          }
        >
          <Box
            className='photo-msg-parent relative'
            height={95}
            sx={
              msg.senderId === currentUser.uid
                ? { backgroundColor: '#e67e22' }
                : { backgroundColor: '#eee8e8' }
            }
          >
          {msg.senderId === currentUser.uid &&
            <HiDotsVertical color='black' onClick={ ()=>{
              if (msg.senderId === currentUser.uid) {
                setPopoverVisible(true)
                setTypeOfFile("Document")}}
              } className='absolute right-[7px] top-[9px] cursor-pointer'/>
            }
            {msg.msgText}
            <Box className='documentss-box cursor-pointer' style={{ height: 70 }}>
              <Box  onClick={() =>
                isMessageReqAccepted && openDocumentInNewTab(msg.message)
              }
                sx={
                  isMessageReqAccepted
                    ? {
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'flex-start',
                        padding: '0.5rem'
                      }
                    : {
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'flex-start',
                        padding: '0.5rem',
                        filter: 'blur(4px)'
                      }
                }
              >
            
                <img
                  src={
                    msg.docType === 'txt'
                      ? '/icons/text.png'
                      : msg.docType === 'xls'
                      ? '/icons/xls-file.png'
                      : msg.docType === 'xlsx'
                      ? '/icons/xlsx.png'
                      : msg.docType === 'pdf'
                      ? '/icons/pdf.png'
                      : msg.docType === 'docx'
                      ? '/icons/docx.png'
                      : null
                  }
                  alt='filetype'
                  height={50}
                />
                <Box style={{ display: 'flex' }}>
                  <Typography
                    sx={{ fontSize: '10px !important', color: 'gray' }}
                    ml={2}
                    className='isTravellingTotext'
                  >
                    {msg.fileName?.length > 20
                      ? `${msg.fileName.slice(0, 17)}...`
                      : msg.fileName}
                  </Typography>
                </Box>
              </Box>
            </Box>
            <Box>
              <Typography
                className='text-msg-timing'
                style={{ paddingTop: '4px', textAlign: 'right' }}
              >
                {msg.isStarred && (
                  <img
                    style={{ marginRight: '0.5rem' }}
                    src='/images/msgIcons/starred.png'
                    height={8}
                    alt=''
                  />
                )}
                {formatTimestamp(msg.timeStamp)}
              </Typography>
            </Box>
          </Box>
        </Box>
      )}

      {msg.type === 'video' && (
        <Box
          sx={
            msg.senderId === currentUser.uid
              ? {
                  paddingLeft: '1rem',
                  paddingRight: '1rem',
                  textAlign: 'right',
                  marginBottom: '6px',
                  color: 'white'
                }
              : {
                  paddingLeft: '1rem',
                  paddingRight: '1rem',
                  textAlign: 'left',
                  marginBottom: '6px'
                }
          }
        >
          <Box
            className='video-msg-box'
            sx={
              msg.senderId === currentUser.uid
                ? { backgroundColor: '#e67e22' }
                : { backgroundColor: '#eee8e8' }
            }
          >
          {msg.senderId === currentUser.uid &&
            <div style={{display:"flex", justifyContent:"end"}}>
            <HiDotsVertical onClick={ ()=>{
              if (msg.senderId === currentUser.uid) {
                setPopoverVisible(true)
                setTypeOfFile("Video")}}
              } className='cursor-pointer'/>
          </div>
          }
          
          
            {msg.msgText}
            <Box
              className='video-box'
              style={
                isMessageReqAccepted
                  ? { backgroundImage: `url(${msg.thumbnailURL})` }
                  : {
                      backgroundImage: `url(${msg.thumbnailURL})`,
                      filter: 'blur(4px)'
                    }
              }
            >
              <PlayCircleFilledIcon
                onClick={() => isMessageReqAccepted && openModal()}
                sx={{ color: 'white', fontSize: '30px' }}
              />
            </Box>
            <Box>
              <Typography
                className='text-msg-timing'
                style={{ paddingTop: '2px', textAlign: 'right' }}
              >
                {msg.isStarred && (
                  <img
                    style={{ marginRight: '0.5rem' }}
                    src='/images/msgIcons/starred.png'
                    height={8}
                    alt=''
                  />
                )}
                {formatTimestamp(msg.timeStamp)}
              </Typography>
            </Box>
          </Box>
        </Box>
      )}

      <VideoModal
        isOpen={modalOpen}
        onClose={closeModal}
        videoUrl={msg.message}
      />

      {msg.type === 'post' && (
        <Box
          sx={
            msg.senderId === currentUser.uid
              ? {
                  paddingLeft: '1rem',
                  paddingRight: '1rem',
                  textAlign: 'right',
                  marginBottom: '6px',
                  marginTop: '0.7rem'
                }
              : {
                  paddingLeft: '1rem',
                  paddingRight: '1rem',
                  textAlign: 'left',
                  marginBottom: '6px',
                  marginTop: '0.7rem'
                }
          }
        >
          <Box
            className='joinplan-msg-box'
            sx={
              msg.senderId === currentUser.uid
                ? { backgroundColor: '#e67e22' }
                : { backgroundColor: '#eee8e8' }
            }
          >
            {msg.msgText}
            <Box
              className='joinplan-box'
              style={{
                backgroundImage: `url(${post?.imageUrl})`,
                backgroundSize: 'cover'
              }}
            >
              <Box
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'space-between',
                  padding: '0.5rem'
                }}
              >
                <Box style={{ display: 'flex', justifyContent: 'center' }}>
                  <Avatar
                    alt='Remy Sharp'
                    sx={{ height: 25, width: 25 }}
                    src={msg.profileUrl}
                  />
                  <Box className='photo-img-texts-aligns'>
                    <Box style={{ display: 'flex' }}>
                      <Typography
                        className='userName'
                        fontWeight='fontWeightBold'
                        sx={{
                          fontSize: '12px',
                          paddingLeft: '0.6rem',
                          lineHeight: 1.3
                        }}
                      >
                        {post?.name}
                      </Typography>
                      <Typography className='isTravellingTotext'>
                        is travelling to
                      </Typography>
                    </Box>
                    <Typography className='isTravellingTotext'>
                      {'Goa'}
                    </Typography>
                  </Box>
                </Box>
                <Box className='photo-img-icon'>
                  <FlightTakeoffOutlinedIcon fontSize='20' />
                </Box>
              </Box>

              <Typography className='join-plan-desc'>
                {post?.postDescription}
              </Typography>
            </Box>
            <Box style={{ display: 'flex' }}>
              <Box className='join-plan-btns'>Join this plan</Box>
              <Box className='view-post-btns'>View Post</Box>
            </Box>
            <Box>
              <Typography
                className='text-msg-timing'
                style={{ paddingTop: '5px', textAlign: 'right' }}
              >
                {'12:00'}
              </Typography>
            </Box>
          </Box>
        </Box>
      )}
    </Box>
  )
}

export default ChatMsg
